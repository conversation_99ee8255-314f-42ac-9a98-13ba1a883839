/**
 * Global Monaco Editor Validation Configuration
 * Centralized validation rules and language service configuration
 */

export const ValidationConfig = {
    html: {
        validate: true,
        format: {
            wrapLineLength: 120,
            contentUnformatted: "pre,code,textarea",
            indentInnerHtml: true,
            preserveNewLines: true,
            maxPreserveNewLines: 2,
            indentHandlebars: false,
            endWithNewline: false,
            extraLiners: "head,body,/html",
            wrapAttributes: "auto"
        },
        suggest: {
            html5: true,
            angular1: false,
            ionic: false
        },
        data: {
            useDefaultDataProvider: true
        }
    },
    css: {
        validate: true,
        lint: {
            compatibleVendorPrefixes: "ignore",
            vendorPrefix: "warning",
            duplicateProperties: "warning",
            emptyRules: "warning",
            importStatement: "ignore",
            boxModel: "ignore",
            universalSelector: "ignore",
            zeroUnits: "ignore",
            fontFaceProperties: "warning",
            hexColorLength: "error",
            argumentsInColorFunction: "error",
            unknownProperties: "warning",
            ieHack: "ignore",
            unknownVendorSpecificProperties: "ignore",
            propertyIgnoredDueToDisplay: "warning",
            important: "ignore",
            float: "ignore",
            idSelector: "ignore"
        }
    },
    javascript: {
        validate: true,
        diagnosticsOptions: {
            noSemanticValidation: false,
            noSyntaxValidation: false,
            noSuggestionDiagnostics: false
        },
        compilerOptions: {
            target: "ES2020",
            allowNonTsExtensions: true,
            moduleResolution: "node",
            module: "none",
            noEmit: true,
            esModuleInterop: false,
            jsx: "preserve",
            allowJs: true,
            checkJs: false,
            strict: false,
            noImplicitAny: false,
            strictNullChecks: false,
            strictFunctionTypes: false,
            strictPropertyInitialization: false,
            noImplicitReturns: false,
            noImplicitThis: false,
            alwaysStrict: false,
            lib: ["ES2020", "DOM"]
        }
    }
};

/**
 * Custom placeholder validation configuration
 */
export const PlaceholderValidation = {
    enabled: true,
    // kept for lenses/completions; validation uses a balanced scanner below
    pattern: /\$\{([^}]*)\}/g,

    // NEW: behavior switches
    mode: "expression",         // "expression" | "strict"
    allowEmpty: true,           // allow ${}
    allowNested: true,          // allow ${${}} and composites like ${_img-${advantage-image}}

    rules: {
        emptyPlaceholder: {
            severity: "error",
            message: "Empty placeholder variable name"
        },
        invalidVariableName: {
            severity: "warning",
            message: "Invalid placeholder variable name",
            pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/
        }
    },
    suggestions: [
        { label: "title", documentation: "Title placeholder" },
        { label: "description", documentation: "Description placeholder" },
        { label: "content", documentation: "Content placeholder" },
        { label: "image", documentation: "Image placeholder" },
        { label: "link", documentation: "Link placeholder" },
        { label: "name", documentation: "Name placeholder" },
        { label: "email", documentation: "Email placeholder" },
        { label: "phone", documentation: "Phone placeholder" },
        { label: "address", documentation: "Address placeholder" },
        { label: "date", documentation: "Date placeholder" }
    ]
};

// export const PlaceholderValidation = {
//     enabled: true,
//     pattern: /\$\{([^}]*)\}/g,
//     rules: {
//         emptyPlaceholder: {
//             severity: "error",
//             message: "Empty placeholder variable name"
//         },
//         invalidVariableName: {
//             severity: "warning",
//             message: "Invalid placeholder variable name",
//             pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/
//         }
//     },
//     suggestions: [
//         { label: "title", documentation: "Title placeholder" },
//         { label: "description", documentation: "Description placeholder" },
//         { label: "content", documentation: "Content placeholder" },
//         { label: "image", documentation: "Image placeholder" },
//         { label: "link", documentation: "Link placeholder" },
//         { label: "name", documentation: "Name placeholder" },
//         { label: "email", documentation: "Email placeholder" },
//         { label: "phone", documentation: "Phone placeholder" },
//         { label: "address", documentation: "Address placeholder" },
//         { label: "date", documentation: "Date placeholder" }
//     ]
// };

const findBalancedPlaceholders = (text) => {
    const tokens = [];
    const n = text.length;

    for (let i = 0; i < n - 1; i++) {
        if (text[i] === '$' && text[i + 1] === '{') {
            const start = i;
            let j = i + 2;
            let depth = 1;

            while (j < n) {
                if (text[j] === '$' && text[j + 1] === '{') {
                    depth++; j += 2; continue;
                }
                if (text[j] === '}') {
                    depth--; j++;
                    if (depth === 0) break;
                    continue;
                }
                j++;
            }

            if (depth === 0) {
                const end = j; // exclusive
                tokens.push({
                    start,
                    end,
                    innerStart: start + 2,
                    innerEnd: end - 1,
                    content: text.slice(start + 2, end - 1)
                });
                i = end - 1;
            }
            // If unmatched, we ignore here; (optional) you can add a diagnostic for unbalanced `${`
        }
    }
    return tokens;
};


/**
 * Language-specific validation profiles
 */
export const ValidationProfiles = {
    strict: {
        html: { ...ValidationConfig.html },
        css: { ...ValidationConfig.css, lint: { ...ValidationConfig.css.lint, unknownProperties: "error" } },
        javascript: { ...ValidationConfig.javascript }
    },
    relaxed: {
        html: { ...ValidationConfig.html },
        css: { ...ValidationConfig.css, lint: { ...ValidationConfig.css.lint, unknownProperties: "ignore" } },
        javascript: { ...ValidationConfig.javascript, diagnosticsOptions: { ...ValidationConfig.javascript.diagnosticsOptions, noSemanticValidation: true } }
    },
    production: {
        html: { ...ValidationConfig.html },
        css: { ...ValidationConfig.css, lint: { ...ValidationConfig.css.lint, emptyRules: "error", duplicateProperties: "error" } },
        javascript: { ...ValidationConfig.javascript, compilerOptions: { ...ValidationConfig.javascript.compilerOptions, strict: true } }
    }
};

/**
 * Initialize Monaco language services with validation configuration
 */
export const initializeMonacoValidation = (monaco, profile = "default") => {
    const config = profile === "default" ? ValidationConfig : ValidationProfiles[profile] || ValidationConfig;

    // Configure HTML language service
    monaco.languages.html.htmlDefaults.setOptions({
        validate: config.html.validate,
        format: config.html.format,
        suggest: config.html.suggest,
        data: config.html.data
    });

    // Configure CSS language service
    monaco.languages.css.cssDefaults.setOptions({
        validate: config.css.validate,
        lint: config.css.lint
    });

    // Configure JavaScript language service for pure vanilla JS
    monaco.languages.typescript.javascriptDefaults.setDiagnosticsOptions(
        config.javascript.diagnosticsOptions
    );

    monaco.languages.typescript.javascriptDefaults.setCompilerOptions(
        config.javascript.compilerOptions
    );

    return config;
};

/**
 * Create custom diagnostic provider for placeholder validation
 */
export const createPlaceholderDiagnosticProvider = (monaco) => {
    return {
        provideCodeLenses: (model) => {
            const lenses = [];
            const content = model.getValue();

            const regex = PlaceholderValidation.pattern; // global
            regex.lastIndex = 0;

            let match;
            while ((match = regex.exec(content)) !== null) {
                const start = model.getPositionAt(match.index);
                const end = model.getPositionAt(match.index + match[0].length);
                lenses.push({
                    range: {
                        startLineNumber: start.lineNumber,
                        startColumn: start.column,
                        endLineNumber: end.lineNumber,
                        endColumn: end.column
                    },
                    id: `placeholder-${match.index}`,
                    command: {
                        id: "placeholder.info",
                        title: `Placeholder: ${match[1] || "empty"}`,
                        arguments: [{
                            range: {
                                startLineNumber: start.lineNumber,
                                startColumn: start.column,
                                endLineNumber: end.lineNumber,
                                endColumn: end.column
                            },
                            placeholder: match[1],
                            fullMatch: match[0]
                        }]
                    }
                });
            }

            return { lenses, dispose: () => { } };
        }
    };
};

/**
 * Create custom completion provider for placeholders
 */
export const createPlaceholderCompletionProvider = (monaco) => {
    return {
        provideCompletionItems: (model, position) => {
            const word = model.getWordUntilPosition(position);
            const range = {
                startLineNumber: position.lineNumber,
                endLineNumber: position.lineNumber,
                startColumn: word.startColumn,
                endColumn: word.endColumn
            };

            const suggestions = [
                {
                    label: "${placeholder}",
                    kind: monaco.languages.CompletionItemKind.Snippet,
                    insertText: "${${1:variableName}}",
                    insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                    documentation: "Insert a placeholder variable",
                    range: range
                },
                ...PlaceholderValidation.suggestions.map(suggestion => ({
                    label: `\${${suggestion.label}}`,
                    kind: monaco.languages.CompletionItemKind.Variable,
                    insertText: `\${${suggestion.label}}`,
                    documentation: suggestion.documentation,
                    range: range
                }))
            ];

            return { suggestions };
        }
    };
};

/**
 * Create custom hover provider for placeholders
 */
export const createPlaceholderHoverProvider = (monaco) => {
    return {
        provideHover: (model, position) => {
            const text = model.getValue();
            const idx = model.getOffsetAt(position);
            const tokens = findBalancedPlaceholders(text);

            const tok = tokens.find(t => idx >= t.start && idx <= t.end);
            if (!tok) return null;

            const placeholderName = tok.content.trim() || "(empty)";
            const suggestion = PlaceholderValidation.suggestions.find(s => s.label === placeholderName);

            const startPos = model.getPositionAt(tok.start);
            const endPos = model.getPositionAt(tok.end);

            return {
                range: {
                    startLineNumber: startPos.lineNumber,
                    startColumn: startPos.column,
                    endLineNumber: endPos.lineNumber,
                    endColumn: endPos.column
                },
                contents: [
                    { value: `**Placeholder**` },
                    { value: `Content: \`${placeholderName}\`` },
                    { value: suggestion ? suggestion.documentation : "Placeholder expression" }
                ]
            };
        }
    };
};

// export const createPlaceholderHoverProvider = (monaco) => {
//     return {
//         provideHover: (model, position) => {
//             const word = model.getWordAtPosition(position);
//             if (!word) return null;

//             const line = model.getLineContent(position.lineNumber);
//             const placeholderMatch = line.match(/\$\{([^}]*)\}/);

//             if (placeholderMatch &&
//                 position.column >= line.indexOf(placeholderMatch[0]) + 1 &&
//                 position.column <= line.indexOf(placeholderMatch[0]) + placeholderMatch[0].length + 1) {

//                 const placeholderName = placeholderMatch[1];
//                 const suggestion = PlaceholderValidation.suggestions.find(s => s.label === placeholderName);

//                 return {
//                     range: {
//                         startLineNumber: position.lineNumber,
//                         startColumn: line.indexOf(placeholderMatch[0]) + 1,
//                         endLineNumber: position.lineNumber,
//                         endColumn: line.indexOf(placeholderMatch[0]) + placeholderMatch[0].length + 1
//                     },
//                     contents: [
//                         { value: `**Placeholder Variable**` },
//                         { value: `Name: \`${placeholderName}\`` },
//                         { value: suggestion ? suggestion.documentation : "Custom placeholder variable" }
//                     ]
//                 };
//             }

//             return null;
//         }
//     };
// };

/**
 * Validate HTML syntax and return diagnostics
 */
export const validateHTML = (content, monaco) => {
    const diagnostics = [];
    if (!content) return diagnostics;

    // 1) Structural first (incomplete/malformed/etc.)
    validateMalformedTags(content, diagnostics, monaco);

    // 2) Then semantic tag-pairing if structure looks okay
    //    (we still run it; structural errors will already exist if needed)
    const selfClosingTags = [
        "area", "base", "br", "col", "embed", "hr", "img", "input",
        "link", "meta", "param", "source", "track", "wbr"
    ];

    const openingTagRegex = /<(\w+)(?:\s[^>]*)?>/gs;
    const closingTagRegex = /<\/(\w+)>/g;
    const selfClosingRegex = /<(\w+)(?:\s[^>]*)?\/>/gs;

    const openTags = [];
    const closeTags = [];
    const selfClosing = [];

    let match;

    // opening tags
    while ((match = openingTagRegex.exec(content)) !== null) {
        const tagName = match[1].toLowerCase();
        if (!selfClosingTags.includes(tagName)) {
            openTags.push({
                name: tagName,
                position: match.index,
                line: getLineNumber(content, match.index),
                column: getColumnNumber(content, match.index),
                fullMatch: match[0]
            });
        }
    }

    // self-closing
    while ((match = selfClosingRegex.exec(content)) !== null) {
        const tagName = match[1].toLowerCase();
        selfClosing.push({
            name: tagName,
            position: match.index,
            line: getLineNumber(content, match.index)
        });
    }

    // closing tags
    while ((match = closingTagRegex.exec(content)) !== null) {
        const tagName = match[1].toLowerCase();
        closeTags.push({
            name: tagName,
            position: match.index,
            line: getLineNumber(content, match.index),
            column: getColumnNumber(content, match.index)
        });
    }

    // remove self-closing that were parsed as opening
    selfClosing.forEach(selfTag => {
        const idx = openTags.findIndex(o =>
            o.name === selfTag.name &&
            Math.abs(o.position - selfTag.position) < 10
        );
        if (idx !== -1) openTags.splice(idx, 1);
    });

    // pair tags (LIFO-style search for last matching opener)
    const unmatchedOpen = [...openTags];
    closeTags.forEach(close => {
        // Find last matching open
        let i = unmatchedOpen.length - 1;
        while (i >= 0 && unmatchedOpen[i].name !== close.name) i--;
        if (i >= 0) {
            unmatchedOpen.splice(i, 1);
        } else {
            diagnostics.push({
                severity: monaco.MarkerSeverity.Error,
                startLineNumber: close.line,
                startColumn: close.column,
                endLineNumber: close.line,
                endColumn: close.column + close.name.length + 3,
                message: `Closing tag </${close.name}> has no matching opening tag`,
                source: "html-validation"
            });
        }
    });

    // unclosed opens
    unmatchedOpen.forEach(openTag => {
        diagnostics.push({
            severity: monaco.MarkerSeverity.Error,
            startLineNumber: openTag.line,
            startColumn: openTag.column,
            endLineNumber: openTag.line,
            endColumn: openTag.column + openTag.fullMatch.length,
            message: `Unclosed tag <${openTag.name}>: missing closing tag </${openTag.name}>`,
            source: "html-validation"
        });
    });

    return diagnostics;
};


/**
 * Advanced validation for malformed HTML tags
 */
const validateMalformedTags = (content, diagnostics, monaco) => {
    // Pass A: global sweep for incomplete tags (structural errors first)
    const incompletes = findIncompleteTagsAll(content);
    incompletes.forEach(({ start, end, reason }) => {
        diagnostics.push({
            severity: monaco.MarkerSeverity.Error,
            startLineNumber: getLineNumber(content, start),
            startColumn: getColumnNumber(content, start),
            endLineNumber: getLineNumber(content, end),
            endColumn: getColumnNumber(content, end),
            message: reason,
            source: "html-validation"
        });
    });

    // Pass B: line-by-line syntactic checks
    const lines = content.split('\n');
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const lineNumber = i + 1;

        // Invalid tag names (starting with number/special char, exclude closing, comments, doctype)
        const invalidTagMatches = line.match(/<([0-9][^>\s/]*|[^a-zA-Z/!][^>\s/]*)/g);
        if (invalidTagMatches) {
            invalidTagMatches.forEach(tok => {
                if (tok.startsWith("</") || tok.startsWith("<!") || tok.startsWith("<?")) return;
                const position = line.indexOf(tok);
                diagnostics.push({
                    severity: monaco.MarkerSeverity.Error,
                    startLineNumber: lineNumber,
                    startColumn: position + 1,
                    endLineNumber: lineNumber,
                    endColumn: position + tok.length + 1,
                    message: "Invalid HTML tag name: tag names must start with a letter",
                    source: "html-validation"
                });
            });
        }

        // Malformed attributes (unquoted values)
        const malformedAttrMatch = line.match(/\s+([a-zA-Z_:][-a-zA-Z0-9_:.]*)=([^"'\s>][^\s>]*)/g);
        if (malformedAttrMatch) {
            malformedAttrMatch.forEach(tok => {
                const position = line.indexOf(tok);
                diagnostics.push({
                    severity: monaco.MarkerSeverity.Warning,
                    startLineNumber: lineNumber,
                    startColumn: position + 1,
                    endLineNumber: lineNumber,
                    endColumn: position + tok.length + 1,
                    message: "Attribute value should be quoted",
                    source: "html-validation"
                });
            });
        }

        // Duplicate attributes on the same element line segment
        const attrNameMatches = line.match(/\s+([a-zA-Z_:][-a-zA-Z0-9_:.]*)=/g);
        if (attrNameMatches) {
            const names = attrNameMatches.map(s => s.trim().replace(/=$/, ""));
            const seen = new Set();
            names.forEach((name, idx) => {
                if (seen.has(name)) {
                    // mark the later occurrence as duplicate
                    const pattern = new RegExp(`\\s+${name}=`, 'g');
                    let m;
                    let count = 0;
                    while ((m = pattern.exec(line)) !== null) {
                        count++;
                        if (count > 1) {
                            diagnostics.push({
                                severity: monaco.MarkerSeverity.Warning,
                                startLineNumber: lineNumber,
                                startColumn: m.index + 1,
                                endLineNumber: lineNumber,
                                endColumn: m.index + m[0].length + 1,
                                message: `Duplicate attribute '${name}'`,
                                source: "html-validation"
                            });
                        }
                    }
                } else {
                    seen.add(name);
                }
            });
        }

        // Truly empty tags like "<   >"
        const emptyTagRegex = /<\s*>/g;
        let em;
        while ((em = emptyTagRegex.exec(line)) !== null) {
            diagnostics.push({
                severity: monaco.MarkerSeverity.Error,
                startLineNumber: lineNumber,
                startColumn: em.index + 1,
                endLineNumber: lineNumber,
                endColumn: em.index + em[0].length + 1,
                message: "Empty HTML tag: missing tag name",
                source: "html-validation"
            });
        }
    }
};

/**
 * Validate placeholder syntax and return diagnostics
 */
export const validatePlaceholders = (content, monaco) => {
    if (!PlaceholderValidation.enabled) return [];

    const diagnostics = [];
    const { mode = "expression", allowEmpty = true, allowNested = true } = PlaceholderValidation;
    const idPattern = PlaceholderValidation.rules.invalidVariableName?.pattern;

    const tokens = findBalancedPlaceholders(content);

    for (const t of tokens) {
        const raw = t.content;
        const name = raw.trim();

        // Empty allowed?
        if (!name) {
            if (!allowEmpty) {
                diagnostics.push({
                    severity: monaco.MarkerSeverity.Error,
                    startLineNumber: getLineNumber(content, t.start),
                    startColumn: getColumnNumber(content, t.start),
                    endLineNumber: getLineNumber(content, t.end),
                    endColumn: getColumnNumber(content, t.end),
                    message: PlaceholderValidation.rules.emptyPlaceholder.message,
                    source: "placeholder-validation"
                });
            }
            continue;
        }

        // Nested allowed?
        if (allowNested && raw.includes("${")) {
            continue; // don't warn on nested/compound
        }

        // Only enforce identifier format in strict mode on simple names
        if (mode === "strict" && idPattern && !idPattern.test(name)) {
            diagnostics.push({
                severity: monaco.MarkerSeverity.Warning,
                startLineNumber: getLineNumber(content, t.start),
                startColumn: getColumnNumber(content, t.start),
                endLineNumber: getLineNumber(content, t.end),
                endColumn: getColumnNumber(content, t.end),
                message: PlaceholderValidation.rules.invalidVariableName.message,
                source: "placeholder-validation"
            });
        }
    }

    return diagnostics;
};

// export const validatePlaceholders = (content, monaco) => {
//     if (!PlaceholderValidation.enabled) return [];

//     const diagnostics = [];
//     const regex = PlaceholderValidation.pattern; // already global
//     regex.lastIndex = 0;

//     let match;
//     while ((match = regex.exec(content)) !== null) {
//         const placeholderRaw = match[0];
//         const placeholder = match[1] ?? "";
//         const startPos = match.index;
//         const endPos = match.index + placeholderRaw.length;

//         // Empty: ${   }
//         if (!placeholder.trim()) {
//             diagnostics.push({
//                 severity: monaco.MarkerSeverity.Error,
//                 startLineNumber: getLineNumber(content, startPos),
//                 startColumn: getColumnNumber(content, startPos),
//                 endLineNumber: getLineNumber(content, endPos),
//                 endColumn: getColumnNumber(content, endPos),
//                 message: PlaceholderValidation.rules.emptyPlaceholder.message,
//                 source: "placeholder-validation"
//             });
//             continue;
//         }

//         // Invalid variable name against configured pattern
//         const pat = PlaceholderValidation.rules.invalidVariableName?.pattern;
//         if (pat && !pat.test(placeholder)) {
//             diagnostics.push({
//                 severity: monaco.MarkerSeverity.Warning,
//                 startLineNumber: getLineNumber(content, startPos),
//                 startColumn: getColumnNumber(content, startPos),
//                 endLineNumber: getLineNumber(content, endPos),
//                 endColumn: getColumnNumber(content, endPos),
//                 message: PlaceholderValidation.rules.invalidVariableName.message,
//                 source: "placeholder-validation"
//             });
//         }
//     }

//     return diagnostics;
// };

/**
 * Validate content based on language type
 */
export const validateContent = (content, language, monaco) => {
    const diagnostics = [];

    // Always validate placeholders
    diagnostics.push(...validatePlaceholders(content, monaco));

    // Language-specific validation
    if (language === "html") {
        diagnostics.push(...validateHTML(content, monaco));
    }

    return diagnostics;
};

/**
 * Utility functions
 */
const getLineNumber = (text, position) => {
    return text.substring(0, position).split("\n").length;
};

const getColumnNumber = (text, position) => {
    const lines = text.substring(0, position).split("\n");
    return lines[lines.length - 1].length + 1;
};

/**
 * Get validation state from Monaco markers
 */
export const getValidationState = (markers) => {
    const errors = markers.filter(marker => marker.severity === 8); // Monaco.MarkerSeverity.Error
    const warnings = markers.filter(marker => marker.severity === 4); // Monaco.MarkerSeverity.Warning

    return {
        isValid: errors.length === 0,
        hasErrors: errors.length > 0,
        hasWarnings: warnings.length > 0,
        errorCount: errors.length,
        warningCount: warnings.length,
        errors,
        warnings,
        totalIssues: markers.length
    };
};

/**
 * Format validation errors for display
 */
export const formatValidationErrors = (markers, maxDisplay = 3) => {
    const errors = markers.filter(marker => marker.severity === 8);
    const warnings = markers.filter(marker => marker.severity === 4);

    const displayErrors = errors.slice(0, maxDisplay);
    const displayWarnings = warnings.slice(0, maxDisplay - displayErrors.length);

    return {
        displayItems: [...displayErrors, ...displayWarnings],
        hasMore: markers.length > maxDisplay,
        moreCount: markers.length - maxDisplay
    };
};


// Finds any "<..." sequences that don't reach a ">" before the next "<" or EOF.
// Skips comments/doctype/processing instructions.
const findIncompleteTagsAll = (content) => {
    const results = [];
    const ltRegex = /</g;
    let m;

    while ((m = ltRegex.exec(content)) !== null) {
        const start = m.index;
        const nextLt = content.indexOf("<", start + 1);
        const nextGt = content.indexOf(">", start + 1);

        // Ignore comments/doctype/PI like <!--, <!DOCTYPE, <?xml
        const nextTwo = content.slice(start, start + 3);
        const nextFour = content.slice(start, start + 9).toUpperCase();
        const isSkippable =
            nextTwo.startsWith("<?") ||
            nextFour.startsWith("<!DOCTYP") || // <!DOCTYPE
            content.slice(start, start + 4) === "<!--";

        if (isSkippable) {
            // For comments, ensure they close properly; if not closed, it's incomplete
            if (content.slice(start, start + 4) === "<!--") {
                const endComment = content.indexOf("-->", start + 4);
                if (endComment === -1) {
                    results.push({ start, end: (nextLt === -1 ? content.length : Math.min(nextLt, content.length)), reason: "Unclosed HTML comment <!-- ... -->" });
                }
            }
            continue;
        }

        // If there is no ">" before the next "<" (or EOF), it's incomplete
        const boundary = (nextLt === -1 ? content.length : nextLt);
        if (nextGt === -1 || nextGt > boundary) {
            results.push({ start, end: boundary, reason: "Incomplete HTML tag: missing closing '>' character" });
        }
    }

    return results;
};
