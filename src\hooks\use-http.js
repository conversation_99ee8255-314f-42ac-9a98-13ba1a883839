import { useState, useCallback } from "react";
import axios from "axios";
import { notification } from "antd";
import Services from "../util/API/service";
import { deleteAuthDetails } from "../util/API/authStorage";
import { useAuth } from "../contexts/AuthContext";
import { useError } from "../contexts/ErrorContext";

export const buildQueryString = (params) => {
  const queryParts = [];

  for (const key in params) {
    if (params.hasOwnProperty(key)) {
      const value = params[key];

      if (key.startsWith("autogenerate-mul-array-") && Array.isArray(value)) {
        const arrayKey = key.slice("autogenerate-mul-array-".length);
        value.forEach((item) => {
          queryParts.push(
            `${arrayKey}=${item}`,
            // `${encodeURIComponent(arrayKey)}=${encodeURIComponent(item)}`
          );
        });
      } else {
        // Handle other cases
        queryParts.push(
          `${key}=${value}`,
          // `${encodeURIComponent(key)}=${encodeURIComponent(value)}`
        );
      }
    }
  }

  return queryParts.length > 0 ? `?${queryParts.join("&")}` : "";
};

const useHttp = () => {
  const [isLoading, setIsLoading] = useState(false);
  const { logout } = useAuth();
  const { handleHttpError, handleNetworkError } = useError();
  const sendRequest = useCallback(
    async (url, responseHandler, payload, successMessage, errorHandler) => {
      setIsLoading(true);
      try {
        let response;
        switch (url.type) {
          case "POST":
            response = await Services.post(url.endpoint, payload);
            break;

          case "PATCH":
            response = await Services.patch(url.endpoint, payload);

            break;
          case "DELETE":
            response = await Services.delete(url.endpoint);
            break;

          case "PATCH":
            response = await Services.patch(url.endpoint, payload);
            break;

          default:
            const queryParams = buildQueryString(payload);
            response = await Services.get(`${url.endpoint}${queryParams}`);
            break;
        }

        const data = await response?.data;
        if (successMessage) {
          notification.success({ message: successMessage, duration: "3" });
        }
        try {
          if (responseHandler) {
            responseHandler(data);
          }
        } catch (e) {
          console.log(e);
        }
      } catch (err) {
        // Handle network errors (no response)
        if (!err.response) {
          handleNetworkError(err);
          if (errorHandler) {
            errorHandler("Network error. Please check your internet connection.");
          }
          return;
        }

        // Handle HTTP errors with proper error pages
        const status = err.response.status;
        const statusText = err.response.statusText;

        // Handle specific error cases with redirects
        // if (err?.response?.data?.status === 401 && err?.response?.data?.message === "Your account is suspended") {
        //   window.location.replace('/suspendeduser');
        //   return;
        // }
        // if (err?.response?.status === 401 && err?.response?.data?.status === "Permission Denied") {
        //   window.location.assign('/blockuser');
        //   return;
        // }
        if (err?.response?.data?.message === "jwt expired" || err?.response?.data?.message === "You are not authorize person") {
          logout();
          window.location.reload();
          return;
        }
        console.log(status, "status")

        // Handle server errors (500+)
        // if (status >= 500) {
        //   window.location.replace('/500');
        //   return;
        // }

        // Handle not found errors
        if (status === 404) {
          window.location.replace('/404');
          return;
        }

        // Handle forbidden errors
        if (status === 403) {
          window.location.replace('/unauthorized');
          return;
        }

        // Handle unauthorized errors
        if (status === 401) {
          window.location.replace('/unauthorized');
          return;
        }

        // Log error to error context
        handleHttpError(err, err.response);

        // Show notification for other errors
        if (errorHandler) {
          errorHandler(err?.response?.data?.message);
        } else if (err?.response?.data?.error) {
          notification.error({
            message: err?.response?.data?.error,
            duration: "3",
          });
        } else {
          notification.error({ message: "Something Wrong Please Try again" });
        }
      }
      setIsLoading(false);
    },
    [],
  );

  const sendBulkRequest = useCallback(
    async (urls, responseHandler, successMessage, errorHandler) => {
      setIsLoading(true);
      try {
        const response = await axios.all(
          urls?.map((url) => {
            switch (url?.url?.type) {
              case "POST":
                return Services.post(url?.url?.endpoint, url?.payload);

              case "PUT":
                return Services.put(url?.url?.endpoint, url?.payload);

              case "DELETE":
                return Services.delete(url?.url?.endpoint);

              case "PATCH":
                return Services.patch(url?.url?.endpoint, url?.payload);

              default:
                return Services.get(url?.endpoint);
            }
          }),
        );

        if (successMessage) {
          notification.success({ message: successMessage, duration: "3" });
        }
        try {
          if (responseHandler) {
            responseHandler(response);
          }
        } catch (e) {
          console.log(e);
        }
      } catch (err) {
        // Handle network errors (no response)
        if (!err.response) {
          handleNetworkError(err);
          if (errorHandler) {
            errorHandler("Network error. Please check your internet connection.");
          }
          return;
        }

        // Handle HTTP errors with proper error pages
        const status = err.response.status;

        // Handle specific error cases with redirects
        // if (err?.response?.data?.status === 401 && err?.response?.data?.message === "Your account is blocked") {
        //   window.location.replace('/blockuser');
        //   return;
        // } else if (err?.response?.data?.status === 401 && err?.response?.data?.message === "Your account is suspended") {
        //   window.location.replace('/suspendeduser');
        //   return;
        // }

        // Handle server errors (500+)
        if (status >= 500) {
          window.location.replace('/500');
          return;
        }

        // Handle not found errors
        if (status === 404) {
          window.location.replace('/404');
          return;
        }

        // Handle forbidden errors
        if (status === 403) {
          window.location.replace('/unauthorized');
          return;
        }

        // Handle unauthorized errors
        if (status === 401) {
          window.location.replace('/unauthorized');
          return;
        }

        // Log error to error context
        handleHttpError(err, err.response);

        console.log(err?.response?.data?.message);
        if (err?.response?.data?.message) {
          notification.error({
            message: err?.response?.data?.message,
            duration: "3",
          });
          if (errorHandler) {
            errorHandler(err?.response?.data?.message);
          }
          return;
        }

        return notification.error({
          message: "Something Wrong Please Try again",
        });
      }
      setIsLoading(false);
    },
    [],
  );
  return {
    isLoading,
    sendRequest,
    sendBulkRequest,
  };
};

export default useHttp;
