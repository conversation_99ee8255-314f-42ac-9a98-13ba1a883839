/**
 * Utility functions for transforming page data between different formats
 * Handles conversion from getOne API response to edit format and vice versa
 */

const generateUniqueId = (componentId) => {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 11);
    return `${componentId}-${timestamp}-${random}`;
};

const findComponentByVersionId = (componentVersionId, components) => {
    return components.find(comp => comp?.id === componentVersionId) || null;
};

const createCategoryData = (categoryId) => {
    // This would typically come from a categories lookup
    // For now, creating a placeholder structure
    return {
        id: categoryId || 1,
        name: "Default Category",
        description: "Default category description",
        colour: "#1FC16B",
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        deletedAt: null,
        createdBy: 1
    };
};

const transformRepeatComponents = (repeatComponents, components) => {
    if (!repeatComponents || !Array.isArray(repeatComponents)) {
        return [];
    }

    return repeatComponents.map(repeatComp => {
        const foundComponent = findComponentByVersionId(repeatComp.componentVersionId, components);

        if (!foundComponent) {
            console.warn(`Component not found for repeatComponent with versionId: ${repeatComp.componentVersionId}`);
            return {
                key: repeatComp.key || "",
                componentVersionId: repeatComp?.componentVersionId,
                value: repeatComp?.componentVersionId,
                label: `Component ${repeatComp?.componentVersionId}`,
                id: repeatComp?.componentVersionId,
                name: `component_${repeatComp?.componentVersionId}`,
                isActive: true,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                deletedAt: null,
                categoryId: 1,
                createdBy: 1,
                componentversions: [{ id: repeatComp.componentVersionId, version: 1 }],
                version: 1,
                html: "",
                css: "",
                js: ""
            };
        }

        return {
            key: repeatComp.key || "",
            versionId: foundComponent?.id,
            componentId: foundComponent?.componentId,
            componentName: foundComponent?.componentId ? `component_${foundComponent?.componentId}` : `component_${foundComponent?.id}`,
            value: foundComponent?.componentId || foundComponent?.id,
            label: foundComponent?.componentId ? `component_${foundComponent?.componentId}` : `component_${foundComponent?.id}`,
            id: foundComponent?.id,
            name: foundComponent?.componentId ? `component_${foundComponent?.componentId}` : `component_${foundComponent?.id}`,
            isActive: true,
            createdAt: foundComponent?.createdAt || new Date()?.toISOString(),
            updatedAt: foundComponent?.updatedAt || new Date()?.toISOString(),
            deletedAt: foundComponent?.deletedAt,
            categoryId: foundComponent?.categoryId || 1,
            createdBy: foundComponent?.createdBy || 1,
            componentversions: [{
                id: foundComponent?.id,
                version: foundComponent?.version || 1
            }],
            version: foundComponent?.version || 1,
            html: foundComponent?.html || "",
            css: foundComponent?.css || "",
            js: foundComponent?.js || ""
        };
    });
};

const transformComponentDataItem = (componentDataItem, components) => {
    const foundComponent = findComponentByVersionId(componentDataItem?.componentVersionId, components);

    if (!foundComponent) {
        console.warn(`Component not found for componentVersionId: ${componentDataItem?.componentVersionId}`);
        return {
            ...componentDataItem,
            id: componentDataItem?.componentVersionId,
            isActive: true,
            createdAt: new Date()?.toISOString(),
            updatedAt: new Date()?.toISOString(),
            deletedAt: null,
            categoryId: 1,
            createdBy: 1,
            componentversions: [{ id: componentDataItem?.componentVersionId, version: 1 }],
            version: 1,
            categoryData: createCategoryData(1),
            uniqueId: generateUniqueId(componentDataItem?.componentVersionId),
            isLoading: false,
            html: "",
            css: "",
            js: "",
            componentId: componentDataItem?.componentVersionId
        };
    }

    const transformedItem = {
        ...componentDataItem,
        id: foundComponent?.componentId,
        name: componentDataItem?.name,
        isActive: true,
        createdAt: foundComponent?.createdAt || new Date()?.toISOString(),
        updatedAt: foundComponent?.updatedAt || new Date()?.toISOString(),
        deletedAt: foundComponent?.deletedAt,
        categoryId: foundComponent?.categoryId || 1,
        createdBy: foundComponent?.createdBy || 1,
        componentversions: [{
            id: foundComponent?.id,
            version: foundComponent?.version || 1
        }],
        version: foundComponent?.version || 1,
        categoryData: createCategoryData(foundComponent?.categoryId || 1),
        uniqueId: generateUniqueId(foundComponent?.componentId || foundComponent?.id),
        isLoading: false,
        html: foundComponent?.html || "",
        css: foundComponent?.css || "",
        js: foundComponent?.js || "",
        componentId: foundComponent?.componentId || foundComponent?.id
    };

    // Handle repeat components if they exist
    if (componentDataItem?.repeatComponents && Array.isArray(componentDataItem?.repeatComponents)) {
        transformedItem.repeatComponents = transformRepeatComponents(componentDataItem?.repeatComponents, components);
    }

    return transformedItem;
};

export const transformGetOneToEditFormat = (getOneResponse) => {
    try {
        if (!getOneResponse || !getOneResponse.data || !Array.isArray(getOneResponse.data) || getOneResponse.data.length === 0) {
            throw new Error('Invalid getOne response format');
        }

        const pageData = getOneResponse.data[0];
        const latestVersion = pageData.latestVersion;

        if (!latestVersion) {
            throw new Error('No latest version found in page data');
        }

        const components = latestVersion?.components || [];
        const componentData = latestVersion?.componentData || [];

        // Transform componentData by merging with component details
        const transformedComponentData = componentData?.map(item =>
            transformComponentDataItem(item, components)
        );

        // Create the transformed page data structure
        const transformedData = {
            id: pageData.id,
            name: pageData.name,
            urlSlug: latestVersion.urlSlug,
            metaTitle: latestVersion.metaTitle,
            metaDescription: latestVersion.metaDescription,
            wrapperClass: latestVersion.wrapperClass,
            customCss: latestVersion.customCss,
            customJs: latestVersion.customJs,
            headers: latestVersion.headers,
            full_page_content: "", // This field doesn't exist in getOne response
            componentData: transformedComponentData,
            sitemapLabel: pageData.name // Using page name as sitemap label
        };

        console.log('Data transformation completed successfully');
        return transformedData;

    } catch (error) {
        console.error('Error transforming getOne response:', error);
        throw error;
    }
};

export const createPartialEditPayload = (originalData, modifiedData) => {
    try {
        const payload = {};

        // List of fields to check for changes
        const fieldsToCheck = [
            'name', 'urlSlug', 'metaTitle', 'metaDescription',
            'wrapperClass', 'customCss', 'customJs', 'headers'
        ];

        // Check each field for changes
        fieldsToCheck.forEach(field => {
            if (originalData[field] !== modifiedData[field]) {
                payload[field] = modifiedData[field];
            }
        });

        // Special handling for componentData - deep comparison
        const componentDataChanged = hasComponentDataChanged(
            originalData.componentData,
            modifiedData.componentData
        );

        if (componentDataChanged) {
            // If componentData changed, include the full array
            payload.componentData = modifiedData.componentData;
            console.log('ComponentData changes detected, including full array in payload');
        }

        console.log('Partial payload created:', payload);
        return payload;

    } catch (error) {
        console.error('Error creating partial edit payload:', error);
        throw error;
    }
};

export const hasComponentDataChanged = (original, modified) => {
    try {
        if (!original && !modified) return false;
        if (!original || !modified) return true;
        if (original.length !== modified.length) return true;

        // Convert to JSON strings for deep comparison
        const originalStr = JSON.stringify(original, null, 0);
        const modifiedStr = JSON.stringify(modified, null, 0);

        return originalStr !== modifiedStr;

    } catch (error) {
        console.error('Error comparing componentData:', error);
        // If comparison fails, assume changes exist to be safe
        return true;
    }
};

export const transformEditToApiFormat = (editData) => {
    try {
        // For now, return the data as-is since the API expects the same format
        // This function can be enhanced if the API expects a different format
        return editData;

    } catch (error) {
        console.error('Error transforming edit data to API format:', error);
        throw error;
    }
};

export default {
    transformGetOneToEditFormat,
    createPartialEditPayload,
    hasComponentDataChanged,
    transformEditToApiFormat
};
