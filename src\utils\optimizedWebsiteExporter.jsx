import JSZip from "jszip";

// Font URLs to download and self-host
const FONT_URLS = {
  "Inter-400.woff2":
    "https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2",
  "Inter-500.woff2":
    "https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2",
  "Inter-700.woff2":
    "https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2",
  "Phudu-600.woff2":
    "https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2",
  "Phudu-700.woff2":
    "https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2",
};

// Optimized Tailwind CSS (purged and minified)
const OPTIMIZED_TAILWIND_CSS = `*,::before,::after{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}::before,::after{--tw-content:''}html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal}body{margin:0;line-height:inherit}.dw-block{display:block}.dw-inline-block{display:inline-block}.dw-inline{display:inline}.dw-flex{display:flex}.dw-inline-flex{display:inline-flex}.dw-grid{display:grid}.dw-hidden{display:none}.dw-static{position:static}.dw-fixed{position:fixed}.dw-absolute{position:absolute}.dw-relative{position:relative}.dw-sticky{position:sticky}.dw-top-0{top:0}.dw-right-0{right:0}.dw-bottom-0{bottom:0}.dw-left-0{left:0}.dw-top-1\\/2{top:50%}.dw-left-1\\/2{left:50%}.dw-z-10{z-index:10}.dw-z-20{z-index:20}.dw-z-30{z-index:30}.dw-z-40{z-index:40}.dw-z-50{z-index:50}.dw-flex-row{flex-direction:row}.dw-flex-row-reverse{flex-direction:row-reverse}.dw-flex-col{flex-direction:column}.dw-flex-col-reverse{flex-direction:column-reverse}.dw-flex-wrap{flex-wrap:wrap}.dw-flex-wrap-reverse{flex-wrap:wrap-reverse}.dw-flex-nowrap{flex-wrap:nowrap}.dw-items-start{align-items:flex-start}.dw-items-end{align-items:flex-end}.dw-items-center{align-items:center}.dw-items-baseline{align-items:baseline}.dw-items-stretch{align-items:stretch}.dw-justify-start{justify-content:flex-start}.dw-justify-end{justify-content:flex-end}.dw-justify-center{justify-content:center}.dw-justify-between{justify-content:space-between}.dw-justify-around{justify-content:space-around}.dw-justify-evenly{justify-content:space-evenly}.dw-gap-1{gap:.25rem}.dw-gap-2{gap:.5rem}.dw-gap-3{gap:.75rem}.dw-gap-4{gap:1rem}.dw-gap-5{gap:1.25rem}.dw-gap-6{gap:1.5rem}.dw-gap-8{gap:2rem}.dw-p-0{padding:0}.dw-p-1{padding:.25rem}.dw-p-2{padding:.5rem}.dw-p-3{padding:.75rem}.dw-p-4{padding:1rem}.dw-p-5{padding:1.25rem}.dw-p-6{padding:1.5rem}.dw-p-8{padding:2rem}.dw-p-10{padding:2.5rem}.dw-p-12{padding:3rem}.dw-p-16{padding:4rem}.dw-px-1{padding-left:.25rem;padding-right:.25rem}.dw-px-2{padding-left:.5rem;padding-right:.5rem}.dw-px-3{padding-left:.75rem;padding-right:.75rem}.dw-px-4{padding-left:1rem;padding-right:1rem}.dw-px-5{padding-left:1.25rem;padding-right:1.25rem}.dw-px-6{padding-left:1.5rem;padding-right:1.5rem}.dw-px-8{padding-left:2rem;padding-right:2rem}.dw-py-1{padding-top:.25rem;padding-bottom:.25rem}.dw-py-2{padding-top:.5rem;padding-bottom:.5rem}.dw-py-3{padding-top:.75rem;padding-bottom:.75rem}.dw-py-4{padding-top:1rem;padding-bottom:1rem}.dw-py-5{padding-top:1.25rem;padding-bottom:1.25rem}.dw-py-6{padding-top:1.5rem;padding-bottom:1.5rem}.dw-py-8{padding-top:2rem;padding-bottom:2rem}.dw-pt-1{padding-top:.25rem}.dw-pt-2{padding-top:.5rem}.dw-pt-3{padding-top:.75rem}.dw-pt-4{padding-top:1rem}.dw-pt-6{padding-top:1.5rem}.dw-pt-8{padding-top:2rem}.dw-pr-1{padding-right:.25rem}.dw-pr-2{padding-right:.5rem}.dw-pr-3{padding-right:.75rem}.dw-pr-4{padding-right:1rem}.dw-pr-6{padding-right:1.5rem}.dw-pr-8{padding-right:2rem}.dw-pb-1{padding-bottom:.25rem}.dw-pb-2{padding-bottom:.5rem}.dw-pb-3{padding-bottom:.75rem}.dw-pb-4{padding-bottom:1rem}.dw-pb-6{padding-bottom:1.5rem}.dw-pb-8{padding-bottom:2rem}.dw-pl-1{padding-left:.25rem}.dw-pl-2{padding-left:.5rem}.dw-pl-3{padding-left:.75rem}.dw-pl-4{padding-left:1rem}.dw-pl-6{padding-left:1.5rem}.dw-pl-8{padding-left:2rem}.dw-m-0{margin:0}.dw-m-1{margin:.25rem}.dw-m-2{margin:.5rem}.dw-m-3{margin:.75rem}.dw-m-4{margin:1rem}.dw-m-5{margin:1.25rem}.dw-m-6{margin:1.5rem}.dw-m-8{margin:2rem}.dw-m-auto{margin:auto}.dw-mx-1{margin-left:.25rem;margin-right:.25rem}.dw-mx-2{margin-left:.5rem;margin-right:.5rem}.dw-mx-3{margin-left:.75rem;margin-right:.75rem}.dw-mx-4{margin-left:1rem;margin-right:1rem}.dw-mx-6{margin-left:1.5rem;margin-right:1.5rem}.dw-mx-8{margin-left:2rem;margin-right:2rem}.dw-mx-auto{margin-left:auto;margin-right:auto}.dw-my-1{margin-top:.25rem;margin-bottom:.25rem}.dw-my-2{margin-top:.5rem;margin-bottom:.5rem}.dw-my-3{margin-top:.75rem;margin-bottom:.75rem}.dw-my-4{margin-top:1rem;margin-bottom:1rem}.dw-my-6{margin-top:1.5rem;margin-bottom:1.5rem}.dw-my-8{margin-top:2rem;margin-bottom:2rem}.dw-mt-1{margin-top:.25rem}.dw-mt-2{margin-top:.5rem}.dw-mt-3{margin-top:.75rem}.dw-mt-4{margin-top:1rem}.dw-mt-6{margin-top:1.5rem}.dw-mt-8{margin-top:2rem}.dw-mr-1{margin-right:.25rem}.dw-mr-2{margin-right:.5rem}.dw-mr-3{margin-right:.75rem}.dw-mr-4{margin-right:1rem}.dw-mr-6{margin-right:1.5rem}.dw-mr-8{margin-right:2rem}.dw-mb-1{margin-bottom:.25rem}.dw-mb-2{margin-bottom:.5rem}.dw-mb-3{margin-bottom:.75rem}.dw-mb-4{margin-bottom:1rem}.dw-mb-6{margin-bottom:1.5rem}.dw-mb-8{margin-bottom:2rem}.dw-ml-1{margin-left:.25rem}.dw-ml-2{margin-left:.5rem}.dw-ml-3{margin-left:.75rem}.dw-ml-4{margin-left:1rem}.dw-ml-6{margin-left:1.5rem}.dw-ml-8{margin-left:2rem}.dw-w-auto{width:auto}.dw-w-full{width:100%}.dw-w-1\\/2{width:50%}.dw-w-1\\/3{width:33.333333%}.dw-w-2\\/3{width:66.666667%}.dw-w-1\\/4{width:25%}.dw-w-3\\/4{width:75%}.dw-w-1\\/5{width:20%}.dw-w-2\\/5{width:40%}.dw-w-3\\/5{width:60%}.dw-w-4\\/5{width:80%}.dw-w-4{width:1rem}.dw-w-5{width:1.25rem}.dw-w-6{width:1.5rem}.dw-w-8{width:2rem}.dw-w-10{width:2.5rem}.dw-w-12{width:3rem}.dw-w-16{width:4rem}.dw-w-20{width:5rem}.dw-w-24{width:6rem}.dw-w-32{width:8rem}.dw-w-40{width:10rem}.dw-w-48{width:12rem}.dw-w-56{width:14rem}.dw-w-64{width:16rem}.dw-h-auto{height:auto}.dw-h-full{height:100%}.dw-h-screen{height:100vh}.dw-h-4{height:1rem}.dw-h-5{height:1.25rem}.dw-h-6{height:1.5rem}.dw-h-8{height:2rem}.dw-h-10{height:2.5rem}.dw-h-12{height:3rem}.dw-h-16{height:4rem}.dw-h-20{height:5rem}.dw-h-24{height:6rem}.dw-h-32{height:8rem}.dw-h-40{height:10rem}.dw-h-48{height:12rem}.dw-h-56{height:14rem}.dw-h-64{height:16rem}.dw-min-w-0{min-width:0}.dw-min-w-full{min-width:100%}.dw-max-w-none{max-width:none}.dw-max-w-xs{max-width:20rem}.dw-max-w-sm{max-width:24rem}.dw-max-w-md{max-width:28rem}.dw-max-w-lg{max-width:32rem}.dw-max-w-xl{max-width:36rem}.dw-max-w-2xl{max-width:42rem}.dw-max-w-3xl{max-width:48rem}.dw-max-w-4xl{max-width:56rem}.dw-max-w-5xl{max-width:64rem}.dw-max-w-6xl{max-width:72rem}.dw-max-w-7xl{max-width:80rem}.dw-max-w-full{max-width:100%}.dw-min-h-0{min-height:0}.dw-min-h-full{min-height:100%}.dw-min-h-screen{min-height:100vh}.dw-max-h-full{max-height:100%}.dw-max-h-screen{max-height:100vh}.dw-font-sans{font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji"}.dw-font-serif{font-family:ui-serif,Georgia,Cambria,"Times New Roman",Times,serif}.dw-font-mono{font-family:ui-monospace,SFMono-Regular,"SF Mono",Consolas,"Liberation Mono",Menlo,monospace}.dw-text-xs{font-size:.75rem;line-height:1rem}.dw-text-sm{font-size:.875rem;line-height:1.25rem}.dw-text-base{font-size:1rem;line-height:1.5rem}.dw-text-lg{font-size:1.125rem;line-height:1.75rem}.dw-text-xl{font-size:1.25rem;line-height:1.75rem}.dw-text-2xl{font-size:1.5rem;line-height:2rem}.dw-text-3xl{font-size:1.875rem;line-height:2.25rem}.dw-text-4xl{font-size:2.25rem;line-height:2.5rem}.dw-text-5xl{font-size:3rem;line-height:1}.dw-text-6xl{font-size:3.75rem;line-height:1}.dw-font-thin{font-weight:100}.dw-font-extralight{font-weight:200}.dw-font-light{font-weight:300}.dw-font-normal{font-weight:400}.dw-font-medium{font-weight:500}.dw-font-semibold{font-weight:600}.dw-font-bold{font-weight:700}.dw-font-extrabold{font-weight:800}.dw-font-black{font-weight:900}.dw-text-left{text-align:left}.dw-text-center{text-align:center}.dw-text-right{text-align:right}.dw-text-justify{text-align:justify}.dw-text-transparent{color:transparent}.dw-text-current{color:currentColor}.dw-text-black{color:rgb(0 0 0)}.dw-text-white{color:rgb(255 255 255)}.dw-text-gray-50{color:rgb(249 250 251)}.dw-text-gray-100{color:rgb(243 244 246)}.dw-text-gray-200{color:rgb(229 231 235)}.dw-text-gray-300{color:rgb(209 213 219)}.dw-text-gray-400{color:rgb(156 163 175)}.dw-text-gray-500{color:rgb(107 114 128)}.dw-text-gray-600{color:rgb(75 85 99)}.dw-text-gray-700{color:rgb(55 65 81)}.dw-text-gray-800{color:rgb(31 41 55)}.dw-text-gray-900{color:rgb(17 24 39)}.dw-text-red-500{color:rgb(239 68 68)}.dw-text-red-600{color:rgb(220 38 38)}.dw-text-blue-500{color:rgb(59 130 246)}.dw-text-blue-600{color:rgb(37 99 235)}.dw-text-green-500{color:rgb(34 197 94)}.dw-text-green-600{color:rgb(22 163 74)}.dw-text-yellow-500{color:rgb(234 179 8)}.dw-text-purple-500{color:rgb(168 85 247)}.dw-text-purple-600{color:rgb(147 51 234)}.dw-bg-transparent{background-color:transparent}.dw-bg-current{background-color:currentColor}.dw-bg-black{background-color:rgb(0 0 0)}.dw-bg-white{background-color:rgb(255 255 255)}.dw-bg-gray-50{background-color:rgb(249 250 251)}.dw-bg-gray-100{background-color:rgb(243 244 246)}.dw-bg-gray-200{background-color:rgb(229 231 235)}.dw-bg-gray-300{background-color:rgb(209 213 219)}.dw-bg-gray-400{background-color:rgb(156 163 175)}.dw-bg-gray-500{background-color:rgb(107 114 128)}.dw-bg-gray-600{background-color:rgb(75 85 99)}.dw-bg-gray-700{background-color:rgb(55 65 81)}.dw-bg-gray-800{background-color:rgb(31 41 55)}.dw-bg-gray-900{background-color:rgb(17 24 39)}.dw-bg-red-500{background-color:rgb(239 68 68)}.dw-bg-red-600{background-color:rgb(220 38 38)}.dw-bg-blue-500{background-color:rgb(59 130 246)}.dw-bg-blue-600{background-color:rgb(37 99 235)}.dw-bg-green-500{background-color:rgb(34 197 94)}.dw-bg-green-600{background-color:rgb(22 163 74)}.dw-bg-yellow-500{background-color:rgb(234 179 8)}.dw-bg-purple-500{background-color:rgb(168 85 247)}.dw-bg-purple-600{background-color:rgb(147 51 234)}.dw-bg-gradient-to-r{background-image:linear-gradient(to right,var(--tw-gradient-stops))}.dw-bg-gradient-to-l{background-image:linear-gradient(to left,var(--tw-gradient-stops))}.dw-bg-gradient-to-t{background-image:linear-gradient(to top,var(--tw-gradient-stops))}.dw-bg-gradient-to-b{background-image:linear-gradient(to bottom,var(--tw-gradient-stops))}.dw-bg-gradient-to-tr{background-image:linear-gradient(to top right,var(--tw-gradient-stops))}.dw-bg-gradient-to-tl{background-image:linear-gradient(to top left,var(--tw-gradient-stops))}.dw-bg-gradient-to-br{background-image:linear-gradient(to bottom right,var(--tw-gradient-stops))}.dw-bg-gradient-to-bl{background-image:linear-gradient(to bottom left,var(--tw-gradient-stops))}.dw-from-blue-500{--tw-gradient-from:#3b82f6 var(--tw-gradient-from-position);--tw-gradient-to:rgb(59 130 246 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from),var(--tw-gradient-to)}.dw-from-blue-600{--tw-gradient-from:#2563eb var(--tw-gradient-from-position);--tw-gradient-to:rgb(37 99 235 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from),var(--tw-gradient-to)}.dw-to-purple-500{--tw-gradient-to:#a855f7 var(--tw-gradient-to-position)}.dw-to-purple-600{--tw-gradient-to:#9333ea var(--tw-gradient-to-position)}.dw-border-0{border-width:0}.dw-border{border-width:1px}.dw-border-2{border-width:2px}.dw-border-4{border-width:4px}.dw-border-8{border-width:8px}.dw-border-t{border-top-width:1px}.dw-border-r{border-right-width:1px}.dw-border-b{border-bottom-width:1px}.dw-border-l{border-left-width:1px}.dw-border-transparent{border-color:transparent}.dw-border-current{border-color:currentColor}.dw-border-black{border-color:rgb(0 0 0)}.dw-border-white{border-color:rgb(255 255 255)}.dw-border-gray-200{border-color:rgb(229 231 235)}.dw-border-gray-300{border-color:rgb(209 213 219)}.dw-border-gray-400{border-color:rgb(156 163 175)}.dw-border-gray-500{border-color:rgb(107 114 128)}.dw-border-blue-500{border-color:rgb(59 130 246)}.dw-border-red-500{border-color:rgb(239 68 68)}.dw-rounded-none{border-radius:0}.dw-rounded-sm{border-radius:.125rem}.dw-rounded{border-radius:.25rem}.dw-rounded-md{border-radius:.375rem}.dw-rounded-lg{border-radius:.5rem}.dw-rounded-xl{border-radius:.75rem}.dw-rounded-2xl{border-radius:1rem}.dw-rounded-3xl{border-radius:1.5rem}.dw-rounded-full{border-radius:9999px}.dw-shadow-sm{box-shadow:0 1px 2px 0 rgb(0 0 0 / 0.05)}.dw-shadow{box-shadow:0 1px 3px 0 rgb(0 0 0 / 0.1),0 1px 2px -1px rgb(0 0 0 / 0.1)}.dw-shadow-md{box-shadow:0 4px 6px -1px rgb(0 0 0 / 0.1),0 2px 4px -2px rgb(0 0 0 / 0.1)}.dw-shadow-lg{box-shadow:0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1)}.dw-shadow-xl{box-shadow:0 20px 25px -5px rgb(0 0 0 / 0.1),0 8px 10px -6px rgb(0 0 0 / 0.1)}.dw-shadow-2xl{box-shadow:0 25px 50px -12px rgb(0 0 0 / 0.25)}.dw-shadow-none{box-shadow:0 0 #0000}.dw-opacity-0{opacity:0}.dw-opacity-5{opacity:.05}.dw-opacity-10{opacity:.1}.dw-opacity-20{opacity:.2}.dw-opacity-25{opacity:.25}.dw-opacity-30{opacity:.3}.dw-opacity-40{opacity:.4}.dw-opacity-50{opacity:.5}.dw-opacity-60{opacity:.6}.dw-opacity-70{opacity:.7}.dw-opacity-75{opacity:.75}.dw-opacity-80{opacity:.8}.dw-opacity-90{opacity:.9}.dw-opacity-95{opacity:.95}.dw-opacity-100{opacity:1}.dw-cursor-auto{cursor:auto}.dw-cursor-default{cursor:default}.dw-cursor-pointer{cursor:pointer}.dw-cursor-wait{cursor:wait}.dw-cursor-text{cursor:text}.dw-cursor-move{cursor:move}.dw-cursor-help{cursor:help}.dw-cursor-not-allowed{cursor:not-allowed}.dw-transition-none{transition-property:none}.dw-transition-all{transition-property:all;transition-timing-function:cubic-bezier(0.4,0,0.2,1);transition-duration:150ms}.dw-transition{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-timing-function:cubic-bezier(0.4,0,0.2,1);transition-duration:150ms}.dw-transition-colors{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(0.4,0,0.2,1);transition-duration:150ms}.dw-transition-opacity{transition-property:opacity;transition-timing-function:cubic-bezier(0.4,0,0.2,1);transition-duration:150ms}.dw-transition-shadow{transition-property:box-shadow;transition-timing-function:cubic-bezier(0.4,0,0.2,1);transition-duration:150ms}.dw-transition-transform{transition-property:transform;transition-timing-function:cubic-bezier(0.4,0,0.2,1);transition-duration:150ms}.dw-duration-75{transition-duration:75ms}.dw-duration-100{transition-duration:100ms}.dw-duration-150{transition-duration:150ms}.dw-duration-200{transition-duration:200ms}.dw-duration-300{transition-duration:300ms}.dw-duration-500{transition-duration:500ms}.dw-duration-700{transition-duration:700ms}.dw-duration-1000{transition-duration:1000ms}.dw-transform{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.dw--translate-y-1\\/2{--tw-translate-y:-50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.dw-hover\\:dw-bg-gray-50:hover{background-color:rgb(249 250 251)}.dw-hover\\:dw-bg-gray-100:hover{background-color:rgb(243 244 246)}.dw-hover\\:dw-bg-blue-600:hover{background-color:rgb(37 99 235)}.dw-hover\\:dw-text-gray-900:hover{color:rgb(17 24 39)}.dw-hover\\:dw-text-blue-600:hover{color:rgb(37 99 235)}.dw-focus\\:dw-ring-2:focus{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow,0 0 #0000)}.dw-focus\\:dw-ring-blue-500:focus{--tw-ring-opacity:1;--tw-ring-color:rgb(59 130 246 / var(--tw-ring-opacity))}.dw-focus\\:dw-border-transparent:focus{border-color:transparent}.dw-container{width:100%}@media (min-width:640px){.dw-container{max-width:640px}}@media (min-width:768px){.dw-container{max-width:768px}}@media (min-width:1024px){.dw-container{max-width:1024px}}@media (min-width:1280px){.dw-container{max-width:1280px}}@media (min-width:1536px){.dw-container{max-width:1536px}}@media (min-width:640px){.dw-sm\\:dw-w-64{width:16rem}.dw-sm\\:dw-text-lg{font-size:1.125rem;line-height:1.75rem}}@media (min-width:768px){.dw-md\\:dw-flex{display:flex}.dw-md\\:dw-hidden{display:none}.dw-md\\:dw-w-1\\/2{width:50%}}@media (min-width:1024px){.dw-lg\\:dw-flex{display:flex}.dw-lg\\:dw-hidden{display:none}.dw-lg\\:dw-w-1\\/3{width:33.333333%}}`;

// Optimized font CSS with local font files
const OPTIMIZED_FONT_CSS = `@font-face{font-family:Inter;font-style:normal;font-weight:400;font-display:swap;src:url(./fonts/Inter-400.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Inter;font-style:normal;font-weight:500;font-display:swap;src:url(./fonts/Inter-500.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Inter;font-style:normal;font-weight:700;font-display:swap;src:url(./fonts/Inter-700.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Phudu;font-style:normal;font-weight:600;font-display:swap;src:url(./fonts/Phudu-600.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Phudu;font-style:normal;font-weight:700;font-display:swap;src:url(./fonts/Phudu-700.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}`;

// Function to optimize HTML content
function optimizeHTMLContent(htmlContent) {
  // Remove Tailwind CDN script and replace with optimized CSS
  let optimizedHTML = htmlContent
    .replace(/<script src="https:\/\/cdn\.tailwindcss\.com"><\/script>/g, "")
    .replace(/<script src="\/\/unpkg\.com\/alpinejs" defer><\/script>/g, "")
    .replace(
      /<script>\s*tailwind\.config\s*=\s*\{[\s\S]*?\};\s*<\/script>/g,
      ""
    );

  // Replace font-face declarations with optimized local fonts
  optimizedHTML = optimizedHTML.replace(/@font-face\s*\{[^}]*\}/g, "");

  // Add optimized CSS and fonts to head
  const headEndIndex = optimizedHTML.indexOf("</head>");
  if (headEndIndex !== -1) {
    const optimizedHead = `
    <link rel="preload" href="./css/styles.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="./css/styles.css"></noscript>
    <link rel="preload" href="./fonts/Inter-400.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="./fonts/Phudu-600.woff2" as="font" type="font/woff2" crossorigin>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Professional bus rental services with modern, comfortable vehicles for all your transportation needs.">
    <meta name="keywords" content="bus rental, charter bus, transportation, group travel">
    <meta property="og:title" content="Bus Rental Services">
    <meta property="og:description" content="Professional bus rental services with modern, comfortable vehicles.">
    <meta property="og:type" content="website">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Bus Rental Services">
    <meta name="twitter:description" content="Professional bus rental services with modern, comfortable vehicles.">
    <link rel="canonical" href="">
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "LocalBusiness",
      "name": "Bus Rental Company",
      "description": "Professional bus rental services",
      "url": "",
      "telephone": "",
      "address": {
        "@type": "PostalAddress",
        "addressCountry": "US"
      }
    }
    </script>
    `;

    optimizedHTML =
      optimizedHTML.slice(0, headEndIndex) +
      optimizedHead +
      optimizedHTML.slice(headEndIndex);
  }

  // Add lazy loading to images
  optimizedHTML = optimizedHTML.replace(
    /<img([^>]*?)src="([^"]*?)"([^>]*?)>/g,
    '<img$1src="$2"$3 loading="lazy">'
  );

  return optimizedHTML;
}

// Function to download fonts
async function downloadFonts(zip) {
  const fontsFolder = zip.folder("fonts");

  for (const [filename, url] of Object.entries(FONT_URLS)) {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      fontsFolder.file(filename, blob);
      console.log(`📦 Downloaded font: ${filename}`);
    } catch (error) {
      console.error(`❌ Failed to download font ${filename}:`, error);
    }
  }
}

// Function to create optimized CSS file
function createOptimizedCSS() {
  return `${OPTIMIZED_FONT_CSS}\n\n${OPTIMIZED_TAILWIND_CSS}`;
}

// Main export function
export async function downloadOptimizedWebsiteZip(
  pageList,
  fileList,
  websiteName
) {
  const zip = new JSZip();

  // Create CSS folder and add optimized styles
  const cssFolder = zip.folder("css");
  cssFolder.file("styles.css", createOptimizedCSS());

  // Download and add fonts
  await downloadFonts(zip);

  // Process pages with optimized HTML
  for (const page of pageList) {
    const optimizedHTML = optimizeHTMLContent(page.html);
    const pagePath = page.path || `${page.name || "page"}.html`;
    zip.file(pagePath, optimizedHTML);
    console.log(`📄 Added optimized page: ${pagePath}`);
  }

  // Process assets (images, etc.)
  if (fileList && fileList.length > 0) {
    const assetsFolder = zip.folder("assets");

    for (const file of fileList) {
      try {
        const response = await fetch(file.url);
        const blob = await response.blob();
        const filename = file.name || file.url.split("/").pop();
        assetsFolder.file(filename, blob);
        console.log(`📦 Added asset: assets/${filename}`);
      } catch (error) {
        console.error(`❌ Failed to download asset ${file.url}:`, error);
      }
    }
  }

  // Generate and download ZIP
  const zipBlob = await zip.generateAsync({ type: "blob" });
  const url = URL.createObjectURL(zipBlob);
  const a = document.createElement("a");
  a.href = url;
  a.download = `${websiteName || "website"}-optimized.zip`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);

  console.log("✅ Optimized website exported successfully!");
}

// Convert page list to export format (same as original)
export const convertPageListToExportFormat = (pageList, fileList) => {
  return pageList.map((page) => ({
    html:
      page?.full_page_content ||
      `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${page?.name || "Page"}</title>
</head>
<body>
  <h1>${page?.name || "Page"}</h1>
  <p>Content will be added here.</p>
</body>
</html>`,
    path: getPagePath(page),
  }));
};

// Helper function to get page path
function getPagePath(page) {
  if (page.slug) {
    return page.slug === "home" ? "index.html" : `${page.slug}.html`;
  }
  return `${page.name || "page"}.html`;
}
