import { CONSTANTS } from '../util/constant/CONSTANTS.js';

/**
 * Maps route paths to header configuration from CONSTANTS.HEADER
 * Uses conservative mapping to avoid breaking existing functionality
 * Returns null if no mapping found (preserves existing behavior)
 */
export const getHeaderFromRoute = (pathname) => {
    // Remove leading slash and normalize path
    const cleanPath = pathname.replace(/^\/+/, '').toLowerCase();

    // Enhanced route mapping with special cases
    const routeMapping = {
        // Main pages
        '': 'dashboard',                    // Root path "/"
        'dashboard': 'dashboard',
        'categories': 'categories',
        'components': 'components',
        'pages': 'pages',
        'templates': 'templates',
        'websites': 'websites',
        'users': 'users',
        'activity-logs': 'activity-logs',
        'migration': 'migration',
        'global-variable': 'global-variable',
        'dynamic-pages': 'pages'            // Special case: dynamic-pages maps to pages
    };

    // Special mapping for add/edit routes that don't follow standard patterns
    const specialRouteMapping = {
        // Exact path mappings for non-standard routes
        'templates/add': 'addTemplate',
        'dynamic-pages': 'pages',
        // Add more special cases as needed
    };

    // Get the base route (first segment) and full path segments
    const pathSegments = cleanPath.split('/');
    const baseRoute = pathSegments[0];
    const secondSegment = pathSegments[1];

    // Check for exact special route match first
    if (specialRouteMapping[cleanPath]) {
        return CONSTANTS.HEADER[specialRouteMapping[cleanPath]] || null;
    }

    // Check for exact match in main routing
    if (routeMapping[cleanPath]) {
        return CONSTANTS.HEADER[routeMapping[cleanPath]] || null;
    }

    // Handle add/edit patterns for standard routes
    if (routeMapping[baseRoute]) {
        // Handle /add routes
        if (secondSegment === 'add') {
            const addKey = getAddKey(baseRoute);
            return CONSTANTS.HEADER[addKey] || CONSTANTS.HEADER[routeMapping[baseRoute]];
        }

        // Handle edit routes (ID patterns: numbers, UUIDs, or alphanumeric)
        if (secondSegment && isEditRoute(secondSegment)) {
            const editKey = getEditKey(baseRoute);
            return CONSTANTS.HEADER[editKey] || CONSTANTS.HEADER[routeMapping[baseRoute]];
        }

        // Return base route header
        return CONSTANTS.HEADER[routeMapping[baseRoute]];
    }

    // Return null if no mapping found (preserves existing behavior)
    return null;
};

/**
 * Generate add key for different route patterns
 */
const getAddKey = (baseRoute) => {
    const addKeyMapping = {
        'templates': 'addTemplate',
        'pages': 'addPage',
        'components': 'addComponent',
        'categories': 'addCategory',
        'websites': 'addWebsite',
        'users': 'addUser'
    };

    return addKeyMapping[baseRoute] || `add${capitalizeFirst(baseRoute.slice(0, -1))}`;
};

/**
 * Generate edit key for different route patterns
 */
const getEditKey = (baseRoute) => {
    const editKeyMapping = {
        'templates': 'editTemplate',
        'pages': 'editPage',
        'components': 'editComponent',
        'categories': 'editCategory',
        'websites': 'editWebsite',
        'users': 'editUser'
    };

    return editKeyMapping[baseRoute] || `edit${capitalizeFirst(baseRoute.slice(0, -1))}`;
};

/**
 * Check if a route segment represents an edit route (ID pattern)
 */
const isEditRoute = (segment) => {
    // Match common ID patterns:
    // - Numbers: 123, 456
    // - UUIDs: 550e8400-e29b-41d4-a716-************
    // - Alphanumeric: abc123, user-123, etc.
    return /^[a-zA-Z0-9-_]+$/.test(segment) &&
        segment !== 'add' &&
        segment !== 'edit' &&
        segment.length > 0;
};

/**
 * Helper function to capitalize first letter
 */
const capitalizeFirst = (str) => {
    return str.charAt(0).toUpperCase() + str.slice(1);
};

/**
 * Get header title and subtitle for current route
 * Returns object with title and subtitle, or null if no mapping
 */
export const getPageHeader = (pathname) => {
    const headerConfig = getHeaderFromRoute(pathname);

    if (!headerConfig) {
        return null;
    }

    return {
        title: headerConfig.title || '',
        subtitle: headerConfig.subtitle || ''
    };
};

/**
 * Debug function to see all available header mappings
 * Useful for development and testing
 */
export const getAvailableHeaders = () => {
    return Object.keys(CONSTANTS.HEADER).map(key => ({
        key,
        title: CONSTANTS.HEADER[key].title,
        subtitle: CONSTANTS.HEADER[key].subtitle
    }));
};
