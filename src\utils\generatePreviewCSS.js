/**
 * Generate Tailwind CSS for component previews
 * This replaces the CDN usage with a compiled version
 */

// Core Tailwind CSS utilities that are commonly used in components
export const generateTailwindCSS = () => {
  return `
/* Tailwind CSS Reset and Base Styles */
*,
::before,
::after {
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
  border-color: #e5e7eb;
}

::before,
::after {
  --tw-content: '';
}

html {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON>l, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-feature-settings: normal;
  font-variation-settings: normal;
}

body {
  margin: 0;
  line-height: inherit;
}

/* Tailwind CSS Utilities with tw- prefix */

/* Display */
.tw-block { display: block; }
.tw-inline-block { display: inline-block; }
.tw-inline { display: inline; }
.tw-flex { display: flex; }
.tw-inline-flex { display: inline-flex; }
.tw-grid { display: grid; }
.tw-hidden { display: none; }

/* Position */
.tw-static { position: static; }
.tw-fixed { position: fixed; }
.tw-absolute { position: absolute; }
.tw-relative { position: relative; }
.tw-sticky { position: sticky; }

/* Top, Right, Bottom, Left */
.tw-top-0 { top: 0px; }
.tw-right-0 { right: 0px; }
.tw-bottom-0 { bottom: 0px; }
.tw-left-0 { left: 0px; }
.tw-top-1\\/2 { top: 50%; }
.tw-left-1\\/2 { left: 50%; }

/* Z-Index */
.tw-z-10 { z-index: 10; }
.tw-z-20 { z-index: 20; }
.tw-z-30 { z-index: 30; }
.tw-z-40 { z-index: 40; }
.tw-z-50 { z-index: 50; }

/* Flex Direction */
.tw-flex-row { flex-direction: row; }
.tw-flex-row-reverse { flex-direction: row-reverse; }
.tw-flex-col { flex-direction: column; }
.tw-flex-col-reverse { flex-direction: column-reverse; }

/* Flex Wrap */
.tw-flex-wrap { flex-wrap: wrap; }
.tw-flex-wrap-reverse { flex-wrap: wrap-reverse; }
.tw-flex-nowrap { flex-wrap: nowrap; }

/* Align Items */
.tw-items-start { align-items: flex-start; }
.tw-items-end { align-items: flex-end; }
.tw-items-center { align-items: center; }
.tw-items-baseline { align-items: baseline; }
.tw-items-stretch { align-items: stretch; }

/* Justify Content */
.tw-justify-start { justify-content: flex-start; }
.tw-justify-end { justify-content: flex-end; }
.tw-justify-center { justify-content: center; }
.tw-justify-between { justify-content: space-between; }
.tw-justify-around { justify-content: space-around; }
.tw-justify-evenly { justify-content: space-evenly; }

/* Gap */
.tw-gap-1 { gap: 0.25rem; }
.tw-gap-2 { gap: 0.5rem; }
.tw-gap-3 { gap: 0.75rem; }
.tw-gap-4 { gap: 1rem; }
.tw-gap-5 { gap: 1.25rem; }
.tw-gap-6 { gap: 1.5rem; }
.tw-gap-8 { gap: 2rem; }

/* Padding */
.tw-p-0 { padding: 0px; }
.tw-p-1 { padding: 0.25rem; }
.tw-p-2 { padding: 0.5rem; }
.tw-p-3 { padding: 0.75rem; }
.tw-p-4 { padding: 1rem; }
.tw-p-5 { padding: 1.25rem; }
.tw-p-6 { padding: 1.5rem; }
.tw-p-8 { padding: 2rem; }
.tw-p-10 { padding: 2.5rem; }
.tw-p-12 { padding: 3rem; }
.tw-p-16 { padding: 4rem; }

.tw-px-1 { padding-left: 0.25rem; padding-right: 0.25rem; }
.tw-px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.tw-px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.tw-px-4 { padding-left: 1rem; padding-right: 1rem; }
.tw-px-5 { padding-left: 1.25rem; padding-right: 1.25rem; }
.tw-px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.tw-px-8 { padding-left: 2rem; padding-right: 2rem; }

.tw-py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.tw-py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.tw-py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.tw-py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.tw-py-5 { padding-top: 1.25rem; padding-bottom: 1.25rem; }
.tw-py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
.tw-py-8 { padding-top: 2rem; padding-bottom: 2rem; }

.tw-pt-1 { padding-top: 0.25rem; }
.tw-pt-2 { padding-top: 0.5rem; }
.tw-pt-3 { padding-top: 0.75rem; }
.tw-pt-4 { padding-top: 1rem; }
.tw-pt-6 { padding-top: 1.5rem; }
.tw-pt-8 { padding-top: 2rem; }

.tw-pr-1 { padding-right: 0.25rem; }
.tw-pr-2 { padding-right: 0.5rem; }
.tw-pr-3 { padding-right: 0.75rem; }
.tw-pr-4 { padding-right: 1rem; }
.tw-pr-6 { padding-right: 1.5rem; }
.tw-pr-8 { padding-right: 2rem; }

.tw-pb-1 { padding-bottom: 0.25rem; }
.tw-pb-2 { padding-bottom: 0.5rem; }
.tw-pb-3 { padding-bottom: 0.75rem; }
.tw-pb-4 { padding-bottom: 1rem; }
.tw-pb-6 { padding-bottom: 1.5rem; }
.tw-pb-8 { padding-bottom: 2rem; }

.tw-pl-1 { padding-left: 0.25rem; }
.tw-pl-2 { padding-left: 0.5rem; }
.tw-pl-3 { padding-left: 0.75rem; }
.tw-pl-4 { padding-left: 1rem; }
.tw-pl-6 { padding-left: 1.5rem; }
.tw-pl-8 { padding-left: 2rem; }

/* Margin */
.tw-m-0 { margin: 0px; }
.tw-m-1 { margin: 0.25rem; }
.tw-m-2 { margin: 0.5rem; }
.tw-m-3 { margin: 0.75rem; }
.tw-m-4 { margin: 1rem; }
.tw-m-5 { margin: 1.25rem; }
.tw-m-6 { margin: 1.5rem; }
.tw-m-8 { margin: 2rem; }
.tw-m-auto { margin: auto; }

.tw-mx-1 { margin-left: 0.25rem; margin-right: 0.25rem; }
.tw-mx-2 { margin-left: 0.5rem; margin-right: 0.5rem; }
.tw-mx-3 { margin-left: 0.75rem; margin-right: 0.75rem; }
.tw-mx-4 { margin-left: 1rem; margin-right: 1rem; }
.tw-mx-6 { margin-left: 1.5rem; margin-right: 1.5rem; }
.tw-mx-8 { margin-left: 2rem; margin-right: 2rem; }
.tw-mx-auto { margin-left: auto; margin-right: auto; }

.tw-my-1 { margin-top: 0.25rem; margin-bottom: 0.25rem; }
.tw-my-2 { margin-top: 0.5rem; margin-bottom: 0.5rem; }
.tw-my-3 { margin-top: 0.75rem; margin-bottom: 0.75rem; }
.tw-my-4 { margin-top: 1rem; margin-bottom: 1rem; }
.tw-my-6 { margin-top: 1.5rem; margin-bottom: 1.5rem; }
.tw-my-8 { margin-top: 2rem; margin-bottom: 2rem; }

.tw-mt-1 { margin-top: 0.25rem; }
.tw-mt-2 { margin-top: 0.5rem; }
.tw-mt-3 { margin-top: 0.75rem; }
.tw-mt-4 { margin-top: 1rem; }
.tw-mt-6 { margin-top: 1.5rem; }
.tw-mt-8 { margin-top: 2rem; }

.tw-mr-1 { margin-right: 0.25rem; }
.tw-mr-2 { margin-right: 0.5rem; }
.tw-mr-3 { margin-right: 0.75rem; }
.tw-mr-4 { margin-right: 1rem; }
.tw-mr-6 { margin-right: 1.5rem; }
.tw-mr-8 { margin-right: 2rem; }

.tw-mb-1 { margin-bottom: 0.25rem; }
.tw-mb-2 { margin-bottom: 0.5rem; }
.tw-mb-3 { margin-bottom: 0.75rem; }
.tw-mb-4 { margin-bottom: 1rem; }
.tw-mb-6 { margin-bottom: 1.5rem; }
.tw-mb-8 { margin-bottom: 2rem; }

.tw-ml-1 { margin-left: 0.25rem; }
.tw-ml-2 { margin-left: 0.5rem; }
.tw-ml-3 { margin-left: 0.75rem; }
.tw-ml-4 { margin-left: 1rem; }
.tw-ml-6 { margin-left: 1.5rem; }
.tw-ml-8 { margin-left: 2rem; }

/* Width */
.tw-w-auto { width: auto; }
.tw-w-full { width: 100%; }
.tw-w-1\\/2 { width: 50%; }
.tw-w-1\\/3 { width: 33.333333%; }
.tw-w-2\\/3 { width: 66.666667%; }
.tw-w-1\\/4 { width: 25%; }
.tw-w-3\\/4 { width: 75%; }
.tw-w-1\\/5 { width: 20%; }
.tw-w-2\\/5 { width: 40%; }
.tw-w-3\\/5 { width: 60%; }
.tw-w-4\\/5 { width: 80%; }

.tw-w-4 { width: 1rem; }
.tw-w-5 { width: 1.25rem; }
.tw-w-6 { width: 1.5rem; }
.tw-w-8 { width: 2rem; }
.tw-w-10 { width: 2.5rem; }
.tw-w-12 { width: 3rem; }
.tw-w-16 { width: 4rem; }
.tw-w-20 { width: 5rem; }
.tw-w-24 { width: 6rem; }
.tw-w-32 { width: 8rem; }
.tw-w-40 { width: 10rem; }
.tw-w-48 { width: 12rem; }
.tw-w-56 { width: 14rem; }
.tw-w-64 { width: 16rem; }

/* Height */
.tw-h-auto { height: auto; }
.tw-h-full { height: 100%; }
.tw-h-screen { height: 100vh; }

.tw-h-4 { height: 1rem; }
.tw-h-5 { height: 1.25rem; }
.tw-h-6 { height: 1.5rem; }
.tw-h-8 { height: 2rem; }
.tw-h-10 { height: 2.5rem; }
.tw-h-12 { height: 3rem; }
.tw-h-16 { height: 4rem; }
.tw-h-20 { height: 5rem; }
.tw-h-24 { height: 6rem; }
.tw-h-32 { height: 8rem; }
.tw-h-40 { height: 10rem; }
.tw-h-48 { height: 12rem; }
.tw-h-56 { height: 14rem; }
.tw-h-64 { height: 16rem; }

/* Min/Max Width */
.tw-min-w-0 { min-width: 0px; }
.tw-min-w-full { min-width: 100%; }
.tw-max-w-none { max-width: none; }
.tw-max-w-xs { max-width: 20rem; }
.tw-max-w-sm { max-width: 24rem; }
.tw-max-w-md { max-width: 28rem; }
.tw-max-w-lg { max-width: 32rem; }
.tw-max-w-xl { max-width: 36rem; }
.tw-max-w-2xl { max-width: 42rem; }
.tw-max-w-3xl { max-width: 48rem; }
.tw-max-w-4xl { max-width: 56rem; }
.tw-max-w-5xl { max-width: 64rem; }
.tw-max-w-6xl { max-width: 72rem; }
.tw-max-w-7xl { max-width: 80rem; }
.tw-max-w-full { max-width: 100%; }

/* Min/Max Height */
.tw-min-h-0 { min-height: 0px; }
.tw-min-h-full { min-height: 100%; }
.tw-min-h-screen { min-height: 100vh; }
.tw-max-h-full { max-height: 100%; }
.tw-max-h-screen { max-height: 100vh; }

/* Font Family */
.tw-font-sans { font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; }
.tw-font-serif { font-family: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif; }
.tw-font-mono { font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace; }

/* Font Size */
.tw-text-xs { font-size: 0.75rem; line-height: 1rem; }
.tw-text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.tw-text-base { font-size: 1rem; line-height: 1.5rem; }
.tw-text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.tw-text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.tw-text-2xl { font-size: 1.5rem; line-height: 2rem; }
.tw-text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.tw-text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.tw-text-5xl { font-size: 3rem; line-height: 1; }
.tw-text-6xl { font-size: 3.75rem; line-height: 1; }

/* Font Weight */
.tw-font-thin { font-weight: 100; }
.tw-font-extralight { font-weight: 200; }
.tw-font-light { font-weight: 300; }
.tw-font-normal { font-weight: 400; }
.tw-font-medium { font-weight: 500; }
.tw-font-semibold { font-weight: 600; }
.tw-font-bold { font-weight: 700; }
.tw-font-extrabold { font-weight: 800; }
.tw-font-black { font-weight: 900; }

/* Text Align */
.tw-text-left { text-align: left; }
.tw-text-center { text-align: center; }
.tw-text-right { text-align: right; }
.tw-text-justify { text-align: justify; }

/* Text Color */
.tw-text-transparent { color: transparent; }
.tw-text-current { color: currentColor; }
.tw-text-black { color: rgb(0 0 0); }
.tw-text-white { color: rgb(255 255 255); }
.tw-text-gray-50 { color: rgb(249 250 251); }
.tw-text-gray-100 { color: rgb(243 244 246); }
.tw-text-gray-200 { color: rgb(229 231 235); }
.tw-text-gray-300 { color: rgb(209 213 219); }
.tw-text-gray-400 { color: rgb(156 163 175); }
.tw-text-gray-500 { color: rgb(107 114 128); }
.tw-text-gray-600 { color: rgb(75 85 99); }
.tw-text-gray-700 { color: rgb(55 65 81); }
.tw-text-gray-800 { color: rgb(31 41 55); }
.tw-text-gray-900 { color: rgb(17 24 39); }
.tw-text-red-500 { color: rgb(239 68 68); }
.tw-text-red-600 { color: rgb(220 38 38); }
.tw-text-blue-500 { color: rgb(59 130 246); }
.tw-text-blue-600 { color: rgb(37 99 235); }
.tw-text-green-500 { color: rgb(34 197 94); }
.tw-text-green-600 { color: rgb(22 163 74); }
.tw-text-yellow-500 { color: rgb(234 179 8); }
.tw-text-purple-500 { color: rgb(168 85 247); }
.tw-text-purple-600 { color: rgb(147 51 234); }

/* Background Color */
.tw-bg-transparent { background-color: transparent; }
.tw-bg-current { background-color: currentColor; }
.tw-bg-black { background-color: rgb(0 0 0); }
.tw-bg-white { background-color: rgb(255 255 255); }
.tw-bg-gray-50 { background-color: rgb(249 250 251); }
.tw-bg-gray-100 { background-color: rgb(243 244 246); }
.tw-bg-gray-200 { background-color: rgb(229 231 235); }
.tw-bg-gray-300 { background-color: rgb(209 213 219); }
.tw-bg-gray-400 { background-color: rgb(156 163 175); }
.tw-bg-gray-500 { background-color: rgb(107 114 128); }
.tw-bg-gray-600 { background-color: rgb(75 85 99); }
.tw-bg-gray-700 { background-color: rgb(55 65 81); }
.tw-bg-gray-800 { background-color: rgb(31 41 55); }
.tw-bg-gray-900 { background-color: rgb(17 24 39); }
.tw-bg-red-500 { background-color: rgb(239 68 68); }
.tw-bg-red-600 { background-color: rgb(220 38 38); }
.tw-bg-blue-500 { background-color: rgb(59 130 246); }
.tw-bg-blue-600 { background-color: rgb(37 99 235); }
.tw-bg-green-500 { background-color: rgb(34 197 94); }
.tw-bg-green-600 { background-color: rgb(22 163 74); }
.tw-bg-yellow-500 { background-color: rgb(234 179 8); }
.tw-bg-purple-500 { background-color: rgb(168 85 247); }
.tw-bg-purple-600 { background-color: rgb(147 51 234); }

/* Background Gradient */
.tw-bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }
.tw-bg-gradient-to-l { background-image: linear-gradient(to left, var(--tw-gradient-stops)); }
.tw-bg-gradient-to-t { background-image: linear-gradient(to top, var(--tw-gradient-stops)); }
.tw-bg-gradient-to-b { background-image: linear-gradient(to bottom, var(--tw-gradient-stops)); }
.tw-bg-gradient-to-tr { background-image: linear-gradient(to top right, var(--tw-gradient-stops)); }
.tw-bg-gradient-to-tl { background-image: linear-gradient(to top left, var(--tw-gradient-stops)); }
.tw-bg-gradient-to-br { background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)); }
.tw-bg-gradient-to-bl { background-image: linear-gradient(to bottom left, var(--tw-gradient-stops)); }

.tw-from-blue-500 { --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position); --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.tw-from-blue-600 { --tw-gradient-from: #2563eb var(--tw-gradient-from-position); --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.tw-to-purple-500 { --tw-gradient-to: #a855f7 var(--tw-gradient-to-position); }
.tw-to-purple-600 { --tw-gradient-to: #9333ea var(--tw-gradient-to-position); }

/* Border */
.tw-border-0 { border-width: 0px; }
.tw-border { border-width: 1px; }
.tw-border-2 { border-width: 2px; }
.tw-border-4 { border-width: 4px; }
.tw-border-8 { border-width: 8px; }

.tw-border-t { border-top-width: 1px; }
.tw-border-r { border-right-width: 1px; }
.tw-border-b { border-bottom-width: 1px; }
.tw-border-l { border-left-width: 1px; }

/* Border Color */
.tw-border-transparent { border-color: transparent; }
.tw-border-current { border-color: currentColor; }
.tw-border-black { border-color: rgb(0 0 0); }
.tw-border-white { border-color: rgb(255 255 255); }
.tw-border-gray-200 { border-color: rgb(229 231 235); }
.tw-border-gray-300 { border-color: rgb(209 213 219); }
.tw-border-gray-400 { border-color: rgb(156 163 175); }
.tw-border-gray-500 { border-color: rgb(107 114 128); }
.tw-border-blue-500 { border-color: rgb(59 130 246); }
.tw-border-red-500 { border-color: rgb(239 68 68); }

/* Border Radius */
.tw-rounded-none { border-radius: 0px; }
.tw-rounded-sm { border-radius: 0.125rem; }
.tw-rounded { border-radius: 0.25rem; }
.tw-rounded-md { border-radius: 0.375rem; }
.tw-rounded-lg { border-radius: 0.5rem; }
.tw-rounded-xl { border-radius: 0.75rem; }
.tw-rounded-2xl { border-radius: 1rem; }
.tw-rounded-3xl { border-radius: 1.5rem; }
.tw-rounded-full { border-radius: 9999px; }

/* Box Shadow */
.tw-shadow-sm { box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05); }
.tw-shadow { box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1); }
.tw-shadow-md { box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); }
.tw-shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); }
.tw-shadow-xl { box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1); }
.tw-shadow-2xl { box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25); }
.tw-shadow-none { box-shadow: 0 0 #0000; }

/* Opacity */
.tw-opacity-0 { opacity: 0; }
.tw-opacity-5 { opacity: 0.05; }
.tw-opacity-10 { opacity: 0.1; }
.tw-opacity-20 { opacity: 0.2; }
.tw-opacity-25 { opacity: 0.25; }
.tw-opacity-30 { opacity: 0.3; }
.tw-opacity-40 { opacity: 0.4; }
.tw-opacity-50 { opacity: 0.5; }
.tw-opacity-60 { opacity: 0.6; }
.tw-opacity-70 { opacity: 0.7; }
.tw-opacity-75 { opacity: 0.75; }
.tw-opacity-80 { opacity: 0.8; }
.tw-opacity-90 { opacity: 0.9; }
.tw-opacity-95 { opacity: 0.95; }
.tw-opacity-100 { opacity: 1; }

/* Cursor */
.tw-cursor-auto { cursor: auto; }
.tw-cursor-default { cursor: default; }
.tw-cursor-pointer { cursor: pointer; }
.tw-cursor-wait { cursor: wait; }
.tw-cursor-text { cursor: text; }
.tw-cursor-move { cursor: move; }
.tw-cursor-help { cursor: help; }
.tw-cursor-not-allowed { cursor: not-allowed; }

/* Transition */
.tw-transition-none { transition-property: none; }
.tw-transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.tw-transition { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.tw-transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.tw-transition-opacity { transition-property: opacity; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.tw-transition-shadow { transition-property: box-shadow; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.tw-transition-transform { transition-property: transform; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }

/* Duration */
.tw-duration-75 { transition-duration: 75ms; }
.tw-duration-100 { transition-duration: 100ms; }
.tw-duration-150 { transition-duration: 150ms; }
.tw-duration-200 { transition-duration: 200ms; }
.tw-duration-300 { transition-duration: 300ms; }
.tw-duration-500 { transition-duration: 500ms; }
.tw-duration-700 { transition-duration: 700ms; }
.tw-duration-1000 { transition-duration: 1000ms; }

/* Transform */
.tw-transform { transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.tw--translate-y-1\\/2 { --tw-translate-y: -50%; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

/* Hover States */
.tw-hover\\:tw-bg-gray-50:hover { background-color: rgb(249 250 251); }
.tw-hover\\:tw-bg-gray-100:hover { background-color: rgb(243 244 246); }
.tw-hover\\:tw-bg-blue-600:hover { background-color: rgb(37 99 235); }
.tw-hover\\:tw-text-gray-900:hover { color: rgb(17 24 39); }
.tw-hover\\:tw-text-blue-600:hover { color: rgb(37 99 235); }

/* Focus States */
.tw-focus\\:tw-ring-2:focus { --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color); box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000); }
.tw-focus\\:tw-ring-blue-500:focus { --tw-ring-opacity: 1; --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity)); }
.tw-focus\\:tw-border-transparent:focus { border-color: transparent; }

/* Container */
.tw-container { width: 100%; }
@media (min-width: 640px) { .tw-container { max-width: 640px; } }
@media (min-width: 768px) { .tw-container { max-width: 768px; } }
@media (min-width: 1024px) { .tw-container { max-width: 1024px; } }
@media (min-width: 1280px) { .tw-container { max-width: 1280px; } }
@media (min-width: 1536px) { .tw-container { max-width: 1536px; } }

/* Responsive Utilities */
@media (min-width: 640px) {
  .tw-sm\\:tw-w-64 { width: 16rem; }
  .tw-sm\\:tw-text-lg { font-size: 1.125rem; line-height: 1.75rem; }
}

@media (min-width: 768px) {
  .tw-md\\:tw-flex { display: flex; }
  .tw-md\\:tw-hidden { display: none; }
  .tw-md\\:tw-w-1\\/2 { width: 50%; }
}

@media (min-width: 1024px) {
  .tw-lg\\:tw-flex { display: flex; }
  .tw-lg\\:tw-hidden { display: none; }
  .tw-lg\\:tw-w-1\\/3 { width: 33.333333%; }
}
`;
};

export default generateTailwindCSS;
