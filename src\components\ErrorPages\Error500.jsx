import React from "react";
import { Result, Button } from "antd";
import { useNavigate } from "react-router-dom";
import { HomeOutlined, ReloadOutlined, BugOutlined } from "@ant-design/icons";

const Error500 = () => {
  const navigate = useNavigate();

  const handleGoHome = () => {
    navigate("/");
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  const handleReportBug = () => {
    // You can implement bug reporting functionality here
    console.log("Report bug clicked");
  };

  return (
    <div className="tw-min-h-screen tw-flex tw-items-center tw-justify-center tw-bg-gray-50">
      <div className="tw-max-w-md tw-w-full tw-mx-4">
        <Result
          status="500"
          title="500"
          subTitle="Sorry, something went wrong on our end. We're working to fix this issue."
          extra={[
            <Button
              type="primary"
              icon={<HomeOutlined />}
              onClick={handleGoHome}
              className="tw-mr-2"
              key="home"
            >
              Back Home
            </Button>,
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              className="tw-mr-2"
              key="refresh"
            >
              Try Again
            </Button>,
            // <Button
            //   icon={<BugOutlined />}
            //   onClick={handleReportBug}
            //   key="report"
            // >
            //   Report Issue
            // </Button>,
          ]}
        />
      </div>
    </div>
  );
};

export default Error500;
