import React from "react";
import { Result, <PERSON><PERSON>, <PERSON>, Typography } from "antd";
import {
  ReloadOutlined,
  ClockCircleOutlined,
  MailOutlined,
} from "@ant-design/icons";

const { Title, Paragraph } = Typography;

const MaintenancePage = () => {
  const handleRefresh = () => {
    window.location.reload();
  };

  const handleContactSupport = () => {
    // You can implement contact support functionality here
    console.log("Contact support clicked");
  };

  return (
    <div className="tw-min-h-screen tw-flex tw-items-center tw-justify-center tw-bg-gradient-to-br tw-from-blue-50 tw-to-indigo-100">
      <div className="tw-max-w-2xl tw-w-full tw-mx-4">
        <Card className="tw-shadow-lg tw-border-0">
          <Result
            icon={
              <ClockCircleOutlined className="tw-text-6xl tw-text-blue-500" />
            }
            title="Under Maintenance"
            subTitle="We're currently performing scheduled maintenance to improve your experience."
            extra={[
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                className="tw-mr-2"
                key="refresh"
              >
                Check Again
              </Button>,
              <Button
                icon={<MailOutlined />}
                onClick={handleContactSupport}
                key="contact"
              >
                Contact Support
              </Button>,
            ]}
          />

          <div className="tw-mt-6 tw-text-center">
            <Title level={4} className="tw-text-gray-600">
              Expected Completion Time
            </Title>
            <Paragraph className="tw-text-lg tw-text-blue-600 tw-font-semibold">
              2-4 hours
            </Paragraph>
            <Paragraph className="tw-text-gray-500">
              We apologize for any inconvenience. Thank you for your patience.
            </Paragraph>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default MaintenancePage;
