# System Patterns

## 1. System Architecture Overview

The application is a frontend-focused system that will interact with external APIs for all data and business logic. The initial MVP included a backend within the same repository, but the final architecture will be purely API-driven.

- **Frontend (Builder UI):** A React application built with Vite.js. It serves as the user interface for the static site generator and is the core of this repository.
- **API Integration:** All backend functionality, including data storage, authentication, and file management, will be handled through API integrations with external services. The integrated backend in the `server` directory is for MVP purposes and will be phased out.
- **Routing:** `react-router-dom` is used for all frontend routing.

## 2. Key Design Patterns

- **State Management:** Global state is managed using React's Context API. `AuthContext` handles user authentication and `SidebarContext` manages the layout's sidebar state.
- **Component-First Development:** The primary development workflow is component-based.
  1. **Identify Components:** Before building a new page or feature, the required UI components are identified.
  2. **Reuse Existing Components:** Existing components are reused wherever possible to maintain consistency and reduce code duplication.
  3. **Create New Components:** If a required component does not exist, it is created as a new, reusable component.
  4. **Page Composition:** Pages are then composed by assembling these components. Raw code within page files is minimized in favor of modular components.
  5. **Separate Logic:** Business logic and utility functions are kept in separate files to keep components clean and focused on the UI.
- **API Communication:** A custom hook, `useHttp`, is used for all API communications, abstracting away the direct `axios` calls. Authentication APIs are handled separately within `AuthContext`.
- **Caching**: A runtime caching utility (`src/utils/runtimeCache.js`) is used for in-memory caching of components and categories. This approach avoids persistent storage and resets on page reload, ensuring fresh data while reducing redundant API calls during a session.
- **Protected Routes:** The application uses a `ProtectedRoute` component to restrict access to certain routes based on the user's authentication status. An `AdminRoute` component further restricts access to admin-only routes.

## 3. Folder Structure

The `src` directory is organized to support a highly modular, component-driven architecture:

- **`components/`**: This is the root directory for all UI elements.
  - **`[PageName]/`**: Each page of the application has its own dedicated folder within `components`. For example, a "Dashboard" page would be located at `components/Dashboard/`.
    - **`index.jsx`**: The main entry point for the page, responsible for assembling the page's layout from various components.
    - **`components/`**: A subfolder containing components that are specific to this page.
    - **`functions/`**: A subfolder for utility functions or hooks that are only used by this page.
  - **`common/`**: This directory holds components that are shared across multiple pages (e.g., `Button`, `Modal`, `FormField`). If a component is used in more than one page, it should be moved from its page-specific folder into `common`.
- **`contexts/`**: Contains all React Context providers for managing global state (e.g., `AuthContext`, `SidebarContext`).
- **`hooks/`**: Holds custom React hooks that are shared across the application, such as the `useHttp` hook for API calls. Pure function files like hooks use the `.js` extension, while React components use `.jsx`.
- **`util/`**: A directory for application-wide utility functions, constants, and API-related code.

## 4. API Integration Workflow

The application follows a standardized pattern for integrating with external APIs. **CRITICAL**: Always follow the "API-First Understanding" approach before implementing any API integration.

### Step 1: API Configuration Setup

- Reference `memory-bank/APIConfig.md` for base URL, headers, authentication, and other configuration details
- Ensure all API configuration is centralized and consistent across the application

### Step 2: API-First Understanding Approach

**Before writing any onSuccess or onResponse functions, ALWAYS:**

1. **Test API Call First:** Make an initial API call to understand the actual response structure
2. **Analyze Response Structure:** Examine the complete response object, including:
   - Data structure and nested objects
   - Array formats and item structures
   - Metadata fields (pagination, counts, etc.)
   - Error response formats
3. **Document Response Structure:** Note the response format for future reference
4. **Design Response Handler:** Only after understanding the structure, write the appropriate onSuccess/onResponse function

### Step 3: Define API Metadata

All API endpoints are defined as metadata objects in `src/util/constant/CONSTANTS.js`. The structure is organized by module (e.g., `categories`, `components`). Each module contains a set of action objects, where the action type (e.g., `get`, `create`) is the key. The value is an object specifying the HTTP `type` and `endpoint`.

```javascript
// Example from CONSTANTS.js
API: {
    categories: { // Module Name
        get: { // Action Type
            type: "GET", // HTTP Method
            endpoint: "/admin/categories" // API Endpoint
        },
        update: {
            type: "PATCH",
            endpoint: `/admin/categories/:id`
        },
    }
}
```

### Step 4: Instantiate `useHttp` Hook

In the component that needs to fetch or send data, the `useHttp` hook (from `src/hooks/use-http.js`) is instantiated.

```javascript
const api = useHttp();
```

### Step 5: Initial API Testing (Required)

Before implementing the final integration, make a test call to understand the response:

```javascript
// Step 5a: Test API call to understand response structure
useEffect(() => {
  api.sendRequest(
    CONSTANTS.API.categories.get,
    (response) => {
      console.log("API Response Structure:", response);
      // Analyze the response structure here
      // Example analysis:
      // - Is data in response.data or response.rows?
      // - What's the pagination structure?
      // - What fields are available?
    },
    { page: 1, limit: 5 } // Small payload for testing
  );
}, []);
```

### Step 6: Implement Response Handler Based on Structure

After understanding the response structure, implement the appropriate handler:

```javascript
// Step 6: Implement based on actual response structure
const handleCategoriesResponse = (response) => {
  // Based on API testing, adapt to actual structure
  if (response.rows) {
    setCategories(response.rows);
    setTotalCount(response.count);
  } else if (response.data) {
    setCategories(response.data);
  }
  // Handle other response patterns as discovered
};

api.sendRequest(CONSTANTS.API.categories.get, handleCategoriesResponse, {
  page: 1,
  limit: 10,
});
```

### Step 7: Complete API Integration

The `sendRequest` function from the `useHttp` hook is called to execute the API request. It takes the following positional arguments:

- **1. `url`** (Required): The API metadata object from `CONSTANTS.js` (e.g., `CONSTANTS.API.categories.get`).
- **2. `responseHandler`** (Required): A callback function to handle the successful response data (designed based on Step 5 analysis).
- **3. `payload`** (Optional): The request payload for POST, PATCH, etc. For GET requests, this is an object of query parameters that will be converted to a query string.
- **4. `successMessage`** (Optional): A string message to display in a notification on success.
- **5. `errorHandler`** (Optional): A callback function to handle errors.

```javascript
// Example: Final implementation after understanding response structure
api.sendRequest(
  CONSTANTS.API.categories.get, // 1. url
  (response) => {
    // 2. responseHandler (designed after API testing)
    setCategories(response.rows || response.data || []);
    if (response.pagination) {
      setPagination(response.pagination);
    }
  },
  { page: 1, limit: 10 }, // 3. payload
  "Categories loaded successfully!", // 4. successMessage
  (error) => {
    // 5. errorHandler
    console.error("Failed to load categories:", error);
  }
);
```

### API Integration Best Practices

1. **Always test API first** before writing response handlers
2. **Use APIConfig.md** for all configuration references
3. **Document response structures** for team reference
4. **Handle multiple response patterns** gracefully
5. **Implement proper error handling** based on actual API error responses
6. **Use consistent naming** for response handler functions
7. **Test with small payloads** initially to understand structure

This pattern ensures robust API integrations that work with actual response structures rather than assumptions, centralizes API definitions, standardizes request handling, and abstracts away the complexity of `axios`, loading states, and error management.

## 5. Component Interaction

- UI components are composed within feature-specific parent components (e.g., `ComponentManager`, `TemplateManager`).
- The main `App.jsx` component wraps the entire application with the necessary context providers (`AuthProvider`, `SidebarProvider`).
- The `Layout` component provides the main structure for the authenticated parts of the application.

## 6. Data Flow

- User authentication is handled by the `AuthContext`, which stores the user's token and data in `localStorage`.
- The application's routes are centrally defined in `src/util/Route.jsx`.
- Data is fetched from external APIs using the `useHttp` hook, and the `AuthContext` is used to provide the necessary authentication headers for these requests.

## 7. Error Handling and Logging

- The `ProtectedRoute` component handles the loading state while checking for user authentication.
- The `AuthContext` includes basic error handling for the login process.
- The SRS specifies a need for more robust error handling pages (404, 500) and user activity logging, which will be implemented as part of the API integration.

## 8. Debouncing User Input

To prevent excessive API calls on every keystroke, especially in search functionalities, the `useDebounce` custom hook should be utilized. This pattern delays the processing of user input until they have stopped typing for a specified duration.

- **Hook Location:** `src/hooks/useDebounce.js`
- **Usage:**

  1.  Import the `useDebounce` hook in your component.
  2.  Create a state variable to hold the immediate input value (e.g., a search term).
  3.  Pass this state variable and a delay (in milliseconds) to the `useDebounce` hook.
  4.  Use the debounced value in a `useEffect` hook to trigger the API call.

  ```javascript
  import React, { useState, useEffect } from "react";
  import { useDebounce } from "../hooks/useDebounce";

  const SearchComponent = () => {
    const [searchTerm, setSearchTerm] = useState("");
    const debouncedSearchTerm = useDebounce(searchTerm, 500); // 500ms delay

    useEffect(() => {
      if (debouncedSearchTerm) {
        // Trigger API call with debouncedSearchTerm
        console.log("Searching for:", debouncedSearchTerm);
      }
    }, [debouncedSearchTerm]);

    return (
      <input
        type="text"
        placeholder="Search..."
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
      />
    );
  };
  ```

## 9. Dynamic Form Generation from Constants

To promote consistency, reusability, and ease of maintenance, the application employs a pattern for dynamically generating forms from a centralized configuration.

### 1. Configuration in `CONSTANTS.js`

- **Location**: All form structures are defined in `src/util/constant/CONSTANTS.js` under the `FORM` key.
- **Structure**: Each form is an array of objects, where each object defines a single form field.

**Example from `CONSTANTS.js`:**

```javascript
FORM: {
    pageSetting: [
        {
            name: "name",
            label: "Page Name",
            required: true,
            tooltip: "The display name of your page",
            rules: [
                { required: true, message: "Please enter page name" },
                { min: 2, message: "Page name must be at least 2 characters" },
            ],
            placeholder: "e.g., Home, About, Contact",
            colProps: { xs: 24, md: 12 },
        },
        {
            name: "meta_description",
            label: "Meta Description",
            tooltip: "Brief description for search engines (recommended: 150-160 characters)",
            type: "textarea", // Determines the input component
            rules: [
                { max: 160, message: "Meta description should be under 160 characters" },
            ],
            placeholder: "Brief description for search engines",
            rows: 3,
            colProps: { xs: 24 },
        },
        // ... other fields
    ],
}
```

### 2. The `FormFields` Component

- A generic `FormFields` component (located in `src/components/common/`) is responsible for rendering the correct Ant Design input based on the `type` property of the field configuration object.
- It handles rendering `Input`, `TextArea`, `Select`, etc., along with labels, tooltips, and validation rules.

### 3. Implementation in a Form Component

- The component that contains the form (e.g., `PageSetting.jsx`) imports the configuration from `CONSTANTS.js`.
- It then maps over the configuration array, rendering a `FormFields` component for each field object.

**Example from `PageSetting.jsx`:**

```jsx
import { CONSTANTS } from "../../../util/constant/CONSTANTS";
import FormFields from "../../common/FormFields";

// ...

<Form form={form} onFinish={handleFormSubmit}>
  <Row gutter={[16, 0]}>
    {CONSTANTS?.FORM?.pageSetting?.map((field) => (
      <Col key={field.name} {...(field.colProps || { xs: 24 })}>
        <FormFields field={field} />
      </Col>
    ))}
  </Row>
</Form>;
```

### Benefits of this Pattern

- **Centralized Management**: Update form fields, validation, and layout in one central location.
- **Rapid Development**: Quickly build new forms by defining a configuration object.
- **Consistency**: Ensures all forms across the application share a consistent design and behavior.
- **Clean Code**: Keeps form components free of repetitive boilerplate code, making them easier to read and maintain.

## 10. Data Transformation Patterns

### Page Edit Data Transformation System

The application implements a comprehensive data transformation system to handle the conversion between API response formats and edit-friendly formats, particularly for complex nested data structures like repeat components.

#### Core Transformation Utilities

**Location**: `src/util/pageDataTransformer.js`

**Key Functions**:

- `transformGetOneToEditFormat()`: Converts API getOne response to edit format
- `createPartialEditPayload()`: Creates optimized payloads with only modified fields
- `hasComponentDataChanged()`: Deep comparison for componentData arrays
- `transformEditToApiFormat()`: Converts edit format back to API format

#### Data Structure Transformation Flow

**1. API Response → Edit Format**

```javascript
// Original API Response Structure (pageGetOne.json)
{
  "data": [{
    "latestVersion": {
      "componentData": [
        {
          "componentVersionId": 33,
          "repeatComponents": [
            { "key": "sample", "componentVersionId": 31 }
          ]
        }
      ],
      "components": [
        { "id": 33, "html": "...", "css": "...", "js": "..." }
      ]
    }
  }]
}

// Transformed Edit Format (wantThisFormate.json)
{
  "componentData": [
    {
      "componentVersionId": 33,
      "html": "...", // Merged from components array
      "css": "...",  // Merged from components array
      "js": "...",   // Merged from components array
      "categoryData": {...}, // Added category info
      "componentversions": [...], // Added version info
      "uniqueId": "...", // Generated unique ID
      "isLoading": false,
      "repeatComponents": [
        {
          "key": "sample",
          "versionId": 31,
          "componentId": 26,
          "html": "...", // Full component details
          "css": "...",
          "js": "...",
          // ... all component metadata
        }
      ]
    }
  ]
}
```

**2. Component Merging Logic**

- Finds components by `componentVersionId` in the components array
- Merges `html`, `css`, `js` fields into componentData items
- Adds `categoryData`, `componentversions`, and metadata
- Generates `uniqueId` for React key stability
- Sets `isLoading: false` for UI state management

**3. RepeatComponents Expansion**

- Processes `repeatComponents` arrays within components
- Finds each repeat component by `componentVersionId`
- Expands with full component details (html, css, js, etc.)
- Maintains the `key` field for placeholder replacement
- Adds all component metadata for form compatibility

#### Partial Payload Optimization

**Smart Change Detection**:

- Only sends modified fields on edit submission
- Deep comparison for `componentData` array changes
- Sends full `componentData` array when any component is modified
- Tracks changes in all editable fields (name, urlSlug, metaTitle, etc.)

**Implementation Example**:

```javascript
// In DragDropBuilder.jsx handleSave()
if (page) {
  try {
    const partialPayload = createPartialEditPayload(page, payload);
    payloadToSend = {
      id: page.id,
      ...partialPayload,
    };
  } catch (transformError) {
    // Fallback to original logic
  }
}
```

#### Form Integration Patterns

**SelectComponentModal Field Mapping**:
The transformation system required fixing field mapping in `SelectComponentModal.jsx` for repeat components:

```javascript
// Fixed field mapping for transformed data
initialValues[`component_${index}`] = rc?.componentId || rc?.value || rc?.id;
initialValues[`version_${index}`] = rc?.versionId || rc?.id;

// Proper selectedComponent structure for modal compatibility
const selectedComponent = {
  id: rc?.componentId || rc?.value || rc?.id,
  name: rc?.name || rc?.componentName || rc?.label,
  componentversions: rc?.componentversions || [
    { id: rc?.versionId || rc?.id, version: rc?.version || 1 },
  ],
  ...rc,
};
```

#### Error Handling & Fallbacks

**Robust Error Handling**:

- Comprehensive try-catch blocks with fallback mechanisms
- User-friendly error messages with console logging for debugging
- Graceful degradation if transformation fails
- Maintains backward compatibility with original logic

**Implementation in PageBuilder.jsx**:

```javascript
try {
  const transformedData = transformGetOneToEditFormat(res);
  setEditingPage({ ...transformedData, isLoading: false });
} catch (transformError) {
  console.error("Error transforming page data:", transformError);
  // Fallback to original logic
  const processedPageData = {
    ...pageData,
    componentData: pageData.componentData || [],
    isLoading: false,
  };
  setEditingPage(processedPageData);
  message.warning("Data transformation failed, using fallback format.");
}
```

#### Key Benefits

1. **Seamless Data Flow**: Automatic transformation between API and edit formats
2. **Optimized API Calls**: Only modified fields sent on edit submission
3. **Smart Component Handling**: Full componentData array sent when any component changes
4. **Form Compatibility**: Proper field mapping for all form components
5. **Robust Error Handling**: Multiple fallback mechanisms ensure system stability
6. **Backward Compatibility**: Original logic preserved as fallback

#### Usage Guidelines

1. **Always use transformation utilities** for page edit operations
2. **Test API responses first** to understand actual data structure
3. **Handle field mapping carefully** in form components
4. **Implement proper error handling** with fallback mechanisms
5. **Document data structure changes** for team reference
6. **Use consistent field naming** across transformation functions
