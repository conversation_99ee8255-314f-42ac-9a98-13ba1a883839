{"status": "success", "data": [{"id": 7, "name": "page to delete", "isActive": true, "createdAt": "2025-09-19T12:49:03.463Z", "updatedAt": "2025-09-19T12:49:03.463Z", "deletedAt": null, "createdBy": 1, "latestVersion": {"id": 12, "version": 2, "urlSlug": "prices", "metaTitle": "${company_name} Charter Bus Prices", "metaDescription": "", "customCss": "", "customJs": "", "headers": "", "wrapperClass": "", "componentVersionIds": [33, 34, 35, 31, 24, 36, 21, 22, 37], "componentData": [{"name": "hero_section_content", "type": "single", "class": "", "index": 0, "componentVersionId": 33}, {"name": "common_buttons", "type": "single", "class": "btn-primary", "index": 1, "componentVersionId": 34}, {"name": "faq_section", "type": "repeat", "class": "faq-style", "index": 2, "repeatComponents": [{"key": "{{sample}}", "componentVersionId": 31}], "componentVersionId": 35}, {"name": "testimonial_cards", "type": "single", "class": "card-grid", "index": 3, "componentVersionId": 35}, {"name": "feature_highlights", "type": "repeat", "class": "highlight-list", "index": 4, "repeatComponents": [{"componentVersionId": 24}], "componentVersionId": 35}, {"name": "pricing_table", "type": "single", "class": "pricing-style", "index": 5, "componentVersionId": 36}, {"name": "team_section", "type": "repeat", "class": "team-grid", "index": 6, "repeatComponents": [{"key": "{{sample}}", "componentVersionId": 21}, {"key": "", "componentVersionId": 22}], "componentVersionId": 35}, {"name": "contact_form", "type": "single", "class": "form-styled", "index": 7, "componentVersionId": 37}], "componentCount": 8, "createdAt": "2025-09-19T12:52:52.926+00:00", "updatedAt": "2025-09-19T12:52:52.926+00:00", "deletedAt": null, "pageId": 7, "createdBy": 1, "components": [{"id": 21, "version": 1, "html": "<div data-component=\"we_offer_card\"><div id=\"we-offer-card\">\n  <h2\n    class=\"lgm:dw-text-[24px] dw-text-[20px] lgx:dw-text-[22px] dw-font-semibold dw-pt-[16px] mdl:dw-pb-[12px] dw-pb-[20px] dw-font-heading\"\n  >\n    <a href=\"${offer_page_redirect_url}\">${service_name} </a>\n  </h2>\n  <img\n    src=\"${_img-${offer-service-image}}\"\n    alt=\"${_alt-${offer-service-image}}\"\n    class=\"dw-rounded-[16px] dw-w-full dw-mb-3\"\n  />\n  <p class=\"dw-text-[16px] xl1:dw-text-[18px] mdx:dw-mb-4 dw-mb-2\">\n    ${service_description}\n  </p>\n  <a\n    href=\"${offer_page_redirect_url}\"\n    class=\"dw-text-theme-main dw-text-[14px] dw-my-2 dw-font-medium dw-flex dw-items-center dw-gap-1\"\n  >\n    Learn More\n    <svg\n      class=\"dw-w-[14px] dw-h-[14px]\"\n      viewBox=\"0 0 448 512\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <path\n        class=\"dw-fill-theme-main\"\n        d=\"M190.5 66.9l22.2-22.2c9.4-9.4 24.6-9.4 33.9 0L441 239c9.4 9.4 9.4 24.6 0 33.9L246.6 467.3c-9.4 9.4-24.6 9.4-33.9 0l-22.2-22.2c-9.5-9.5-9.3-25 .4-34.3L311.4 296H24c-13.3 0-24-10.7-24-24v-32c0-13.3 10.7-24 24-24h287.4L190.9 101.2c-9.8-9.3-10-24.8-.4-34.3z\"\n      />\n    </svg>\n  </a>\n</div></div>", "css": "", "js": "", "createdAt": "2025-09-11T10:21:29.85+00:00", "updatedAt": "2025-09-11T10:21:29.85+00:00", "deletedAt": null, "componentId": 16, "createdBy": 1}, {"id": 22, "version": 1, "html": "<div data-component=\"book_in_minutes\"><div\n  class=\"homepage-cta dw-bg-theme-mainColorShade dw-bg-[url(${_img-${book_in_minutes_cta_bg}})] dw-bg-no-repeat dw-bg-cover dw-py-[64px] mdx:dw-pb-[96px] mdl:dw-px-[160px] lgm:dw-px-[240px] xl1:dw-px-[160px] xl1:dw-py-[80px]\"\n>\n  <div\n    class=\"dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"row\">\n      <div class=\"dw-w-full\">\n        <h2\n          class=\"dw-mx-1 dw-text-white dw-font-heading dw-text-center dw-mb-[30px] xl1:dw-w-1/2 dw-w-full mdl:dw-mx-auto lgm:dw-text-[48px] lgx:dw-text-[50px] mdx:dw-text-[46px] dw-text-[26px] dw-font-semibold xl1:dw-leading-[60px] lgx:dw-leading-[46px] dw-pb-4\"\n        >\n          ${book_in_minutes_title}\n        </h2>\n        <div\n          class=\"dw-flex dw-flex-col mdx:dw-flex-row dw-items-center dw-justify-center dw-gap-4 dw-mt-6\"\n        >\n          <!-- Call Us Button -->\n          <a\n            href=\"tel:${contact_number}\"\n            class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center mdx:dw-gap-[10px] dw-pe-[40px] dw-gap-[30px] mdx:dw-pe-[10px] lgs:dw-text-[18px] dw-text-[16px] dw-ps-[3px] dw-py-[3px] dw-bg-white dw-rounded-full hover:dw-bg-theme-main hover:dw-text-white dw-border-[2px] dw-border-white hover:dw-border-theme-main dw-transition\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"42\"\n              height=\"42\"\n              viewBox=\"0 0 36 36\"\n              fill=\"none\"\n            >\n              <rect width=\"36\" height=\"36\" rx=\"18\" class=\"dw-fill-theme-main\" />\n              <path\n                d=\"M27.7388 22.4138C27.5716 23.6841 26.9477 24.8502 25.9837 25.6942C25.0196 26.5382 23.7813 27.0023 22.5 27.0001C15.0563 27.0001 9.00001 20.9438 9.00001 13.5001C8.99771 12.2188 9.4619 10.9805 10.3059 10.0164C11.1499 9.05234 12.3159 8.42847 13.5863 8.2613C13.9075 8.22208 14.2328 8.2878 14.5136 8.44865C14.7944 8.60951 15.0157 8.85687 15.1444 9.1538L17.1244 13.5741V13.5854C17.2229 13.8127 17.2636 14.0608 17.2428 14.3077C17.222 14.5545 17.1404 14.7924 17.0053 15.0001C16.9884 15.0254 16.9706 15.0488 16.9519 15.0722L15 17.386C15.7022 18.8129 17.1947 20.2922 18.6403 20.9963L20.9222 19.0547C20.9446 19.0359 20.9681 19.0184 20.9925 19.0022C21.2 18.8639 21.4387 18.7794 21.687 18.7565C21.9353 18.7336 22.1854 18.7729 22.4147 18.871L22.4269 18.8766L26.8434 20.8557C27.1409 20.9839 27.3889 21.205 27.5503 21.4858C27.7116 21.7667 27.7778 22.0922 27.7388 22.4138Z\"\n                fill=\"white\"\n              ></path>\n            </svg>\n            ${contact_number_button_label}\n          </a>\n\n          <!-- Get Quote Button -->\n          <a\n                      class=\"pop dw-flex dw-font-medium dw-w-fit dw-items-center dw-gap-[6px] lgs:dw-text-[18px] dw-text-[16px] dw-pe-[10px] dw-border-[2px] hover:dw-bg-theme-main hover:dw-text-white dw-rounded-full dw-bg-white dw-border-white hover:dw-border-theme-main dw-transition\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              id=\"Layer_1\"\n              data-name=\"Layer 1\"\n              viewBox=\"0 0 72 84\"\n              width=\"48\"\n              height=\"48\"\n              class=\"dw-fill-white\"\n            >\n              <circle\n                cx=\"36\"\n                cy=\"42\"\n                r=\"36\"\n                class=\"dw-fill-theme-main\"\n              ></circle>\n              <g transform=\"translate(18, 18) scale(0.8)\">\n                <path\n                  d=\"M21.45 7.46c2.09-.11 4.18.1 6.23.44l.13.22c-.51 1.39-2.17.52-3.38.48-9.62-.33-17.23 4.17-21.45 12.76-.11.23-.47 1.43-.53 1.49-.23.26-1.08.01-1.14-.18-.08-.25 1.18-2.92 1.4-3.33C6.35 12.59 13.75 7.86 21.44 7.45Z\"\n                  class=\"cls-3\"\n                ></path>\n                <path\n                  d=\"M22.15 9.65c.52-.02 4.24.09 4.25.48-.74 1.21-.54.76-1.62.75-7.73-.07-14.38 2.32-18.55 9.17-.33.55-1.51 3.39-1.673.51-.13.1-1.1-.11-1.14-.26-.04-.13.91-2.17 1.05-2.46C7.73 14.38 14.91 9.88 22.15 9.66Z\"\n                  class=\"cls-3\"\n                ></path>\n                <path\n                  d=\"M.13 29.21c.06-.03.97 0 1.01.04.09.13-.04 2.64 0 3.07.56 6.25 4.19 12.15 9.25 15.75.45.32 2.88 1.55 2.94 1.71.02.05-.36.77-.39.92C5.95 47.64.79 40.17.09 32.59c-.04-.45-.2-3.25.04-3.38\"\n                  class=\"cls-1\"\n                ></path>\n                <path\n                  d=\"M21.71 11.93c1.05-.06 2.11.02 3.16.09.26.1-.47 1.03-.53 1.05-.37.13-1.97-.06-2.54 0-5.7.56-10.71 3.36-13.73 8.29-.29.48-1.09 2.47-1.23 2.63-.21.23-.9.01-1.14-.18 2.33-6.64 8.99-11.48 16.01-11.89Z\"\n                  class=\"cls-3\"\n                ></path>\n                <path\n                  d=\"M2.42 29.21c.07-.03.96 0 1.01.04-.56 6.56 2.81 12.97 8.03 16.8.39.29 2.65 1.47 2.68 1.62 0 .05-.37.76-.39.92-5.39-2.46-9.76-8.03-11.01-13.82-.15-.7-.78-5.36-.31-5.57Z\"\n                  class=\"cls-1\"\n                ></path>\n                <path\n                  d=\"m4.61 29.21 1.01.04c-.47 6.78 3.33 13.04 9.21 16.23l-.31 1.01C8.17 43.38 3.99 36.33 4.61 29.21M2.94 24.56s.54.12.66.13c-.19.44-.08 3.28-.22 3.38-.03.02-.62.02-.66-.04-.02-.46-.01-3.28.22-3.46ZM5.31 25.09l.66.13c-.14.37-.1 2.77-.22 2.85-.03.02-.62.02-.66-.04 0-.98-.06-1.98.22-2.94\"\n                  class=\"cls-1\"\n                ></path>\n                <path\n                  d=\"M35.05 0 25.01 27.68l8.95.26-23.39 32.5c3.39-9.18 7.02-18.39 9.82-27.72l-8.73-.13z\"\n                  style=\"fill: #fdfeff\"\n                ></path>\n                <path\n                  d=\"M29.52 8.95c.26 0 0 .31-.04.39-4.07 7.61-10.02 14.97-14.3 22.54l-1.71.13z\"\n                  style=\"fill: #fefefe\"\n                ></path>\n              </g>\n            </svg>\n            ${get_quote_button_text}\n          </a>\n        </div>\n      </div>\n    </div>\n  </div>\n</div></div>", "css": "", "js": "", "createdAt": "2025-09-11T10:22:50.032+00:00", "updatedAt": "2025-09-11T10:22:50.032+00:00", "deletedAt": null, "componentId": 17, "createdBy": 1}, {"id": 24, "version": 1, "html": "<div data-component=\"testimonial_card\"><div\n  class=\"dw-bg-body-bg dw-p-[20px_20px_40px] dw-rounded-[16px] dw-h-[315px] dw-shadow-[1px_1px_7px_rgba(0,0,0,0.13),0_0_2px_rgba(0,0,0,0.05)] dw-transition-transform dw-duration-300 dw-ease-out hover:dw--translate-y-1.5\"\n>\n  <div class=\"dw-flex dw-items-center dw-gap-3\">\n    <img\n      src=\"${_img-${user-image}}\"\n      alt=\"${user_name}\"\n      class=\"dw-w-[60px] dw-h-[60px] dw-rounded-full dw-object-cover\"\n    />\n    <p\n      class=\"dw-mb-0 dw-font-bold xl1:dw-text-[18px] lgx:dw-text-[14px] dw-text-[16px]\"\n    >\n      ${user_name}\n    </p>\n  </div>\n  <div class=\"dw-text-yellow-500 dw-text-[25px] dw-leading-none dw-py-[10px]\">\n    ★★★★★\n  </div>\n  <p class=\"xl1:dw-text-[16px] dw-text-[14px]\">${testimonial}</p>\n</div>\n</div>", "css": "", "js": "", "createdAt": "2025-09-11T10:26:02.506+00:00", "updatedAt": "2025-09-11T10:26:02.506+00:00", "deletedAt": null, "componentId": 19, "createdBy": 1}, {"id": 31, "version": 1, "html": "<div data-component=\"faq_question\"><div class=\"dw-border-t dw-border-[#ddd] dw-py-[15px]\">\n  <h3\n    class=\"dw-cursor-pointer accordion-btn dw-w-full dw-text-left dw-flex dw-justify-between dw-items-center dw-font-heading dw-font-semibold dw-text-lg dw-mb-2 dw-leading-tight\"\n  >\n    ${faq_question}\n  </h3>\n  <div class=\"dw-mt-2 accordion-body dw-hidden\">\n    <p class=\"dw-text-base\">${faq_answer}</p>\n  </div>\n</div>\n</div>", "css": "", "js": "", "createdAt": "2025-09-11T10:35:41.179+00:00", "updatedAt": "2025-09-11T10:35:41.179+00:00", "deletedAt": null, "componentId": 26, "createdBy": 1}, {"id": 33, "version": 1, "html": "<div data-component=\"hero_section_content\"><div\n  class=\"dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n>\n  <div\n    class=\"dw-w-full dw-mx-auto xlx:dw-w-[62%] lgm:dw-w-[81%] lgx:dw-w-[75%] lgx:dw-px-0 dw-px-6\"\n  >\n    <h1\n      class=\"dw-capitalize dw-mx-auto dw-text-[32px] mdx:dw-text-[48px] dw-font-bold dw-text-heading dw-text-center dw-pb-5 dw-font-heading dw-leading-tight\"\n    >\n      ${hero_section_title}\n    </h1>\n    <p class=\"xl1:dw-text-lg dw-text-base\">${hero_section_description}</p>\n  </div>\n</div>\n</div>", "css": "", "js": "", "createdAt": "2025-09-12T09:47:06.882+00:00", "updatedAt": "2025-09-12T09:47:06.882+00:00", "deletedAt": null, "componentId": 28, "createdBy": 1}, {"id": 34, "version": 1, "html": "<div data-component=\"common_buttons\"><div\n  class=\"dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n>\n  <div\n    class=\"dw-mx-8 dw-flex dw-items-center mdx:dw-flex-row dw-flex-col mdx:dw-gap-5 dw-gap-4 dw-justify-center\"\n  >\n    <a\n           class=\"pop dw-inline-flex dw-w-fit dw-font-medium dw-items-center dw-gap-[6px] lgs:dw-text-[18px] dw-text-[16px] dw-pe-[10px] dw-ps-3 dw-py-[9.5px] dw-text-white dw-border-[2px] dw-border-theme-main dw-rounded-full dw-bg-theme-main\"\n    >\n      <svg\n        xmlns=\"http://www.w3.org/2000/svg\"\n        id=\"Layer_1\"\n        data-name=\"Layer 1\"\n        viewBox=\"0 0 35.05 60.44\"\n        width=\"30\"\n        height=\"30\"\n        fill=\"white\"\n      >\n        <path\n          d=\"M21.45 7.46c2.09-.11 4.18.1 6.23.44l.13.22c-.51 1.39-2.17.52-3.38.48-9.62-.33-17.23 4.17-21.45 12.76-.11.23-.47 1.43-.53 1.49-.23.26-1.08.01-1.14-.18-.08-.25 1.18-2.92 1.4-3.33C6.35 12.59 13.75 7.86 21.44 7.45Z\"\n          class=\"cls-3\"\n        />\n        <path\n          d=\"M22.15 9.65c.52-.02 4.24.09 4.25.48-.74 1.21-.54.76-1.62.75-7.73-.07-14.38 2.32-18.55 9.17-.33.55-1.51 3.39-1.67 3.51-.13.1-1.1-.11-1.14-.26-.04-.13.91-2.17 1.05-2.46C7.73 14.38 14.91 9.88 22.15 9.66Z\"\n          class=\"cls-3\"\n        />\n        <path\n          d=\"M.13 29.21c.06-.03.97 0 1.01.04.09.13-.04 2.64 0 3.07.56 6.25 4.19 12.15 9.25 15.75.45.32 2.88 1.55 2.94 1.71.02.05-.36.77-.39.92C5.95 47.64.79 40.17.09 32.59c-.04-.45-.2-3.25.04-3.38\"\n          class=\"cls-1\"\n        />\n        <path\n          d=\"M21.71 11.93c1.05-.06 2.11.02 3.16.09.26.1-.47 1.03-.53 1.05-.37.13-1.97-.06-2.54 0-5.7.56-10.71 3.36-13.73 8.29-.29.48-1.09 2.47-1.23 2.63-.21.23-.9.01-1.14-.18 2.33-6.64 8.99-11.48 16.01-11.89Z\"\n          class=\"cls-3\"\n        />\n        <path\n          d=\"M2.42 29.21c.07-.03.96 0 1.01.04-.56 6.56 2.81 12.97 8.03 16.8.39.29 2.65 1.47 2.68 1.62 0 .05-.37.76-.39.92-5.39-2.46-9.76-8.03-11.01-13.82-.15-.7-.78-5.36-.31-5.57Z\"\n          class=\"cls-1\"\n        />\n        <path\n          d=\"m4.61 29.21 1.01.04c-.47 6.78 3.33 13.04 9.21 16.23l-.31 1.01C8.17 43.38 3.99 36.33 4.61 29.21M2.94 24.56s.54.12.66.13c-.19.44-.08 3.28-.22 3.38-.03.02-.62.02-.66-.04-.02-.46-.01-3.28.22-3.46ZM5.31 25.09l.66.13c-.14.37-.1 2.77-.22 2.85-.03.02-.62.02-.66-.04 0-.98-.06-1.98.22-2.94\"\n          class=\"cls-1\"\n        />\n        <path\n          d=\"M35.05 0 25.01 27.68l8.95.26-23.39 32.5c3.39-9.18 7.02-18.39 9.82-27.72l-8.73-.13z\"\n          style=\"fill: #fdfeff\"\n        />\n        <path\n          d=\"M29.52 8.95c.26 0 0 .31-.04.39-4.07 7.61-10.02 14.97-14.3 22.54l-1.71.13z\"\n          style=\"fill: #fefefe\"\n        />\n      </svg>\n      ${get_quote_button_text}\n    </a>\n    <a\n      href=\"tel:${contact_number}\"\n      class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center dw-gap-[10px] dw-pe-[10px] lgs:dw-text-[18px] dw-text-[16px] dw-ps-[3px] dw-py-[3px] dw-text-white dw-bg-body-text dw-rounded-full hover:dw-bg-theme-main dw-border-[2px] dw-border-body-text hover:dw-border-theme-main dw-transition\"\n    >\n      <svg\n        xmlns=\"http://www.w3.org/2000/svg\"\n        width=\"42\"\n        height=\"42\"\n        viewBox=\"0 0 36 36\"\n        fill=\"none\"\n      >\n        <rect width=\"36\" height=\"36\" rx=\"18\" class=\"dw-fill-theme-main\" />\n        <path\n          d=\"M27.7388 22.4138C27.5716 23.6841 26.9477 24.8502 25.9837 25.6942C25.0196 26.5382 23.7813 27.0023 22.5 27.0001C15.0563 27.0001 9.00001 20.9438 9.00001 13.5001C8.99771 12.2188 9.4619 10.9805 10.3059 10.0164C11.1499 9.05234 12.3159 8.42847 13.5863 8.2613C13.9075 8.22208 14.2328 8.2878 14.5136 8.44865C14.7944 8.60951 15.0157 8.85687 15.1444 9.1538L17.1244 13.5741V13.5854C17.2229 13.8127 17.2636 14.0608 17.2428 14.3077C17.222 14.5545 17.1404 14.7924 17.0053 15.0001C16.9884 15.0254 16.9706 15.0488 16.9519 15.0722L15 17.386C15.7022 18.8129 17.1947 20.2922 18.6403 20.9963L20.9222 19.0547C20.9446 19.0359 20.9681 19.0184 20.9925 19.0022C21.2 18.8639 21.4387 18.7794 21.687 18.7565C21.9353 18.7336 22.1854 18.7729 22.4147 18.871L22.4269 18.8766L26.8434 20.8557C27.1409 20.9839 27.3889 21.205 27.5503 21.4858C27.7116 21.7667 27.7778 22.0922 27.7388 22.4138Z\"\n          fill=\"white\"\n        ></path>\n      </svg>\n      ${contact_number_button_label}\n    </a>\n  </div>\n</div>\n</div>", "css": "", "js": "", "createdAt": "2025-09-12T09:50:09.12+00:00", "updatedAt": "2025-09-12T09:50:09.12+00:00", "deletedAt": null, "componentId": 29, "createdBy": 1}, {"id": 35, "version": 1, "html": "<div\n      class=\"lgx:dw-px-0 dw-px-3 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n    >\n      <p\n        class=\"lgx:dw-px-0 dw-px-1 dw-pb-5 dw-font-heading dw-text-2xl dw-text-center dw-w-full dw-leading-tight dw-mx-auto\"\n      >\n        ${form_title}\n      </p>\n      <p\n        class=\"lgx:dw-px-0 dw-px-1 dw-font-heading dw-text-xl dw-text-center dw-py-5 lgx:dw-pb-5 lgx:dw-pt-0 dw-text-center dw-leading-tight\"\n      >\n        ${form_subtitle}\n      </p>\n      <div\n        class=\"dw-p-4 dw-border dw-border-theme-cardBorder dw-rounded-[16px] dw-mx-auto lgl:dw-w-[50%] lgm:dw-w-[55%] lgx:dw-w-[62%] mdl:dw-w-[76%] lgs:dw-w-[60%]\"\n      >\n        ${form_script_embed}\n      </div>\n    </div>", "css": "", "js": "", "createdAt": "2025-09-12T09:50:50.074+00:00", "updatedAt": "2025-09-12T09:50:50.074+00:00", "deletedAt": null, "componentId": 30, "createdBy": 1}, {"id": 36, "version": 1, "html": "<div data-component=\"pricepage_cta\">\n      <div\n        class=\"quote-section dw-bg-theme-mainColorShade dw-py-[64px] mdx:dw-py-[64px] mdx:dw-px-[16px] mdl:dw-px-[24px] lgx:dw-py-[80px] lgx:dw-px-[48px] xl1:dw-px-[128px]\"\n      >\n        <div\n          class=\"dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n        >\n          <div\n            class=\"dw-grid dw-grid-cols-1 mdl:dw-grid-cols-12 mdx:dw-gap-6 dw-gap-10\"\n          >\n            <div\n              class=\"mdl:dw-col-span-6 dw-flex dw-items-center dw-justify-center smm:dw-px-1 dw-px-4\"\n            >\n              <p\n                class=\"dw-text-white dw-text-[24px] dw-font-semibold dw-font-heading dw-text-center lgl:dw-leading-[56px] dw-leading-[48px]\"\n              >\n                ${cta_quote_title}\n              </p>\n            </div>\n\n            <div\n              class=\"mdl:dw-col-span-6 dw-flex dw-items-center dw-justify-center dw-px-1\"\n            >\n              <div\n                class=\"dw-flex dw-flex-col dw-items-center dw-justify-center\"\n              >\n                <p\n                  class=\"dw-text-white dw-font-medium mdl:dw-text-base dw-text-sm dw-mb-8 dw-w-fit\"\n                >\n                  ${call_button_related_text}\n                </p>\n                <a\n                  href=\"tel:${contact_number}\"\n                  class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center mdx:dw-gap-[10px] dw-pe-[40px] dw-gap-[30px] mdx:dw-pe-[10px] lgs:dw-text-[18px] dw-text-[16px] dw-ps-[3px] dw-py-[3px] dw-bg-white dw-rounded-full hover:dw-bg-theme-main hover:dw-text-white dw-border-[2px] dw-border-white hover:dw-border-theme-main dw-transition\"\n                >\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    width=\"42\"\n                    height=\"42\"\n                    viewBox=\"0 0 36 36\"\n                    fill=\"none\"\n                  >\n                    <rect\n                      width=\"36\"\n                      height=\"36\"\n                      rx=\"18\"\n                      fill=\"#d5232b\"\n                      class=\"dw-fill-theme-main\"\n                    />\n                    <path\n                      d=\"M27.7388 22.4138C27.5716 23.6841 26.9477 24.8502 25.9837 25.6942C25.0196 26.5382 23.7813 27.0023 22.5 27.0001C15.0563 27.0001 9.00001 20.9438 9.00001 13.5001C8.99771 12.2188 9.4619 10.9805 10.3059 10.0164C11.1499 9.05234 12.3159 8.42847 13.5863 8.2613C13.9075 8.22208 14.2328 8.2878 14.5136 8.44865C14.7944 8.60951 15.0157 8.85687 15.1444 9.1538L17.1244 13.5741V13.5854C17.2229 13.8127 17.2636 14.0608 17.2428 14.3077C17.222 14.5545 17.1404 14.7924 17.0053 15.0001C16.9884 15.0254 16.9706 15.0488 16.9519 15.0722L15 17.386C15.7022 18.8129 17.1947 20.2922 18.6403 20.9963L20.9222 19.0547C20.9446 19.0359 20.9681 19.0184 20.9925 19.0022C21.2 18.8639 21.4387 18.7794 21.687 18.7565C21.9353 18.7336 22.1854 18.7729 22.4147 18.871L22.4269 18.8766L26.8434 20.8557C27.1409 20.9839 27.3889 21.205 27.5503 21.4858C27.7116 21.7667 27.7778 22.0922 27.7388 22.4138Z\"\n                      fill=\"white\"\n                    ></path>\n                  </svg>\n                  ${contact_number_button_label}\n                </a>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>", "css": "", "js": "", "createdAt": "2025-09-12T09:52:08.37+00:00", "updatedAt": "2025-09-12T09:52:08.37+00:00", "deletedAt": null, "componentId": 31, "createdBy": 1}, {"id": 37, "version": 1, "html": "<div data-component=\"content_title\"><div\n  class=\"dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n>\n  <h2\n    class=\"dw-capitalize dw-mx-auto dw-text-[32px] mdx:dw-text-[48px] dw-font-semibold dw-text-heading dw-text-center dw-pb-5 xl1:dw-w-[65%] dw-font-heading dw-leading-tight lgx:dw-px-0 dw-px-6\"\n  >\n    ${content_title}\n  </h2>\n</div></div>", "css": "", "js": "", "createdAt": "2025-09-12T09:56:21.441+00:00", "updatedAt": "2025-09-12T09:56:21.441+00:00", "deletedAt": null, "componentId": 32, "createdBy": 1}]}}]}