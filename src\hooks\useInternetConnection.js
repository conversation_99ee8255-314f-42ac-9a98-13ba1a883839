import { useState, useEffect, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useInternet } from '../contexts/InternetContext';
import { ROUTES } from '../util/Route';
const STORAGE_KEY = 'lastPathBeforeOffline';
const getFullPath = (loc) =>
    `${loc.pathname}${loc.search || ''}${loc.hash || ''}`;
const useInternetConnection = (options = {}) => {
    const {
        redirectOnOffline = true,
        redirectUrl = `/${ROUTES.internetLoss}`,
        autoRetry = true,
        maxRetries = 3,
        retryDelay = 2000,
        showNotifications = true
    } = options;

    const navigate = useNavigate();
    const location = useLocation();
    const {
        isOnline,
        connectionQuality,
        isRetrying,
        checkConnection,
        getConnectionStats
    } = useInternet();

    const [lastPath, setLastPath] = useState(null);
    const [retryCount, setRetryCount] = useState(0);
    const [isManualRetry, setIsManualRetry] = useState(false);

    // Handle connection state changes with durable lastPath
    useEffect(() => {
        if (!redirectOnOffline) return;

        const currentPath = getFullPath(location);

        if (!isOnline) {
            // If we're going offline and we're not already on the offline page,
            // remember where we were and go to the offline route.
            if (location.pathname !== redirectUrl) {
                sessionStorage.setItem(STORAGE_KEY, currentPath);
                navigate(redirectUrl, { replace: true, state: { from: currentPath } });
            }
        } else {
            // If we are online AND currently on the offline route,
            // go back to the remembered path (if any).
            if (location.pathname === redirectUrl) {
                const fromState = (location.state)?.from;
                const stored = sessionStorage.getItem(STORAGE_KEY) || fromState;

                if (stored && stored !== redirectUrl) {
                    sessionStorage.removeItem(STORAGE_KEY);
                    navigate(stored, { replace: true });
                }
            }
        }
    }, [isOnline, redirectOnOffline, redirectUrl, navigate, location]);

    // Handle connection state changes
    // useEffect(() => {
    //     if (!isOnline && redirectOnOffline) {
    //         // Store current path before redirecting
    //         if (location.pathname !== redirectUrl) {
    //             // setLastPath(location.pathname);
    //             // navigate(redirectUrl);
    //             sessionStorage.setItem(STORAGE_KEY, currentPath);
    //             navigate(redirectUrl, { replace: true, state: { from: currentPath } });
    //         }
    //     } else if (isOnline && lastPath && location.pathname === redirectUrl) {
    //         const fromState = (location.state)?.from;
    //         const stored = sessionStorage.getItem(STORAGE_KEY) || fromState;

    //         if (stored && stored !== redirectUrl) {
    //             sessionStorage.removeItem(STORAGE_KEY);
    //             navigate(stored, { replace: true });
    //         }
    //         // Return to last path when connection is restored
    //         // navigate(lastPath);
    //         // setLastPath(null);
    //     }
    // }, [isOnline, redirectOnOffline, redirectUrl, navigate]);

    // Auto-retry mechanism
    // useEffect(() => {
    //     if (!isOnline && autoRetry && !isManualRetry) {
    //         const retryTimer = setTimeout(() => {
    //             if (retryCount < maxRetries) {
    //                 setRetryCount(prev => prev + 1);
    //                 checkConnection();
    //             }
    //         }, retryDelay * (retryCount + 1));

    //         return () => clearTimeout(retryTimer);
    //     }
    // }, [isOnline, autoRetry, retryCount, maxRetries, retryDelay, checkConnection, isManualRetry]);

    // Auto-retry mechanism (exponential-ish backoff)
    useEffect(() => {
        if (!isOnline && autoRetry && !isManualRetry) {
            const delay = retryDelay * (retryCount + 1);
            const retryTimer = setTimeout(() => {
                if (retryCount < maxRetries) {
                    setRetryCount(prev => prev + 1);
                    checkConnection();
                }
            }, delay);

            return () => clearTimeout(retryTimer);
        }
    }, [isOnline, autoRetry, retryCount, maxRetries, retryDelay, checkConnection, isManualRetry]);


    // Manual retry function
    const manualRetry = useCallback(async () => {
        setIsManualRetry(true);
        setRetryCount(0);

        const success = await checkConnection();

        if (success) {
            setRetryCount(0);
        }

        setIsManualRetry(false);
        return success;
    }, [checkConnection]);

    // Force check connection
    const forceCheck = useCallback(async () => {
        return await checkConnection();
    }, [checkConnection]);

    // Reset retry count when connection is restored
    useEffect(() => {
        if (isOnline) {
            setRetryCount(0);
            setIsManualRetry(false);
        }
    }, [isOnline]);

    // Get connection status with additional info
    const getConnectionStatus = useCallback(() => {
        const stats = getConnectionStats();
        return {
            isOnline,
            connectionQuality,
            isRetrying: isRetrying || isManualRetry,
            retryCount,
            lastOnline: stats.lastOnline,
            uptime: stats.uptime,
            canRetry: retryCount < maxRetries,
            isManualRetry
        };
    }, [
        isOnline,
        connectionQuality,
        isRetrying,
        isManualRetry,
        retryCount,
        maxRetries,
        getConnectionStats
    ]);

    // Connection quality indicators
    const getQualityInfo = useCallback(() => {
        const qualityMap = {
            excellent: { color: 'green', label: 'Excellent', description: 'Very fast connection' },
            good: { color: 'blue', label: 'Good', description: 'Good connection speed' },
            fair: { color: 'orange', label: 'Fair', description: 'Moderate connection speed' },
            poor: { color: 'red', label: 'Poor', description: 'Slow connection' },
            unknown: { color: 'gray', label: 'Unknown', description: 'Connection quality unknown' }
        };

        return qualityMap[connectionQuality] || qualityMap.unknown;
    }, [connectionQuality]);

    return {
        // Connection state
        isOnline,
        connectionQuality,
        isRetrying: isRetrying || isManualRetry,
        retryCount,

        // Actions
        manualRetry,
        forceCheck,

        // Computed state
        getConnectionStatus,
        getQualityInfo,
        canRetry: retryCount < maxRetries,

        // Configuration
        maxRetries,
        retryDelay,
        showNotifications
    };
};

export default useInternetConnection;
