## **📄 Software Requirements Specification (SRS)**

**Project Name:** Static Site Generator for Informative Websites

### **1\. Introduction**

####

#### **i. Purpose**

The purpose of this document is to define the functional and non-functional requirements for the Static Site Generator (SSG). This system enables non-technical users to create, customise, and deploy HTML websites using a drag-and-drop interface with pre-built components, branding options, and content injection via structured data.

####

#### **ii. Definitions**

- **Component Category:** A Component type identifier block (e.g., hero section, testimonial block).
- **Component:** A reusable design block (e.g., hero section, testimonial block).
- **Template-Page:** A group of ordered components.
- **Template:** A collection of multiple pages arranged to form a structure.
- **Website:** A brand-configured version of a template deployed as a full website.
- **Template-Page:** A group of ordered components with content and media.
- **Versioning:** Historical tracking of all entities (components, pages, templates, websites).
- **SFTP:** Secure File Transfer Protocol used to deploy websites.
- **Admin:** Full access user role.
- **Content Team:** Role with restricted access—can only manage content and media.

###

### **2\. Description**

####

#### **i. Project Overview**

This SSG ( Static Site Generation ) platform enables users to:

- Create and manage reusable visual components.
- Group them into pages and templates.
- Add branded content via sheets or dynamic variables.
- Generate and export HTML-based websites.
- Deploy the site via SFTP or download for external hosting.
- Manage versions of every entity for rollback or migration.

#### **ii. Business Need**

Building static websites for multiple clients or departments manually is time-consuming. This SSG provides a scalable and templated solution that saves time, allows branding customization, enables team collaboration, and ensures rollback and deployment automation without requiring development skills.

####

#### **iii. Key Features**

- Drag-and-drop page builder from categorized components.
- Dynamic content insertion via sheet or JSON (key-value).
- Version control for components, pages, templates, and websites.
- HTML export and SFTP-based deployment with rollback history.
- Auto image upload, storage, and content mapping.
- Role-based access (Admin, Content Team).
- System pages: 404, 500, Maintenance, Unauthorized Access.

#### **iv. Assumptions and Dependencies**

- All deployment actions will use SFTP/SSH-based transfer APIs.
- Content updates are expected in the form of JSON or spreadsheet key-value pairs.
- Hosting is external; no integrated hosting panel is part of the MVP.
- Image files are handled via an upload and mapping with the file name.

### **3\. User Roles**

#### **i. List of User Types**

1. **Admin**
2. **Content Team**

#### **ii. Role-wise Access and Functionality**

| Role             | Permissions                                                                                                                                                                                                            |
| ---------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Admin**        | Full access including: \- Create/Edit/Delete: Category, Component, Page, Template, Website \- Manage Branding Configurations \- Deploy/Rollback Websites \- Manage Versions \- Export HTML \- Track Activity & History |
| **Content Team** | Restricted access: \- View Templates & Websites \- Upload and manage content & media (JSON, images) \- Cannot create/delete components, pages, templates, or deploy websites                                           |

###

### **4\. Platform Overview**

#### **i. Platforms Targeted**

- **Web Application (**Builder**)**

#### **ii. Deployment Environments**

- **Production** (for real-time deployment via SFTP)
- Deployment handled via:
  - SFTP Credentials (IP, Path, Username)

### **5\. Functionality Requirements**

####

#### **✅ Module 1: Category Management**

- Add/Edit/Delete categories
- Link the variety of components to categories

#### **✅ Module 2: Component Management**

- Fields: Name, HTML, CSS, JS, CategoryID, Version
- Use placeholders for dynamic content, e.g., `${heading}`
- Allow multiple versions of the same component
- Preview & Responsive support
- Component update & migration

#### **✅ Module 3: Page Builder**

- Drag & drop UI for adding components to a page
- Auto-generate from JSON structure
- Version control for page-level changes
- Import/export JSON of component mapping
- Create a Duplicate Page with the same component

####

#### **✅ Module 4: Template Management**

- Create templates from a group of pages
- Assign version

Actions:

- Inject Scripts
  - Set up page-wise path, metaTitle, metaDescription, and scripts

#### **✅ Module 5: Website Generator**

- Create a website from a selected template
- Fill branding fields:

  - Brand Name
  - Primary & Secondary Colours Fields
  - SFTP details (IP, Path, Username)

- Actions:
  - Export full HTML site
  - Inject content & image objects
  - Inject Scripts
  - Inject Dynamic Variables
  - Deploy via SFTP
  - Rollback from history
  - Maintain current version linkages for Component → Page → Template → Website

#### **✅ Module 6: Dynamic Content Engine**

- Accept content via:
  - JSON Key-Value object (to replace `${key}` in components)
  - Sheet-to-JSON converter
- Automatically map to variables in component HTML
- Show placeholder in preview mode if content is missing
- Batch content upload for 40+ pages
- Grouped uploads are supported by tagging/grouping media

####

####

#### **✅ Module 7: Image Upload & Auto-Mapping**

- Upload multiple images in a batch
- Map to placeholder variables in the component to the Image fileName

#### **✅ Module 8: System & UX Support Pages**

(These pages will be auto-generated and used in the default website flow)

| Page Name | Description                           |
| --------- | ------------------------------------- |
| 404 Page  | Displayed when the route is not found |

####

#### **✅ Module 9: Version Control & Migration**

- Versioning across:

  - Components

- Migration options:

  - Update a single version of the component
  - Replace one component with another

- Roll back any deployed version
- Deployment history maintained

#### **✅ Module 10: User Activity & Logs**

- Track all actions by the Admin and Content Team
- Activities like create, edit, delete, deploy, and rollback
- View logs filtered by user/date/entity type

###

### **6\. System & UX Support Pages**

Based on platform requirements (Web Only), the following pages are to be included:

#### **System & UX Support Pages**

| Page          | Platform | Notes                                      |
| ------------- | -------- | ------------------------------------------ |
| 404 Not Found | Web      | Shown when the route or page doesn't exist |

These pages will be part of every website generated and stored as reusable blocks within the platform.
