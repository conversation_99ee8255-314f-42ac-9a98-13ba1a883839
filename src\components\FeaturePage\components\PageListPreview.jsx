import { But<PERSON> } from "antd";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import SearchBar from "../../../components/common/SearchBar";
import PreviewTab from "../../../components/Components/EditorComponent/PreviewTab";
import JsonContentCollapse from "../../../components/common/JsonContentCollapse";
import { generateGlobalPreviewHTML } from "../../../components/Components/content";
import { buildAllSlugJSON, buildVarsForAllSlugs } from "./function";
import DownloadWebsiteButton from "../../../components/DownloadWebsiteButton";

const PageListPreview = ({
  // contentJSON = {},
  // components = [],
  pageList = [],
  exporthandler,
  extraDetail,
  bradingDetails,
}) => {
  const [expandedAll, setExpandedAll] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(false);

  const toggleExpandAll = () => {
    setLoading(true);
    setTimeout(() => {
      setExpandedAll((prev) => !prev);
      setLoading(false);
    }, 0);
  };
  const handleSearch = useCallback((value) => {
    setSearchTerm(value);
  }, []);

  const filteredContent = useMemo(() => {
    if (!pageList) return [];
    if (!searchTerm && searchTerm == "") return pageList;
    return pageList?.filter((page) =>
      page?.name?.toLowerCase()?.includes(searchTerm.toLowerCase())
    );
  }, [searchTerm, pageList]);

  return (
    <div className="tw-flex-1 tw-w-full tw-flex tw-flex-col tw-gap-2">
      <div className="tw-flex tw-items-center tw-justify-between">
        <div>
          <h3 className="tw-text-xl tw-font-bold tw-text-gray-900">Preview</h3>
          <span>pages: {pageList ? pageList?.length : 0}</span>
        </div>
        <div className="tw-flex tw-space-x-2">
          <DownloadWebsiteButton
            exporthandler={exporthandler}
            extraDetail={extraDetail}
            pageList={pageList}
            size="large"
            className="tw-px-6 tw-w-full tw-h-10 tw-flex tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
          />
          <Button
            size="large"
            onClick={toggleExpandAll}
            loading={loading}
            className="tw-flex tw-w-full tw-items-center tw-text-black tw-border-blue-200 hover:tw-bg-blue-50"
          >
            {expandedAll ? "Collapse All" : "Expand All"}
          </Button>
        </div>
      </div>
      <SearchBar handleSearch={(e) => handleSearch(e)} />
      <div className="tw-flex-1 tw-overflow-y-auto tw-p-4">
        {filteredContent?.length > 0 ? (
          <JsonContentCollapse
            expanded={expandedAll}
            itemList={filteredContent?.map((page) => {
              const firststatic = generateGlobalPreviewHTML({
                data: page?.components,
                pageData: page,
                isHtml: true,
                dynamicContentForm: extraDetail?.dynamicContentForm,
                GVariable: extraDetail?.GVariable,
                mediaFiles: extraDetail?.templateObj?.FileList,
                bradingDetails: bradingDetails,
              });
              return {
                key: page?.type == "dynamic" ? page?.name : page.id,
                label: page.name,
                children: (
                  <PreviewTab
                    //   formData={page}
                    generatePreview={firststatic}
                  />
                ),
              };
            })}
          />
        ) : (
          <div className="tw-text-center tw-py-8">
            <p className="tw-text-gray-500 tw-mb-2">No pages found</p>
            <p className="tw-text-sm tw-text-gray-400">
              Add pages to see them here
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PageListPreview;
