import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
} from "react";
import { notification } from "antd";

const InternetContext = createContext();

export const useInternet = () => {
  const context = useContext(InternetContext);
  if (!context) {
    throw new Error("useInternet must be used within an InternetProvider");
  }
  return context;
};

export const InternetProvider = ({ children }) => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [connectionQuality, setConnectionQuality] = useState("unknown");
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);
  const [lastOnlineTime, setLastOnlineTime] = useState(Date.now());
  const [connectionHistory, setConnectionHistory] = useState([]);

  // Test connection quality by making a request to a reliable endpoint
  const testConnectionQuality = useCallback(async () => {
    const startTime = Date.now();
    try {
      const response = await fetch("https://www.google.com/favicon.ico", {
        method: "HEAD",
        mode: "no-cors",
        cache: "no-cache",
      });
      const endTime = Date.now();
      const latency = endTime - startTime;

      if (latency < 500) {
        setConnectionQuality("excellent");
      } else if (latency < 1000) {
        setConnectionQuality("good");
      } else if (latency < 2000) {
        setConnectionQuality("fair");
      } else {
        setConnectionQuality("poor");
      }

      return true;
    } catch (error) {
      setConnectionQuality("poor");
      return false;
    }
  }, []);

  // Enhanced connection check with retry mechanism
  const checkConnection = useCallback(
    async (maxRetries = 3) => {
      setIsRetrying(true);
      let attempts = 0;

      while (attempts < maxRetries) {
        attempts++;
        setRetryCount(attempts);

        const isConnected = await testConnectionQuality();

        if (isConnected) {
          setIsOnline(true);
          setLastOnlineTime(Date.now());
          setConnectionHistory((prev) => [
            ...prev,
            {
              timestamp: Date.now(),
              status: "online",
              quality: connectionQuality,
            },
          ]);

          if (attempts > 1) {
            notification.success({
              message: "Connection Restored",
              description: "Your internet connection has been restored.",
              duration: 3,
            });
          }

          setIsRetrying(false);
          return true;
        }

        // Wait before retrying
        if (attempts < maxRetries) {
          await new Promise((resolve) => setTimeout(resolve, 2000 * attempts));
        }
      }

      setIsOnline(false);
      setConnectionHistory((prev) => [
        ...prev,
        {
          timestamp: Date.now(),
          status: "offline",
          quality: "none",
        },
      ]);

      notification.error({
        message: "Connection Failed",
        description:
          "Unable to establish internet connection after multiple attempts.",
        duration: 5,
      });

      setIsRetrying(false);
      return false;
    },
    [testConnectionQuality, connectionQuality]
  );

  // Handle online/offline events
  const handleOnline = useCallback(() => {
    setIsOnline(true);
    setLastOnlineTime(Date.now());
    setConnectionHistory((prev) => [
      ...prev,
      {
        timestamp: Date.now(),
        status: "online",
        quality: "unknown",
      },
    ]);

    // Test connection quality when coming back online
    testConnectionQuality();

    // notification.success({
    //   message: "Back Online",
    //   description: "Your internet connection has been restored.",
    //   duration: 3,
    // });
  }, [testConnectionQuality]);

  const handleOffline = useCallback(() => {
    setIsOnline(false);
    setConnectionHistory((prev) => [
      ...prev,
      {
        timestamp: Date.now(),
        status: "offline",
        quality: "none",
      },
    ]);

    // notification.warning({
    //   message: "Connection Lost",
    //   description: "You are currently offline. Some features may not work.",
    //   duration: 5,
    // });
  }, []);

  // Monitor connection quality periodically
  useEffect(() => {
    if (isOnline) {
      const interval = setInterval(() => {
        testConnectionQuality();
      }, 30000); // Check every 30 seconds

      return () => clearInterval(interval);
    }
  }, [isOnline, testConnectionQuality]);

  // Set up event listeners
  useEffect(() => {
    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    // Initial connection quality test
    if (isOnline) {
      testConnectionQuality();
    }

    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, [handleOnline, handleOffline, isOnline, testConnectionQuality]);

  // Get connection statistics
  const getConnectionStats = useCallback(() => {
    const now = Date.now();
    const last24Hours = connectionHistory.filter(
      (entry) => now - entry.timestamp < 24 * 60 * 60 * 1000
    );

    const onlineTime = last24Hours.filter(
      (entry) => entry.status === "online"
    ).length;
    const totalTime = last24Hours.length;
    const uptime = totalTime > 0 ? (onlineTime / totalTime) * 100 : 100;

    return {
      uptime: Math.round(uptime),
      totalConnections: connectionHistory.length,
      lastOnline: lastOnlineTime,
      currentQuality: connectionQuality,
      isRetrying,
    };
  }, [connectionHistory, lastOnlineTime, connectionQuality, isRetrying]);

  const value = {
    isOnline,
    connectionQuality,
    retryCount,
    isRetrying,
    lastOnlineTime,
    connectionHistory,
    checkConnection,
    getConnectionStats,
    testConnectionQuality,
  };

  return (
    <InternetContext.Provider value={value}>
      {children}
    </InternetContext.Provider>
  );
};

export default InternetContext;
