import { Select, Spin } from "antd";
import React, { useMemo, useState } from "react";
import useHttp from "../../../hooks/use-http";
import { apiGenerator } from "../../../util/functions";
import { CONSTANTS } from "../../../util/constant/CONSTANTS";

const VersionSelect = ({ componentData, handleSavePageSettings }) => {
  const [open, setOpen] = useState(false);
  const api = useHttp();
  //   const [loading, setLoading] = useState(false);
  const [versions, setVersions] = useState(() => {
    // Seed with whatever you already have so first open shows something
    if (componentData?.pageversions?.length) {
      return componentData.pageversions.map((v) => ({
        ...v,
        value: v.id,
        label: `v${v.version}`,
        extra: v,
      }));
    }
    if (componentData?.versionData) {
      return [
        {
          value: componentData.versionData.pageVersionId,
          label: `v${componentData.versionData.pageVersion}`,
          extra: componentData.versionData,
        },
      ];
    }
    return [];
  });

  const currentValue = useMemo(
    () =>
      componentData?.versionData?.id ||
      componentData?.versionData?.pageVersionId,
    [componentData?.versionData]
  );

  const needFetch =
    !componentData?.pageversions?.length && versions.length <= 1;

  const fetchPageVersions = (pageId) => {
    if (!pageId) return;

    api.sendRequest(
      apiGenerator(
        CONSTANTS.API.pages.getAllForTemplate,
        {},
        `?pageId=${pageId}`
      ),
      (res) => {
        const list =
          res?.data?.rows?.[0]?.pageversions?.map((v) => ({
            ...v,
            value: v.id,
            label: `v${v.version}`,
            extra: v,
          })) || [];
        setVersions(list); // update local list first; keep popup steady
      },
      null,
      null,
      (err) => {
        console.error("Error fetching pages:", err);
      }
    );
  };

  return (
    <Select
      size="large"
      value={currentValue}
      onChange={(value, option) => {
        handleSavePageSettings({ versionData: { ...option?.extra } });
      }}
      className="tw-w-12 tw-h-3 tw-text-sm version-class"
      style={{ width: 55, height: 30, borderRadius: "100px", fontSize: "12px" }}
      /* ✅ control visibility and fetch on first open */
      open={open}
      onDropdownVisibleChange={(next) => {
        setOpen(next);
        if (next && needFetch)
          fetchPageVersions(componentData?.versionData?.pageId);
      }}
      /* ✅ keep popup inside the scrollable parent (AntD recommendation) */
      getPopupContainer={(trigger) => trigger.parentElement}
      /* ✅ let dropdown be wider than the tiny 55px input & avoid deprecated prop */
      popupMatchSelectWidth={false}
      /* ✅ smooth loading without remount flicker */
      loading={api.isLoading}
      notFoundContent={
        api.isLoading ? (
          <div style={{ padding: 8 }}>
            <Spin size="small" /> Loading…
          </div>
        ) : null
      }
      /* ✅ use the stable local options list */
      options={
        versions.length
          ? versions
          : [
              {
                value: componentData?.versionData?.pageVersionId,
                label: `v${componentData?.versionData?.pageVersion}`,
                extra: componentData?.versionData,
              },
            ]
      }
    />
  );
};
export default VersionSelect;
