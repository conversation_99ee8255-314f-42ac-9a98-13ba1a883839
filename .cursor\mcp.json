{
  "servers": {
    // "atlassian": {
    //   "type": "sse",
    //   "url": "http://localhost:9000/sse"
    // }
    "mcp-atlassian": {
      "command": "docker",
      "args": [
        "run",
        "-i",
        "--rm",
        "-e",
        "JIRA_URL",
        "-e",
        "JIRA_USERNAME",
        "-e",
        "JIRA_API_TOKEN",
        "ghcr.io/sooperset/mcp-atlassian:latest"
      ],
      "env": {
        "JIRA_URL": "https://allsocialpost.atlassian.net",
        "JIRA_USERNAME": "<EMAIL>",
        "JIRA_API_TOKEN": "ATATT3xFfGF02_Rlz66x1aAYjo0cwG-WfLT_6yXxALH2nJjt1XgNQLF00OhTIj-LuozokUgCbvn2Y2p2iAb5ep5FZT_uPqKYb1aLZpS40U8qu46Vu8lYgmmxaKw_Ivb6sTJoFJSVnp874lVRNfydTUT_3I0PpaIKAXfooKXpeLzz9wB1PcKtMiw=1CFC3337"
      },
      "autoApprove": [
        "jira_get_all_projects",
        "jira_create_issue",
        "jira_search"
      ]
    },
    "context7": {
      "autoApprove": ["resolve-library-id"],
      "disabled": false,
      "timeout": 60,
      "type": "stdio",
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp", "--api-key", "<Key>"]
    }
  }
}
