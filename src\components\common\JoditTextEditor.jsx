// import React, { useMemo, useRef, useCallback, useState } from "react";
// import Jo<PERSON><PERSON><PERSON><PERSON>, { Jo<PERSON> } from "jodit-react";
// // Import the CSS that matches your installed Jodit version:
// import "jodit/es2021/jodit.min.css"; // try this; if it 404s, use 'jodit/build/jodit.min.css'

// const JoditTextEditor = ({
//   value = "",
//   onChange = () => {},
//   onBlur = () => {},
//   onFocus,
//   className = "",
//   placeholder = "Enter your content here...",
//   readOnly = false,
//   height = 320, // number (not "320px")
//   config = {},
//   tabIndex,
//   key = "jodit-editor",
// }) => {
//   const editor = useRef(null);
//   const [isPopupOpen, setIsPopupOpen] = useState(false);
//   const blurTimeoutRef = useRef(null);

//   const stableOnChange = useCallback((html) => {
//     // html?.preventDefault();
//     onChange(html);
//   }, []);

//   // Enhanced onBlur handler that prevents triggering when popups are open
//   const handleBlur = useCallback(
//     (value) => {
//       // Clear any existing timeout
//       if (blurTimeoutRef.current) {
//         clearTimeout(blurTimeoutRef.current);
//       }

//       // Use a small delay to check if focus moved to a popup
//       blurTimeoutRef.current = setTimeout(() => {
//         // Check if any Jodit popup is currently open
//         const joditPopups = document.querySelectorAll(
//           ".jodit-popup, .jodit-dialog, .jodit-toolbar-popup"
//         );
//         const hasOpenPopups = Array.from(joditPopups).some(
//           (popup) =>
//             popup.style.display !== "none" && popup.offsetParent !== null
//         );

//         // Also check if the active element is within a Jodit popup
//         const activeElement = document.activeElement;
//         const isInPopup =
//           activeElement &&
//           (activeElement.closest(".jodit-popup") ||
//             activeElement.closest(".jodit-dialog") ||
//             activeElement.closest(".jodit-toolbar-popup"));

//         // Only trigger onBlur if no popups are open, focus is not in a popup, and our state shows no popup
//         if (!hasOpenPopups && !isInPopup && !isPopupOpen) {
//           onBlur(value);
//         }
//       }, 100); // Small delay to allow popup to render
//     },
//     [onBlur, isPopupOpen]
//   );

//   const defaultConfig = useMemo(
//     () => ({
//       disablePlugins: "add-new-line",
//       readonly: readOnly,
//       placeholder,
//       height: Number(height) || 320,
//       minHeight: 160,
//       // Keep toolbar fully visible (no adaptive collapsing)
//       toolbarAdaptive: false,
//       toolbarSticky: false,

//       // Show counters/statusbar only if you need them
//       statusbar: false,
//       showCharsCounter: false,
//       showWordsCounter: false,
//       showXPathInStatusbar: false,

//       // Paste behavior
//       enter: "P",
//       defaultActionOnPaste: "insert_clear_html",
//       askBeforePasteHTML: false,
//       askBeforePasteFromWord: false,
//       defaultActionOnPasteFromWord: "insert_clear_html",

//       // Enable built-in spellcheck button behavior
//       spellcheck: true, // native browser spellcheck; button toggles it

//       // Do NOT disable plugins if you want "everything"
//       disablePlugins: "", // e.g. "add-new-line,ordered-list" to hide some later :contentReference[oaicite:3]{index=3}

//       // Full toolbar set (playground-like)
//       buttons: [
//         // text styles
//         "fontsize",
//         "|",
//         "brush",
//         "|",
//         "bold",
//         "italic",
//         "underline",
//         "strikethrough",
//         // "eraser",
//         // "|",
//         // lists
//         "|",
//         "left",
//         "center",
//         "right",
//         // "justify",
//         "|",
//         "ul",
//         "ol",
//         "|",
//         "link",
//         "unlink",
//         "|",
//         "undo",
//         "redo",
//         // "|",
//         // typography
//         // "font",

//         // "paragraph",
//         // "lineHeight",
//         // "superscript",
//         // "subscript",
//         // "classSpan",
//         // "|",
//         // media & files
//         // "file",
//         // "image",
//         // "video",
//         // "|",
//         // tools
//         // "spellcheck",
//         // "speechRecognize",
//         // "|",
//         // "|",
//         // "link",
//         // "unlink",
//         // "|",
//         // "undo",
//         // "redo",
//         // "|",
//         // "fullsize",
//         // "preview",
//       ],

//       // Provide extra control config where needed
//       controls: {
//         lineHeight: {
//           // Customize the dropdown values
//           list: Jodit.atom([1, 1.15, 1.5, 1.75, 2, 2.5, 3]), // per docs pattern :contentReference[oaicite:4]{index=4}
//         },
//         classSpan: {
//           // Optional: your class presets to apply to selection
//           // Shown in the classSpan dropdown
//           list: Jodit.atom([
//             { name: "Note", value: "text-note" },
//             { name: "Warning", value: "text-warning" },
//             { name: "Muted", value: "text-muted" },
//           ]),
//         },
//       },

//       // Basic image handling (no server): inline as Base64
//       uploader: {
//         insertImageAsBase64URI: true,
//         imagesExtensions: ["jpg", "jpeg", "png", "gif", "svg", "webp"],
//         // If you have an upload API, configure `url` and handlers here.
//       },

//       // Keep things light
//       showPlaceholder: true,
//       theme: "default",
//       language: "en",

//       // Owner document/window for SSR safety
//       ownerDocument:
//         typeof window !== "undefined" ? window.document : undefined,
//       ownerWindow: typeof window !== "undefined" ? window : undefined,
//     }),
//     [readOnly, placeholder, height]
//   );

//   const finalConfig = useMemo(
//     () => ({
//       ...defaultConfig,
//       ...config,
//       readonly: readOnly,
//       placeholder,
//       height: Number(height) || 320,
//     }),
//     [defaultConfig, config, readOnly, placeholder, height]
//   );

//   // Enhanced popup detection with event listeners
//   React.useEffect(() => {
//     const handlePopupOpen = () => {
//       setIsPopupOpen(true);
//     };

//     const handlePopupClose = () => {
//       setIsPopupOpen(false);
//     };

//     // Listen for clicks on Jodit toolbar buttons that open popups
//     const handleToolbarClick = (event) => {
//       const target = event.target;
//       if (
//         target.closest('.jodit-toolbar-button[data-tooltip*="link"]') ||
//         target.closest('.jodit-toolbar-button[data-tooltip*="Link"]') ||
//         target.closest('[data-tooltip*="link"]') ||
//         target.closest('[data-tooltip*="Link"]')
//       ) {
//         setIsPopupOpen(true);
//       }
//     };

//     // Listen for focus events to detect when focus moves to popups
//     const handleFocusChange = () => {
//       const activeElement = document.activeElement;
//       if (
//         activeElement &&
//         (activeElement.closest(".jodit-popup") ||
//           activeElement.closest(".jodit-dialog") ||
//           activeElement.closest(".jodit-toolbar-popup"))
//       ) {
//         setIsPopupOpen(true);
//       } else {
//         // Check if any popups are still visible
//         const visiblePopups = document.querySelectorAll(
//           '.jodit-popup:not([style*="display: none"]), .jodit-dialog:not([style*="display: none"])'
//         );
//         if (visiblePopups.length === 0) {
//           setIsPopupOpen(false);
//         }
//       }
//     };

//     // Listen for Jodit popup events
//     document.addEventListener("jodit-popup-opened", handlePopupOpen);
//     document.addEventListener("jodit-popup-closed", handlePopupClose);
//     document.addEventListener("jodit-dialog-opened", handlePopupOpen);
//     document.addEventListener("jodit-dialog-closed", handlePopupClose);
//     document.addEventListener("click", handleToolbarClick);
//     document.addEventListener("focusin", handleFocusChange);
//     document.addEventListener("focusout", handleFocusChange);

//     // Also listen for DOM mutations to detect popup changes
//     const observer = new MutationObserver((mutations) => {
//       mutations.forEach((mutation) => {
//         if (mutation.type === "childList") {
//           const addedNodes = Array.from(mutation.addedNodes);
//           const removedNodes = Array.from(mutation.removedNodes);

//           // Check if Jodit popups were added or removed
//           const hasJoditPopups = [...addedNodes, ...removedNodes].some(
//             (node) =>
//               node.nodeType === Node.ELEMENT_NODE &&
//               (node.classList?.contains("jodit-popup") ||
//                 node.classList?.contains("jodit-dialog") ||
//                 node.querySelector?.(".jodit-popup, .jodit-dialog"))
//           );

//           if (hasJoditPopups) {
//             // Check current popup state
//             const currentPopups = document.querySelectorAll(
//               ".jodit-popup, .jodit-dialog"
//             );
//             const visiblePopups = Array.from(currentPopups).filter(
//               (popup) =>
//                 popup.style.display !== "none" && popup.offsetParent !== null
//             );
//             setIsPopupOpen(visiblePopups.length > 0);
//           }
//         }
//       });
//     });

//     // Start observing
//     observer.observe(document.body, {
//       childList: true,
//       subtree: true,
//     });

//     return () => {
//       if (blurTimeoutRef.current) {
//         clearTimeout(blurTimeoutRef.current);
//       }
//       document.removeEventListener("jodit-popup-opened", handlePopupOpen);
//       document.removeEventListener("jodit-popup-closed", handlePopupClose);
//       document.removeEventListener("jodit-dialog-opened", handlePopupOpen);
//       document.removeEventListener("jodit-dialog-closed", handlePopupClose);
//       document.removeEventListener("click", handleToolbarClick);
//       document.removeEventListener("focusin", handleFocusChange);
//       document.removeEventListener("focusout", handleFocusChange);
//       observer.disconnect();
//     };
//   }, []);

//   return (
//     <div className={`jodit-text-editor-wrapper ${className}`}>
//       <JoditEditor
//         id={`jodit-editor-${key}`}
//         key={`jodit-editor`}
//         ref={editor}
//         value={value}
//         config={finalConfig}
//         tabIndex={tabIndex}
//         onChange={stableOnChange}
//         onBlur={handleBlur}
//         onFocus={() => onFocus()}
//       />
//       {/* // border: 1px solid #d9d9d9;
//           // border-radius: 8px; */}
//       <style jsx>{`
//         .jodit-text-editor-wrapper .jodit-container {
//           background: #fff;
//           overflow: visible;
//           border-radius: 10px 10px 10px 10px;
//           border: 1px solid #d9dbdf;
//         }
//         .jodit-text-editor-wrapper .jodit-toolbar__box {
//           background: #f6f7f9;
//           border-radius: 10px 10px 0px 0px;
//           border-bottom: 1px solid #d9dbdf;
//         }
//         .jodit-text-editor-wrapper .jodit-workplace {
//           border-radius: 0px 0px 10px 10px;
//         }
//         .jodit-text-editor-wrapper .jodit-toolbar__box {
//           overflow: visible;
//         }
//         .jodit-text-editor-wrapper .jodit-wysiwyg {
//           padding: 12px;
//           font-size: 14px;
//           line-height: 1.6;
//         }
//         .jodit-add-new-line {
//           display: none !important;
//         }
//       `}</style>
//     </div>
//   );
// };

// export default JoditTextEditor;
import React, { useMemo, useRef, useCallback, useEffect } from "react";
import JoditEditor, { Jodit } from "jodit-react";
import "jodit/es2021/jodit.min.css";

const JoditTextEditor = ({
  value = "",
  onChange = () => {},
  onBlur = () => {},
  onFocus,
  className = "",
  placeholder = "Enter your content here...",
  readOnly = false,
  height = 320,
  config = {},
  tabIndex,
  key = "jodit-editor",
}) => {
  const editor = useRef(null);
  const wrapperRef = useRef(null);

  // Flag to suppress blur when interacting inside Jodit chrome (toolbars/popups)
  const suppressNextBlurRef = useRef(false);

  useEffect(() => {
    const markIfInternal = (evt) => {
      const t = evt.target;
      if (!t) return;

      // 1) Inside our wrapper?
      const inWrapper = wrapperRef.current?.contains(t);

      // 2) Or inside any Jodit popup (some popups render outside the wrapper/body)
      //    We check up the DOM tree for a popup class.
      const inJoditPopup = !!(
        t.closest &&
        (t.closest(".jodit-ui-popup") ||
          t.closest(".jodit-popup") ||
          t.closest(".jodit-context-menu"))
      );

      if (inWrapper || inJoditPopup) {
        suppressNextBlurRef.current = true;
        // Reset the flag shortly after to only suppress the very next blur
        setTimeout(() => {
          suppressNextBlurRef.current = false;
        }, 0);
      }
    };

    // Use pointerdown (better than mousedown on touch/pen)
    document.addEventListener("pointerdown", markIfInternal, true);
    return () => {
      document.removeEventListener("pointerdown", markIfInternal, true);
    };
  }, []);

  const stableOnChange = useCallback(
    (html) => {
      onChange(html);
    },
    [onChange]
  );

  const defaultConfig = useMemo(
    () => ({
      disablePlugins: "add-new-line",
      readonly: readOnly,
      placeholder,
      height: Number(height) || 320,
      minHeight: 160,
      toolbarAdaptive: false,
      toolbarSticky: false,
      statusbar: false,
      showCharsCounter: false,
      showWordsCounter: false,
      showXPathInStatusbar: false,
      enter: "P",
      defaultActionOnPaste: "insert_clear_html",
      askBeforePasteHTML: false,
      askBeforePasteFromWord: false,
      defaultActionOnPasteFromWord: "insert_clear_html",
      spellcheck: true,
      disablePlugins: "",
      buttons: [
        "fontsize",
        "|",
        "brush",
        "|",
        "bold",
        "italic",
        "underline",
        "strikethrough",
        "|",
        "left",
        "center",
        "right",
        "|",
        "ul",
        "ol",
        "|",
        "link",
        "unlink",
        "|",
        "undo",
        "redo",
      ],
      controls: {
        lineHeight: {
          list: Jodit.atom([1, 1.15, 1.5, 1.75, 2, 2.5, 3]),
        },
        classSpan: {
          list: Jodit.atom([
            { name: "Note", value: "text-note" },
            { name: "Warning", value: "text-warning" },
            { name: "Muted", value: "text-muted" },
          ]),
        },
      },
      uploader: {
        insertImageAsBase64URI: true,
        imagesExtensions: ["jpg", "jpeg", "png", "gif", "svg", "webp"],
      },
      showPlaceholder: true,
      theme: "default",
      language: "en",
      ownerDocument:
        typeof window !== "undefined" ? window.document : undefined,
      ownerWindow: typeof window !== "undefined" ? window : undefined,

      // (Optional) Jodit native events — if you want the real DOM blur event with relatedTarget:
      events: {
        blur: (e) => {
          // If you ever want to gate *Jodit’s* blur directly:
          // if (suppressNextBlurRef.current) e?.stopImmediatePropagation?.();
        },
      },
      // iframe: true,
      //     iframeStyle: `
      // html,body{font-family:inherit;font-size:14px;line-height:1.6;padding:4px;margin:0;}
      //   ul{list-style:disc;padding-left:1.5rem;margin:0.5rem 0;}
      //   ol{list-style:decimal;padding-left:1.5rem;margin:0.5rem 0;}
      //   li{display:list-item;margin:0.25rem 0;}
      // `,
    }),
    [readOnly, placeholder, height]
  );

  const finalConfig = useMemo(
    () => ({
      ...defaultConfig,
      ...config,
      readonly: readOnly,
      placeholder,
      height: Number(height) || 320,
    }),
    [defaultConfig, config, readOnly, placeholder, height]
  );

  // Wrapped onBlur: ignore when still inside Jodit chrome
  const handleBlur = useCallback(
    (valOrEvent) => {
      if (suppressNextBlurRef.current) {
        // swallow this blur — user clicked a Jodit popup/button
        return;
      }

      // Extra safety: if focus moved to an element still inside our wrapper, ignore.
      const active = document.activeElement;
      if (active && wrapperRef.current?.contains(active)) {
        return;
      }

      onBlur(valOrEvent);
    },
    [onBlur]
  );

  return (
    <div ref={wrapperRef} className={`jodit-text-editor-wrapper ${className}`}>
      <JoditEditor
        id={`jodit-editor-${key}`}
        key={`jodit-editor`}
        ref={editor}
        value={value}
        config={finalConfig}
        tabIndex={tabIndex}
        onChange={stableOnChange}
        onBlur={handleBlur}
        onFocus={() => onFocus && onFocus()}
      />
      <style jsx>{`
        .jodit-text-editor-wrapper .jodit-container {
          background: #fff;
          overflow: visible;
          border-radius: 10px;
          border: 1px solid #d9dbdf;
        }
        .jodit-text-editor-wrapper .jodit-toolbar__box {
          background: #f6f7f9;
          border-radius: 10px 10px 0 0;
          border-bottom: 1px solid #d9dbdf;
        }
        .jodit-text-editor-wrapper .jodit-workplace {
          border-radius: 0 0 10px 10px;
        }
        .jodit-text-editor-wrapper .jodit-toolbar__box {
          overflow: visible;
        }
        .jodit-text-editor-wrapper .jodit-wysiwyg {
          padding: 12px;
          font-size: 14px;
          line-height: 1.6;
        }
        .jodit-add-new-line {
          display: none !important;
        }
        /* ✅ Force list markers inside the editor */
        .jodit-text-editor-wrapper .jodit-wysiwyg ul {
          list-style: disc;
          padding-left: 1.5rem;
          margin: 0.5rem 0 0.5rem 0;
        }
        .jodit-text-editor-wrapper .jodit-wysiwyg ol {
          list-style: decimal;
          padding-left: 1.5rem;
          margin: 0.5rem 0 0.5rem 0;
        }
        .jodit-text-editor-wrapper .jodit-wysiwyg li {
          display: list-item; /* in case a reset set this to block */
          margin: 0.25rem 0;
        }

        /* Hide the extra “add new line” handle */
        .jodit-add-new-line {
          display: none !important;
        }
      `}</style>
    </div>
  );
};

export default JoditTextEditor;
