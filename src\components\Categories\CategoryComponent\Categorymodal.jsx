import {
  Button,
  ColorPicker,
  Divider,
  Form,
  Input,
  InputNumber,
  Modal,
  Space,
  Tooltip,
  Typography,
} from "antd";
import { Palette } from "lucide-react";
import { useRef } from "react";
import { generateSlug } from "../../../util/storage/jsonStorage";

const { Text } = Typography;
const { TextArea } = Input;

const Categorymodal = ({
  showForm,
  setShowForm,
  editingCategory,
  setEditingCategory,
  saving,
  setSaving,
  formData,
  setFormData,
  handleSubmit,
  predefinedColors,
  form,
  resetForm,
}) => {
  const colorPickerRef = useRef(null);

  const handleColorCircleClick = (color) => {
    setFormData({ ...formData, colour: color });
    form.setFieldValue("colour", color);

    // Open the color picker popup
    // if (colorPickerRef.current) {
    //   colorPickerRef.current.focus();
    //   // Trigger click to open the popup
    //   setTimeout(() => {
    //     const colorPickerElement = colorPickerRef.current?.querySelector(
    //       ".ant-color-picker-trigger"
    //     );
    //     if (colorPickerElement) {
    //       colorPickerElement.click();
    //     }
    //   }, 100);
    // }
  };

  // const handleCustomColorClick = () => {
  //   // Open the color picker popup for custom color selection
  //   if (colorPickerRef.current) {
  //     colorPickerRef.current.focus();
  //     setTimeout(() => {
  //       const colorPickerElement = colorPickerRef.current?.querySelector(
  //         ".ant-color-picker-trigger"
  //       );
  //       if (colorPickerElement) {
  //         colorPickerElement.click();
  //       }
  //     }, 100);
  //   }
  // };
  return (
    <>
      <Modal
        title={
          <Space>
            {editingCategory ? "Edit Category" : "Add New Category"}
          </Space>
        }
        open={showForm}
        onCancel={resetForm}
        footer={null}
        width={550}
        className=""
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={formData}
          onFinish={handleSubmit}
          className="tw-mt-6"
          size="large"
          requiredMark={false}
        >
          <Form.Item
            name="name"
            label={
              <span className="tw-text-sm tw-font-medium tw-text-font-color">
                Category Name
              </span>
            }
            required
            rules={[
              {
                required: true,
                message: "Please enter a category name",
              },
              {
                min: 2,
                message: "Category name must be at least 2 characters",
              },
              {
                max: 100,
                message: "Category name must be less than 100 characters",
              },
            ]}
          >
            <Input
              placeholder="e.g., Headers, Content, Forms"
              className="tw-rounded-lg"
            />
          </Form.Item>

          <Form.Item
            name="description"
            label={
              <span className="tw-text-sm tw-font-medium tw-text-font-color">
                Description
              </span>
            }
          >
            <TextArea
              placeholder="Brief description of this category"
              rows={4}
              className="tw-rounded-lg"
            />
          </Form.Item>

          <div className="tw-mb-6">
            <Text className="tw-text-sm tw-font-medium tw-text-font-color tw-block tw-mb-3">
              Color
            </Text>
            <div className="tw-flex tw-flex-wrap tw-gap-2 tw-mb-3">
              {/* Custom Color Picker Circle */}
              {/* <Tooltip title="Choose custom color">
                <div
                  className={`tw-w-10 tw-h-10 tw-p-[3px] tw-border-solid tw-border-2 tw-rounded-full tw-border-gray-300`}
                >
                  <button
                    type="button"
                    onClick={handleCustomColorClick}
                    className="tw-w-full tw-h-full tw-rounded-full tw-transition-all tw-cursor-pointer hover:tw-scale-110 tw-bg-gradient-to-br tw-from-gray-100 tw-to-gray-200 hover:tw-from-gray-200 hover:tw-to-gray-300 tw-flex tw-items-center tw-justify-center tw-border-0"
                  >
                    <Palette className="tw-w-4 tw-h-4 tw-text-gray-600" />
                  </button>
                </div>
              </Tooltip> */}

              {/* Predefined Color Circles */}
              {predefinedColors?.map((color) => (
                <Tooltip key={color} title={`Select ${color}`}>
                  <div
                    className={`tw-w-10 tw-h-10 tw-p-[3px] tw-border-solid tw-border-2 tw-rounded-full tw-transition-all`}
                    style={{
                      borderColor:
                        formData?.colour === color ? color : "#e5e7eb",
                      boxShadow:
                        formData?.colour === color
                          ? `0 0 0 2px ${color}33`
                          : "none",
                    }}
                  >
                    <button
                      type="button"
                      onClick={() => handleColorCircleClick(color)}
                      className="tw-w-full tw-h-full tw-rounded-full tw-transition-all tw-cursor-pointer hover:tw-scale-110 tw-border-0 tw-shadow-sm hover:tw-shadow-md"
                      style={{ backgroundColor: color }}
                    />
                  </div>
                </Tooltip>
              ))}
            </div>
            <Form.Item name="colour">
              <div ref={colorPickerRef}>
                <ColorPicker
                  className="custom-color-picker tw-w-full"
                  value={formData?.colour}
                  onChange={(color) => {
                    const hexColor = color?.toHexString();
                    setFormData({
                      ...formData,
                      colour: hexColor,
                    });
                    // Also update the form field value
                    form.setFieldValue("colour", hexColor);
                  }}
                  size="large"
                  format="hex"
                  // placement="topLeft"
                  // getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  // showText
                />
              </div>
            </Form.Item>
          </div>

          <Divider className="tw-my-6" />

          <div className="tw-flex tw-justify-center tw-gap-3">
            <Button
              type="primary"
              size="large"
              htmlType="submit"
              loading={saving}
              // icon={saving ? null : <SaveOutlined />}
              className="tw-w-full tw-px-6 tw-h-12 tw-rounded-lg tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-border-0 hover:tw-from-blue-700 hover:tw-to-purple-700"
            >
              {saving ? "Saving..." : editingCategory ? "Update" : "Create"}
            </Button>
            <Button
              size="large"
              onClick={resetForm}
              className="tw-w-full tw-px-6 tw-h-12 tw-rounded-lg"
            >
              Cancel
            </Button>
          </div>
        </Form>
      </Modal>
    </>
  );
};

export default Categorymodal;
