import React, { useState, useEffect } from "react";
import { Result, <PERSON><PERSON>, <PERSON>, Typography, Progress } from "antd";
import { WifiOutlined, ReloadOutlined, HomeOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import { useInternet } from "../../contexts/InternetContext";
import useInternetConnection from "../../hooks/useInternetConnection";

const { Title, Paragraph } = Typography;

const InternetLossPage = () => {
  const navigate = useNavigate();
  const { isOnline, connectionQuality, isRetrying, getConnectionStats } =
    useInternet();
  const { manualRetry, getConnectionStatus, canRetry } = useInternetConnection({
    redirectOnOffline: false, // Don't redirect from this page
    autoRetry: false, // Manual retry only
  });

  const [retryCount, setRetryCount] = useState(0);
  const connectionStatus = getConnectionStatus();
  const stats = getConnectionStats();

  const handleRetry = async () => {
    const success = await manualRetry();
    if (success) {
      // Connection restored, go back to previous page
      navigate(-1);
    } else {
      setRetryCount((prev) => prev + 1);
    }
  };

  const handleGoHome = () => {
    navigate("/");
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  // Auto-redirect when connection is restored
  useEffect(() => {
    if (isOnline) {
      // Small delay to show the connection restored state
      setTimeout(() => {
        navigate(-1); // Go back to previous page
      }, 1000);
    }
  }, [isOnline, navigate]);

  return (
    <div className="tw-min-h-screen tw-flex tw-items-center tw-justify-center tw-bg-gradient-to-br tw-from-red-50 tw-to-orange-100">
      <div className="tw-max-w-2xl tw-w-full tw-mx-4">
        <Card className="tw-shadow-lg tw-border-0">
          <Result
            icon={<WifiOutlined className="tw-text-6xl tw-text-red-500" />}
            title="No Internet Connection"
            subTitle="Please check your internet connection and try again."
            extra={[
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={handleRetry}
                loading={isRetrying}
                disabled={!canRetry}
                className="tw-mr-2"
                key="retry"
              >
                {isRetrying ? "Checking..." : "Retry Connection"}
              </Button>,
              <Button
                icon={<HomeOutlined />}
                onClick={handleGoHome}
                className="tw-mr-2"
                key="home"
              >
                Go Home
              </Button>,
              <Button
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                key="refresh"
              >
                Refresh Page
              </Button>,
            ]}
          />

          <div className="tw-mt-6 tw-text-center">
            <Title level={4} className="tw-text-gray-600">
              Connection Status
            </Title>
            <div className="tw-mb-4">
              <Progress
                percent={isOnline ? 100 : 0}
                status={isOnline ? "success" : "exception"}
                strokeColor={isOnline ? "#52c41a" : "#ff4d4f"}
              />
            </div>
            <Paragraph className="tw-text-gray-500">
              Retry attempts: {connectionStatus.retryCount}
            </Paragraph>
            <Paragraph className="tw-text-gray-500">
              Connection Quality: {connectionQuality}
            </Paragraph>
            <Paragraph className="tw-text-sm tw-text-gray-400">
              • Check your WiFi or mobile data connection
              <br />
              • Try moving to a different location
              <br />• Contact your internet service provider if the problem
              persists
            </Paragraph>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default InternetLossPage;
