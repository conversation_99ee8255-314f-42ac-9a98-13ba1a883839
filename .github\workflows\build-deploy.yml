name: Build

on:
  push:
    branches: [ staging ]

jobs:
  # Job 1: Create build from staging branch
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout staging branch
        uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22.x'
          cache: 'npm'
      - name: Generate .env for staging
        run: |
          cat > .env <<'EOF'
          VITE_API_URL=${{ secrets.STAGING_VITE_API_URL }}
          VITE_STORAGE_MODE=${{ secrets.STAGING_VITE_STORAGE_MODE }}
          VITE_LAYOUT_MODE=${{ secrets.STAGING_VITE_LAYOUT_MODE }}
          EOF
          echo "Created .env with keys:" && grep '^VITE_' .env || true
      - name: Install dependencies
        run: npm i
      - name: Create build
        run: npm run build
      - name: Upload build folder
        uses: actions/upload-artifact@v4
        with:
          name: dist
          path: dist
          retention-days: 1

  # Job 2: Upload dist folder to Utho server
  upload-to-utho:
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Download dist folder
        uses: actions/download-artifact@v4
        with:
          name: dist
          path: dist/
      - name: Upload to Utho server
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.UTHO_HOST }}
          username: ${{ secrets.UTHO_USERNAME }}
          key: ${{ secrets.UTHO_SSH_KEY }}
          port: ${{ secrets.UTHO_PORT }}
          source: dist
          target: ${{ secrets.STAGING_DEPLOY_PATH }}

  # Job 3: Move build folder and restart PM2
  deploy-and-restart:
    runs-on: ubuntu-latest
    needs: upload-to-utho
    steps:
      - name: Move build folder and restart PM2
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.UTHO_HOST }}
          username: ${{ secrets.UTHO_USERNAME }}
          key: ${{ secrets.UTHO_SSH_KEY }}
          port: ${{ secrets.UTHO_PORT }}
          script: |
            APP_PATH="${{ secrets.STAGING_DEPLOY_PATH }}"
            echo "📁 Using application path: $APP_PATH"
            cd "$APP_PATH" || { echo "❌ Cannot cd into $APP_PATH"; exit 1; }
            if [ ! -d dist ]; then
              echo "❌ dist folder not found in $APP_PATH";
              ls -la;
              exit 1;
            fi
            echo "✅ dist folder present. Listing contents:"
            ls -la dist | head -50
            echo "🔄 Restarting PM2 process: ${{ secrets.STAGING_PM2_PROCESS_NAME }}"
            pm2 restart "${{ secrets.STAGING_PM2_PROCESS_NAME }}"
            echo "✅ PM2 process restarted successfully"
            echo "📊 Final PM2 status:"
            pm2 status
            echo "✅ Deployment completed successfully"