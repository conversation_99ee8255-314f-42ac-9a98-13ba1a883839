import React, { useState, useEffect } from "react";
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Button,
  Space,
  Spin,
  List,
} from "antd";
import { RocketOutlined, TrophyOutlined } from "@ant-design/icons";
import useHttp from "../../hooks/use-http";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { quickActions, recentActivity, statCards } from "../../util/content";

const { Text } = Typography;

const Dashboard = () => {
  const api = useHttp();
  const [stats, setStats] = useState({
    websites: 0,
    components: 0,
    pages: 0,
    templates: 0,
  });
  // const [recentActivity, setRecentActivity] = useState([]);

  useEffect(() => {
    // Fetch websites
    // api.sendRequest(CONSTANTS.API.websites.get, (websites) => {
    //   setStats((prev) => ({ ...prev, websites: websites.length || 0 }));
    // });
    // // Fetch components
    // api.sendRequest(CONSTANTS.API.components.get, (components) => {
    //   setStats((prev) => ({ ...prev, components: components.length || 0 }));
    // });
    // // Fetch pages
    // api.sendRequest(CONSTANTS.API.pages.get, (pages) => {
    //   setStats((prev) => ({ ...prev, pages: pages.length || 0 }));
    // });
    // // Fetch templates
    // api.sendRequest(CONSTANTS.API.templates.get, (templates) => {
    //   setStats((prev) => ({ ...prev, templates: templates.length || 0 }));
    // });
  }, []);

  if (api.isLoading) {
    return (
      <div className="tw-flex tw-justify-center tw-items-center tw-h-96">
        <Spin size="large" tip="Loading dashboard data..." />
      </div>
    );
  }

  return (
    <div className="tw-p-6 tw-bg-gray-50 tw-min-h-screen">
      <div className="tw-max-w-7xl tw-mx-auto">
        {/* Welcome Header */}
        {/* <div className="tw-mb-8">
          <Title level={2} className="!tw-mb-2">
            Welcome back, {user?.username || "User"}! 👋
          </Title>
          <Text type="secondary" className="tw-text-lg">
            Here's what's happening with your website builder today.
          </Text>
          <Divider className="tw-my-6" />
        </div> */}

        {/* Stats Grid */}
        <Row gutter={[24, 24]} className="tw-mb-8">
          {statCards?.map((stat) => (
            <Col xs={24} sm={12} lg={6} key={stat.name}>
              <Card
                className="tw-h-full tw-shadow-sm hover:tw-shadow-lg tw-transition-all tw-duration-300 tw-border-0 tw-rounded-2xl"
                styles={{
                  body: {
                    padding: "24px",
                  },
                }}
              >
                <div className="tw-flex tw-items-center tw-justify-between">
                  <div>
                    <Statistic
                      title={
                        <Text className="tw-text-sm tw-font-medium tw-text-gray-600">
                          {stat.name}
                        </Text>
                      }
                      value={stats?.[stat?.key] || 0}
                      valueStyle={{
                        color: "#1f2937",
                        fontSize: "2rem",
                        fontWeight: "bold",
                      }}
                    />
                  </div>
                  <div className={`tw-p-3 tw-rounded-xl ${stat.bgColor}`}>
                    <stat.icon className={`tw-w-6 tw-h-6 ${stat.iconColor}`} />
                  </div>
                </div>
              </Card>
            </Col>
          ))}
        </Row>

        {/* Main Content Grid */}
        <Row gutter={[24, 24]}>
          {/* Quick Actions */}
          <Col xs={24} lg={12}>
            <Card
              title={
                <Space>
                  <RocketOutlined className="tw-text-blue-600" />
                  <span className="tw-text-lg tw-font-semibold">
                    Quick Action
                  </span>
                </Space>
              }
              className="tw-h-full tw-shadow-sm hover:tw-shadow-lg tw-transition-all tw-duration-300 tw-border-0 tw-rounded-2xl"
              styles={{
                body: {
                  padding: "24px",
                },
              }}
            >
              <Space direction="vertical" size="middle" className="tw-w-full">
                {quickActions?.map((action) => (
                  <Button
                    key={action.name}
                    type="text"
                    size="large"
                    href={action.href}
                    className="tw-w-full tw-h-auto tw-p-4 tw-text-left tw-border tw-border-gray-200 hover:tw-border-blue-300 hover:tw-shadow-sm tw-transition-all tw-rounded-xl"
                    style={{ height: "auto" }}
                  >
                    <div className="tw-flex tw-items-center tw-w-full">
                      <div
                        className={`tw-p-3 tw-rounded-xl tw-bg-gradient-to-r ${action.color} tw-text-white tw-mr-4`}
                      >
                        <action.icon className="tw-w-5 tw-h-5" />
                      </div>
                      <div className="tw-flex-1 tw-text-left">
                        <div className="tw-font-medium tw-text-gray-900 tw-text-base">
                          {action.name}
                        </div>
                        <div className="tw-text-sm tw-text-gray-500 tw-mt-1">
                          {action.description}
                        </div>
                      </div>
                    </div>
                  </Button>
                ))}
              </Space>
            </Card>
          </Col>

          {/* Getting Started Guide */}
          <Col xs={24} lg={12}>
            <Card
              title={
                <Space>
                  <TrophyOutlined className="tw-text-green-600" />
                  <span className="tw-text-lg tw-font-semibold">
                    Getting Started
                  </span>
                </Space>
              }
              className="tw-h-full tw-shadow-sm hover:tw-shadow-lg tw-transition-all tw-duration-300 tw-border-0 tw-rounded-2xl"
              styles={{
                body: {
                  padding: "24px",
                },
              }}
            >
              <List
                dataSource={recentActivity}
                renderItem={(step, index) => (
                  <List.Item className="tw-border-0 tw-py-3">
                    <div className="tw-flex tw-items-start tw-space-x-4 tw-w-full">
                      <div
                        className="tw-flex tw-items-center tw-justify-center tw-w-8 tw-h-8 tw-rounded-full tw-text-white tw-font-bold tw-text-sm tw-flex-shrink-0"
                        style={{
                          backgroundColor: getStepColor(step.color),
                        }}
                      >
                        {step.id}
                      </div>
                      <div className="tw-flex-1 tw-min-w-0">
                        <div className="tw-font-medium tw-text-gray-900 tw-text-base tw-mb-1">
                          {step.label}
                        </div>
                        <div className="tw-text-sm tw-text-gray-500 tw-leading-relaxed">
                          {step.description}
                        </div>
                      </div>
                    </div>
                  </List.Item>
                )}
              />
            </Card>
          </Col>
        </Row>

        {/* Additional Stats Section */}
        {/* <Row gutter={[24, 24]} className="tw-mt-8">
          <Col xs={24}>
            <Card
              title={
                <Space>
                  <Activity className="tw-w-5 tw-h-5 tw-text-purple-600" />
                  <span className="tw-text-lg tw-font-semibold">
                    Recent Activity
                  </span>
                </Space>
              }
              className="tw-shadow-sm hover:tw-shadow-lg tw-transition-all tw-duration-300 tw-border-0 tw-rounded-2xl"
              styles={{
                body: {
                  padding: "24px",
                },
              }}
            >
              <div className="tw-text-center tw-py-8">
                <ClockCircleOutlined className="tw-text-4xl tw-text-gray-400 tw-mb-4" />
                <Text type="secondary" className="tw-text-lg">
                  No recent activity yet. Start building to see your progress
                  here!
                </Text>
              </div>
            </Card>
          </Col>
        </Row> */}
      </div>
    </div>
  );
};

// Helper function to get step colors
const getStepColor = (color) => {
  const colorMap = {
    blue: "#3B82F6",
    purple: "#8B5CF6",
    green: "#10B981",
    orange: "#F59E0B",
    red: "#EF4444",
  };
  return colorMap[color] || "#6B7280";
};

export default Dashboard;
