import { <PERSON>, Col, Radio, Row, Tag } from "antd";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Palette } from "lucide-react";
import React, { useEffect, useState } from "react";
import EditorSnippet from "../../common/EditorSnippet";
import {
  cssPlaceholder,
  htmlPlaceholder,
  jsPlaceholder,
  tabList,
} from "../content";
import PreviewTab from "./PreviewTab";
import {
  autoDetectPlaceholders,
  autoDetectRepeatPlaceHolder,
} from "../../../util/functions";
import SearchBar from "../../common/SearchBar";

const TabList = ({
  formData,
  setFormData,
  globalVarible,
  onValidationStateChange,
  onValidationChange,
}) => {
  // const [placeholderInput, setPlaceholderInput] = useState("");
  const [previewMode, setPreviewMode] = useState("code");
  const [searchTerm, setSearchTerm] = useState("");
  const [validationStates, setValidationStates] = useState({
    html: { isValid: true, hasErrors: false, errorCount: 0 },
    css: { isValid: true, hasErrors: false, errorCount: 0 },
    js: { isValid: true, hasErrors: false, errorCount: 0 },
  });

  const tabContents = {
    code: {
      key: "html",
      type: "html",
      textareaId: "html-editor",
      placeholder: htmlPlaceholder,
      label: "HTML",
      icon: <Code className="tw-w-4 tw-h-4 tw-text-orange-600 tw-mr-2" />,
    },
    css: {
      key: "css",
      type: "css",
      textareaId: "css-editor",
      placeholder: cssPlaceholder,
      label: "CSS",
      icon: <Palette className="tw-w-4 tw-h-4 tw-text-blue-600 tw-mr-2" />,
    },
    javascript: {
      key: "js",
      type: "js",
      textareaId: "js-editor",
      placeholder: jsPlaceholder,
      label: "JavaScript",
      icon: <Braces className="tw-w-4 tw-h-4 tw-text-yellow-600 tw-mr-2" />,
    },
    preview: {
      key: "preview",
      // label: "Live Preview",
      icon: <Eye className="tw-w-4 tw-h-4 tw-text-green-600 tw-mr-2" />,
      content: <PreviewTab formData={formData} />,
    },
  };

  useEffect(() => {
    const timeout = setTimeout(() => {
      // if (formData?.html) {
      const placeholders = autoDetectPlaceholders(formData?.html);
      const repeatedPlaceholder = autoDetectRepeatPlaceHolder(formData?.html);
      setFormData({
        ...formData,
        placeholders,
        repeatedPlaceholder,
      });
      // }
    }, 1000);

    return () => clearTimeout(timeout);
  }, [formData?.html]);

  const handleSearch = (value) => {
    setSearchTerm(value);
  };

  const handleOnValidationChange = (marker) => {};

  // Handle validation state changes from individual editors
  const handleValidationStateChange = (editorType, validationState) => {
    console.log(editorType, validationState);
    setValidationStates((prev) => {
      const newStates = {
        ...prev,
        [editorType]: validationState,
      };

      // Calculate overall validation state
      const hasAnyErrors = Object.values(newStates).some(
        (state) => state.hasErrors
      );
      const totalErrorCount = Object.values(newStates).reduce(
        (sum, state) => sum + state.errorCount,
        0
      );
      const isOverallValid = Object.values(newStates).every(
        (state) => state.isValid
      );

      // Notify parent component about overall validation state
      if (onValidationStateChange) {
        onValidationStateChange({
          isValid: isOverallValid,
          hasErrors: hasAnyErrors,
          errorCount: totalErrorCount,
          editorStates: newStates,
        });
      }

      return newStates;
    });
  };

  return (
    <>
      {/* Right Panel - Code and Preview */}
      <div className="tw-space-y-4">
        {/* Preview/Code Toggle */}
        <div className="tw-bg-white tw-rounded-xl tw-shadow-sm tw-border tw-border-gray-200 tw-p-4">
          <Radio.Group
            value={previewMode}
            onChange={(e) => setPreviewMode(e.target.value)}
            buttonStyle="solid"
            size="large"
            className="component-tab-list tw-w-full tw-p-[2px] tw-border tw-border-[#2563EB] tw-rounded-[10px]"
          >
            {tabList.map((tab) => (
              <Radio.Button
                key={tab.key}
                value={tab.key}
                className="tw-flex-1 tw-text-center !tw-rounded-[10px] before:!tw-w-0 tw-border-0 border-b-"
                style={{ width: "25%" }}
              >
                <div className="tw-flex tw-items-center tw-justify-center">
                  {tab.icon}
                  {tab?.tab}
                </div>
              </Radio.Button>
            ))}
          </Radio.Group>
        </div>
        <div className="tw-space-y-4">
          <Card className="tw-shadow-sm">
            {/* <div className="tw-flex tw-items-center tw-mb-4">
             
              <span className="tw-text-sm tw-font-semibold tw-text-gray-900">
                {tabContents[previewMode]?.label}
              </span>
            </div> */}

            {/* Fixed height content area to match PreviewTab */}
            <div className=" tw-flex tw-flex-col">
              <Row gutter={[16, 0]}>
                <Col span={previewMode == "code" ? 15 : 24}>
                  {tabContents[previewMode]?.label ? (
                    <div className="tw-flex tw-items-center tw-mb-4">
                      {/* {tabContents[previewMode].icon} */}
                      <span className="tw-text-base tw-font-semibold tw-text-gray-900">
                        {tabContents[previewMode]?.label}
                      </span>
                    </div>
                  ) : (
                    ""
                  )}
                  {tabContents[previewMode].content ? (
                    tabContents[previewMode].content
                  ) : (
                    <div className="tw-flex-1 tw-border tw-border-gray-300 tw-rounded-lg tw-overflow-hidden tw-mb-4">
                      <EditorSnippet
                        type={tabContents[previewMode].type}
                        defaultValue={
                          formData?.[tabContents?.[previewMode]?.key]
                        }
                        onValueChange={(code) => {
                          setFormData({
                            ...formData,
                            [tabContents[previewMode].key]: code,
                          });
                        }}
                        value={formData?.[tabContents[previewMode]?.key]}
                        placeholder={tabContents?.[previewMode]?.placeholder}
                        textareaId={tabContents?.[previewMode]?.textareaId}
                        onValidationChange={(markers, type) => {
                          onValidationChange((pr) => {
                            return {
                              ...pr,
                              [type]: markers,
                            };
                          });
                        }}
                        onValidationStateChange={(validationState) =>
                          handleValidationStateChange(
                            tabContents[previewMode].key,
                            validationState
                          )
                        }
                      />
                    </div>
                  )}
                </Col>
                {previewMode == "code" && (
                  <Col span={9} className="tw-h-full">
                    <div className="tw-flex tw-items-center tw-mb-4">
                      <span className="tw-text-base tw-font-semibold tw-text-gray-900">
                        Global Variables
                      </span>
                    </div>
                    <SearchBar
                      type="page"
                      handleSearch={(e) => handleSearch(e)}
                    />
                    <div className="tw-flex tw-flex-wrap tw-gap-1 tw-gap-y-2 tw-mt-2  tw-overflow-y-auto">
                      {globalVarible?.length > 0 ? (
                        globalVarible
                          ?.filter((placeholder) =>
                            placeholder
                              ?.toLowerCase()
                              ?.includes(searchTerm?.toLowerCase())
                          )
                          ?.map((placeholder, index) => (
                            <Tag
                              className="tw-rounded-full"
                              color="purple"
                              key={index}
                            >
                              ${placeholder}
                            </Tag>
                          ))
                      ) : (
                        <span className="tw-text-xs tw-text-gray-500 tw-italic">
                          No placeholders detected
                        </span>
                      )}
                    </div>
                  </Col>
                )}
              </Row>
              {/* </div> */}

              {/* Placeholders section - fixed at bottom */}
              {previewMode == "code" ? (
                <div className="tw-border-t tw-pt-4 tw-mt-auto">
                  <div className="tw-w-full tw-flex tw-items-center">
                    <p className="tw-mb-[2px] tw-text-sm tw-font-medium tw-text-gray-700">
                      Placeholders:
                    </p>
                  </div>
                  <div className="tw-flex tw-flex-wrap tw-gap-1 tw-mt-2 tw-max-h-20 tw-overflow-y-auto">
                    {formData?.placeholders?.length > 0 ? (
                      formData?.placeholders?.map((placeholder, index) => (
                        <Tag
                          className="tw-rounded-full"
                          color="blue"
                          key={index}
                        >
                          ${placeholder}
                        </Tag>
                      ))
                    ) : (
                      <span className="tw-text-xs tw-text-gray-500 tw-italic">
                        No placeholders detected
                      </span>
                    )}
                  </div>
                </div>
              ) : (
                <></>
              )}
            </div>
          </Card>
        </div>
      </div>
    </>
  );
};

export default TabList;
