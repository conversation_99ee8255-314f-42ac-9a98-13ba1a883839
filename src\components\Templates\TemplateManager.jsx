import React, { useState, useCallback } from "react";
import { useNavigate, useLocation, Outlet } from "react-router-dom";
import {
  Plus,
  Layout,
  Edit2,
  Trash2,
  Eye,
  Search,
  Copy,
  Loader2,
  CopyPlus,
  <PERSON>cil,
  Filter,
} from "lucide-react";
// import useHttp from "../../hooks/use-http"; // Commented for future API use
import useStorage from "../../hooks/use-storage"; // Using JSON storage
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { apiGenerator } from "../../util/functions";
import { useAuth } from "../../contexts/AuthContext";
import {
  But<PERSON>,
  Card,
  Popconfirm,
  Tag,
  Tooltip,
  Typography,
  Select,
  message,
} from "antd";
import useDebounce from "../../hooks/useDebounce";
import SearchBar from "../common/SearchBar";
import ScrollPagination from "../common/ScrollPagination";
import { getSortParams, templateFilterOptions } from "../../util/content";
import useHttp from "../../hooks/use-http";

const { Title, Text, Paragraph } = Typography;

const TemplateManager = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const isEditorRoute =
    location.pathname.startsWith("/templates/") &&
    location.pathname !== "/templates";
  const [searchTerm, setSearchTerm] = useState("");
  const [filterOption, setFilterOption] = useState("recently_modified");
  const [updatedTemplate, setUpdatedTemplate] = useState(null);
  const [operationLoading, setOperationLoading] = useState({});
  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  const api = useHttp();
  // API-based load function for ScrollPagination
  const loadTemplates = useCallback(
    async ({ page, pageSize }) => {
      try {
        const sortParams = getSortParams(filterOption);
        const payload = {
          page,
          limit: pageSize,
          sort: sortParams?.sort,
          sortBy: sortParams?.sortBy,
          search: debouncedSearchTerm || "",
        };

        return new Promise((resolve, reject) => {
          api.sendRequest(
            CONSTANTS.API.templates.get,
            (res) => {
              // Handle different response structures
              const items = res?.data?.rows || [];
              const totalCount = res?.data?.count;

              resolve({
                items,
                totalCount,
              });
            },
            payload,
            null,
            (error) => {
              message.error("Failed to load templates. Please try again.");
              reject(error);
            }
          );
        });
      } catch (error) {
        message.error("An unexpected error occurred while loading templates.");
        throw error;
      }
    },
    [api, filterOption, debouncedSearchTerm]
  );

  // Handle search from SearchBar component
  const handleSearch = useCallback((value) => {
    setSearchTerm(value || "");
  }, []);

  const handleEdit = (template) => {
    navigate(`/templates/${template.id}`);
  };

  const handleDelete = async (template) => {
    try {
      setOperationLoading((prev) => ({
        ...prev,
        [`delete-${template.id}`]: true,
      }));

      api.sendRequest(
        apiGenerator(CONSTANTS.API.templates.delete, { id: template.id }),
        (res) => {
          setUpdatedTemplate({ ...template, type: "delete" });
          message.success("Template deleted successfully!");
        },
        null,
        null,
        (error) => {
          message.error("Failed to delete template. Please try again.");
        }
      );
    } catch (error) {
      message.error("An unexpected error occurred while deleting template.");
    } finally {
      setOperationLoading((prev) => ({
        ...prev,
        [`delete-${template.id}`]: false,
      }));
    }
  };

  const handleDuplicate = async (template) => {
    try {
      setOperationLoading((prev) => ({
        ...prev,
        [`duplicate-${template.id}`]: true,
      }));

      const duplicateData = {
        name: `${template?.name} (Copy)`,
        description: template.description,
        pages: template.pages,
      };

      api.sendRequest(
        apiGenerator(CONSTANTS.API.templates.createDuplicate, {
          id: template?.id,
        }),
        (res) => {
          const newTemplate = res?.data || res;
          setUpdatedTemplate({ ...newTemplate, type: "create" });
          message.success("Template duplicated successfully!");
        },
        duplicateData,
        null,
        (error) => {
          message.error("Failed to duplicate template. Please try again.");
        }
      );
    } catch (error) {
      message.error("An unexpected error occurred while duplicating template.");
    } finally {
      setOperationLoading((prev) => ({
        ...prev,
        [`duplicate-${template.id}`]: false,
      }));
    }
  };

  // Render individual template card
  const renderTemplateCard = useCallback(
    (template, index) => {
      return (
        <Card
          className="tw-h-full tw-shadow-sm hover:tw-shadow-lg tw-transition-all tw-duration-300 tw-border-1 tw-border-gray-200 tw-rounded-[20px]"
          key={template?.id || index}
          styles={{
            body: {
              padding: "24px",
            },
          }}
        >
          <div className="tw-mb-4">
            <div className="tw-flex tw-items-center tw-justify-between">
              <div className="tw-flex tw-items-center tw-mb-3">
                <Tag color="default" className="tw-rounded-xl">
                  v{template?.templateversions?.[0]?.version || "1"}
                </Tag>
              </div>

              <div>
                <Tooltip title="Duplicate Template" key="duplicate">
                  <Popconfirm
                    title="Duplicate Template"
                    description="Are you sure you want to duplicate this template?"
                    onConfirm={() => handleDuplicate(template)}
                    okText="Yes"
                    cancelText="No"
                    okButtonProps={{
                      danger: true,
                      loading: operationLoading[`duplicate-${template.id}`],
                    }}
                    disabled={
                      operationLoading[`duplicate-${template.id}`] ||
                      operationLoading[`delete-${template.id}`]
                    }
                  >
                    <Button
                      type="text"
                      icon={<CopyPlus className="tw-w-4 tw-h-4" />}
                      // onClick={() => handleDuplicate(template)}
                      loading={operationLoading[`duplicate-${template.id}`]}
                      disabled={
                        operationLoading[`duplicate-${template.id}`] ||
                        operationLoading[`delete-${template.id}`]
                      }
                      className="tw-text-gray-500 hover:tw-text-blue-600"
                      title="Duplicate Template"
                    />
                  </Popconfirm>
                </Tooltip>
                <Tooltip title="Edit Template" key="edit">
                  <Button
                    type="text"
                    icon={<Edit2 className="tw-w-4 tw-h-4" />}
                    onClick={() => handleEdit(template)}
                    disabled={
                      operationLoading[`duplicate-${template.id}`] ||
                      operationLoading[`delete-${template.id}`]
                    }
                    className="tw-text-gray-500 hover:tw-text-blue-600"
                  />
                </Tooltip>

                <Tooltip title="Delete Template" key="delete">
                  <Popconfirm
                    title="Delete Template"
                    description="Are you sure you want to delete this template?"
                    onConfirm={() => handleDelete(template)}
                    okText="Yes"
                    cancelText="No"
                    okButtonProps={{
                      danger: true,
                      loading: operationLoading[`delete-${template.id}`],
                    }}
                    disabled={
                      operationLoading[`duplicate-${template.id}`] ||
                      operationLoading[`delete-${template.id}`]
                    }
                  >
                    <Button
                      type="text"
                      danger
                      icon={<Trash2 className="tw-w-4 tw-h-4" />}
                      loading={operationLoading[`delete-${template.id}`]}
                      disabled={
                        operationLoading[`duplicate-${template.id}`] ||
                        operationLoading[`delete-${template.id}`]
                      }
                      className="tw-text-gray-500 hover:tw-text-red-600"
                    />
                  </Popconfirm>
                </Tooltip>
              </div>
            </div>
            <div>
              <Title
                level={4}
                className="!tw-mb-0 tw-text-gray-900 tw-truncate"
              >
                {template.name}
              </Title>
            </div>

            {template.description && (
              <Paragraph
                ellipsis={{ rows: 2, expandable: false }}
                className="tw-text-gray-600 tw-text-sm tw-mb-2 tw-mt-2"
              >
                {template.description}
              </Paragraph>
            )}
          </div>

          <div className="tw-flex tw-items-center tw-justify-between tw-pt-1 tw-border-gray-100">
            <div className="tw-flex tw-items-center tw-gap-2">
              <Text type="secondary" className="tw-text-xs">
                Pages: {template?.templateversions?.[0]?.pageCount || 0}
              </Text>
            </div>
            <Text type="secondary" className="tw-text-xs">
              {new Date(template?.createdAt)?.toLocaleDateString()}
            </Text>
          </div>
        </Card>
      );
    },
    [handleEdit, handleDelete, handleDuplicate]
  );

  // Empty state component
  const emptyScreen = (
    <div className="tw-text-center tw-py-12">
      <Layout className="tw-w-16 tw-h-16 tw-text-gray-400 tw-mx-auto tw-mb-4" />
      <h3 className="tw-text-lg tw-font-medium tw-text-gray-900 tw-mb-2">
        {searchTerm ? "No templates found" : "No templates yet"}
      </h3>
      <p className="tw-text-gray-500 tw-mb-4">
        {searchTerm
          ? "Try adjusting your search criteria"
          : "Create your first template by grouping pages together"}
      </p>
    </div>
  );

  if (isEditorRoute) {
    return <Outlet />;
  }

  return (
    <div className="tw-p-6">
      {/* Header Section */}
      <div className="tw-flex tw-flex-col tw-lg:tw-flex-row tw-justify-between tw-items-start tw-lg:tw-items-center tw-mb-6 tw-space-y-4 tw-lg:tw-space-y-0">
        <div className="tw-flex tw-items-center tw-justify-between tw-w-full">
          <h2 className="tw-text-xl tw-font-bold tw-text-gray-900">
            Templates
          </h2>
          {user?.role === "admin" && (
            <Button
              type="primary"
              size="large"
              onClick={() => navigate("/templates/add")}
              icon={<Plus className="tw-w-4 tw-h-4 tw-mr-2" />}
              className="tw-px-4 tw-h-10 tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
            >
              Add Templates
            </Button>
          )}
        </div>

        {/* Search and Filter */}
        <div className="tw-flex tw-items-center tw-justify-between tw-w-full tw-space-x-4">
          <div className="tw-w-full">
            <SearchBar handleSearch={handleSearch} type="page" />
          </div>
          <div className="tw-flex tw-items-center">
            <Select
              prefix={
                <Filter width={16} height={16} className="tw-text-gray-400" />
              }
              value={filterOption}
              onChange={setFilterOption}
              size="middle"
              style={{
                borderRadius: "10px",
                minWidth: "160px",
              }}
              options={templateFilterOptions}
            />
          </div>
        </div>
      </div>

      {/* Templates with ScrollPagination */}
      <ScrollPagination
        key={`tm-${filterOption}-${debouncedSearchTerm}`}
        loadPage={loadTemplates}
        renderItem={renderTemplateCard}
        pageSize={12}
        className="tw-grid tw-grid-cols-1 sm:tw-grid-cols-2 lg:tw-grid-cols-2 xl:tw-grid-cols-2 tw-gap-6"
        emptyScreen={emptyScreen}
        emptyDescription="No templates found"
        updatedItem={updatedTemplate}
        idKey="id"
      />
    </div>
  );
};

export default TemplateManager;
