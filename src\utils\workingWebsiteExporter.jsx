import JSZip from "jszip";
import { buses<PERSON><PERSON><PERSON>, service<PERSON>reaD<PERSON><PERSON> } from "../util/content";

// Sitemap XML generation functions
function generateSitemapXML(urls) {
  const now = new Date().toISOString();
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urls
  .map(
    (url) => `  <url>
    <loc>${url.loc}</loc>
    <lastmod>${url.lastmod || now}</lastmod>
    <changefreq>${url.changefreq || "weekly"}</changefreq>
    <priority>${url.priority || "0.8"}</priority>
  </url>`
  )
  .join("\n")}
</urlset>`;
  return sitemap;
}

// Font URLs to download and self-host
const FONT_URLS = {
  "Inter-400.woff2":
    "https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2",
  "Inter-500.woff2":
    "https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2",
  "Inter-700.woff2":
    "https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2",
  "Phudu-600.woff2":
    "https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2",
  "Phudu-700.woff2":
    "https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2",
};

// Complete working CSS with fonts and Tailwind
const WORKING_CSS = `
/* Font Face Declarations */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('../fonts/Inter-400.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url('../fonts/Inter-500.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url('../fonts/Inter-700.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Phudu';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url('../fonts/Phudu-600.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Phudu';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url('../fonts/Phudu-700.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
`;

// Function to optimize HTML content
function optimizeHTMLContent(htmlContent) {
  // Remove Tailwind CDN script and replace with optimized CSS
  let optimizedHTML = htmlContent
    .replace(/<script src="https:\/\/cdn\.tailwindcss\.com"><\/script>/g, "")
    .replace(/<script src="\/\/unpkg\.com\/alpinejs" defer><\/script>/g, "")
    .replace(
      /<script>\s*tailwind\.config\s*=\s*\{[\s\S]*?\};\s*<\/script>/g,
      ""
    );

  // Replace font-face declarations with optimized local fonts
  optimizedHTML = optimizedHTML.replace(/@font-face\s*\{[^}]*\}/g, "");

  // Add optimized CSS and fonts to head
  const headEndIndex = optimizedHTML.indexOf("</head>");
  if (headEndIndex !== -1) {
    const optimizedHead = `
    <link rel="preload" href="./css/styles.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="./css/styles.css"></noscript>
    <link rel="preload" href="../fonts/Inter-400.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="../fonts/Phudu-600.woff2" as="font" type="font/woff2" crossorigin>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Professional bus rental services with modern, comfortable vehicles for all your transportation needs.">
    <meta name="keywords" content="bus rental, charter bus, transportation, group travel">
    <meta property="og:title" content="Bus Rental Services">
    <meta property="og:description" content="Professional bus rental services with modern, comfortable vehicles.">
    <meta property="og:type" content="website">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Bus Rental Services">
    <meta name="twitter:description" content="Professional bus rental services with modern, comfortable vehicles.">
    <link rel="canonical" href="">
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "LocalBusiness",
      "name": "Bus Rental Company",
      "description": "Professional bus rental services",
      "url": "",
      "telephone": "",
      "address": {
        "@type": "PostalAddress",
        "addressCountry": "US"
      }
    }
    </script>
    `;

    optimizedHTML =
      optimizedHTML.slice(0, headEndIndex) +
      optimizedHead +
      optimizedHTML.slice(headEndIndex);
  }

  // Fix image paths to use relative paths
  optimizedHTML = optimizedHTML.replace(
    /src="\/asset\/([^"]+)"/g,
    'src="./assets/$1"'
  );

  // Add lazy loading to images
  optimizedHTML = optimizedHTML.replace(
    /<img([^>]*?)src="([^"]*?)"([^>]*?)>/g,
    '<img$1src="$2"$3 loading="lazy">'
  );

  return optimizedHTML;
}

// Function to download fonts
async function downloadFonts(zip) {
  const fontsFolder = zip.folder("fonts");

  for (const [filename, url] of Object.entries(FONT_URLS)) {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      fontsFolder.file(filename, blob);
      console.log(`📦 Downloaded font: ${filename}`);
    } catch (error) {
      console.error(`❌ Failed to download font ${filename}:`, error);
    }
  }
}

// Main export function
export async function downloadWorkingWebsiteZip(
  pageList,
  fileList,
  websiteName,
  dynamicContent = null,
  baseUrl = "https://example.com/"
) {
  const zip = new JSZip();

  // Create CSS folder and add working styles
  const cssFolder = zip.folder("css");
  cssFolder.file("styles.css", WORKING_CSS);

  // Download and add fonts
  await downloadFonts(zip);

  // Process pages with optimized HTML
  for (const page of pageList) {
    const optimizedHTML = optimizeHTMLContent(page.html);
    const pagePath = page.path || `${page.name || "page"}.html`;
    zip.file(pagePath, optimizedHTML);
    console.log(`📄 Added optimized page: ${pagePath}`);
  }

  // Process assets (images, etc.) with proper naming
  if (fileList && Object.keys(fileList).length > 0) {
    const assetsFolder = zip.folder("assets");

    for (const [key, file] of Object.entries(fileList)) {
      try {
        const response = await fetch(file.url);
        const blob = await response.blob();
        // Use the original filename from the file object
        const filename = file.name || file.url.split("/").pop();
        assetsFolder.file(filename, blob);
        console.log(`📦 Added asset: assets/${filename}`);
      } catch (error) {
        console.error(`❌ Failed to download asset ${file.url}:`, error);
      }
    }
  }

  // Generate single comprehensive sitemap XML file
  const currentDate = new Date().toISOString().split("T")[0];
  const allUrls = [];

  // Add static pages
  pageList.forEach((page) => {
    allUrls.push({
      loc: `${baseUrl}${page?.path || `${page?.name || "page"}.html`}`,
      lastmod: currentDate,
      changefreq: "weekly",
      priority: page.path === "index.html" ? "1.0" : "0.8",
    });
  });

  // Add dynamic content pages if available
  if (dynamicContent) {
    // Add bus type pages
    const busSection = dynamicContent?.[busesDKey]?.sections?.[0];
    if (busSection?.sectionItems) {
      busSection.sectionItems.forEach((item) => {
        allUrls.push({
          loc: `${baseUrl}${item.slug}.html`,
          lastmod: currentDate,
          changefreq: "monthly",
          priority: "0.7",
        });
      });
    }

    // Add city pages
    const citySection = dynamicContent?.[serviceAreaDKey]?.sections?.[0];
    if (citySection?.sectionItems) {
      citySection.sectionItems.forEach((item) => {
        allUrls.push({
          loc: `${baseUrl}${item.slug}.html`,
          lastmod: currentDate,
          changefreq: "monthly",
          priority: "0.7",
        });
      });
    }

    // Add service pages
    Object.keys(dynamicContent).forEach((key) => {
      if (key.includes("service") && dynamicContent[key]?.sections) {
        dynamicContent[key].sections.forEach((section) => {
          if (section.sectionItems) {
            section.sectionItems.forEach((item) => {
              allUrls.push({
                loc: `${baseUrl}${item.slug}.html`,
                lastmod: currentDate,
                changefreq: "monthly",
                priority: "0.7",
              });
            });
          }
        });
      }
    });
  }

  // Generate single comprehensive sitemap
  if (allUrls.length > 0) {
    const comprehensiveSitemap = generateSitemapXML(allUrls);
    zip.file("sitemap.xml", comprehensiveSitemap);
    console.log(
      `🗺️ Added comprehensive sitemap: sitemap.xml (${allUrls.length} URLs)`
    );
  }

  // Generate and download ZIP
  const zipBlob = await zip.generateAsync({ type: "blob" });
  const url = URL.createObjectURL(zipBlob);
  const a = document.createElement("a");
  a.href = url;
  a.download = `${websiteName || "website"}-working.zip`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);

  console.log("✅ Working website exported successfully with sitemaps!");
}

// Convert page list to export format (same as original)
export const convertPageListToExportFormat = (
  pageList,
  fileList,
  dynamicContent = null,
  baseUrl = "https://example.com/"
) => {
  return pageList.map((page) => ({
    html:
      page?.full_page_content ||
      `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${page?.name || "Page"}</title>
</head>
<body>
  <h1>${page?.name || "Page"}</h1>
  <p>Content will be added here.</p>
</body>
</html>`,
    path: getPagePath(page),
  }));
};

// Helper function to get page path
function getPagePath(page) {
  if (page.slug) {
    return page.slug === "home" ? "index.html" : `${page.slug}.html`;
  }
  return `${page.name || "page"}.html`;
}
