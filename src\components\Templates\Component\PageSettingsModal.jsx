import React, { useEffect } from "react";
import { Modal, Form, Row, Col, Button, message, Space } from "antd";
import { CONSTANTS } from "../../../util/constant/CONSTANTS";
import FormFields from "../../common/FormFields";
import Title from "antd/es/typography/Title";
import { X } from "lucide-react";

const PageSettingsModal = ({
  visible,
  onCancel,
  onSave,
  pageData,
  loading = false,
}) => {
  const [form] = Form.useForm();
  // console.log("this page");
  // Populate form with page data when modal opens
  useEffect(() => {
    if (visible && pageData) {
      form.setFieldsValue({
        name: pageData?.name || "",
        urlSlug: pageData?.urlSlug || pageData?.url?.replace(/^\//, "") || "",
        metaTitle: pageData?.metaTitle || "",
        metaDescription: pageData?.metaDescription || "",
        siteMapLabel: pageData?.siteMapLabel || "",
        wrapperClass: pageData?.wrapperClass || "",
        customCss: pageData?.customCss || "",
        customJs: pageData?.customJs || "",
        headers: pageData?.headers || "",
      });
    }
  }, [visible, pageData, form]);

  // Handle form submission
  const handleFormSubmit = async (values) => {
    try {
      // Ensure URL slug starts with '/' if not empty
      const processedValues = {
        ...values,
        url: values?.urlSlug ? `/${values?.urlSlug?.replace(/^\//, "")}` : "",
      };
      // console.log(processedValues, "processedValues");
      await onSave({
        versionData: { ...pageData, ...processedValues },
      });
      message.success("Page settings updated successfully!");
      form.resetFields();
    } catch (error) {
      console.error("Error saving page settings:", error);
      message.error("Failed to save page settings. Please try again.");
    }
  };

  // Handle modal cancel
  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      // title={`${pageData?.name || "Unknown Page"} Settings`}
      title={
        <Space className="tw-items-center">
          {/* <Settings className="tw-w-5 tw-h-5 tw-text-blue-600" /> */}
          <Title level={4} className="!tw-mb-0 tw-text-gray-800">
            {`${pageData?.name || "Unknown Page"} Settings`}
          </Title>
        </Space>
      }
      open={visible}
      onCancel={handleCancel}
      width={800}
      footer={
        <div className="tw-flex tw-justify-end tw-space-x-3 tw-mt-6 tw-pt-4 tw-border-t tw-border-gray-200">
          <Button onClick={handleCancel} className="tw-px-6 tw-h-10">
            Cancel
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            onClick={() => form.submit()}
            className="tw-px-6 tw-h-10 tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
          >
            Save
          </Button>
        </div>
      }
      className="tw-top-4"
      styles={{
        body: { padding: "6px" },
        header: { borderBottom: "1px solid #f0f0f0", paddingBottom: "8px" },
      }}
      closeIcon={
        <Button
          type="text"
          icon={<X className="tw-w-4 tw-h-4" />}
          className="tw-text-gray-400 hover:tw-text-gray-600 tw-border-0"
        />
      }
      // destroyOnClose
      // className="tw-top-8"
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleFormSubmit}
        className="tw-space-y-4"
        size="large"
      >
        <Row gutter={[16, 0]}>
          {CONSTANTS?.FORM?.pageSetting?.map((field) => (
            <Col key={field.name} {...(field.colProps || { xs: 24 })}>
              <FormFields field={field} />
            </Col>
          ))}
        </Row>
      </Form>
    </Modal>
  );
};

export default PageSettingsModal;
