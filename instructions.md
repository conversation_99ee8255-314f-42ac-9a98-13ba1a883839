# Dream Builder Frontend – Development Guide (Project-Specific)

This document captures how this codebase is organized and how to build new features consistent with the existing patterns.

### TL;DR Quick Start

- Build UI with Ant Design + Tailwind (tw- prefix) inside src/components/<Domain>/...
- Define endpoints in src/util/constant/CONSTANTS.js and build URLs with apiGenerator(...)
- Fetch via use-http: sendRequest(urlConfig, onSuccess, payload, successMsg, onError)
- Lists use <ScrollPagination> and patch the list locally with updatedItem { ...item, type: 'create'|'update'|'delete' }
- For edits, PATCH only changed keys; for delete, pass id via apiGenerator(..., { id })
- Use placeholder rules in components (${key}, ${\_img-key}, {{repeatSlot}}) so preview/export replacement works
- Export with downloadWebsiteZip({ pageList, fileList, dynamicContent, baseUrl }) and include images from public/assets only

## 1) Tech stack and tooling

- Build: Vite (React 18)
- Language: JavaScript with JSX (.jsx). TypeScript types exist but components are JS.
- UI: Ant Design + Tailwind CSS (tw- prefix classes). Icons via lucide-react.
- Routing: react-router-dom v7 (nested routes with Layout + Outlet).
- HTTP: Axios instance in src/util/API/service.js with VITE_API_URL and auth interceptor.
- State: React hooks and Context (AuthContext, SidebarContext).

## 2) Directory layout (as used here)

- src/components/... Feature-first folders (Categories, Components, Pages, Templates, Websites, Layout, common, etc.)
- src/contexts/... Context providers/hooks (AuthContext, SidebarContext)
- src/hooks/... Reusable hooks (use-http, use-storage, useDebounce, useMediaManager)
- src/util/... Routing, API, constants, content helpers, storage, functions
- src/utils/... Exporters, content JSON, preview utilities, scripts
- src/FeaturePage/... Feature layout/pages for special env layout
- public/assets Images used for preview/export
  Tip: There is no src/pages folder; page-level UIs live under components/<Domain>.

## 3) Styling and UI conventions

- Prefer Ant Design components; compose with Tailwind utility classes prefixed with tw- (configured in tailwind.config.js).
- Keep preview cards and iframes visually consistent:
  - Component previews are static iframe snapshots scaled to fit height 208px; no scrolling/interaction in cards.
  - Use responsive containers that scale preview proportionally (transform scale with origin top-left).
- Empty states: use Antd Empty or purpose-built placeholders; avoid dummy content.
- Buttons: gradient primary style commonly used: from blue-600 to purple-600 and border-0.
- Sidebar: collapsible desktop sidebar + mobile Drawer, controlled by SidebarContext; header shows mobile menu button.

## 4) Routing and layout

- Routes are defined in src/util/Route.jsx. Login lives at /login. All app routes under "/" are wrapped with ProtectedRoute and the shared Layout (Header + Sidebar + Outlet). Fallback "\*" redirects to /login.
- Current getLayoutComponent() returns Layout by default; env-based switching (FeatureLayout) is commented out. To re-enable, wire VITE_LAYOUT_MODE to choose between Layout and FeatureLayout.
- Children under "/": index Dashboard, pages, dynamic-pages, categories, components, templates, templates/add, templates/:id, websites, activity-logs, users.

## 5) State and context

- useAuth() from AuthContext provides { user, loading, login(), logout(), getAuthHeaders() }.
  - ProtectedRoute reads user/loading to gate app routes.
  - login() stores token and user in localStorage; logout() clears token/user and erases SAID cookie; getAuthHeaders() returns Authorization Bearer header.
- useSidebar() from SidebarContext provides { isCollapsed, isMobile, isMobileOpen, toggleCollapse, toggleMobileMenu, closeMobileMenu }.
  - Layout uses isCollapsed/isMobile to compute content margins; mobile uses a Drawer triggered from Header.
- Prefer Context over prop drilling for global UI state.

## 6) Data layer pattern

- Centralized API endpoint definitions in src/util/constant/CONSTANTS.js with shape { type, endpoint }.
- Build URLs with apiGenerator(CONSTANTS.API.resource.action, { id }) to replace :id.
- HTTP via use-http hook (src/hooks/use-http.js):
  - sendRequest(urlConfig, responseHandler, payload, successMessage, errorHandler)
  - GET query params are built by buildQueryString; POST/PATCH/DELETE use body.
  - Auth header auto-injected via interceptor; handles common 401/expired JWT flows.
- Local JSON storage alternative via use-storage (src/hooks/use-storage.js):
  - Mirrors sendRequest signature and maps endpoints to storageAdapter operations.
  - Provides convenience methods: categories/components/pages/templates with getAll/getById/create/update/delete.
- Rule: Prefer the custom hook API (sendRequest) and CONSTANTS over raw axios/fetch (except AuthContext.login which is bespoke).

## 7) CRUD and list update conventions

- Pagination pattern: Reusable ScrollPagination component (src/components/common/ScrollPagination.jsx):
  - Parent supplies loadPage({ page, pageSize }) => Promise<{ items, totalCount }>.
  - For optimistic/local updates after create/update/delete, set updatedItem state with shape { ...item, type: 'create'|'update'|'delete' } and pass as updatedItem; ScrollPagination patches in-place (no refetch).
- PATCH semantics: For update flows (e.g., Pages builder), compute and send only changed keys compared to original object.
- Debounce: Use 300 ms debounce for search inputs (useDebounce). Do not debounce drag/drop interactions.

## 8) Categories, Components, Pages (patterns to follow)

- Categories (CategoryManager): Antd modal for add/edit; color picker consistent with existing modal; infinite scroll list; optimistic patching via updatedItem; delete with Popconfirm.
- Components (ComponentManager): search + category filter + infinite scroll; admin-only edit/delete; preview via iframe using getPreviewHTML; auto-detected placeholders displayed as chips.
- Pages (PageBuilder + DragDropBuilder): left library + center preview + right structure; react-dnd for drag/drop; responsive behavior for mobile/tablet; save builds pagePlaceHolder, repeated components metadata, and full_page_content via generateGlobalPreviewHTML.

## 9) Template workflow

- Template routes: /templates (list), /templates/add (create), /templates/:id (edit) via TemplateEditorWrapper.
- Wrapper loads pages and (for edit) template by id via use-storage directOperation.
- Maintain draft/edit semantics in UI; navigate back to list on save/cancel.

## 10) Preview and content replacement

- Placeholders in component HTML use ${variable} and array repeat slots use {{placeholder}}.
- Image variables follow ${_img-${key}} pattern; alt can be ${\_alt-key}. When exporting or previewing:
  - findAndReplaceVariable in src/util/functions.js replaces text, image src with blob/object URLs, and auto-fills alt from filename.
- For repeated components, map arrays to repeated component HTML and inject joined markup into {{placeholder}}.

## 11) Website export

- Use DownloadWebsiteButton (src/components/DownloadWebsiteButton.jsx) and exporter utilities in src/utils/:
  - simpleWebsiteExporter.jsx → downloadWebsiteZip({ pageList, fileList, dynamicContent, baseUrl })
  - optimizedWebsiteExporter.jsx → optional optimized export with local fonts and purged CSS
- Include only images from public/assets in ZIP; copy as assets/<filename>. Keep generated sitemap.xml (single file) based on static and dynamic pages.
- Paths:
  - Static pages: index.html for home; others {slug}.html
  - Dynamic pages: {slug}.html (slug from page.url last segment)

## 12) Naming, files, and code style

- Components: PascalCase files in feature folders, one component per file.
- Variables/functions: camelCase; constants live in src/util/constant/CONSTANTS.js.
- Prefer lucide-react icons; Antd icons only when already used locally.
- Keep commented “future API” code paths; do not delete until backend is ready.
- Use small, focused functions. Add comments for non-obvious logic.

## 13) Environment variables

- VITE_API_URL: base URL for axios service
- VITE_LAYOUT_MODE: "feature" or default to control layout selection

## 14) Linting and quality

- Run npm run lint before commit. Follow existing patterns; Prettier isn’t enforced here—match current formatting.
- Commits: conventional prefixes (feat, fix, refactor, chore, test).

## 15) How to add a new feature (checklist)

## 16) Pattern examples (CRUD + ScrollPagination + sendRequest)

Minimal example for categories; follow the same pattern for other resources.

```jsx
import { useCallback, useState } from "react";
import useHttp from "@/hooks/use-http";
import ScrollPagination from "@/components/common/ScrollPagination";
import { CONSTANTS } from "@/util/constant/CONSTANTS";
import { apiGenerator } from "@/util/functions";

export default function Example() {
  const { sendRequest } = useHttp();
  const [updatedItem, setUpdatedItem] = useState(null);
  const [search, setSearch] = useState("");

  const loadPage = useCallback(
    ({ page, pageSize }) =>
      new Promise((resolve, reject) => {
        sendRequest(
          CONSTANTS.API.categories.get,
          (res) => {
            resolve({
              items: res?.data?.rows || [],
              totalCount: res?.data?.count || 0,
            });
          },
          { page, limit: pageSize, search },
          null,
          (err) => reject(err)
        );
      }),
    [sendRequest, search]
  );

  const createCategory = (values) => {
    sendRequest(
      CONSTANTS.API.categories.create,
      (res) => setUpdatedItem({ ...(res?.data || values), type: "create" }),
      values,
      "Category created"
    );
  };

  const updateCategory = (original, values) => {
    const delta = Object.fromEntries(
      Object.entries(values).filter(([k, v]) => v !== original[k])
    );
    if (!Object.keys(delta).length) return;
    sendRequest(
      apiGenerator(CONSTANTS.API.categories.update, { id: original.id }),
      () => setUpdatedItem({ ...original, ...delta, type: "update" }),
      delta,
      "Category updated"
    );
  };

  const deleteCategory = (item) => {
    sendRequest(
      apiGenerator(CONSTANTS.API.categories.delete, { id: item.id }),
      () => setUpdatedItem({ id: item.id, type: "delete" })
    );
  };

  return (
    <ScrollPagination
      loadPage={loadPage}
      updatedItem={updatedItem}
      idKey="id"
      renderItem={(item) => (
        <div key={item.id}>
          {item.name}
          {/* buttons call createCategory / updateCategory / deleteCategory */}
        </div>
      )}
      pageSize={12}
      useWindow
      className="tw-grid tw-gap-4"
    />
  );
}
```

## 17) Export checklist (simpleWebsiteExporter)

- Prepare pageList as an array of objects with `html` and `path`:
  - `html`: final HTML string (use `page.full_page_content` from preview generation)
  - `path`: use static `index.html` for home, otherwise `{slug}.html`; for dynamic, last segment of `page.url` → `{slug}.html`
- Prepare `fileList` as an object map of media keys to `{ url, name, path }` (only include items from public/assets)
- Call exporter:

```js
import { downloadWebsiteZip } from "@/utils/simpleWebsiteExporter";

await downloadWebsiteZip({
  pageList, // [{ html, path }, ...]
  fileList, // { [key]: { url, name, path } }
  dynamicContent, // optional (for sitemap)
  baseUrl: "https://your-domain.com/",
});
```

Notes:

- Only images from public/assets should be included in ZIP (they are stored under assets/<filename> in the archive).
- A single sitemap.xml is generated from `pageList` and `dynamicContent`.
- For optimized export (local fonts + purged CSS), see `utils/optimizedWebsiteExporter.jsx`.

## 18) Content replacement do’s and don’ts

Do

- Use `${key}` for text placeholders, and `{{placeholder}}` for repeated sections.
- Use image placeholders as `${_img-key}`; provide alt as `${_alt-key}` (auto-filled from filename when available).
- Keep keys simple, lowercase, hyphen/underscore separated; they’re normalized in replacement logic.
- Add `data-component="{component-name-id}"` on the main wrapper to aid preview/content mapping.
- For dynamic pages, rely on provided `${slug}`/`slug_key` variables from the preview/export pipeline.

Don’t

- Don’t hardcode absolute URLs for images; use `${_img-key}` so previews use blob/object URLs and export swaps them to asset paths.
- Don’t nest template syntax unnecessarily; nested `${...}` is supported but keep expressions straightforward.

## 19) Component preview best practices

- Use an iframe for previews; fix the visible height to 208px and scale content proportionally (transform: scale) to avoid scroll inside cards.
- Compute preview width based on selected device and available middle container space; animate device changes smoothly.
- Always render default component HTML if no content is provided; avoid blank previews.
- Add data-component attributes to root wrappers for mapping content.

## 20) Partial PATCH helper (utility snippet)

```js
export const diffKeys = (original = {}, draft = {}, allow = null) => {
  const keys = allow
    ? allow
    : Array.from(new Set([...Object.keys(original), ...Object.keys(draft)]));
  return keys.reduce((acc, k) => {
    if (draft[k] !== original[k]) acc[k] = draft[k];
    return acc;
  }, {});
};
```

Usage:

```js
const delta = diffKeys(original, values);
if (Object.keys(delta).length) {
  sendRequest(
    apiGenerator(CONSTANTS.API.categories.update, { id: original.id }),
    onOk,
    delta,
    "Updated"
  );
}
```

## 21) Local JSON storage (use-storage) usage

- Drop-in alternative to use-http; same sendRequest signature.
- Direct operations available: categories/components/pages/templates with getAll/getById/create/update/delete.

```js
import useStorage from "@/hooks/use-storage";
const { isLoading, sendRequest, categories } = useStorage();

// Mirroring API style
await sendRequest(CONSTANTS.API.categories.get, (res) =>
  setRows(res.data.rows)
);

// Or direct operations
const all = await categories.getAll();
const created = await categories.create({ name: "New" });
```

## 22) Search + debounce on list screens

- Debounce user inputs (e.g., 300ms) using useDebounce; do not debounce drag/drop.

```js
import useDebounce from "@/hooks/useDebounce";
import useHttp from "@/hooks/use-http";
const [search, setSearch] = useState("");
const api = useHttp();
const debouncedSearch = useDebounce(search, 300);

const loadPage = useCallback(
  ({ page, pageSize }) =>
    new Promise((resolve, reject) => {
      api.sendRequest(
        CONSTANTS.API.components.get,
        (res) =>
          resolve({
            items: res?.data?.rows || [],
            totalCount: res?.data?.count || 0,
          }),
        { page, limit: pageSize, search: debouncedSearch },
        null,
        (e) => reject(e)
      );
    }),
  [sendRequest, debouncedSearch]
);
```

## 23) Drag & drop patterns (react-dnd)

- Wrap the builder area with DnD provider once; keep type strings stable per domain.
- Item shape: { id, type, payload }; Drop target accepts a list of types.
- Do not debounce library drag interactions; apply minimal debounce only when persisting page structure sequence.
- On drop, update in-memory structure first (optimistic), then persist.

## 24) Error handling and notifications

- Success: prefer antd notification/message only once (avoid duplicates alongside component-level messages).
- Auth errors: 401 + jwt expired → logout() and reload handled by use-http; do not duplicate handling in components.
- Provide empty screens and retry affordances for failures in list views.

## 25) Media file and fileList structure for preview/export

- Preview uses blob/object URLs; export swaps to asset paths.
- fileList map shape preferred in exporters: { key: { url, name, path } }.
- Keys align with `${_img-key}` placeholders (normalized to lowercase, hyphenated).
- Alt text auto-filled from filename when `${_alt-key}` exists; otherwise generated during replacement.

## 26) Sidebar and responsive layout

- Desktop: collapsible sidebar (SidebarContext.isCollapsed) controlled by a toggle button.
- Mobile: Drawer opened from header 3-dot/menu; close on route change.
- Middle container width is the reference for preview sizing; compute responsive preview based on actual available width, not viewport.

- Don’t remove commented API paths; keep them until backend parity is verified.

1. Create UI under src/components/<Domain>/<Feature>.jsx using Antd + Tailwind (tw- classes).
2. Define endpoints in src/util/constant/CONSTANTS.js; use apiGenerator for :id substitutions.
3. Use use-http (preferred) or use-storage for local JSON during prototyping.
4. For lists, wire ScrollPagination with server-side loader and optimistic updatedItem patching.
5. Add route in src/util/Route.jsx and sidebar item if needed (respect adminOnly and mobile drawer behavior).
6. For edit flows, send only changed keys on PATCH.
7. Keep empty states graceful and consistent; reuse common components from src/components/common.
8. Preserve commented API code paths for future backend; don’t remove until feature parity is confirmed.

This guide should be kept in sync with the codebase—update when adding new patterns/components.
