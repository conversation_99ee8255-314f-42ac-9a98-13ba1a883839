# MCP Atlassian (self-hosted) - Example .env
# Copy this file to ".env" in the same folder and fill the values.
# Lines starting with # are comments.

# --- Required (Cloud via API Token) ---
JIRA_URL=https://your-company.atlassian.net
JIRA_USERNAME=<EMAIL>
JIRA_API_TOKEN=your_jira_api_token

# If you also want Confluence:
CONFLUENCE_URL=https://your-company.atlassian.net/wiki
CONFLUENCE_USERNAME=<EMAIL>
CONFLUENCE_API_TOKEN=your_confluence_api_token

# --- Optional safety/controls ---
# Set to true to disable write operations (create/update/transition/etc.)
READ_ONLY_MODE=true

# Comma-separated project/space filters to limit scope
JIRA_PROJECTS_FILTER=WEB,ENG
CONFLUENCE_SPACES_FILTER=ENG,OPS

# Logging
MCP_LOGGING_STDOUT=true
MCP_VERBOSE=true

# --- Server/Data Center (instead of Cloud tokens) ---
# For on-prem, use Personal Access Tokens (PATs) and your on-prem URLs:
# JIRA_URL=https://jira.your-company.com
# JIRA_PERSONAL_TOKEN=your_jira_pat
# CONFLUENCE_URL=https://confluence.your-company.com
# CONFLUENCE_PERSONAL_TOKEN=your_confluence_pat
# If using self-signed certs, you can disable SSL verification (not recommended):
# JIRA_SSL_VERIFY=false
# CONFLUENCE_SSL_VERIFY=false

