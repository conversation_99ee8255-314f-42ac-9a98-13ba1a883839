import React, { useState, useEffect, useRef } from "react";
import {
  Card,
  Row,
  Col,
  Input,
  Select,
  Button,
  Form,
  Space,
  Typography,
  Divider,
  message,
} from "antd";

const { TextArea } = Input;
const { Title, Text } = Typography;

import useHttp from "../../hooks/use-http";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { apiGenerator, removeSpacesDeep } from "../../util/functions";
import TabList from "./EditorComponent/TabList";

const ComponentEditor = ({
  component,
  categories,
  onSave,
  onCancel,
  globalVarible = [],
}) => {
  const api = useHttp();
  const [form] = Form.useForm();
  const [formData, setFormData] = useState({
    name: "",
    categoryId: null,
    html: "",
    css: "",
    js: "",
    placeholders: [],
    repeatedPlaceholder: [],
    categoryData: {},
    // status: "draft", // New field: draft | published
    // tags: [], // New field: string array
    // thumbnail_url: "", // New field: optional image URL
  });

  const [saving, setSaving] = useState(false);
  const [validationState, setValidationState] = useState({
    isValid: true,
    hasErrors: false,
    errorCount: 0,
    editorStates: {},
  });
  const [validation, setValidation] = useState({
    html: [],
    css: [],
    js: [],
  });
  // console.log(formData, validation);
  useEffect(() => {
    if (component) {
      const initialData = {
        name: component?.name || "",
        categoryId: component?.categoryId || null,
        html: component?.html || "",
        css: component?.css || "",
        js: component?.js || "",
        placeholders: component?.placeholders || [],
        repeatedPlaceholder: component?.repeatedPlaceholder || [],
        categoryData: component?.categoryData || {},
        // status: component?.status || "draft", // New field
        // tags: component?.tags || [], // New field
        // thumbnail_url: component?.thumbnail_url || "", // New field
      };
      setFormData(initialData);
      form.setFieldsValue(initialData);
    } else {
      // Reset form for new component
      const initialData = {
        name: "",
        categoryId: null,
        html: "",
        css: "",
        js: "",
        placeholders: [],
        repeatedPlaceholder: [],
        categoryData: {},
        // status: "draft", // New field
        // tags: [], // New field
        // thumbnail_url: "", // New field
      };
      setFormData(initialData);
      form.setFieldsValue(initialData);
    }
  }, [component, form]);

  const handleSubmit = async (values) => {
    try {
      setSaving(true);
      console.log(validationState);

      // Check for validation errors first - like VS Code preventing save with errors
      if (
        validation?.html?.length ||
        validation?.css?.length ||
        validation?.js?.length
      ) {
        let errorLang = "";
        let errorCount = 0;

        if (validation?.html?.length) {
          errorLang = "HTML";
          errorCount = validation.html.length;
        } else if (validation?.js?.length) {
          errorLang = "JavaScript";
          errorCount = validation.js.length;
        } else if (validation?.css?.length) {
          errorLang = "CSS";
          errorCount = validation.css.length;
        }

        message.error(
          `Cannot save component with ${errorCount} ${errorLang} syntax error${
            errorCount > 1 ? "s" : ""
          }. Please fix the errors in your code before saving.`
        );

        setSaving(false);
        return;
      }

      // Merge form values with current formData (includes code content from TabList)
      let submitData = {
        ...formData,
        ...values,
      };
      // remove repeatedPlaceholder, categoryData, placeholders before submit
      delete submitData?.repeatedPlaceholder;
      delete submitData?.categoryData;
      delete submitData?.placeholders;

      const missingKey = !submitData.name?.trim()
        ? "Component Name"
        : !submitData.categoryId
        ? "Category"
        : !submitData.html?.trim()
        ? "HTML"
        : null;

      // Required fields validation
      if (missingKey) {
        message.error(`${missingKey} is required`);
        setSaving(false);
        return;
      }

      const apiConfig = component
        ? apiGenerator(CONSTANTS.API.components.update, { id: component.id })
        : CONSTANTS.API.components.create;

      // For update: send only changed keys
      const payload = component
        ? Object.keys(submitData).reduce((acc, key) => {
            if (submitData[key] !== component[key]) acc[key] = submitData[key];
            return acc;
          }, {})
        : submitData;

      // If nothing changed on update, short-circuit
      if (component && Object.keys(payload).length === 0) {
        message.info("No changes to update");
        setSaving(false);
        onSave(component);
        return;
      }
      const newPayload = {
        ...payload,
        name: payload?.name?.trim(),
      };

      api.sendRequest(
        apiConfig,
        (res) => {
          const saved = res?.data || { ...(component || {}), ...submitData };
          message.success(
            component
              ? "Component updated successfully!"
              : "Component created successfully!"
          );
          setSaving(false);
          onSave(saved);
        },
        newPayload,
        null,
        (error) => {
          // console.error("Error saving component:", error);
          message.error(error || "Failed to save component. Please try again.");
          setSaving(false);
        }
      );
    } catch (error) {
      console.error("Form validation failed:", error);
      setSaving(false);
    }
  };

  const handleFormValuesChange = (changedValues, allValues) => {
    // Update formData when form values change (for basic form fields)
    setFormData((prev) => ({
      ...prev,
      ...allValues,
    }));
  };

  return (
    <>
      <div className="tw-p-6 tw-bg-gray-50 tw-min-h-screen">
        <div className="tw-max-w-7xl tw-mx-auto">
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            onValuesChange={handleFormValuesChange}
            className="tw-space-y-6"
            size="large"
            requiredMark={false}
          >
            <div className="tw-grid tw-grid-cols-1 tw-lg:tw-grid-cols-2 tw-gap-6">
              {/* Left Panel - Component Details */}
              <div className="tw-space-y-6">
                <Card
                  title={
                    <Space>
                      <span className="tw-text-lg tw-font-semibold tw-text-gray-900">
                        Component Details
                      </span>
                    </Space>
                  }
                  className="tw-shadow-sm tw-border-0"
                  styles={{
                    header: {
                      borderBottom: "1px solid #f0f0f0",
                      paddingBottom: "0px",
                    },
                    body: {
                      paddingTop: "16px",
                    },
                  }}
                >
                  <Row gutter={[24, 24]}>
                    <Col xs={24} sm={24} md={12} lg={24} xl={12}>
                      <Form.Item
                        name="name"
                        className="tw-mb-0"
                        label={
                          <span className="tw-text-sm tw-font-medium tw-text-font-color">
                            Component Name
                          </span>
                        }
                        rules={[
                          {
                            required: true,
                            message: "Please enter a component name",
                          },
                          {
                            min: 3,
                            message:
                              "Component name must be at least 3 characters",
                          },
                          {
                            max: 100,
                            message:
                              "Component name must be less than 100 characters",
                          },
                        ]}
                      >
                        <Input
                          placeholder="e.g., Hero Section, Navigation Bar"
                          className="tw-rounded-lg"
                          onChange={(e) =>
                            setFormData({
                              ...formData,
                              name: e.target.value.trim(),
                            })
                          }
                        />
                      </Form.Item>
                    </Col>
                    <Col xs={24} sm={24} md={12} lg={24} xl={12}>
                      <Form.Item
                        name="categoryId"
                        className="tw-mb-0"
                        label={
                          <span className="tw-text-sm tw-font-medium tw-text-font-color">
                            Category
                          </span>
                        }
                        rules={[
                          {
                            required: true,
                            message: "Please select a category",
                          },
                        ]}
                      >
                        <Select
                          placeholder="Select a category"
                          className="tw-w-full"
                          onChange={(value) => {
                            setFormData({
                              ...formData,
                              categoryId: value,
                              categoryData:
                                categories.find(
                                  (category) => category.id == value
                                ) || {},
                            });
                          }}
                          options={categories?.map((category) => ({
                            label: category?.name?.trim(),
                            value: category?.id,
                          }))}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>
              </div>

              {/* Right Panel - Code and Preview */}
              <div className="tw-space-y-6">
                <TabList
                  formData={formData}
                  setFormData={setFormData}
                  globalVarible={globalVarible}
                  onValidationStateChange={setValidationState}
                  onValidationChange={setValidation}
                />
              </div>
            </div>

            {/* Action Buttons */}
            <Divider className="tw-my-8" />

            {/* Validation Status Display */}
            {/* {validationState.hasErrors && (
              <div className="tw-mb-4 tw-p-4 tw-bg-red-50 tw-border tw-border-red-200 tw-rounded-lg">
                <div className="tw-flex tw-items-center tw-text-red-800">
                  <svg
                    className="tw-w-5 tw-h-5 tw-mr-2"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <span className="tw-font-medium">
                    {validationState.errorCount} syntax error
                    {validationState.errorCount > 1 ? "s" : ""} found
                  </span>
                </div>
                <p className="tw-text-red-700 tw-text-sm tw-mt-1">
                  Please fix the syntax errors in your code before saving. Check
                  the red underlines in the editor for details.
                </p>
              </div>
            )} */}

            <div className="tw-flex tw-justify-end tw-gap-4">
              <Button
                type="default"
                size="large"
                onClick={onCancel}
                // icon={<CloseOutlined />}
                className="tw-px-8 tw-h-12 tw-rounded-lg tw-font-medium tw-border-gray-300 hover:tw-border-gray-400"
              >
                Cancel
              </Button>
              <Button
                type="primary"
                size="large"
                htmlType="submit"
                loading={saving}
                // disabled={validationState.hasErrors}
                // icon={saving ? <LoadingOutlined /> : <SaveOutlined />}
                className={`tw-px-8 tw-h-12 tw-rounded-lg tw-font-medium tw-border-0 ${
                  // validationState.hasErrors
                  //   ? "tw-bg-gray-400 tw-cursor-not-allowed"
                  //   :
                  "tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700"
                }`}
                // title={
                //   validationState.hasErrors
                //     ? `Cannot save with ${
                //         validationState.errorCount
                //       } syntax error${
                //         validationState.errorCount > 1 ? "s" : ""
                //       }`
                //     : ""
                // }
              >
                {component ? "Update Component" : "Create Component"}
              </Button>
            </div>
          </Form>
        </div>
      </div>
    </>
  );
};

export default ComponentEditor;
