import React, { useState } from "react";
import { Button, message } from "antd";
import { Download } from "lucide-react";
import {
  downloadWebsiteZip,
  convertPageListToExportFormat,
} from "../utils/simpleWebsiteExporter";

/**
 * Simple Download Website Button Component
 * Can be used anywhere in the app to download website ZIP
 */
const DownloadWebsiteButton = ({
  pageList = [],
  className = "",
  size = "default",
  type = "primary",
  children,
  zipName,
  exporthandler,
  extraDetail,
}) => {
  const [downloading, setDownloading] = useState(false);

  const handleDownload = async () => {
    if (!pageList || pageList.length === 0) {
      message.warning(
        "No pages available to export. Please create some pages first."
      );
      return;
    }

    setDownloading(true);
    try {
      await exporthandler(extraDetail);
      // console.log('Starting website download...');

      // // Convert pageList to export format
      // const pages = convertPageListToExportFormat(pageList);

      // // Generate ZIP filename
      // const filename = zipName || `website-export-${new Date().toISOString().split('T')[0]}.zip`;

      // // Download the ZIP
      // await downloadWebsiteZip(pages, filename);

      // message.success(`Website downloaded successfully! ${pageList.length} pages exported.`);
    } catch (error) {
      console.error("Download failed:", error);
      message.error(`Download failed: ${error.message}`);
    } finally {
      setDownloading(false);
    }
  };

  return (
    <Button
      type={type}
      size={size}
      // icon={<Download className="tw-w-4 tw-h-4" />}
      onClick={handleDownload}
      loading={downloading}
      disabled={!pageList || pageList.length === 0}
      className={`tw-flex tw-items-center tw-gap-2 ${className}`}
    >
      {children || (downloading ? "Exporting..." : `Export`)}
    </Button>
  );
};

export default DownloadWebsiteButton;
