import React from "react";
import MonacoCodeEditor from "./MonacoCodeEditor";

const EditorSnippet = ({
  type = "html",
  placeholder = "Enter your HTML content here...",
  onValueChange,
  defaultValue = "",
  value = "",
  textareaId = "html",
  // Additional props for backward compatibility
  height = "410px",
  readOnly = false,
  showMinimap = false,
  fontSize = 14,
  lineNumbers = "on",
  wordWrap = "on",
  theme = "vs-dark",
  onValidationChange,
  onValidationStateChange, // New prop for validation state
}) => {
  return (
    <div key={`${textareaId}-keys`} className="prism-editor-wrapper">
      <MonacoCodeEditor
        key={`${textareaId}-key`}
        type={type}
        placeholder={placeholder}
        onValueChange={onValueChange}
        defaultValue={defaultValue}
        value={value}
        textareaId={textareaId}
        height={height}
        readOnly={readOnly}
        showMinimap={showMinimap}
        fontSize={fontSize}
        lineNumbers={lineNumbers}
        wordWrap={wordWrap}
        theme={theme}
        onValidationChange={onValidationChange}
        onValidationStateChange={onValidationStateChange}
      />
    </div>
  );
};

export default EditorSnippet;
