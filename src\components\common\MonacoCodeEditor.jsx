import React, { useRef, useEffect, useState } from "react";
import Editor from "@monaco-editor/react";
import { message } from "antd";
import {
  globalValidationManager,
  ValidationUtils,
  ErrorDisplayUtils,
} from "../../utils/validationUtils.js";

const MonacoCodeEditor = ({
  type = "html",
  placeholder = "Enter your code here...",
  onValueChange,
  defaultValue = "",
  value = "",
  textareaId = "monaco-editor",
  height = "410px",
  readOnly = false,
  showMinimap = false,
  fontSize = 14,
  lineNumbers = "on",
  wordWrap = "on",
  theme = "vs-dark",
  onValidationChange,
  onValidationStateChange, // New prop to track validation state
}) => {
  const editorRef = useRef(null);
  const monacoRef = useRef(null);
  const validationManagerRef = useRef(null);
  const [isEditorReady, setIsEditorReady] = useState(false);
  const [validationErrors, setValidationErrors] = useState([]);
  const [validationState, setValidationState] = useState({
    isValid: true,
    hasErrors: false,
    hasWarnings: false,
    errorCount: 0,
    warningCount: 0,
  });

  // Get language from type using ValidationUtils
  const getLanguage = ValidationUtils.getLanguage;

  // Cleanup function
  useEffect(() => {
    return () => {
      // Cleanup validation manager when component unmounts
      if (validationManagerRef.current) {
        validationManagerRef.current.dispose();
        validationManagerRef.current = null;
      }
    };
  }, []);

  // Handle editor mount with new global validation system
  const handleEditorDidMount = (editor, monaco) => {
    editorRef.current = editor;
    monacoRef.current = monaco;
    setIsEditorReady(true);

    // Initialize global validation manager
    const validationManager = globalValidationManager.initializeEditor(
      monaco,
      editor,
      getLanguage(type),
      {
        editorId: textareaId,
        onValidationChange: (markers) => {
          setValidationErrors(markers);
          if (onValidationChange) {
            onValidationChange(markers);
          }
        },
        onValidationStateChange: (state) => {
          setValidationState(state);
          if (onValidationStateChange) {
            onValidationStateChange(state);
          }
        },
      }
    );

    // Store validation manager reference for cleanup
    validationManagerRef.current = validationManager;

    // Add keyboard shortcuts
    ValidationUtils.addKeyboardShortcuts(monaco, editor);

    // Set save callback if needed
    ValidationUtils.setSaveCallback(editor, () => {
      // Custom save logic can be added here
      console.log("Save triggered");
    });
  };

  // Handle value change
  const handleChange = (newValue) => {
    if (onValueChange) {
      onValueChange(newValue || "");
    }
    // Note: Validation is now handled by the marker system in handleEditorDidMount
  };

  // Format document function
  const formatDocument = () => {
    if (editorRef.current) {
      editorRef.current.getAction("editor.action.formatDocument").run();
    }
  };

  // Validate code function for external use
  const validateCode = () => {
    if (!isEditorReady || !editorRef.current || !monacoRef.current) {
      return { isValid: false, errors: ["Editor not ready"] };
    }

    const model = editorRef.current.getModel();
    if (!model) {
      return { isValid: false, errors: ["No model available"] };
    }

    // Use ValidationUtils to validate code
    return ValidationUtils.validateCode(monacoRef.current, model);
  };

  // Expose methods for external use
  useEffect(() => {
    if (isEditorReady && editorRef.current) {
      // Attach methods to the editor instance for external access
      editorRef.current.formatDocument = formatDocument;
      editorRef.current.validateCode = validateCode;
    }
  }, [isEditorReady]);

  const editorOptions = {
    selectOnLineNumbers: true,
    roundedSelection: false,
    readOnly: readOnly,
    cursorStyle: "line",
    automaticLayout: true,
    fontSize: fontSize,
    lineNumbers: lineNumbers,
    wordWrap: wordWrap,
    minimap: {
      enabled: showMinimap,
    },
    scrollBeyondLastLine: false,
    folding: true,
    lineDecorationsWidth: 5,
    lineNumbersMinChars: 2,
    glyphMargin: false,
    contextmenu: true,
    mouseWheelZoom: true,
    formatOnPaste: true,
    formatOnType: true,
    autoIndent: "advanced",
    bracketPairColorization: {
      enabled: true,
    },
    guides: {
      bracketPairs: true,
      indentation: true,
    },
    suggest: {
      showKeywords: true,
      showSnippets: true,
      showFunctions: true,
      showConstructors: true,
      showFields: true,
      showVariables: true,
      showClasses: true,
      showStructs: true,
      showInterfaces: true,
      showModules: true,
      showProperties: true,
      showEvents: true,
      showOperators: true,
      showUnits: true,
      showValues: true,
      showConstants: true,
      showEnums: true,
      showEnumMembers: true,
      showColors: true,
      showFiles: true,
      showReferences: true,
      showFolders: true,
      showTypeParameters: true,
    },
  };

  return (
    <div className="monaco-editor-wrapper">
      <Editor
        height={height}
        language={getLanguage(type)}
        value={value}
        defaultValue={defaultValue}
        onChange={handleChange}
        onMount={handleEditorDidMount}
        onValidate={(markers, same) => {
          console.log("Validation errors:", markers, same);
          if (onValidationChange) {
            onValidationChange(markers, type);
          }
        }}
        theme={theme}
        className="monaco-editor tw-py-2"
        options={editorOptions}
        loading={
          <div className="tw-flex tw-items-center tw-justify-center tw-h-full tw-w-full tw-bg-gray-900 tw-text-white">
            <div className="tw-text-center">
              <div className="tw-animate-spin tw-rounded-full tw-h-8 tw-w-8 tw-border-b-2 tw-border-white tw-mx-auto tw-mb-2"></div>
              <div>Loading Editor...</div>
            </div>
          </div>
        }
      />

      {/* Validation Error Display */}
      {validationErrors.length > 0 && (
        <div className="tw-my-2 tw-mx-2 tw-text-sm">
          {validationErrors
            // .filter((error) => error.severity === 8)
            .slice(0, 3) // Show only first 3 errors
            .map((error, index) => (
              <div key={index} className="tw-text-red-500 tw-mb-1">
                Line {error.startLineNumber}: {error.message}
              </div>
            ))}
          {
            // .filter((error) => error.severity === 8)
            validationErrors.length > 3 && (
              <div className="tw-text-red-500 tw-text-xs">
                {" "}
                {
                  // .filter((error) => error.severity === 8)
                  validationErrors.length - 3
                }{" "}
                more errors
              </div>
            )
          }
        </div>
      )}
    </div>
  );
};

export default MonacoCodeEditor;
