import React, {
  useCallback,
  useMemo,
  useRef,
  useState,
  useEffect,
} from "react";
import SearchBar from "../../common/SearchBar";
import { Button, Input, message, Tag, Divider, Spin } from "antd";
import JoditTextEditor from "../../common/JoditTextEditor";
import mammoth from "mammoth";
import {
  addAtPath,
  deleteAtPath,
  parseContent,
  updateAtPath,
} from "../../../util/functions";
import { deepMerge } from "../../FeaturePage/components/function";
import { prePareContent } from "../../../utils/Content";
import { Trash2, GripVertical } from "lucide-react";

const { TextArea } = Input;

const ContentScrollBar = ({
  contentJSON,
  setContentJSON,
  saving,
  setReset,
  templateObj = {},
  dynamicContentForm = {},
  isImportDoc = false,
  isLoading = false,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [importing, setImporting] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isAddingItem, setIsAddingItem] = useState(false);
  const [isUpdatingContent, setIsUpdatingContent] = useState(false);
  const fileInputRef = useRef(null);
  const fileInputExportRef = useRef(null);
  const originalValuesRef = useRef(new Map());

  // Helper function to check if content has actually changed
  const hasContentChanged = useCallback((key, newValue) => {
    const originalValue = originalValuesRef.current.get(key);
    return originalValue !== newValue;
  }, []);

  // Helper function to store original value
  const storeOriginalValue = useCallback((key, value) => {
    originalValuesRef.current.set(key, value);
  }, []);

  // Clear original values when contentJSON changes significantly
  useEffect(() => {
    // Clear the original values map when contentJSON changes
    // This ensures we don't have stale references
    originalValuesRef.current.clear();
  }, [contentJSON]);

  const handleAddItem = useCallback((path, key) => {
    setIsAddingItem(true);
    try {
      // setContentJSON((prev) => addAtPath(prev, path));
      addItemHandler(path, key);
      message.success("Item added");
    } catch (e) {
      console.error("Error in handleAddItem:", e);
      message.error("Failed to add item");
    } finally {
      // Add a small delay to show the loading state
      setTimeout(() => {
        setIsAddingItem(false);
      }, 500);
    }
  }, []);

  const handleSearch = useCallback((value) => {
    // console.log(value);
    setSearchTerm(value);
  }, []);

  const handleDelete = useCallback((path, fieldKey = null) => {
    try {
      setContentJSON((prev) => deleteAtPath(prev, path, fieldKey));
      message.success("Item deleted successfully");
    } catch (e) {
      console.error("Error in handleDelete:", e);
      message.error("Failed to delete item");
    }
  }, []);
  const contentChangeHandler = ({ variableName, value, e, path }) => {
    // console.log(
    //   { variableName, value: e.target.value, path },
    //   "contentChangeHandler"
    // );
    if (path?.length < 2) return console.error("path is less than 2");

    const editorKey = [...path, variableName].join("_");

    // Check if content has actually changed
    if (!hasContentChanged(editorKey, value)) {
      return; // No change, skip update
    }

    setIsUpdatingContent(true);
    setContentJSON((prev) => {
      // console.log(prev, "prev");
      const newValue = JSON.parse(JSON.stringify(prev));
      let current = newValue;
      path?.map((key) => {
        current = current[key] || {};
      });
      current[variableName] = value;
      return newValue;
    });
    setReset((prev) => !prev);

    // Update the stored original value
    storeOriginalValue(editorKey, value);

    // Reset loading state after content update
    setTimeout(() => {
      setIsUpdatingContent(false);
    }, 300);
  };

  const addItemHandler = (path, key) => {
    setIsUpdatingContent(true);
    setContentJSON((prev) => {
      // console.log(prev, "prev");
      const newValue = JSON.parse(JSON.stringify(prev));
      let currentpage = templateObj?.pages?.find(
        (page) => page.name?.trim() == path[0]?.trim()
      );
      let currentCom = currentpage?.components?.find(
        (com) => com.name?.trim() == path[1]?.trim()
      );
      let current = newValue;
      path?.map((key) => {
        current = current[key] || {};
      });
      const intialPlaceholder = {};

      // Add safety check for repeatedComponent
      if (currentCom?.repeatedComponent?.placeholders) {
        currentCom.repeatedComponent.placeholders.map((placeholder) => {
          intialPlaceholder[placeholder] = "";
        });
      } else {
        console.warn(
          `repeatedComponent or placeholders not found for component: ${currentCom?.name}`
        );
        // Create a basic placeholder structure if repeatedComponent is missing
        intialPlaceholder["content"] = "";
      }

      current[key].push(intialPlaceholder);
      return newValue;
    });
    setReset((prev) => !prev);

    // Reset loading state after content update
    setTimeout(() => {
      setIsUpdatingContent(false);
    }, 300);
  };

  const deleteItemHandler = (path, index) => {
    setContentJSON((prev) => {
      // console.log(prev, "prev");
      const newValue = JSON.parse(JSON.stringify(prev));
      let current = newValue;
      path?.map((key) => {
        current = current[key] || {};
      });
      current.splice(index, 1);
      // console.log({ newValue }, "newValue");
      return newValue;
    });
    setReset((prev) => !prev);
  };

  const buildItemList = (data, path = []) => {
    if (Array.isArray(data)) {
      return data.map((item, index) => (
        <div key={[...path, index].join("_")} className="tw-mb-4">
          <div className="tw-flex tw-items-center tw-justify-between tw-mb-2">
            <div className="tw-flex tw-items-center tw-space-x-2">
              <GripVertical className="tw-w-4 tw-h-4 tw-text-gray-400" />
              <span className="tw-font-medium tw-text-gray-700">
                Position {index + 1}
              </span>
            </div>
            <Button
              type="text"
              danger
              size="small"
              icon={<Trash2 className="tw-w-4 tw-h-4" />}
              onClick={() => deleteItemHandler([...path], index)}
            />
          </div>
          {typeof item === "object" && item !== null ? (
            <div className="tw-ml-4">
              {buildItemList(item, [...path, index])}
            </div>
          ) : (
            <JoditTextEditor
              value={item}
              key={[...path, index].join("_")}
              height="100"
              placeholder="Enter your content here..."
              onBlur={(newContent) => {
                const editorKey = [...path, index].join("_");

                // Store original value on first render if not already stored
                if (!originalValuesRef.current.has(editorKey)) {
                  storeOriginalValue(editorKey, item);
                }

                // Check if content has actually changed
                if (!hasContentChanged(editorKey, newContent)) {
                  return; // No change, skip update
                }

                setIsUpdatingContent(true);
                setContentJSON((prev) =>
                  updateAtPath(prev, [...path, index], () => newContent)
                );

                // Update the stored original value
                storeOriginalValue(editorKey, newContent);

                setTimeout(() => {
                  setIsUpdatingContent(false);
                }, 300);
              }}
            />
          )}
        </div>
      ));
    } else if (typeof data === "object" && data !== null) {
      return Object.keys(data).map((key) => (
        <div key={[...path, key].join("_")} className="tw-mb-4">
          <div className="tw-flex tw-items-center tw-justify-between tw-mb-2">
            <div className="tw-flex tw-items-center tw-space-x-2">
              <GripVertical className="tw-w-4 tw-h-4 tw-text-gray-400" />
              <span className="tw-font-medium tw-text-gray-700">{key}</span>
              {Array.isArray(data[key]) && (
                <Tag className="tw-p-1 tw-px-2 tw-rounded-xl" color="purple">
                  Repeat
                </Tag>
              )}
            </div>
          </div>
          {typeof data[key] === "object" && data[key] !== null ? (
            <>
              <div className="tw-ml-4">
                {buildItemList(data[key], [...path, key])}
              </div>
              {Array.isArray(data[key]) && (
                <div className="tw-mt-2 tw-w-full tw-flex tw-justify-center tw-items-center">
                  <Button
                    type="dashed"
                    className="tw-w-full"
                    onClick={() => handleAddItem([...path], key)}
                    loading={isAddingItem}
                    disabled={isAddingItem || isUpdatingContent}
                  >
                    {isAddingItem ? "Adding Item..." : "+ Add Item"}
                  </Button>
                </div>
              )}
            </>
          ) : (
            <JoditTextEditor
              value={data[key]}
              height="100"
              placeholder="Enter your content here..."
              key={[...path, key].join("_")}
              onBlur={(newContent) => {
                const editorKey = [...path, key].join("_");

                // Store original value on first render if not already stored
                if (!originalValuesRef.current.has(editorKey)) {
                  storeOriginalValue(editorKey, data[key]);
                }

                // Check if content has actually changed
                if (!hasContentChanged(editorKey, newContent)) {
                  return; // No change, skip update
                }

                contentChangeHandler({
                  variableName: key,
                  value: newContent,
                  path,
                });
              }}
            />
          )}
        </div>
      ));
    } else {
      // ✅ Primitive leaf node → directly render input
      return (
        <JoditTextEditor
          value={data}
          height="100"
          key={path.join("_")}
          placeholder="Enter your content here..."
          onBlur={(newContent) => {
            const editorKey = path.join("_");

            // Store original value on first render if not already stored
            if (!originalValuesRef.current.has(editorKey)) {
              storeOriginalValue(editorKey, data);
            }

            // Check if content has actually changed
            if (!hasContentChanged(editorKey, newContent)) {
              return; // No change, skip update
            }

            setIsUpdatingContent(true);
            setContentJSON((prev) =>
              updateAtPath(prev, path, () => newContent)
            );

            // Update the stored original value
            storeOriginalValue(editorKey, newContent);

            setTimeout(() => {
              setIsUpdatingContent(false);
            }, 300);
          }}
        />
      );
    }
  };

  const handleImportJSON = () => {
    if (fileInputRef.current) fileInputRef.current.click();
  };

  const onFileChange = async (e) => {
    const file = e.target.files?.[0];
    if (!file) return;
    setImporting(true);
    try {
      const text = await file.text();
      const parsed = JSON.parse(text);
      if (!parsed || typeof parsed !== "object" || Array.isArray(parsed)) {
        throw new Error("Invalid JSON structure. Expected an object at root.");
      }
      // console.log(parsed, "parsed");
      setContentJSON((pr) => {
        // console.log(deepMerge(pr, parsed), "deepMerge", parsed, pr);
        return deepMerge(pr, parsed);
      });
      message.success("JSON imported successfully");
    } catch (err) {
      console.error("Import JSON error:", err);
      message.error("Failed to import JSON. Please check the file.");
    } finally {
      setImporting(false);
      setReset((prev) => !prev);
      // Reset input value to re-trigger change event if same file is selected again
      if (fileInputRef.current) fileInputRef.current.value = "";
    }
  };

  const filteredContent = useMemo(() => {
    if (!contentJSON) return {};
    if (!searchTerm) return contentJSON;

    const lowercasedTerm = searchTerm.toLowerCase();
    return Object.keys(contentJSON)
      .filter((key) => key.toLowerCase().includes(lowercasedTerm))
      .reduce((obj, key) => {
        obj[key] = contentJSON[key];
        return obj;
      }, {});
  }, [contentJSON, searchTerm]);

  const itemList = useMemo(() => {
    const dataToDisplay = searchTerm ? filteredContent : contentJSON;
    if (!dataToDisplay) return [];

    // Group by pages first
    const pages = Object.keys(dataToDisplay).map((pageKey) => {
      const pageData = dataToDisplay[pageKey];
      const components = Object.keys(pageData).map((componentKey) => ({
        key: `${pageKey}_${componentKey}`,
        label: componentKey,
        extra: Array.isArray(pageData[componentKey]),
        content: pageData[componentKey],
        isComponent: true,
      }));

      return {
        key: pageKey,
        label: pageKey,
        isPage: true,
        components: components,
      };
    });

    return pages;
  }, [contentJSON, searchTerm, filteredContent, handleDelete, handleAddItem]);

  const handleImportDOC = () => {
    if (fileInputExportRef.current) fileInputExportRef.current.click();
  };

  const handleFileSelect = async (e) => {
    e.preventDefault();

    const selectedFile = e.target.files?.[0];
    if (!selectedFile) return;

    // setFile(selectedFile);
    // setError(null)
    setIsProcessing(true);

    try {
      const arrayBuffer = await selectedFile.arrayBuffer();
      const result = await mammoth.convertToHtml({ arrayBuffer });
      // console.log(result);
      if (result?.messages && result?.messages?.length > 0) {
        console.warn("Conversion warnings:", result?.messages);
      }

      // setHtmlContent(result.value);

      // Parse the HTML content into structured JSON
      const structuredData = parseContent(result.value);
      // const structuredData = parser.parse();
      // console.log(structuredData, "structuredData");
      const content = prePareContent(structuredData, dynamicContentForm);
      // console.log(content, "content");
      setContentJSON((pr) => {
        // console.log(pr, content, deepMerge(pr, content), "deepMerge");
        return deepMerge(pr, content);
      });
      // setParsedData(structuredData);
    } catch (err) {
      console.error("Error processing file:", err);
      // setError(`Error processing file: ${err.message}`)
    } finally {
      setIsProcessing(false);
      setReset((prev) => !prev);
    }
  };

  return (
    <div className="tw-h-full tw-flex tw-flex-col tw-overflow-hidden">
      {/* Header Section - Fixed */}
      <div className="tw-flex-shrink-0 tw-space-y-3 tw-px-4">
        <div className="">
          <SearchBar type="page" handleSearch={(e) => handleSearch(e)} />
        </div>
        <div className="tw-flex tw-space-x-4 tw-w-full tw-justify-between tw-items-center">
          <Button
            type="primary"
            size="large"
            onClick={handleImportJSON}
            disabled={saving || importing}
            className="tw-px-6 tw-w-full tw-h-10 tw-flex tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
          >
            Import JSON
          </Button>
          {isImportDoc && (
            <Button
              type="primary"
              size="large"
              onClick={handleImportDOC}
              disabled={saving || isProcessing}
              className="tw-px-6 tw-w-full tw-h-10 tw-flex tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
            >
              Import Doc
            </Button>
          )}
        </div>
        {/* Hidden file input for JSON import */}
        <input
          type="file"
          accept="application/json,.json"
          ref={fileInputRef}
          onChange={onFileChange}
          style={{ display: "none" }}
        />
        <input
          ref={fileInputExportRef}
          type="file"
          accept=".docx"
          onChange={handleFileSelect}
          className="file-input"
          style={{ display: "none" }}
        />
      </div>

      {/* Scrollable Content Section */}
      <div className="tw-flex tw-flex-1  tw-min-h-0 tw-p-4 tw-relative">
        {/* Loading Overlay */}
        {(isAddingItem || isUpdatingContent) && (
          <div
            aria-live="polite"
            aria-busy="true"
            className="tw-absolute tw-inset-0 tw-h-full tw-opacity-75 tw-bg-white tw-flex tw-items-center tw-justify-center tw-z-10 tw-rounded-lg "
          >
            <div className="tw-text-center">
              <Spin size="large" />
              <p className="tw-mt-2 tw-text-gray-600">
                {isAddingItem
                  ? "Adding new position..."
                  : "Updating content..."}
              </p>
            </div>
          </div>
        )}
        <div
          className={`tw-flex-1 tw-overflow-y-auto tw-min-h-0 tw-p-4 tw-relative ${
            isAddingItem || isUpdatingContent ? " tw-pointer-events-none" : ""
          }`}
        >
          {itemList?.length ? (
            <div className="tw-space-y-8">
              {itemList?.map((page, pageIndex) => (
                <div key={page.key} className="tw-space-y-4">
                  {/* Page Header */}
                  {/* <div className="tw-bg-blue-50 tw-rounded-lg tw-border tw-border-blue-200 tw-p-4">
                  <div className="tw-flex tw-items-center tw-justify-between tw-space-x-3">
                    <GripVertical className="tw-w-5 tw-h-5 tw-text-blue-600" />
                    <h2 className="tw-text-xl tw-font-bold ">{page.label}</h2>
                    <Tag className="tw-p-1 tw-px-3 tw-rounded-xl" color="blue">
                      Page
                    </Tag>
                  </div>
                </div> */}
                  <div className="tw-flex tw-items-center tw-justify-between tw-space-x-3">
                    <h2 className="tw-text-xl tw-font-bold ">
                      {page?.label || ""}
                    </h2>
                    {/* <Tag className="tw-p-1 tw-px-3 tw-rounded-xl" color="blue">
                    Page
                  </Tag> */}
                  </div>

                  {/* Components within Page */}
                  <div className="tw-space-y-4">
                    {page?.components?.map((component, componentIndex) => (
                      <div key={component.key} className="tw-space-y-4">
                        {/* Component Header */}
                        <div className="tw-bg-white tw-rounded-lg tw-border tw-border-gray-200 tw-p-4">
                          <div className="tw-flex tw-items-center tw-justify-between tw-mb-4">
                            <div className="tw-w-full tw-flex tw-items-center tw-justify-between tw-space-x-3">
                              {/* <GripVertical className="tw-w-4 tw-h-4 tw-text-gray-400" /> */}
                              <h3 className="tw-text-lg tw-font-bold tw-text-gray-800">
                                {component.label}
                              </h3>
                              {/* {component.extra !== undefined && (
                              <Tag
                                className="tw-p-1 tw-px-2 tw-rounded-xl"
                                color={component.extra ? "purple" : "blue"}
                              >
                                {component.extra ? "Repeat" : "Single"}
                              </Tag>
                            )} */}
                            </div>
                          </div>

                          {/* Component Content */}
                          <div className="tw-space-y-4">
                            {buildItemList(component?.content, [
                              page.key,
                              component.label,
                            ])}
                          </div>
                        </div>

                        {/* Component-level Divider - only show if not the last component */}
                        {componentIndex < page.components.length - 1 && (
                          <Divider className="tw-my-4 tw-border-gray-300" />
                        )}
                      </div>
                    ))}
                  </div>

                  {/* Page-level Divider - only show if not the last page */}
                  {pageIndex < itemList.length - 1 && (
                    <Divider className="tw-my-8 tw-border-blue-300 tw-border-2" />
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="tw-text-center tw-py-8">
              <p className="tw-text-gray-500 tw-mb-2">
                No content fields found
              </p>
              <p className="tw-text-sm tw-text-gray-400">
                Add content fields to see them here
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ContentScrollBar;
