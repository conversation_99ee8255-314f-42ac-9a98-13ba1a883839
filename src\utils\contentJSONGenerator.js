/**
 * Global Content JSON Generator Utility
 * Handles complex nested object structures for content generation
 */

/**
 * Generate content JSON structure from form data with nested objects
 * @param {Object} formData - The form data containing pages/templates structure
 * @param {Object} options - Configuration options
 * @returns {Object} - Generated nested content JSON structure
 */
export const generateContentJSON = (formData, options = {}) => {
  const {
    defaultValue = "",
    includeRepeatedComponents = true,
    mergeWithExisting = true,
    existingContent = {},
    customProcessors = {}
  } = options;

  let keydata = {};

  // Handle different data structures
  const dataSource = formData.pages || formData.components || [];
  dataSource.forEach((item) => {
    const itemKey = item?.name || item?.title || item?.id;
    if (!itemKey) return;

    // Check for placeholders in different possible locations
    const placeholderSources = [
      item?.pagePlaceHolder,
      item?.placeholders,
      item?.componentPlaceholders,
      item?.content
    ].filter(Boolean);

    if (placeholderSources.length > 0) {
      let itemData = {};

      placeholderSources.forEach((source) => {
        if (Array.isArray(source)) {
          // Handle array of placeholder objects
          source.forEach((category) => {
            const categoryData = processCategoryData(category, {
              defaultValue,
              includeRepeatedComponents,
              customProcessors
            });

            const categoryKey = category?.categoryName || category?.name || category?.type;
            if (categoryKey && Object.keys(categoryData).length > 0) {
              itemData[categoryKey] = categoryData;
            }
          });
        } else if (typeof source === 'object') {
          // Handle direct object structure
          const processedData = processDirectObject(source, {
            defaultValue,
            includeRepeatedComponents,
            customProcessors
          });
          itemData = { ...itemData, ...processedData };
        }
      });

      if (Object.keys(itemData).length > 0) {
        keydata[itemKey] = itemData;
      }
    }
  });

  console.log("Generated keydata:", keydata);

  // Merge with existing content if specified
  if (mergeWithExisting && existingContent) {
    // return deepMerge(existingContent, keydata);
    return { [formData?.name]: deepMerge(existingContent, keydata) };
  }

  // return keydata;
  return { [formData?.name]: keydata };
};

/**
 * Process category data with placeholders and repeated components
 * @param {Object} category - Category object containing placeholders
 * @param {Object} options - Processing options
 * @returns {Object} - Processed category data
 */
const processCategoryData = (category, options) => {
  const { defaultValue, includeRepeatedComponents, customProcessors } = options;
  let placeholdersData = {};

  // Process normal placeholders
  if (category?.placeholders && Array.isArray(category.placeholders)) {
    category.placeholders.forEach((ph) => {
      if (typeof ph === 'string') {
        placeholdersData[ph] = defaultValue;
      } else if (typeof ph === 'object' && ph.key) {
        placeholdersData[ph.key] = ph.defaultValue || defaultValue;
      }
    });
  }

  // Process repeated components
  if (includeRepeatedComponents && category?.repeatedComponentId && category?.repeatedComponent) {
    const repeatedData = processRepeatedComponent(category, { defaultValue, customProcessors });
    placeholdersData = { ...placeholdersData, ...repeatedData };
  }

  // Apply custom processors if provided
  if (customProcessors && typeof customProcessors === 'object') {
    Object.keys(customProcessors).forEach(processorKey => {
      if (typeof customProcessors[processorKey] === 'function') {
        placeholdersData = customProcessors[processorKey](placeholdersData, category) || placeholdersData;
      }
    });
  }

  return placeholdersData;
};

/**
 * Process repeated component data
 * @param {Object} category - Category containing repeated component info
 * @param {Object} options - Processing options
 * @returns {Object} - Processed repeated component data
 */
const processRepeatedComponent = (category, options) => {
  const { defaultValue, customProcessors } = options;
  let repeatedData = {};

  // Handle multiple repeated component names
  const repeatedNames = Array.isArray(category?.repeatedComponentName)
    ? category.repeatedComponentName
    : [category?.repeatedComponentName].filter(Boolean);

  repeatedNames.forEach(repeatedKey => {
    if (repeatedKey) {
      const repeatedPlaceholders = category.repeatedComponent?.placeholders || [];

      // Create initial structure with one empty item
      const initialItem = repeatedPlaceholders.reduce((acc, ph) => {
        if (typeof ph === 'string') {
          acc[ph] = defaultValue;
        } else if (typeof ph === 'object' && ph.key) {
          acc[ph.key] = ph.defaultValue || defaultValue;
        }
        return acc;
      }, {});

      // Apply custom processors for repeated components
      if (customProcessors?.repeatedComponent) {
        const processedItem = customProcessors.repeatedComponent(initialItem, category);
        repeatedData[repeatedKey] = [processedItem || initialItem];
      } else {
        repeatedData[repeatedKey] = [initialItem];
      }
    }
  });

  return repeatedData;
};

/**
 * Process direct object structure (non-array placeholders)
 * @param {Object} source - Direct object source
 * @param {Object} options - Processing options
 * @returns {Object} - Processed object data
 */
const processDirectObject = (source, options) => {
  const { defaultValue, customProcessors } = options;
  let processedData = {};

  Object.keys(source).forEach(key => {
    const value = source[key];

    if (Array.isArray(value)) {
      // Handle array values
      processedData[key] = value.map(item => {
        if (typeof item === 'string') {
          return defaultValue;
        } else if (typeof item === 'object') {
          return processDirectObject(item, options);
        }
        return item;
      });
    } else if (typeof value === 'object' && value !== null) {
      // Handle nested objects
      processedData[key] = processDirectObject(value, options);
    } else if (typeof value === 'string') {
      // Handle string placeholders
      processedData[key] = defaultValue;
    } else {
      // Keep other types as-is
      processedData[key] = value;
    }
  });

  // Apply custom processors
  if (customProcessors?.directObject) {
    processedData = customProcessors.directObject(processedData, source) || processedData;
  }

  return processedData;
};

/**
 * Deep merge two objects
 * @param {Object} target - Target object
 * @param {Object} source - Source object to merge
 * @returns {Object} - Merged object
 */
const deepMerge = (target, source) => {
  const result = { ...target };

  Object.keys(source).forEach(key => {
    if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
      if (result[key] && typeof result[key] === 'object' && !Array.isArray(result[key])) {
        result[key] = deepMerge(result[key], source[key]);
      } else {
        result[key] = source[key];
      }
    } else {
      result[key] = source[key];
    }
  });

  return result;
};

/**
 * Add new item to repeated component array
 * @param {Object} contentJSON - Current content JSON
 * @param {string} pageName - Page name
 * @param {string} categoryName - Category name
 * @param {string} repeatedKey - Repeated component key
 * @param {Object} newItem - New item to add
 * @returns {Object} - Updated content JSON
 */
export const addRepeatedItem = (contentJSON, pageName, categoryName, repeatedKey, newItem = {}) => {
  const updatedContent = { ...contentJSON };

  if (updatedContent[pageName] && updatedContent[pageName][categoryName] && updatedContent[pageName][categoryName][repeatedKey]) {
    if (Array.isArray(updatedContent[pageName][categoryName][repeatedKey])) {
      updatedContent[pageName][categoryName][repeatedKey].push(newItem);
    }
  }

  return updatedContent;
};

/**
 * Remove item from repeated component array
 * @param {Object} contentJSON - Current content JSON
 * @param {string} pageName - Page name
 * @param {string} categoryName - Category name
 * @param {string} repeatedKey - Repeated component key
 * @param {number} index - Index to remove
 * @returns {Object} - Updated content JSON
 */
export const removeRepeatedItem = (contentJSON, pageName, categoryName, repeatedKey, index) => {
  const updatedContent = { ...contentJSON };

  if (updatedContent[pageName] && updatedContent[pageName][categoryName] && updatedContent[pageName][categoryName][repeatedKey]) {
    if (Array.isArray(updatedContent[pageName][categoryName][repeatedKey])) {
      updatedContent[pageName][categoryName][repeatedKey].splice(index, 1);
    }
  }

  return updatedContent;
};

/**
 * Update specific field in content JSON
 * @param {Object} contentJSON - Current content JSON
 * @param {string} path - Dot notation path (e.g., "page1.category1.field1")
 * @param {any} value - New value
 * @returns {Object} - Updated content JSON
 */
export const updateContentField = (contentJSON, path, value) => {
  const updatedContent = { ...contentJSON };
  const pathArray = path.split('.');

  let current = updatedContent;
  for (let i = 0; i < pathArray.length - 1; i++) {
    if (!current[pathArray[i]]) {
      current[pathArray[i]] = {};
    }
    current = current[pathArray[i]];
  }

  current[pathArray[pathArray.length - 1]] = value;
  return updatedContent;
};

/**
 * Get nested value from content JSON
 * @param {Object} contentJSON - Content JSON object
 * @param {string} path - Dot notation path
 * @param {any} defaultValue - Default value if path not found
 * @returns {any} - Found value or default
 */
export const getContentField = (contentJSON, path, defaultValue = "") => {
  const pathArray = path.split('.');
  let current = contentJSON;

  for (const key of pathArray) {
    if (current && typeof current === 'object' && current.hasOwnProperty(key)) {
      current = current[key];
    } else {
      return defaultValue;
    }
  }

  return current;
};

/**
 * Generate content structure for specific data types
 * @param {Object} formData - Form data
 * @param {string} dataType - Type of data ('pages', 'templates', 'components')
 * @param {Object} options - Generation options
 * @returns {Object} - Generated content structure
 */
export const generateContentByType = (formData, dataType = 'pages', options = {}) => {
  const modifiedFormData = {
    [dataType]: formData[dataType] || formData.components || []
  };

  return generateContentJSON(modifiedFormData, options);
};

/**
 * Validate content JSON structure
 * @param {Object} contentJSON - Content JSON to validate
 * @returns {Object} - Validation result with errors and warnings
 */
export const validateContentJSON = (contentJSON) => {
  const errors = [];
  const warnings = [];

  if (!contentJSON || typeof contentJSON !== 'object') {
    errors.push('Content JSON must be an object');
    return { isValid: false, errors, warnings };
  }

  // Check for empty values
  const checkEmptyValues = (obj, path = '') => {
    Object.keys(obj).forEach(key => {
      const currentPath = path ? `${path}.${key}` : key;
      const value = obj[key];

      if (value === '' || value === null || value === undefined) {
        warnings.push(`Empty value at path: ${currentPath}`);
      } else if (typeof value === 'object' && !Array.isArray(value)) {
        checkEmptyValues(value, currentPath);
      } else if (Array.isArray(value) && value.length === 0) {
        warnings.push(`Empty array at path: ${currentPath}`);
      }
    });
  };

  checkEmptyValues(contentJSON);

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

export default {
  generateContentJSON,
  addRepeatedItem,
  removeRepeatedItem,
  updateContentField,
  getContentField,
  generateContentByType,
  validateContentJSON
};
