import { useLocation } from 'react-router-dom';
import { getPageHeader } from '../utils/headerUtils';

/**
 * Custom hook for managing page headers
 * Safely integrates with existing title/subtitle prop system
 * Provides automatic header detection based on current route
 */
export const usePageHeader = (manualTitle = null, manualSubtitle = null) => {
    const location = useLocation();

    // Priority system: manual props override auto-detection
    // This ensures existing functionality is preserved
    if (manualTitle !== null || manualSubtitle !== null) {
        return {
            title: manualTitle || '',
            subtitle: manualSubtitle || '',
            source: 'manual' // For debugging
        };
    }

    // Auto-detect from route only when no manual props provided
    const autoHeader = getPageHeader(location.pathname);

    if (autoHeader) {
        return {
            title: autoHeader.title,
            subtitle: autoHeader.subtitle,
            source: 'auto' // For debugging
        };
    }

    // Fallback to default values (preserves existing behavior)
    return {
        title: 'Component Library',
        subtitle: 'Build and manage reusable components for your websites',
        source: 'default' // For debugging
    };
};

/**
 * Alternative hook for components that want only auto-detection
 * without fallback to defaults (returns null if no match)
 */
export const useAutoPageHeader = () => {
    const location = useLocation();
    return getPageHeader(location.pathname);
};
