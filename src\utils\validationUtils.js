/**
 * Validation Utility Functions
 * Helper functions for Monaco Editor validation management
 */

import {
    getValidationState,
    formatValidationErrors,
    validateContent,
    validatePlaceholders,
    initializeMonacoValidation,
    createPlaceholderCompletionProvider,
    createPlaceholderHoverProvider,
    createPlaceholderDiagnosticProvider
} from "./monacoValidation.js";

/**
 * Monaco Editor Validation Manager
 * Centralized validation management for Monaco Editor instances
 */
export class ValidationManager {
    constructor() {
        this.providers = new Map();
        this.disposables = new Map();
        this.validationProfile = "default";
    }

    /**
     * Initialize validation for a Monaco Editor instance
     */
    initializeEditor(monaco, editorInstance, language, options = {}) {
        const editorId = options.editorId || `editor-${Date.now()}`;

        // Initialize Monaco language services
        const config = initializeMonacoValidation(monaco, this.validationProfile);

        // Register custom providers for this editor
        this.registerProviders(monaco, language, editorId);

        // Set up validation listeners
        this.setupValidationListeners(monaco, editorInstance, editorId, options);

        return {
            editorId,
            config,
            dispose: () => this.disposeEditor(editorId)
        };
    }

    /**
     * Register custom providers for language features
     */
    registerProviders(monaco, language, editorId) {
        const providers = [];

        // Register completion provider for placeholders
        const completionProvider = monaco.languages.registerCompletionItemProvider(
            language,
            createPlaceholderCompletionProvider(monaco)
        );
        providers.push(completionProvider);

        // Register hover provider for placeholders
        const hoverProvider = monaco.languages.registerHoverProvider(
            language,
            createPlaceholderHoverProvider(monaco)
        );
        providers.push(hoverProvider);

        // Register code lens provider for placeholders
        const codeLensProvider = monaco.languages.registerCodeLensProvider(
            language,
            createPlaceholderDiagnosticProvider(monaco)
        );
        providers.push(codeLensProvider);

        this.providers.set(editorId, providers);
    }

    /**
     * Set up validation listeners for an editor
     */
    setupValidationListeners(monaco, editor, editorId, options) {
        const model = editor.getModel();
        if (!model) return;

        const disposables = [];

        // Listen for content changes
        const contentDisposable = model.onDidChangeContent(() => {
            this.validateModel(monaco, model, options);
        });
        disposables.push(contentDisposable);

        // Listen for marker changes
        const markerDisposable = monaco.editor.onDidChangeMarkers((uris) => {
            if (uris.includes(model.uri)) {
                this.handleMarkerChanges(monaco, model, options);
            }
        });
        disposables.push(markerDisposable);

        this.disposables.set(editorId, disposables);

        // Initial validation
        this.validateModel(monaco, model, options);
    }

    /**
     * Validate a Monaco model
     */
    validateModel(monaco, model, options = {}) {
        const content = model.getValue();
        const language = model.getLanguageId();

        // Get comprehensive validation including HTML, placeholders, etc.
        const customDiagnostics = validateContent(content, language, monaco);

        // Set custom markers for all validation types
        if (customDiagnostics.length > 0) {
            monaco.editor.setModelMarkers(model, "custom-validation", customDiagnostics);
        } else {
            // Clear custom markers if no errors
            monaco.editor.setModelMarkers(model, "custom-validation", []);
        }

        // Debounce validation callback
        if (options.onValidationChange) {
            clearTimeout(this.validationTimeout);
            this.validationTimeout = setTimeout(() => {
                this.handleMarkerChanges(monaco, model, options);
            }, 300);
        }
    }

    /**
     * Handle marker changes
     */
    handleMarkerChanges(monaco, model, options) {
        const allMarkers = monaco.editor.getModelMarkers({
            resource: model.uri
        });

        const validationState = getValidationState(allMarkers);

        // Call validation callbacks
        if (options.onValidationChange) {
            options.onValidationChange(allMarkers);
        }

        if (options.onValidationStateChange) {
            options.onValidationStateChange(validationState);
        }
    }

    /**
     * Set validation profile
     */
    setValidationProfile(profile) {
        this.validationProfile = profile;
    }

    /**
     * Dispose of editor resources
     */
    disposeEditor(editorId) {
        // Dispose providers
        const providers = this.providers.get(editorId);
        if (providers) {
            providers.forEach(provider => provider.dispose());
            this.providers.delete(editorId);
        }

        // Dispose listeners
        const disposables = this.disposables.get(editorId);
        if (disposables) {
            disposables.forEach(disposable => disposable.dispose());
            this.disposables.delete(editorId);
        }
    }

    /**
     * Dispose all resources
     */
    dispose() {
        this.providers.forEach((providers) => {
            providers.forEach(provider => provider.dispose());
        });
        this.providers.clear();

        this.disposables.forEach((disposables) => {
            disposables.forEach(disposable => disposable.dispose());
        });
        this.disposables.clear();

        clearTimeout(this.validationTimeout);
    }
}

/**
 * Global validation manager instance
 */
export const globalValidationManager = new ValidationManager();

/**
 * Validation utility functions
 */
export const ValidationUtils = {
    /**
     * Get validation state from markers
     */
    getValidationState,

    /**
     * Format validation errors for display
     */
    formatValidationErrors,

    /**
     * Validate code and return result
     */
    validateCode: (monaco, model) => {
        if (!monaco || !model) {
            return { isValid: false, errors: ["Editor not ready"] };
        }

        const markers = monaco.editor.getModelMarkers({
            resource: model.uri
        });

        const validationState = getValidationState(markers);
        const formattedErrors = formatValidationErrors(markers);

        return {
            isValid: validationState.isValid,
            hasErrors: validationState.hasErrors,
            hasWarnings: validationState.hasWarnings,
            errors: validationState.errors.map(error =>
                `Line ${error.startLineNumber}: ${error.message}`
            ),
            warnings: validationState.warnings.map(warning =>
                `Line ${warning.startLineNumber}: ${warning.message}`
            ),
            displayItems: formattedErrors.displayItems,
            hasMore: formattedErrors.hasMore,
            moreCount: formattedErrors.moreCount,
            totalIssues: validationState.totalIssues
        };
    },

    /**
     * Update validation rules for a language
     */
    updateValidationRules: (monaco, language, rules) => {
        try {
            switch (language.toLowerCase()) {
                case "html":
                    monaco.languages.html.htmlDefaults.setOptions(rules);
                    break;
                case "css":
                    monaco.languages.css.cssDefaults.setOptions(rules);
                    break;
                case "javascript":
                case "js":
                    if (rules.diagnosticsOptions) {
                        monaco.languages.typescript.javascriptDefaults.setDiagnosticsOptions(
                            rules.diagnosticsOptions
                        );
                    }
                    if (rules.compilerOptions) {
                        monaco.languages.typescript.javascriptDefaults.setCompilerOptions(
                            rules.compilerOptions
                        );
                    }
                    break;
                default:
                    console.warn(`Unsupported language for validation rules: ${language}`);
            }
            return true;
        } catch (error) {
            console.error("Error updating validation rules:", error);
            return false;
        }
    },

    /**
     * Get language from file type
     */
    getLanguage: (type) => {
        switch (type.toLowerCase()) {
            case "html":
                return "html";
            case "css":
                return "css";
            case "js":
            case "javascript":
                return "javascript";
            case "json":
                return "json";
            default:
                return "html";
        }
    },

    /**
     * Create editor options with validation settings
     */
    createEditorOptions: (baseOptions = {}) => {
        return {
            selectOnLineNumbers: true,
            roundedSelection: false,
            cursorStyle: "line",
            automaticLayout: true,
            scrollBeyondLastLine: false,
            folding: true,
            lineDecorationsWidth: 5,
            lineNumbersMinChars: 2,
            glyphMargin: false,
            contextmenu: true,
            mouseWheelZoom: true,
            formatOnPaste: true,
            formatOnType: true,
            autoIndent: "advanced",
            bracketPairColorization: {
                enabled: true
            },
            guides: {
                bracketPairs: true,
                indentation: true
            },
            suggest: {
                showKeywords: true,
                showSnippets: true,
                showFunctions: true,
                showConstructors: true,
                showFields: true,
                showVariables: true,
                showClasses: true,
                showStructs: true,
                showInterfaces: true,
                showModules: true,
                showProperties: true,
                showEvents: true,
                showOperators: true,
                showUnits: true,
                showValues: true,
                showConstants: true,
                showEnums: true,
                showEnumMembers: true,
                showColors: true,
                showFiles: true,
                showReferences: true,
                showFolders: true,
                showTypeParameters: true
            },
            ...baseOptions
        };
    },

    /**
     * Format document using Monaco's built-in formatter
     */
    formatDocument: (editor) => {
        if (editor && editor.getAction) {
            const formatAction = editor.getAction("editor.action.formatDocument");
            if (formatAction) {
                formatAction.run();
                return true;
            }
        }
        return false;
    },

    /**
     * Add keyboard shortcuts for common actions
     */
    addKeyboardShortcuts: (monaco, editor) => {
        // Format document: Ctrl+Shift+F
        editor.addCommand(
            monaco.KeyMod.CtrlCmd | monaco.KeyMod.Shift | monaco.KeyCode.KeyF,
            () => {
                ValidationUtils.formatDocument(editor);
            }
        );

        // Save: Ctrl+S (can be customized)
        editor.addCommand(
            monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS,
            () => {
                // Trigger save callback if provided
                const saveCallback = editor._saveCallback;
                if (saveCallback) {
                    saveCallback();
                }
            }
        );
    },

    /**
     * Set save callback for editor
     */
    setSaveCallback: (editor, callback) => {
        editor._saveCallback = callback;
    }
};

/**
 * Error display utilities
 */
export const ErrorDisplayUtils = {
    /**
     * Create error display component data
     */
    createErrorDisplay: (markers, maxDisplay = 3) => {
        const formatted = formatValidationErrors(markers, maxDisplay);

        return {
            items: formatted.displayItems.map((marker, index) => ({
                id: `error-${index}`,
                type: marker.severity === 8 ? "error" : "warning",
                line: marker.startLineNumber,
                message: marker.message,
                source: marker.source || "validation"
            })),
            hasMore: formatted.hasMore,
            moreCount: formatted.moreCount,
            totalCount: markers.length
        };
    },

    /**
     * Get error summary text
     */
    getErrorSummary: (validationState) => {
        const { errorCount, warningCount } = validationState;

        if (errorCount === 0 && warningCount === 0) {
            return "No issues found";
        }

        const parts = [];
        if (errorCount > 0) {
            parts.push(`${errorCount} error${errorCount > 1 ? "s" : ""}`);
        }
        if (warningCount > 0) {
            parts.push(`${warningCount} warning${warningCount > 1 ? "s" : ""}`);
        }

        return parts.join(", ");
    },

    /**
     * Get CSS classes for error display
     */
    getErrorClasses: (type) => {
        const baseClasses = "tw-text-sm tw-mb-1";

        switch (type) {
            case "error":
                return `${baseClasses} tw-text-red-500`;
            case "warning":
                return `${baseClasses} tw-text-yellow-500`;
            default:
                return `${baseClasses} tw-text-gray-500`;
        }
    }
};

export default ValidationUtils;
