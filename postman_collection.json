{"info": {"_postman_id": "f55b1f30-0bad-43d4-b608-4d1e113feec5", "name": "API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "41568307", "_collection_link": "https://lively-rocket-882530.postman.co/workspace/DreamBuilder~dd0e464e-4d88-4afa-a23a-887019796b79/collection/43320885-f55b1f30-0bad-43d4-b608-4d1e113feec5?action=share&source=collection_link&creator=41568307"}, "item": [{"name": "users", "item": [{"name": "Create", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{  \n    \"name\": \"jeel\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"<PERSON><PERSON><PERSON>\",\n  \"role\": \"admin\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/users", "host": ["{{URL}}"], "path": ["admin", "users"]}}, "response": []}, {"name": "Create first admin", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"<PERSON><PERSON><PERSON><PERSON>\",\n  \"role\": \"admin\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/users/firstAdmin", "host": ["{{URL}}"], "path": ["admin", "users", "firstAdmin"]}}, "response": []}, {"name": "GetOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/users/:id", "host": ["{{URL}}"], "path": ["admin", "users", ":id"], "variable": [{"key": "id", "value": "4"}]}}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/users/me", "host": ["{{URL}}"], "path": ["users", "me"]}}, "response": []}, {"name": "GetAll", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/users", "host": ["{{URL}}"], "path": ["admin", "users"], "query": [{"key": "search", "value": "sddsdsfsdds aaffdf", "disabled": true}]}}, "response": []}, {"name": "UpdateOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n//   \"name\": \"<PERSON><PERSON>\",\n//   \"email\": \"<EMAIL>\",\n  \"password\": \"<PERSON><PERSON><PERSON><PERSON>\"\n//   \"role\": \"\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/users/:id", "host": ["{{URL}}"], "path": ["admin", "users", ":id"], "variable": [{"key": "id", "value": "2"}]}}, "response": []}, {"name": "DeleteOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/users/:id", "host": ["{{URL}}"], "path": ["admin", "users", ":id"], "variable": [{"key": "id", "value": "24", "type": "string"}]}}, "response": []}, {"name": "login", "event": [{"listen": "test", "script": {"exec": ["pm.environment.set(\"JWT\", pm.response.json()?.token);"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n//testing-admin\n// \"email\": \"<EMAIL>\",\n// \"password\": \"Prashant\"\n\n// testing-content\n// \"email\": \"<EMAIL>\",\n// \"password\": \"<PERSON>riya<PERSON>\"\n\n    // admin\n//   \"email\": \"<EMAIL>\",\n//   \"password\": \"Bhavya\"\n\n// content-team\n\"email\": \"<EMAIL>\",\n  \"password\": \"<PERSON><PERSON><PERSON><PERSON>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/users/login", "host": ["{{URL}}"], "path": ["users", "login"]}}, "response": []}]}, {"name": "useractivitylogs", "item": [{"name": "Create", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"action\": \"BEwDa\",\n  \"entityType\": \"YZsgu\",\n  \"message\": \"Tp1qv\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/useractivitylogs", "host": ["{{URL}}"], "path": ["useractivitylogs"]}}, "response": []}, {"name": "GetOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/useractivitylogs/:id", "host": ["{{URL}}"], "path": ["useractivitylogs", ":id"], "variable": [{"key": "id"}]}}, "response": []}, {"name": "GetAll", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/useractivitylogs", "host": ["{{URL}}"], "path": ["useractivitylogs"]}}, "response": []}, {"name": "UpdateOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n  \"action\": \"Vb1Ws\",\n  \"entityType\": \"S4qT0\",\n  \"message\": \"SPCSo\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/useractivitylogs/:id", "host": ["{{URL}}"], "path": ["useractivitylogs", ":id"], "variable": [{"key": "id"}]}}, "response": []}, {"name": "DeleteOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/useractivitylogs/:id", "host": ["{{URL}}"], "path": ["useractivitylogs", ":id"], "variable": [{"key": "id"}]}}, "response": []}]}, {"name": "categories", "item": [{"name": "Create", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Repeat\",\n  \"description\": \"All repetead components\",\n  \"colour\": \"#1FC16B\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/categories", "host": ["{{URL}}"], "path": ["admin", "categories"]}}, "response": []}, {"name": "GetOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/categories/:id", "host": ["{{URL}}"], "path": ["admin", "categories", ":id"], "variable": [{"key": "id", "value": "29"}]}}, "response": []}, {"name": "GetAll", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/categories", "host": ["{{URL}}"], "path": ["admin", "categories"], "query": [{"key": "search", "value": "sfffsgsffd", "disabled": true}, {"key": "limit", "value": "", "disabled": true}, {"key": "page", "value": "0", "disabled": true}, {"key": "id", "value": "4", "disabled": true}]}}, "response": []}, {"name": "GetAll categories for dropdown", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/categories/getCategoriesForDropdown?search=😀", "host": ["{{URL}}"], "path": ["admin", "categories", "getCategoriesForDropdown"], "query": [{"key": "search", "value": "😀"}, {"key": "limit", "value": "1", "disabled": true}, {"key": "page", "value": "0", "disabled": true}]}}, "response": []}, {"name": "UpdateOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n//   \"name\": \"<PERSON>er\",\n// \"isActive\":true\n  \"colour\": \"#10B981\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/categories/:id", "host": ["{{URL}}"], "path": ["admin", "categories", ":id"], "variable": [{"key": "id", "value": "4"}]}}, "response": []}, {"name": "DeleteOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/categories/:id", "host": ["{{URL}}"], "path": ["admin", "categories", ":id"], "variable": [{"key": "id", "value": "1"}]}}, "response": []}]}, {"name": "components", "item": [{"name": "Create", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"name\": \"component to delete\",\n  \"categoryId\":4,\n  \"html\": \"<div data-component=\\\"common_cta_section\\\"><div\\n  class=\\\"dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px] dw-px-8 dw-hidden mdx:dw-block\\\"\\n>\\n  <div class=\\\"dw-flex dw-w-full dw-rounded-[50px] dw-overflow-hidden\\\">\\n    <!-- Left Column -->\\n    <div\\n      class=\\\"dw-w-[70%] dw-bg-theme-mainColorShade lgm:dw-py-[88px] lgm:dw-px-[80px] dw-py-[56px] dw-px-[48px] mdl:dw-py-[72px] mdl:dw-px-[48px]\\\"\\n    >\\n      <p\\n        class=\\\"mdl:dw-text-[30px] dw-text-2xl dw-font-heading dw-font-semibold dw-text-white\\\"\\n      >\\n        ${cta_title}\\n      </p>\\n\\n      <div class=\\\"dw-flex dw-flex-col mdl:dw-flex-row dw-gap-4 dw-mt-6\\\">\\n        <!-- Call Us Button -->\\n        <a\\n          href=\\\"tel:${contact_number}\\\"\\n          class=\\\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center mdx:dw-gap-[10px] dw-gap-[35px] dw-pe-[10px] lgs:dw-text-[18px] dw-text-[16px] dw-ps-[3px] dw-py-[3px] dw-bg-white dw-rounded-full hover:dw-bg-theme-main hover:dw-text-white dw-border-[2px] dw-border-white hover:dw-border-theme-main dw-transition\\\"\\n        >\\n          <svg\\n            xmlns=\\\"http://www.w3.org/2000/svg\\\"\\n            width=\\\"42\\\"\\n            height=\\\"42\\\"\\n            viewBox=\\\"0 0 36 36\\\"\\n            fill=\\\"none\\\"\\n          >\\n            <rect width=\\\"36\\\" height=\\\"36\\\" rx=\\\"18\\\" class=\\\"dw-fill-theme-main\\\" />\\n            <path\\n              d=\\\"M27.7388 22.4138C27.5716 23.6841 26.9477 24.8502 25.9837 25.6942C25.0196 26.5382 23.7813 27.0023 22.5 27.0001C15.0563 27.0001 9.00001 20.9438 9.00001 13.5001C8.99771 12.2188 9.4619 10.9805 10.3059 10.0164C11.1499 9.05234 12.3159 8.42847 13.5863 8.2613C13.9075 8.22208 14.2328 8.2878 14.5136 8.44865C14.7944 8.60951 15.0157 8.85687 15.1444 9.1538L17.1244 13.5741V13.5854C17.2229 13.8127 17.2636 14.0608 17.2428 14.3077C17.222 14.5545 17.1404 14.7924 17.0053 15.0001C16.9884 15.0254 16.9706 15.0488 16.9519 15.0722L15 17.386C15.7022 18.8129 17.1947 20.2922 18.6403 20.9963L20.9222 19.0547C20.9446 19.0359 20.9681 19.0184 20.9925 19.0022C21.2 18.8639 21.4387 18.7794 21.687 18.7565C21.9353 18.7336 22.1854 18.7729 22.4147 18.871L22.4269 18.8766L26.8434 20.8557C27.1409 20.9839 27.3889 21.205 27.5503 21.4858C27.7116 21.7667 27.7778 22.0922 27.7388 22.4138Z\\\"\\n              fill=\\\"white\\\"\\n            ></path>\\n          </svg>\\n          ${contact_number_button_label}\\n        </a>\\n\\n        <!-- Get Quote Button -->\\n        <a\\n                   class=\\\"pop dw-flex dw-font-medium dw-w-fit dw-items-center dw-gap-[6px] lgs:dw-text-[18px] dw-text-[16px] dw-pe-[10px] dw-border-[2px] hover:dw-bg-theme-main hover:dw-text-white dw-rounded-full dw-bg-white dw-border-white hover:dw-border-theme-main dw-transition\\\"\\n        >\\n          <svg\\n            xmlns=\\\"http://www.w3.org/2000/svg\\\"\\n            id=\\\"Layer_1\\\"\\n            data-name=\\\"Layer 1\\\"\\n            viewBox=\\\"0 0 72 84\\\"\\n            width=\\\"48\\\"\\n            height=\\\"48\\\"\\n            class=\\\"dw-fill-white\\\"\\n          >\\n            <circle cx=\\\"36\\\" cy=\\\"42\\\" r=\\\"36\\\" class=\\\"dw-fill-theme-main\\\"></circle>\\n            <g transform=\\\"translate(18, 18) scale(0.8)\\\">\\n              <path\\n                d=\\\"M21.45 7.46c2.09-.11 4.18.1 6.23.44l.13.22c-.51 1.39-2.17.52-3.38.48-9.62-.33-17.23 4.17-21.45 12.76-.11.23-.47 1.43-.53 1.49-.23.26-1.08.01-1.14-.18-.08-.25 1.18-2.92 1.4-3.33C6.35 12.59 13.75 7.86 21.44 7.45Z\\\"\\n                class=\\\"cls-3\\\"\\n              ></path>\\n              <path\\n                d=\\\"M22.15 9.65c.52-.02 4.24.09 4.25.48-.74 1.21-.54.76-1.62.75-7.73-.07-14.38 2.32-18.55 9.17-.33.55-1.51 3.39-1.673.51-.13.1-1.1-.11-1.14-.26-.04-.13.91-2.17 1.05-2.46C7.73 14.38 14.91 9.88 22.15 9.66Z\\\"\\n                class=\\\"cls-3\\\"\\n              ></path>\\n              <path\\n                d=\\\"M.13 29.21c.06-.03.97 0 1.01.04.09.13-.04 2.64 0 3.07.56 6.25 4.19 12.15 9.25 15.75.45.32 2.88 1.55 2.94 1.71.02.05-.36.77-.39.92C5.95 47.64.79 40.17.09 32.59c-.04-.45-.2-3.25.04-3.38\\\"\\n                class=\\\"cls-1\\\"\\n              ></path>\\n              <path\\n                d=\\\"M21.71 11.93c1.05-.06 2.11.02 3.16.09.26.1-.47 1.03-.53 1.05-.37.13-1.97-.06-2.54 0-5.7.56-10.71 3.36-13.73 8.29-.29.48-1.09 2.47-1.23 2.63-.21.23-.9.01-1.14-.18 2.33-6.64 8.99-11.48 16.01-11.89Z\\\"\\n                class=\\\"cls-3\\\"\\n              ></path>\\n              <path\\n                d=\\\"M2.42 29.21c.07-.03.96 0 1.01.04-.56 6.56 2.81 12.97 8.03 16.8.39.29 2.65 1.47 2.68 1.62 0 .05-.37.76-.39.92-5.39-2.46-9.76-8.03-11.01-13.82-.15-.7-.78-5.36-.31-5.57Z\\\"\\n                class=\\\"cls-1\\\"\\n              ></path>\\n              <path\\n                d=\\\"m4.61 29.21 1.01.04c-.47 6.78 3.33 13.04 9.21 16.23l-.31 1.01C8.17 43.38 3.99 36.33 4.61 29.21M2.94 24.56s.54.12.66.13c-.19.44-.08 3.28-.22 3.38-.03.02-.62.02-.66-.04-.02-.46-.01-3.28.22-3.46ZM5.31 25.09l.66.13c-.14.37-.1 2.77-.22 2.85-.03.02-.62.02-.66-.04 0-.98-.06-1.98.22-2.94\\\"\\n                class=\\\"cls-1\\\"\\n              ></path>\\n              <path\\n                d=\\\"M35.05 0 25.01 27.68l8.95.26-23.39 32.5c3.39-9.18 7.02-18.39 9.82-27.72l-8.73-.13z\\\"\\n                style=\\\"fill: #fdfeff\\\"\\n              ></path>\\n              <path\\n                d=\\\"M29.52 8.95c.26 0 0 .31-.04.39-4.07 7.61-10.02 14.97-14.3 22.54l-1.71.13z\\\"\\n                style=\\\"fill: #fefefe\\\"\\n              ></path>\\n            </g>\\n          </svg>\\n          ${get_quote_button_text}\\n        </a>\\n      </div>\\n\\n      <div class=\\\"dw-mt-6 dw-flex dw-items-center dw-space-x-4\\\">\\n        <img\\n          src=\\\"${_img-${cta-review-photo}}\\\"\\n          alt=\\\"${_alt-${cta-review-photo}}\\\"\\n          class=\\\"mdl:dw-w-[20%] dw-w-[35%] dw-h-auto dw-object-contain\\\"\\n        />\\n        <p class=\\\"dw-text-white mdl:dw-text-[16px] dw-text-sm dw-font-medium\\\">\\n          ${available_24_7_text}\\n        </p>\\n      </div>\\n    </div>\\n\\n    <!-- Right Column -->\\n    <div\\n      class=\\\"mdl:dw-w-[30%] dw-w-[35%] dw-bg-theme-main dw-flex dw-items-center\\\"\\n    >\\n      <img\\n        src=\\\"${_img-${cta-bus-rental-image}}\\\"\\n        alt=\\\"${_alt-${cta-bus-rental-image}}\\\"\\n        class=\\\"dw-w-full dw-h-auto dw-object-contain\\\"\\n      />\\n    </div>\\n  </div>\\n</div></div>\",\n  \"css\": \"\",\n  \"js\": \"\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/components", "host": ["{{URL}}"], "path": ["admin", "components"]}}, "response": []}, {"name": "GetOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/components/:id", "host": ["{{URL}}"], "path": ["admin", "components", ":id"], "variable": [{"key": "id", "value": "17"}]}}, "response": []}, {"name": "GetAll", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/components", "host": ["{{URL}}"], "path": ["admin", "components"], "query": [{"key": "categoryId", "value": "", "disabled": true}, {"key": "search", "value": "herosection", "disabled": true}, {"key": "page", "value": "2", "disabled": true}, {"key": "limit", "value": "10", "disabled": true}]}}, "response": []}, {"name": "GetAll for page", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/components/getForPage?categoryId=7", "host": ["{{URL}}"], "path": ["admin", "components", "getForPage"], "query": [{"key": "categoryId", "value": "7"}, {"key": "page", "value": "1", "disabled": true}, {"key": "limit", "value": "", "disabled": true}]}}, "response": []}, {"name": "UpdateOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n//   \"name\": \"Hero section10\"\n//   \"isActive\":true,\n//   \"categoryId\":3\n//   \"html\": \"<!DOCTYPE html>\\n<html lang=\\\"en\\\">\\n  <head>\\n    <meta charset=\\\"UTF-8\\\" />\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\" />\\n    <title>Document</title>\\n    <style>\\n      @font-face {\\n        font-family: Inter;\\n        font-style: normal;\\n        font-weight: 400;\\n        font-display: swap;\\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\\n          format(\\\"woff2\\\");\\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n      }\\n      @font-face {\\n        font-family: Inter;\\n        font-style: normal;\\n        font-weight: 500;\\n        font-display: swap;\\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\\n          format(\\\"woff2\\\");\\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n      }\\n      @font-face {\\n        font-family: Inter;\\n        font-style: normal;\\n        font-weight: 700;\\n        font-display: swap;\\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\\n          format(\\\"woff2\\\");\\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n      }\\n      @font-face {\\n        font-family: Phudu;\\n        font-style: normal;\\n        font-weight: 600;\\n        font-display: swap;\\n        src: url(https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2)\\n          format(\\\"woff2\\\");\\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n      }\\n      @font-face {\\n        font-family: Phudu;\\n        font-style: normal;\\n        font-weight: 700;\\n        font-display: swap;\\n        src: url(https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2)\\n          format(\\\"woff2\\\");\\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n      }\\n    </style>\\n    <script src=\\\"https://cdn.tailwindcss.com\\\"></script>\\n    <script>\\n      tailwind.config = {\\n        content: [\\\"./*.html\\\"],\\n        // prefix: \\\"dw-\\\",\\n        theme: {\\n          extend: {\\n            colors: {\\n              body: {\\n                bg: \\\"#ffffff\\\", // --body-bg-color\\n                text: \\\"#333333\\\", // --body-text-color\\n              },\\n              theme: {\\n                main: \\\"#d5232b\\\", // --theme-main-color\\n                mainColorShade: \\\"#C41421\\\",\\n                hoverBox: \\\"rgba(82, 164, 206, 0.43)\\\", // --theme-box-hover-color\\n                cardBorder: \\\"#e0e0e0\\\",\\n                cardBg: \\\"#fafafa\\\",\\n                divider: \\\"#eee\\\",\\n                lowOpacityTextColor: \\\"#111827\\\",\\n              },\\n            },\\n            fontFamily: {\\n              heading: [\\\"Phudu\\\", \\\"sans-serif\\\"], // --theme-heading-font\\n              text: [\\\"Inter\\\", \\\"sans-serif\\\"], // --theme-text-font\\n            },\\n            screens: {\\n              xs: \\\"200px\\\",\\n              smx: \\\"376px\\\",\\n              smm: \\\"567px\\\",\\n              mdx: \\\"768px\\\",\\n              mdl: \\\"993px\\\",\\n              lgx: \\\"1171px\\\",\\n              lgs: \\\"1221px\\\",\\n              lgm: \\\"1281px\\\",\\n              lgl: \\\"1361px\\\",\\n              xlx: \\\"1400px\\\",\\n              xl1: \\\"1441px\\\",\\n              xl2: \\\"1600px\\\",\\n              xxl: \\\"1900px\\\",\\n            },\\n          },\\n        },\\n        plugins: [],\\n      };\\n    </script>\\n  </head>\\n  <body class=\\\"bg-body-bg text-body-text font-text\\\">\\n    <div\\n      class=\\\"my-16 mx-auto w-full px-6 xl1:max-w-[1440px] lgl:max-w-[1300px] lgl:px-6 lgs:max-w-[1200px] lgx:max-w-[1100px] mdl:max-w-[1080px] mdl:px-6 mdx:max-w-[1000px] smm:max-w-[940px] smm:px-6 smx:max-w-[700px] smx:px-6\\\"\\n    >\\n      <div class=\\\"flex justify-center\\\">\\n        <h2\\n          class=\\\"xl1:mx-[395px] font-semibold lgm:text-[48px] lgx:text-[50px] mdx:text-[46px] text-[26px] xl1:leading-[60px] lgx:leading-[46px] uppercase text-center pb-[25px] font-heading\\\"\\n        >\\n          Our Clifton Bus Rental Options\\n        </h2>\\n      </div>\\n      <div\\n        class=\\\"grid grid-cols-1 mdx:grid-cols-2 lgs:grid-cols-3 mdx:gap-8 gap-4 xl1:gap-[50px] mb-4\\\"\\n      >\\n        <div\\n          class=\\\"border border-theme-cardBorder rounded-[32px] bg-theme-cardBg xl1:p-[3rem_2rem_1.5rem] p-[2.5rem_1rem_1rem] min-h-full relative\\\"\\n        >\\n          <span\\n            class=\\\"absolute z-10 bg-body-bg text-theme-lowOpacityTextColor font-normal border shadow-sm text-[14px] px-[15px] py-[8px] ml-[10px] -translate-y-[20px] rounded-full\\\"\\n          >\\n            $180 – $500+ per hour\\n          </span>\\n          <img\\n            src=\\\"https://busrentalcompanyclifton.com/wp-content/uploads/50-passenger-charter-bus-clifton.jpeg\\\"\\n            class=\\\"rounded-[16px] w-full h-auto\\\"\\n            alt=\\\"50 passenger charter bus clifton\\\"\\n          />\\n          <img\\n            src=\\\"https://busrentalcompanyclifton.com/wp-content/uploads/50-passenger-charter-bus-interior-clifton.jpeg\\\"\\n            class=\\\"rounded-[16px] w-full h-auto mt-4\\\"\\n            alt=\\\"50 passenger charter bus interior clifton\\\"\\n          />\\n          <h3\\n            class=\\\"font-semibold lgm:text-[24px] lgx:text-[22px] text-[20px] leading-[32px] uppercase tracking-[-0.176px] text-center pt-[32px] pb-[25px] font-heading mb-2\\\"\\n          >\\n            <a\\n              href=\\\"https://busrentalcompanyclifton.com/50-passenger-charter-bus-rental/\\\"\\n            >\\n              50 Passenger Charter Bus\\n            </a>\\n          </h3>\\n          <div class=\\\"flex justify-center gap-4 pb-[5px]\\\">\\n            <a\\n              href=\\\"https://busrentalcompanyclifton.com/50-passenger-charter-bus-rental/\\\"\\n              class=\\\"bg-body-text text-white border-2 border-body-text rounded-full px-[16px] py-[13px] text-[18px] leading-[24px] font-medium tracking-[-0.24px] hover:border-theme-main hover:bg-theme-main\\\"\\n            >\\n              View This Bus\\n            </a>\\n            <a\\n              href=\\\"/get-quote/\\\"\\n              class=\\\"bg-theme-main text-white border-2 border-theme-main rounded-full px-[16px] py-[13px] inline-flex items-center justify-center text-[18px] leading-[24px] font-medium tracking-[-0.24px]\\\"\\n            >\\n              Get a Quote\\n            </a>\\n          </div>\\n        </div>\\n        \\n      </div>\\n    </div>\\n    <!-- <div\\n      class=\\\"mx-auto w-full xl1:max-w-[1440px] lgl:max-w-[1300px] lgs:max-w-[1200px] lgx:max-w-[1100px] mdl:max-w-[1080px] mdx:max-w-[1000px] smm:max-w-[940px] smx:max-w-[700px]\\\"\\n    ></div> -->\\n  </body>\\n</html>\",\n//   \"css\": \"\",\n  \"js\": \"const busOptions = [\\n  {\\n    title: \\\"50 Passenger Charter Bus\\\",\\n    price: \\\"$180 – $500+ per hour\\\",\\n    image1: \\\"https://busrentalcompanyclifton.com/wp-content/uploads/50-passenger-charter-bus-clifton.jpeg\\\",\\n    image2: \\\"https://busrentalcompanyclifton.com/wp-content/uploads/50-passenger-charter-bus-interior-clifton.jpeg\\\",\\n    link: \\\"https://busrentalcompanyclifton.com/50-passenger-charter-bus-rental/\\\"\\n  },\\n  {\\n    title: \\\"40 Passenger Party Bus\\\",\\n    price: \\\"$150 – $450+ per hour\\\",\\n    image1: \\\"https://busrentalcompanyclifton.com/wp-content/uploads/25-passenger-minibus-clifton.jpeg\\\",\\n    image2: \\\"https://busrentalcompanyclifton.com/wp-content/uploads/15-passenger-minibus-interior-clifton.jpeg\\\",\\n    link: \\\"https://busrentalcompanyclifton.com/40-passenger-party-bus-rental/\\\"\\n  },\\n  {\\n    title: \\\"Sprinter Van Rental\\\",\\n    price: \\\"$80 – $150+ per hour\\\",\\n    image1: \\\"https://busrentalcompanyclifton.com/wp-content/uploads/sprinter-van-with-driver-clifton.jpeg\\\",\\n    image2: \\\"https://busrentalcompanyclifton.com/wp-content/uploads/sprinter-van-with-driver-interior-clifton.jpeg\\\",\\n    link: \\\"https://busrentalcompanyclifton.com/sprinter-van-rental/\\\"\\n  }\\n];\\n\\nwindow.addEventListener(\\\"DOMContentLoaded\\\", () => {\\n  const container = document.querySelector(\\\".grid\\\");\\n\\n  busOptions.forEach(option => {\\n    const card = `\\n      <div class=\\\"border border-theme-cardBorder rounded-[32px] bg-theme-cardBg xl1:p-[3rem_2rem_1.5rem] p-[2.5rem_1rem_1rem] min-h-full relative\\\">\\n        <span class=\\\"absolute z-10 bg-body-bg text-theme-lowOpacityTextColor font-normal border shadow-sm text-[14px] px-[15px] py-[8px] ml-[10px] -translate-y-[20px] rounded-full\\\">\\n          ${option.price}\\n        </span>\\n        <img src=\\\"${option.image1}\\\" class=\\\"rounded-[16px] w-full h-auto\\\" alt=\\\"${option.title.toLowerCase()}\\\">\\n        <img src=\\\"${option.image2}\\\" class=\\\"rounded-[16px] w-full h-auto mt-4\\\" alt=\\\"${option.title.toLowerCase()} interior\\\">\\n        <h3 class=\\\"font-semibold lgm:text-[24px] lgx:text-[22px] text-[20px] leading-[32px] uppercase tracking-[-0.176px] text-center pt-[32px] pb-[25px] font-heading mb-2\\\">\\n          <a href=\\\"${option.link}\\\">${option.title}</a>\\n        </h3>\\n        <div class=\\\"flex justify-center gap-4 pb-[5px]\\\">\\n          <a href=\\\"${option.link}\\\" class=\\\"bg-body-text text-white border-2 border-body-text rounded-full px-[16px] py-[13px] text-[18px] leading-[24px] font-medium tracking-[-0.24px] hover:border-theme-main hover:bg-theme-main\\\">\\n            View This Bus\\n          </a>\\n          <a href=\\\"/get-quote/\\\" class=\\\"bg-theme-main text-white border-2 border-theme-main rounded-full px-[16px] py-[13px] inline-flex items-center justify-center text-[18px] leading-[24px] font-medium tracking-[-0.24px]\\\">\\n            Get a Quote\\n          </a>\\n        </div>\\n      </div>\\n    `;\\n\\n    container.insertAdjacentHTML(\\\"beforeend\\\", card);\\n  });\\n});\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/components/:id", "host": ["{{URL}}"], "path": ["admin", "components", ":id"], "variable": [{"key": "id", "value": "37"}]}}, "response": []}, {"name": "DeleteOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/components/:id", "host": ["{{URL}}"], "path": ["admin", "components", ":id"], "variable": [{"key": "id", "value": "3"}]}}, "response": []}]}, {"name": "componentversions", "item": [{"name": "Create", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n\"componentId\":2,\n  \"html\": \"<!DOCTYPE html>\\n<html lang=\\\"en\\\">\\n  <head>\\n    <meta charset=\\\"UTF-8\\\" />\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\" />\\n    <title>Document</title>\\n    <style>\\n      @font-face {\\n        font-family: Inter;\\n        font-style: normal;\\n        font-weight: 400;\\n        font-display: swap;\\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\\n          format(\\\"woff2\\\");\\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n      }\\n      @font-face {\\n        font-family: Inter;\\n        font-style: normal;\\n        font-weight: 500;\\n        font-display: swap;\\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\\n          format(\\\"woff2\\\");\\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n      }\\n      @font-face {\\n        font-family: Inter;\\n        font-style: normal;\\n        font-weight: 700;\\n        font-display: swap;\\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\\n          format(\\\"woff2\\\");\\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n      }\\n      @font-face {\\n        font-family: Phudu;\\n        font-style: normal;\\n        font-weight: 600;\\n        font-display: swap;\\n        src: url(https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2)\\n          format(\\\"woff2\\\");\\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n      }\\n      @font-face {\\n        font-family: Phudu;\\n        font-style: normal;\\n        font-weight: 700;\\n        font-display: swap;\\n        src: url(https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2)\\n          format(\\\"woff2\\\");\\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n      }\\n    </style>\\n    <script src=\\\"https://cdn.tailwindcss.com\\\"></script>\\n    <script>\\n      tailwind.config = {\\n        content: [\\\"./*.html\\\"],\\n        // prefix: \\\"dw-\\\",\\n        theme: {\\n          extend: {\\n            colors: {\\n              body: {\\n                bg: \\\"#ffffff\\\", // --body-bg-color\\n                text: \\\"#333333\\\", // --body-text-color\\n              },\\n              theme: {\\n                main: \\\"#d5232b\\\", // --theme-main-color\\n                mainColorShade: \\\"#C41421\\\",\\n                hoverBox: \\\"rgba(82, 164, 206, 0.43)\\\", // --theme-box-hover-color\\n                cardBorder: \\\"#e0e0e0\\\",\\n                cardBg: \\\"#fafafa\\\",\\n                divider: \\\"#eee\\\",\\n                lowOpacityTextColor: \\\"#111827\\\",\\n              },\\n            },\\n            fontFamily: {\\n              heading: [\\\"Phudu\\\", \\\"sans-serif\\\"], // --theme-heading-font\\n              text: [\\\"Inter\\\", \\\"sans-serif\\\"], // --theme-text-font\\n            },\\n            screens: {\\n              xs: \\\"200px\\\",\\n              smx: \\\"376px\\\",\\n              smm: \\\"567px\\\",\\n              mdx: \\\"768px\\\",\\n              mdl: \\\"993px\\\",\\n              lgx: \\\"1171px\\\",\\n              lgs: \\\"1221px\\\",\\n              lgm: \\\"1281px\\\",\\n              lgl: \\\"1361px\\\",\\n              xlx: \\\"1400px\\\",\\n              xl1: \\\"1441px\\\",\\n              xl2: \\\"1600px\\\",\\n              xxl: \\\"1900px\\\",\\n            },\\n          },\\n        },\\n        plugins: [],\\n      };\\n    </script>\\n  </head>\\n  <body class=\\\"bg-body-bg text-body-text font-text\\\">\\n    <div\\n      class=\\\"my-16 mx-auto w-full px-6 xl1:max-w-[1440px] lgl:max-w-[1300px] lgl:px-6 lgs:max-w-[1200px] lgx:max-w-[1100px] mdl:max-w-[1080px] mdl:px-6 mdx:max-w-[1000px] smm:max-w-[940px] smm:px-6 smx:max-w-[700px] smx:px-6\\\"\\n    >\\n      <div class=\\\"flex justify-center\\\">\\n        <h2\\n          class=\\\"xl1:mx-[395px] font-semibold lgm:text-[48px] lgx:text-[50px] mdx:text-[46px] text-[26px] xl1:leading-[60px] lgx:leading-[46px] uppercase text-center pb-[25px] font-heading\\\"\\n        >\\n          Our Clifton Bus Rental Options\\n        </h2>\\n      </div>\\n      <div\\n        class=\\\"grid grid-cols-1 mdx:grid-cols-2 lgs:grid-cols-3 mdx:gap-8 gap-4 xl1:gap-[50px] mb-4\\\"\\n      >\\n        <div\\n          class=\\\"border border-theme-cardBorder rounded-[32px] bg-theme-cardBg xl1:p-[3rem_2rem_1.5rem] p-[2.5rem_1rem_1rem] min-h-full relative\\\"\\n        >\\n          <span\\n            class=\\\"absolute z-10 bg-body-bg text-theme-lowOpacityTextColor font-normal border shadow-sm text-[14px] px-[15px] py-[8px] ml-[10px] -translate-y-[20px] rounded-full\\\"\\n          >\\n            $180 – $500+ per hour\\n          </span>\\n          <img\\n            src=\\\"https://busrentalcompanyclifton.com/wp-content/uploads/50-passenger-charter-bus-clifton.jpeg\\\"\\n            class=\\\"rounded-[16px] w-full h-auto\\\"\\n            alt=\\\"50 passenger charter bus clifton\\\"\\n          />\\n          <img\\n            src=\\\"https://busrentalcompanyclifton.com/wp-content/uploads/50-passenger-charter-bus-interior-clifton.jpeg\\\"\\n            class=\\\"rounded-[16px] w-full h-auto mt-4\\\"\\n            alt=\\\"50 passenger charter bus interior clifton\\\"\\n          />\\n          <h3\\n            class=\\\"font-semibold lgm:text-[24px] lgx:text-[22px] text-[20px] leading-[32px] uppercase tracking-[-0.176px] text-center pt-[32px] pb-[25px] font-heading mb-2\\\"\\n          >\\n            <a\\n              href=\\\"https://busrentalcompanyclifton.com/50-passenger-charter-bus-rental/\\\"\\n            >\\n              50 Passenger Charter Bus\\n            </a>\\n          </h3>\\n          <div class=\\\"flex justify-center gap-4 pb-[5px]\\\">\\n            <a\\n              href=\\\"https://busrentalcompanyclifton.com/50-passenger-charter-bus-rental/\\\"\\n              class=\\\"bg-body-text text-white border-2 border-body-text rounded-full px-[16px] py-[13px] text-[18px] leading-[24px] font-medium tracking-[-0.24px] hover:border-theme-main hover:bg-theme-main\\\"\\n            >\\n              View This Bus\\n            </a>\\n            <a\\n              href=\\\"/get-quote/\\\"\\n              class=\\\"bg-theme-main text-white border-2 border-theme-main rounded-full px-[16px] py-[13px] inline-flex items-center justify-center text-[18px] leading-[24px] font-medium tracking-[-0.24px]\\\"\\n            >\\n              Get a Quote\\n            </a>\\n          </div>\\n        </div>\\n        \\n      </div>\\n    </div>\\n    <!-- <div\\n      class=\\\"mx-auto w-full xl1:max-w-[1440px] lgl:max-w-[1300px] lgs:max-w-[1200px] lgx:max-w-[1100px] mdl:max-w-[1080px] mdx:max-w-[1000px] smm:max-w-[940px] smx:max-w-[700px]\\\"\\n    ></div> -->\\n  </body>\\n</html>\",\n  \"css\": \"\",\n  \"js\": \"\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/componentversions", "host": ["{{URL}}"], "path": ["admin", "componentversions"]}}, "response": []}, {"name": "get component by ids", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"ids\":[11,12,13,9,10,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/componentversions/get-by-ids", "host": ["{{URL}}"], "path": ["admin", "componentversions", "get-by-ids"]}}, "response": []}, {"name": "GetOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/componentversions/:id", "host": ["{{URL}}"], "path": ["admin", "componentversions", ":id"], "variable": [{"key": "id", "value": "22"}]}}, "response": []}, {"name": "GetAll", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/componentversions", "host": ["{{URL}}"], "path": ["componentversions"]}}, "response": []}, {"name": "UpdateOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n  \"version\": 6820941250166784,\n  \"html\": \"MsGFh\",\n  \"css\": \"qtjEj\",\n  \"js\": \"son91\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/componentversions/:id", "host": ["{{URL}}"], "path": ["componentversions", ":id"], "variable": [{"key": "id"}]}}, "response": []}, {"name": "DeleteOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/componentversions/:id", "host": ["{{URL}}"], "path": ["componentversions", ":id"], "variable": [{"key": "id"}]}}, "response": []}]}, {"name": "pages", "item": [{"name": "Create", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"name\": \"page to delete\",\n  \"urlSlug\": \"prices\",\n  \"metaTitle\": \"${company_name} Charter Bus Prices\",\n  \"metaDescription\": \"\",\n  \"customCss\": \"\",\n  \"customJs\": \"\",\n  \"headers\": \"\",\n  \"wrapperClass\": \"\",\n  \"componentData\":[\n  {\n    \"index\": 0,\n    \"name\": \"hero_section_content\",\n    \"class\": \"\",\n    \"type\": \"single\",\n    \"componentVersionId\": 33\n  },\n  {\n    \"index\": 1,\n    \"name\": \"common_buttons\",\n    \"class\": \"btn-primary\",\n    \"type\": \"single\",\n    \"componentVersionId\": 34\n  },\n  {\n    \"index\": 2,\n    \"name\": \"faq_section\",\n    \"class\": \"faq-style\",\n    \"type\": \"repeat\",\n    \"componentVersionId\": 35,\n    \"repeatComponents\": [\n      { \"componentVersionId\": 31, \"key\": \"{{sample}}\" }\n    ]\n  },\n  {\n    \"index\": 3,\n    \"name\": \"testimonial_cards\",\n    \"class\": \"card-grid\",\n    \"type\": \"single\",\n    \"componentVersionId\": 35\n  },\n  {\n    \"index\": 4,\n    \"name\": \"feature_highlights\",\n    \"class\": \"highlight-list\",\n    \"type\": \"repeat\",\n    \"componentVersionId\": 35,\n    \"repeatComponents\": [\n      { \"componentVersionId\": 24 }\n    ]\n  },\n  {\n    \"index\": 5,\n    \"name\": \"pricing_table\",\n    \"class\": \"pricing-style\",\n    \"type\": \"single\",\n    \"componentVersionId\": 36\n  },\n  {\n    \"index\": 6,\n    \"name\": \"team_section\",\n    \"class\": \"team-grid\",\n    \"type\": \"repeat\",\n    \"componentVersionId\": 35,\n    \"repeatComponents\": [\n      { \"componentVersionId\": 21, \"key\": \"{{sample}}\" },\n      { \"componentVersionId\": 22, \"key\": \"\" }\n    ]\n  },\n  {\n    \"index\": 7,\n    \"name\": \"contact_form\",\n    \"class\": \"form-styled\",\n    \"type\": \"single\",\n    \"componentVersionId\": 37\n  },\n  {\n    \"index\": 8,\n    \"name\": \"newsletter_signup\",\n    \"class\": \"signup-box\",\n    \"type\": \"single\",\n    \"componentVersionId\": 38\n  },\n  {\n    \"index\": 9,\n    \"name\": \"footer_links\",\n    \"class\": \"footer-class\",\n    \"type\": \"single\",\n    \"componentVersionId\": 39\n  }\n]\n\n//  \"componentData\": [\n//   { \"index\": 0, \"name\": \"hero_section_content\", \"class\": \"\", \"type\": \"single\", \"componentVersionId\": 33 },\n//   { \"index\": 1, \"name\": \"common_buttons\", \"class\": \"dw-mt-[20px] smm:dw-mt-[25px] mdx:dw-mt-[30px] dw-mb-[35px] smm:dw-mb-[40px] mdx:dw-mb-[40px] mdl:dw-mb-[50px] lgs:dw-mb-[50px] xl1:dw-mb-[60px]\", \"type\": \"single\", \"componentVersionId\": 34 },\n//   { \"index\": 2, \"name\": \"form_cmp\", \"class\": \"\", \"type\": \"single\", \"componentVersionId\": 35 },\n//   { \"index\": 3, \"name\": \"term_and_conditions1\", \"class\": \"dw-mt-[20px] smm:dw-mt-[25px] mdx:dw-mt-[30px]\", \"type\": \"single\", \"componentVersionId\": 28 },\n//   { \"index\": 4, \"name\": \"pricepage_cta\", \"class\": \"\", \"type\": \"single\", \"componentVersionId\": 36 },\n//   { \"index\": 5, \"name\": \"charter_bus\", \"class\": \"dw-mt-[35px] smm:dw-mt-[40px] mdx:dw-mt-[40px] mdl:dw-mt-[50px] lgs:dw-mt-[50px] xl1:dw-mt-[60px]\", \"type\": \"repeat\", \"componentVersionId\": 25, \"repeatComponentVersionId\": 26, \"repeatComponentKey\": \"{{key1}}\" },\n//   { \"index\": 6, \"name\": \"party_bus\", \"class\": \"\", \"type\": \"repeat\", \"componentVersionId\": 27, \"repeatComponentVersionId\": 26, \"repeatComponentKey\": \"\" },\n//   { \"index\": 7, \"name\": \"term_and_conditions\", \"class\": \"\", \"type\": \"single\", \"componentVersionId\": 28 },\n//   { \"index\": 8, \"name\": \"common_buttons1\", \"class\": \"dw-mb-[35px] smm:dw-mb-[40px] mdx:dw-mb-[40px] mdl:dw-mb-[50px]\", \"type\": \"single\", \"componentVersionId\": 34 },\n//   { \"index\": 9, \"name\": \"divider_100\", \"class\": \"\", \"type\": \"single\", \"componentVersionId\": 15 },\n//   { \"index\": 10, \"name\": \"h2_content_title1\", \"class\": \"dw-mt-[35px] smm:dw-mt-[40px] mdx:dw-mt-[40px] mdl:dw-mt-[50px] lgs:dw-mt-[50px] xl1:dw-mt-[60px]\", \"type\": \"single\", \"componentVersionId\": 37 },\n//   { \"index\": 11, \"name\": \"content_description1\", \"class\": \"dw-mb-[20px] smm:dw-mb-[25px] mdx:dw-mb-[30px]\", \"type\": \"single\", \"componentVersionId\": 38 },\n//   { \"index\": 12, \"name\": \"h2_content_title2\", \"class\": \"dw-mt-[35px] smm:dw-mt-[40px] mdx:dw-mt-[40px] mdl:dw-mt-[50px] lgs:dw-mt-[50px] xl1:dw-mt-[60px]\", \"type\": \"single\", \"componentVersionId\": 37 },\n//   { \"index\": 13, \"name\": \"content_description2\", \"class\": \"dw-mb-[20px] smm:dw-mb-[25px] mdx:dw-mb-[30px]\", \"type\": \"single\", \"componentVersionId\": 38 },\n//   { \"index\": 14, \"name\": \"common_cta_quote_today\", \"class\": \"\", \"type\": \"single\", \"componentVersionId\": 39 },\n//   { \"index\": 15, \"name\": \"h2_content_title3\", \"class\": \"dw-mt-[35px] smm:dw-mt-[40px] mdx:dw-mt-[40px] mdl:dw-mt-[50px] lgs:dw-mt-[50px] xl1:dw-mt-[60px]\", \"type\": \"single\", \"componentVersionId\": 37 },\n//   { \"index\": 16, \"name\": \"content_description3\", \"class\": \"dw-mb-[20px] smm:dw-mb-[25px] mdx:dw-mb-[30px]\", \"type\": \"single\", \"componentVersionId\": 38 },\n//   { \"index\": 17, \"name\": \"h2_content_title4\", \"class\": \"dw-mt-[35px] smm:dw-mt-[40px] mdx:dw-mt-[40px] mdl:dw-mt-[50px] lgs:dw-mt-[50px] xl1:dw-mt-[60px]\", \"type\": \"single\", \"componentVersionId\": 37 },\n//   { \"index\": 18, \"name\": \"content_description4\", \"class\": \"dw-mb-[20px] smm:dw-mb-[25px] mdx:dw-mb-[30px]\", \"type\": \"single\", \"componentVersionId\": 38 },\n//   { \"index\": 19, \"name\": \"content_image4\", \"class\": \"dw-my-[20px] smm:dw-my-[25px] mdx:dw-my-[30px]\", \"type\": \"single\", \"componentVersionId\": 40 },\n//   { \"index\": 20, \"name\": \"common_buttons2\", \"class\": \"\", \"type\": \"single\", \"componentVersionId\": 34 },\n//   { \"index\": 21, \"name\": \"h2_content_title5\", \"class\": \"dw-mt-[35px] smm:dw-mt-[40px] mdx:dw-mt-[40px] mdl:dw-mt-[50px] lgs:dw-mt-[50px] xl1:dw-mt-[60px]\", \"type\": \"single\", \"componentVersionId\": 37 },\n//   { \"index\": 22, \"name\": \"content_description5\", \"class\": \"dw-mb-[20px] smm:dw-mb-[25px] mdx:dw-mb-[30px]\", \"type\": \"single\", \"componentVersionId\": 38 },\n//   { \"index\": 23, \"name\": \"content_image5\", \"class\": \"dw-my-[20px] smm:dw-my-[25px] mdx:dw-my-[30px]\", \"type\": \"single\", \"componentVersionId\": 40 },\n//   { \"index\": 24, \"name\": \"common_buttons3\", \"class\": \"\", \"type\": \"single\", \"componentVersionId\": 34 },\n//   { \"index\": 25, \"name\": \"h2_content_title6\", \"class\": \"dw-mt-[35px] smm:dw-mt-[40px] mdx:dw-mt-[40px] mdl:dw-mt-[50px] lgs:dw-mt-[50px] xl1:dw-mt-[60px]\", \"type\": \"single\", \"componentVersionId\": 37 },\n//   { \"index\": 26, \"name\": \"content_description6\", \"class\": \"dw-mb-[20px] smm:dw-mb-[25px] mdx:dw-mb-[30px]\", \"type\": \"single\", \"componentVersionId\": 38 },\n//   { \"index\": 27, \"name\": \"h2_content_title7\", \"class\": \"dw-mt-[35px] smm:dw-mt-[40px] mdx:dw-mt-[40px] mdl:dw-mt-[50px] lgs:dw-mt-[50px] xl1:dw-mt-[60px]\", \"type\": \"single\", \"componentVersionId\": 37 },\n//   { \"index\": 28, \"name\": \"content_description7\", \"class\": \"dw-mb-[20px] smm:dw-mb-[25px] mdx:dw-mb-[30px]\", \"type\": \"single\", \"componentVersionId\": 38 },\n//   { \"index\": 29, \"name\": \"h2_content_title8\", \"class\": \"dw-mt-[35px] smm:dw-mt-[40px] mdx:dw-mt-[40px] mdl:dw-mt-[50px] lgs:dw-mt-[50px] xl1:dw-mt-[60px]\", \"type\": \"single\", \"componentVersionId\": 37 },\n//   { \"index\": 30, \"name\": \"content_description8\", \"class\": \"dw-mb-[20px] smm:dw-mb-[25px] mdx:dw-mb-[30px]\", \"type\": \"single\", \"componentVersionId\": 38 },\n//   { \"index\": 31, \"name\": \"h2_content_title9\", \"class\": \"dw-mt-[35px] smm:dw-mt-[40px] mdx:dw-mt-[40px] mdl:dw-mt-[50px] lgs:dw-mt-[50px] xl1:dw-mt-[60px]\", \"type\": \"single\", \"componentVersionId\": 37 },\n//   { \"index\": 32, \"name\": \"content_description9\", \"class\": \"dw-mb-[20px] smm:dw-mb-[25px] mdx:dw-mb-[30px]\", \"type\": \"single\", \"componentVersionId\": 38 },\n//   { \"index\": 33, \"name\": \"h2_content_title10\", \"class\": \"dw-mt-[35px] smm:dw-mt-[40px] mdx:dw-mt-[40px] mdl:dw-mt-[50px] lgs:dw-mt-[50px] xl1:dw-mt-[60px]\", \"type\": \"single\", \"componentVersionId\": 37 },\n//   { \"index\": 34, \"name\": \"content_description10\", \"class\": \"dw-mb-[20px] smm:dw-mb-[25px] mdx:dw-mb-[30px]\", \"type\": \"single\", \"componentVersionId\": 38 },\n//   { \"index\": 35, \"name\": \"h2_content_title11\", \"class\": \"dw-mt-[35px] smm:dw-mt-[40px] mdx:dw-mt-[40px] mdl:dw-mt-[50px] lgs:dw-mt-[50px] xl1:dw-mt-[60px]\", \"type\": \"single\", \"componentVersionId\": 37 },\n//   { \"index\": 36, \"name\": \"content_description11\", \"class\": \"dw-mb-[20px] smm:dw-mb-[25px] mdx:dw-mb-[30px]\", \"type\": \"single\", \"componentVersionId\": 38 },\n//   { \"index\": 37, \"name\": \"h2_content_title12\", \"class\": \"dw-mt-[35px] smm:dw-mt-[40px] mdx:dw-mt-[40px] mdl:dw-mt-[50px] lgs:dw-mt-[50px] xl1:dw-mt-[60px]\", \"type\": \"single\", \"componentVersionId\": 37 },\n//   { \"index\": 38, \"name\": \"content_description12\", \"class\": \"dw-mb-[20px] smm:dw-mb-[25px] mdx:dw-mb-[30px]\", \"type\": \"single\", \"componentVersionId\": 38 },\n//   { \"index\": 39, \"name\": \"common_cta_section\", \"class\": \"dw-mb-[35px] smm:dw-mb-[50px] mdx:dw-mb-[50px] mdl:dw-mb-[60px] lgs:dw-mb-[60px] xl1:dw-mb-[70px]\", \"type\": \"single\", \"componentVersionId\": 41 },\n//   { \"index\": 40, \"name\": \"sticky-footer\", \"class\": \"\", \"type\": \"single\", \"componentVersionId\": 32 }\n// ]\n\n}\n\n// \"componentData\": [\n//     { \"index\": 0, \"name\": \"hero_section_home_page\", \"class\": \"\", \"type\": \"single\", \"componentVersionId\": 11 },\n//     { \"index\": 1, \"name\": \"why_section_home_page\", \"class\": \"\", \"type\": \"single\", \"componentVersionId\": 12 },\n//     { \"index\": 2, \"name\": \"buses_grid_title\", \"class\": \"dw-mt-[80px] smm:dw-mt-[80px] mdx:dw-mt-[70px] mdl:dw-mt-[60px] lgs:dw-mt-[60px] xl1:dw-mt-[70px] dw-mt-[80px] smm:dw-mt-[80px] mdx:dw-mt-[85px] mdl:dw-mt-[65px] lgs:dw-mt-[90px] xl1:dw-mt-[100px]\", \"type\": \"single\", \"componentVersionId\": 13  },\n//     { \"index\": 3, \"name\": \"bus_grid_list\", \"class\": \"\", \"type\": \"repeat\", \"componentVersionId\": 9 , \"repeatComponentVersionId\" :10,\"repeatComponentKey\":\"{{key1}}\"},\n//     { \"index\": 4, \"name\": \"divider_60\", \"class\": \"dw-my-[40px] smm:dw-my-[40px] mdx:dw-my-[40px] mdl:dw-my-[40px] lgs:dw-my-[40px] xl1:dw-my-[50px]\", \"type\": \"single\", \"componentVersionId\": 14  },\n//     { \"index\": 5, \"name\": \"divider_100\", \"class\": \"\", \"type\": \"single\", \"componentVersionId\": 15  },\n//     { \"index\": 6, \"name\": \"amenities_list\", \"class\": \"dw-my-[40px] smm:dw-my-[40px] mdx:dw-my-[40px] mdl:dw-my-[40px] lgs:dw-my-[40px] xl1:dw-my-[50px]\", \"type\": \"repeat\", \"componentVersionId\": 16 , \"repeatComponentVersionId\" :17,\"repeatComponentKey\":\"{{jhdd}}\"},\n\n//     { \"index\": 7, \"name\": \"divider_60_1\", \"class\": \"\", \"type\": \"single\", \"componentVersionId\": 14  },\n\n//     { \"index\": 8, \"name\": \"worked_with\", \"class\": \"dw-mt-[80px] smm:dw-mt-[80px] mdx:dw-mt-[70px] mdl:dw-mt-[60px] lgs:dw-mt-[60px] xl1:dw-mt-[70px] dw-my-[35px] smm:dw-my-[40px] mdx:dw-my-[40px] mdl:dw-my-[40px] lgs:dw-my-[50px] xl1:dw-my-[60px]\", \"type\": \"repeat\", \"componentVersionId\": 18 , \"repeatComponentVersionId\" :19,\"repeatComponentKey\":\"{{kdfd1}}\"},\n\n//     { \"index\": 9, \"name\": \"divider_60_2\", \"class\": \"\", \"type\": \"single\", \"componentVersionId\": 14  },\n\n//     { \"index\": 10, \"name\": \"we_offer_list\", \"class\": \"dw-my-[35px] smm:dw-my-[40px] mdx:dw-my-[40px] mdl:dw-my-[40px] lgs:dw-my-[50px] xl1:dw-my-[60px]\", \"type\": \"repeat\", \"componentVersionId\": 20 , \"repeatComponentVersionId\" :21,\"repeatComponentKey\":\"{{fdvdd}}\"},\n//     { \"index\": 11, \"name\": \"divider_100_1\", \"class\": \"\", \"type\": \"single\", \"componentVersionId\": 15  },\n//     { \"index\": 12, \"name\": \"book_in_minutes\", \"class\": \"\", \"type\": \"single\", \"componentVersionId\": 22  },\n\n//     { \"index\": 13, \"name\": \"testimonial\", \"class\": \"\", \"type\": \"repeat\", \"componentVersionId\": 23 , \"repeatComponentVersionId\" :24  },\n\n//     { \"index\": 14, \"name\": \"charter_bus\", \"class\": \"dw-mt-[35px] smm:dw-mt-[40px] mdx:dw-mt-[40px] mdl:dw-mt-[50px] lgs:dw-mt-[50px] xl1:dw-mt-[60px]\", \"type\": \"repeat\", \"componentVersionId\": 25 , \"repeatComponentVersionId\" :26,\"repeatComponentKey\":\"{{key1}}\"  },\n\n//     { \"index\": 15, \"name\": \"party_bus\", \"class\": \"\", \"type\": \"repeat\", \"componentVersionId\": 27 , \"repeatComponentVersionId\" :26,\"repeatComponentKey\":\"\"  },\n//     { \"index\": 17, \"name\": \"term_and_conditions\", \"class\": \"\", \"type\": \"single\", \"componentVersionId\": 28  },\n//     { \"index\": 18, \"name\": \"call_first_common_button1\", \"class\": \"dw-mb-[35px] smm:dw-mb-[50px] mdx:dw-mb-[50px] mdl:dw-mb-[60px] lgs:dw-mb-[60px] xl1:dw-mb-[70px]\", \"type\": \"single\", \"componentVersionId\": 29  },\n\n//     { \"index\": 19, \"name\": \"faqs\", \"class\": \"\", \"type\": \"repeat\", \"componentVersionId\": 30 , \"repeatComponentVersionId\" :31,\"repeatComponentKey\":\"\"  },\n\n//     { \"index\": 20, \"name\": \"sticky-footer\", \"class\": \"\", \"type\": \"single\", \"componentVersionId\": 32  }\n//   ]\n\n// {\n//   \"name\": \"Services\",\n//   \"urlSlug\": \"services\",\n//   \"metaTitle\": \"Our Services - What We Offer\",\n//   \"metaDescription\": \"Check out our wide range of services tailored to meet your needs.\",\n//   \"customCss\": \".services-wrapper { padding: 20px; }\",\n//   \"customJs\": \"console.log('Services page ready');\",\n//   \"headers\": \"<meta name='robots' content='index, follow'>\",\n//   \"wrapperClass\": \"services-wrapper\",\n//   \"componentData\": [\n//     { \"id\": 1, \"index\": 0, \"name\": \"servicesHero\", \"class\": \"services-hero\", \"type\": \"single\", \"componentVersionId\": 3 },\n//     { \"id\": 2, \"index\": 1, \"name\": \"serviceList\", \"class\": \"services-list\", \"type\": \"repeat\", \"componentVersionId\": 5 },\n//     { \"id\": 3, \"index\": 2, \"name\": \"pricingTable\", \"class\": \"pricing-section\", \"type\": \"single\", \"componentVersionId\": 9 },\n//     { \"id\": 4, \"index\": 3, \"name\": \"faqSection\", \"class\": \"faq-container\", \"type\": \"repeat\", \"componentVersionId\": 5 }\n//   ]\n// }\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/pages", "host": ["{{URL}}"], "path": ["admin", "pages"]}}, "response": []}, {"name": "GetOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/pages/:id", "host": ["{{URL}}"], "path": ["admin", "pages", ":id"], "variable": [{"key": "id", "value": "7"}]}}, "response": []}, {"name": "GetAll", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/pages", "host": ["{{URL}}"], "path": ["admin", "pages"], "query": [{"key": "search", "value": "service", "disabled": true}]}}, "response": []}, {"name": "UpdateOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n//   \"name\": \"Service page\",\n//   \"isActive\":true\n//   \"urlSlug\": \"services\",\n//   \"metaTitle\": \"Our Services - What We Offer\",\n//   \"metaDescription\": \"\",\n//   \"customCss\": \"\",\n//   \"customJs\": \"console.log('Services page ready');\",\n//   \"headers\": \"<meta name='robots' content='index, follow'>\",\n//   \"wrapperClass\": \"services-wrapper\",\n\"siteMapLabel\":\"home\"\n//   \"componentData\":[\n//   {\n//     \"index\": 0,\n//     \"name\": \"hero_section_content\",\n//     \"class\": \"\",\n//     \"type\": \"single\",\n//     \"componentVersionId\": 33\n//   },\n//   {\n//     \"index\": 1,\n//     \"name\": \"common_buttons\",\n//     \"class\": \"btn-primary\",\n//     \"type\": \"single\",\n//     \"componentVersionId\": 34\n//   },\n//   {\n//     \"index\": 2,\n//     \"name\": \"faq_section\",\n//     \"class\": \"faq-style\",\n//     \"type\": \"repeat\",\n//     \"componentVersionId\": 35,\n//     \"repeatComponents\": [\n//       { \"componentVersionId\": 31, \"key\": \"{{sample}}\" }\n//     ]\n//   },\n//   {\n//     \"index\": 3,\n//     \"name\": \"testimonial_cards\",\n//     \"class\": \"card-grid\",\n//     \"type\": \"single\",\n//     \"componentVersionId\": 35\n//   },\n//   {\n//     \"index\": 4,\n//     \"name\": \"feature_highlights\",\n//     \"class\": \"highlight-list\",\n//     \"type\": \"repeat\",\n//     \"componentVersionId\": 35,\n//     \"repeatComponents\": [\n//       { \"componentVersionId\": 24 }\n//     ]\n//   },\n//   {\n//     \"index\": 5,\n//     \"name\": \"pricing_table\",\n//     \"class\": \"pricing-style\",\n//     \"type\": \"single\",\n//     \"componentVersionId\": 36\n//   },\n//   {\n//     \"index\": 6,\n//     \"name\": \"team_section\",\n//     \"class\": \"team-grid\",\n//     \"type\": \"repeat\",\n//     \"componentVersionId\": 35,\n//     \"repeatComponents\": [\n//       { \"componentVersionId\": 21, \"key\": \"{{sample}}\" },\n//       { \"componentVersionId\": 22, \"key\": \"\" }\n//     ]\n//   },\n//   {\n//     \"index\": 7,\n//     \"name\": \"contact_form\",\n//     \"class\": \"form-styled\",\n//     \"type\": \"single\",\n//     \"componentVersionId\": 37\n//   }\n// ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/pages/:id", "host": ["{{URL}}"], "path": ["admin", "pages", ":id"], "variable": [{"key": "id", "value": "7"}]}}, "response": []}, {"name": "DeleteOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/pages/:id", "host": ["{{URL}}"], "path": ["admin", "pages", ":id"], "variable": [{"key": "id", "value": "6"}]}}, "response": []}, {"name": "get for template", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{URL}}/admin/pages/getForTemplate", "host": ["{{URL}}"], "path": ["admin", "pages", "getForTemplate"], "query": [{"key": "search", "value": "home", "disabled": true}, {"key": "limit", "value": "2", "disabled": true}, {"key": "page", "value": "2", "disabled": true}]}}, "response": []}]}, {"name": "pageversions", "item": [{"name": "Create", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"version\": 3974177528741888,\n  \"urlSlug\": \"FG2Mn\",\n  \"metaTitle\": \"RiXOc\",\n  \"metaDescription\": \"OKLK8\",\n  \"customCss\": \"iJDIr\",\n  \"customJs\": \"GvhZo\",\n  \"headers\": \"gcu7T\",\n  \"wrapperClass\": \"aHgeE\",\n  \"componentVersionIds\": \"vqPRH0gGljZYBwEwA1AG\",\n  \"componentData\": \"3hDwBbbdWqHpq1Ydtaoi\",\n  \"componentCount\": 5211918367195136\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/pageversions", "host": ["{{URL}}"], "path": ["pageversions"]}}, "response": []}, {"name": "GetOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/pageversions/:id", "host": ["{{URL}}"], "path": ["admin", "pageversions", ":id"], "variable": [{"key": "id", "value": "5"}]}}, "response": []}, {"name": "GetAll", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/pageversions", "host": ["{{URL}}"], "path": ["pageversions"]}}, "response": []}, {"name": "UpdateOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n  \"version\": 337301114716160,\n  \"urlSlug\": \"x0FlY\",\n  \"metaTitle\": \"v35Az\",\n  \"metaDescription\": \"Ot6kv\",\n  \"customCss\": \"jO9Zm\",\n  \"customJs\": \"N8EZ5\",\n  \"headers\": \"DVhb5\",\n  \"wrapperClass\": \"b04ux\",\n  \"componentVersionIds\": \"7eBG3QnELlqI0SUJ8BuL\",\n  \"componentData\": \"WABVfLORF5jvsYoXSofs\",\n  \"componentCount\": 5676777062006784\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/pageversions/:id", "host": ["{{URL}}"], "path": ["pageversions", ":id"], "variable": [{"key": "id"}]}}, "response": []}, {"name": "DeleteOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/pageversions/:id", "host": ["{{URL}}"], "path": ["pageversions", ":id"], "variable": [{"key": "id"}]}}, "response": []}]}, {"name": "templates", "item": [{"name": "Create", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Only Home and prices website.\",\n  \"description\": \"A multi-page website for showcasing products and services.\",\n  \"isActive\": true,\n  \"pageData\": [\n    {\n        \"url\": \"/\",\n        \"showNavbar\": true,\n        \"type\": \"static\",\n        \"pageVersionId\": 9,\n        \"name\": \"Home\",\n        \"metaTitle\": \"Welcome to Our Website - Home\",\n        \"metaDescription\": \"Discover our services, latest updates, and everything you need on our homepage.\",\n        \"customCss\": \".home-wrapper { background-color: #f9f9f9; padding: 40px; }\",\n        \"customJs\": \"console.log('Home page loaded successfully');\",\n        \"headers\": \"<meta name='robots' content='index, follow'>\",\n        \"wrapperClass\": \"home-wrapper\"\n    },\n    {\n      \"url\": \"/prices\",\n      \"showNavbar\": true,\n      \"type\": \"static\",\n      \"pageVersionId\": 8,\n      \"name\": \"prices\",\n      \"metaTitle\": \"${company_name} Charter Bus Prices\",\n        \"metaDescription\": \"\",\n        \"customCss\": \"\",\n        \"customJs\": \"\",\n        \"headers\": \"\",\n        \"wrapperClass\": \"\"\n    },\n    {\n      \"url\": \"/contact\",\n      \"showNavbar\": true,\n      \"type\": \"static\",\n      \"pageVersionId\": 6,\n      \"name\": \"contact\"\n    },\n    {\n      \"url\": \"\",\n      \"showNavbar\": true,\n      \"type\": \"static\",\n      \"pageVersionId\": 7,\n      \"name\": \"services\"\n    }\n    \n  ],\n  \"content\":{\n  \"heroTitle\": \"Welcome to Our Platform\",\n  \"heroSubtitle\": \"We help you grow faster with AI-powered solutions\",\n  \"heroImage\": \"/assets/images/hero-banner.jpg\",\n  \"ctaPrimary\": \"Get Started\",\n  \"ctaSecondary\": \"Learn More\",\n  \"feature1Title\": \"Fast Performance\",\n  \"feature1Desc\": \"Optimized systems built for speed and scalability.\",\n  \"feature2Title\": \"Seamless Integration\",\n  \"feature2Desc\": \"Works smoothly with your existing tools and workflows.\",\n  \"feature3Title\": \"Secure by Design\",\n  \"feature3Desc\": \"Your data is encrypted and protected at every step.\",\n  \"aboutHeading\": \"About Us\",\n  \"aboutText\": \"We are a passionate team building next-generation technology solutions.\",\n  \"aboutImage\": \"/assets/images/about-team.jpg\",\n  \"servicesHeading\": \"Our Services\",\n  \"service1\": \"Custom Web Development\",\n  \"service2\": \"Mobile App Solutions\",\n  \"service3\": \"AI-Powered Automation\",\n  \"testimonialsHeading\": \"What Our Clients Say\",\n  \"testimonial1Name\": \"Sarah Johnson\",\n  \"testimonial1Text\": \"This platform transformed how we manage our business online!\",\n  \"testimonial2Name\": \"David Lee\",\n  \"testimonial2Text\": \"Great experience, easy to use and very effective.\",\n  \"pricingHeading\": \"Choose Your Plan\",\n  \"pricingBasic\": \"Basic - $19/month\",\n  \"pricingPro\": \"Pro - $49/month\",\n  \"pricingEnterprise\": \"Enterprise - $99/month\",\n  \"faqHeading\": \"Frequently Asked Questions\",\n  \"faq1Q\": \"How do I get started?\",\n  \"faq1A\": \"Simply sign up and start exploring within minutes.\",\n  \"footerText\": \"© 2025 Example Inc. All rights reserved.\"\n}\n\n\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/templates", "host": ["{{URL}}"], "path": ["admin", "templates"]}}, "response": []}, {"name": "add media", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "media", "type": "file", "src": ["/home/<USER>/Downloads/clifton-images-special/Home Page/clifton-56-passenger-charter-bus.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/sprinter-limo-rental-interior-clifton.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/party-bus-rental-interior-clifton.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/clifton-government-and-military-bus-rentals.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/clifton-prom-and-homecoming-party-bus-rentals.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/clifton-wine-tour-and-pub-crawl-bus-rentals.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/clifton-construction-site-shuttle-services.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/clifton-emergency-transportation-service.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/clifton-hospital-and-healthcare-shuttles.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/clifton-religious-charter-bus-minibus-rentals.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/clifton-summer-camp-transportation-and-bus-rentals.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/clifton-vacation-bus-rentals-for-family-trips.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/clifton-bus-rentals-for-travel-agents.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/clifton-private-event-transportation.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/clifton-airport-shuttles.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/clifton-school-event-transportation.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/clifton-sporting-event-transportation.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/clifton-wedding-transportation.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/clifton-corporate-bus-rental.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/sprinter-limo-rental-clifton.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/party-bus-rental-clifton.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/school-bus-rental-interior-clifton.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/school-bus-rental-clifton.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/sprinter-van-with-driver-interior-clifton.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/sprinter-van-with-driver-clifton.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/56-passenger-charter-bus-interior-clifton.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/56-passenger-charter-bus-clifton.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/55-passenger-charter-bus-interior-clifton.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/55-passenger-charter-bus-clifton.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/54-passenger-charter-bus-interior-clifton.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/54-passenger-charter-bus-clifton.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/50-passenger-charter-bus-interior-clifton.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/50-passenger-charter-bus-clifton.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/35-passenger-minibus-interior-clifton.jpeg", "/home/<USER>/Downloads/clifton-images-special/Home Page/35-passenger-minibus-clifton.jpeg"]}, {"key": "removeMediaIds", "value": "13", "type": "text", "disabled": true}]}, "url": {"raw": "{{URL}}/admin/templates/media?id=8&type=template", "host": ["{{URL}}"], "path": ["admin", "templates", "media"], "query": [{"key": "id", "value": "8"}, {"key": "type", "value": "template"}]}}, "response": []}, {"name": "add media content-team", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "media", "type": "file", "src": ["/home/<USER>/Downloads/clifton-images-special/Individual Buses Page/clifton-sprinter-limo-rental-inside.jpeg", "/home/<USER>/Downloads/clifton-images-special/Individual Buses Page/clifton-sprinter-limo-rental-interior.jpeg", "/home/<USER>/Downloads/clifton-images-special/Individual Buses Page/clifton-sprinter-limo-rental-rental.jpeg", "/home/<USER>/Downloads/clifton-images-special/Individual Buses Page/clifton-party-bus-rental-inside.jpeg"]}, {"key": "removeMediaIds", "value": "13", "type": "text", "disabled": true}]}, "url": {"raw": "{{URL}}/templates/media?id=10&type=template", "host": ["{{URL}}"], "path": ["templates", "media"], "query": [{"key": "id", "value": "10"}, {"key": "type", "value": "template"}]}}, "response": []}, {"name": "GetOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/templates/:id", "host": ["{{URL}}"], "path": ["admin", "templates", ":id"], "variable": [{"key": "id", "value": "2"}]}}, "response": []}, {"name": "GetAll", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{URL}}/admin/templates", "host": ["{{URL}}"], "path": ["admin", "templates"], "query": [{"key": "search", "value": "", "disabled": true}]}}, "response": []}, {"name": "get for website", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/templates/for-website", "host": ["{{URL}}"], "path": ["admin", "templates", "for-website"], "query": [{"key": "search", "value": "marke", "disabled": true}]}}, "response": []}, {"name": "UpdateOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n//   \"name\": \"Marketing Website1\"\n//   \"description\": \"A multi-page website for showcasing products and services.\",\n//   \"isActive\": true,\n//   \"pageData\": [\n//     {\n//       \"url\": \"/\",\n//       \"showNavbar\": true,\n//       \"type\": \"static\",\n//       \"pageVersionId\": 2\n//     },\n//     {\n//       \"url\": \"/service\",\n//       \"showNavbar\": true,\n//       \"type\": \"static\",\n//       \"pageVersionId\": 5\n//     }\n//   ]\n//   \"content\": {\n//   \"heroTitle\": \"Your Productivity, Supercharged\",\n//   \"heroSubtitle\": \"All-in-one platform to manage tasks, projects, and teams.\",\n//   \"ctaButton\": \"Start Free Trial\",\n//   \"secondaryButton\": \"Learn More\",\n//   \"features\": [\n//     {\n//       \"icon\": \"📅\",\n//       \"title\": \"Smart Scheduling\",\n//       \"description\": \"Automate task planning with AI-powered insights.\"\n//     },\n//     {\n//       \"icon\": \"🔔\",\n//       \"title\": \"Real-Time Notifications\",\n//       \"description\": \"Stay updated with instant alerts across all devices.\"\n//     },\n//     {\n//       \"icon\": \"📊\",\n//       \"title\": \"Advanced Analytics\",\n//       \"description\": \"Track performance with detailed reports and dashboards.\"\n//     }\n//   ],\n//   \"testimonials\": [\n//     {\n//       \"name\": \"Jane Doe\",\n//       \"role\": \"CEO, StartupX\",\n//       \"quote\": \"This platform has doubled our team’s efficiency!\"\n//     },\n//     {\n//       \"name\": \"<PERSON> <PERSON>\",\n//       \"role\": \"Project Manager, BuildIt\",\n//       \"quote\": \"Managing multiple projects has never been this easy.\"\n//     }\n//   ],\n//   \"pricing\": [\n//     {\n//       \"plan\": \"Basic\",\n//       \"price\": \"$9/mo\",\n//       \"features\": [\"Task Management\", \"Calendar Sync\", \"Basic Reports\"]\n//     },\n//     {\n//       \"plan\": \"Pro\",\n//       \"price\": \"$29/mo\",\n//       \"features\": [\"Everything in Basic\", \"Team Collaboration\", \"Advanced Analytics\"]\n//     }\n//   ],\n//   \"footerText\": \"© 2025 TaskFlow Inc. All rights reserved.\",\n//   \"socialLinks\": {\n//     \"facebook\": \"https://facebook.com/taskflow\",\n//     \"twitter\": \"https://twitter.com/taskflow\",\n//     \"linkedin\": \"https://linkedin.com/company/taskflow\"\n//   }\n// }\n\n\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/templates/:id", "host": ["{{URL}}"], "path": ["admin", "templates", ":id"], "query": [{"key": "mediaChanged", "value": "true", "disabled": true}], "variable": [{"key": "id", "value": "1"}]}}, "response": []}, {"name": "Update Content-team", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n//   \"name\": \"Marketing Website1\"\n//   \"description\": \"A multi-page website for showcasing products and services.\",\n//   \"isActive\": true,\n//   \"pageData\": [\n//     {\n//       \"url\": \"/\",\n//       \"showNavbar\": true,\n//       \"type\": \"static\",\n//       \"pageVersionId\": 2\n//     },\n//     {\n//       \"url\": \"/service\",\n//       \"showNavbar\": true,\n//       \"type\": \"static\",\n//       \"pageVersionId\": 5\n//     },\n//     {\n//       \"url\": \"/prices\",\n//       \"showNavbar\": true,\n//       \"type\": \"static\",\n//       \"pageVersionId\": 8\n//     }\n//   ],\n//   \"content\": {\n//   \"heroTitle\": \"Your Productivity, Supercharged\",\n//   \"heroSubtitle\": \"All-in-one platform to manage tasks, projects, and teams.\",\n//   \"ctaButton\": \"Start Free Trial\",\n//   \"secondaryButton\": \"Learn More\",\n//   \"testimonials\": [\n//     {\n//       \"name\": \"<PERSON>\",\n//       \"role\": \"CEO, StartupX\",\n//       \"quote\": \"This platform has doubled our team’s efficiency!\"\n//     },\n//     {\n//       \"name\": \"<PERSON>\",\n//       \"role\": \"Project Manager, BuildIt\",\n//       \"quote\": \"Managing multiple projects has never been this easy.\"\n//     }\n//   ],\n//   \"pricing\": [\n//     {\n//       \"plan\": \"Basic\",\n//       \"price\": \"$9/mo\",\n//       \"features\": [\"Task Management\", \"Calendar Sync\", \"Basic Reports\"]\n//     },\n//     {\n//       \"plan\": \"Pro\",\n//       \"price\": \"$29/mo\",\n//       \"features\": [\"Everything in Basic\", \"Team Collaboration\", \"Advanced Analytics\"]\n//     }\n//   ],\n//   \"footerText\": \"© 2025 TaskFlow Inc. All rights reserved.\",\n//   \"socialLinks\": {\n//     \"facebook\": \"https://facebook.com/taskflow\",\n//     \"twitter\": \"https://twitter.com/taskflow\",\n//     \"linkedin\": \"https://linkedin.com/company/taskflow\"\n//   }\n// }\n\n\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/templates/:id?mediaChanged=true", "host": ["{{URL}}"], "path": ["templates", ":id"], "query": [{"key": "mediaChanged", "value": "true"}], "variable": [{"key": "id", "value": "1"}]}}, "response": []}, {"name": "DeleteOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/templates/:id", "host": ["{{URL}}"], "path": ["templates", ":id"], "variable": [{"key": "id"}]}}, "response": []}]}, {"name": "templateversions", "item": [{"name": "Create", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"version\": 4126464484048896,\n  \"pageVersionIds\": \"aDKs3TqLlX6QjdgXqQrx\",\n  \"pageData\": \"UZM7zIFUSwoe75w0dlXQ\",\n  \"pageCount\": 5419954434211840,\n  \"content\": \"gtnZITTKC9XNmZrbTZfj\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/templateversions", "host": ["{{URL}}"], "path": ["templateversions"]}}, "response": []}, {"name": "GetOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/templateversions/:id", "host": ["{{URL}}"], "path": ["templateversions", ":id"], "variable": [{"key": "id", "value": "8"}]}}, "response": [{"name": "GetOne", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/templateversions/:id", "host": ["{{URL}}"], "path": ["templateversions", ":id"], "variable": [{"key": "id", "value": "8"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": null, "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin, Accept-Encoding"}, {"key": "Content-Security-Policy", "value": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests"}, {"key": "Cross-Origin-Opener-Policy", "value": "same-origin"}, {"key": "Cross-Origin-Resource-Policy", "value": "same-origin"}, {"key": "Origin-Agent-<PERSON><PERSON>", "value": "?1"}, {"key": "Referrer-Policy", "value": "same-origin"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-DNS-Prefetch-Control", "value": "off"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "0"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "ETag", "value": "W/\"2283-2fxnbg+tfgxxhJOOXd4cmZTfPWM\""}, {"key": "Content-Encoding", "value": "br"}, {"key": "Date", "value": "Fri, 19 Sep 2025 08:53:52 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}, {"key": "Transfer-Encoding", "value": "chunked"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"data\": {\n        \"id\": 8,\n        \"version\": 1,\n        \"pageVersionIds\": [\n            9,\n            8,\n            6,\n            7\n        ],\n        \"pageData\": [\n            {\n                \"url\": \"/\",\n                \"showNavbar\": true,\n                \"type\": \"static\",\n                \"pageVersionId\": 9,\n                \"name\": \"Home\",\n                \"metaTitle\": \"Welcome to Our Website - Home\",\n                \"metaDescription\": \"Discover our services, latest updates, and everything you need on our homepage.\",\n                \"customCss\": \".home-wrapper { background-color: #f9f9f9; padding: 40px; }\",\n                \"customJs\": \"console.log('Home page loaded successfully');\",\n                \"headers\": \"<meta name='robots' content='index, follow'>\",\n                \"wrapperClass\": \"home-wrapper\"\n            },\n            {\n                \"url\": \"/prices\",\n                \"showNavbar\": true,\n                \"type\": \"static\",\n                \"pageVersionId\": 8,\n                \"name\": \"prices\",\n                \"metaTitle\": \"${company_name} Charter Bus Prices\",\n                \"metaDescription\": \"\",\n                \"customCss\": \"\",\n                \"customJs\": \"\",\n                \"headers\": \"\",\n                \"wrapperClass\": \"\"\n            },\n            {\n                \"url\": \"/contact\",\n                \"showNavbar\": true,\n                \"type\": \"static\",\n                \"pageVersionId\": 6,\n                \"name\": \"contact\"\n            },\n            {\n                \"url\": \"\",\n                \"showNavbar\": true,\n                \"type\": \"static\",\n                \"pageVersionId\": 7,\n                \"name\": \"services\"\n            }\n        ],\n        \"pageCount\": 4,\n        \"createdAt\": \"2025-09-15T09:26:22.259Z\",\n        \"updatedAt\": \"2025-09-15T09:42:39.587Z\",\n        \"deletedAt\": null,\n        \"templateId\": 2,\n        \"contentId\": 5,\n        \"createdBy\": 1,\n        \"mediaSetId\": 9,\n        \"content\": {\n            \"id\": 5,\n            \"content\": {\n                \"heroTitle\": \"Welcome to Our Platform\",\n                \"heroSubtitle\": \"We help you grow faster with AI-powered solutions\",\n                \"heroImage\": \"/assets/images/hero-banner.jpg\",\n                \"ctaPrimary\": \"Get Started\",\n                \"ctaSecondary\": \"Learn More\",\n                \"feature1Title\": \"Fast Performance\",\n                \"feature1Desc\": \"Optimized systems built for speed and scalability.\",\n                \"feature2Title\": \"Seamless Integration\",\n                \"feature2Desc\": \"Works smoothly with your existing tools and workflows.\",\n                \"feature3Title\": \"Secure by Design\",\n                \"feature3Desc\": \"Your data is encrypted and protected at every step.\",\n                \"aboutHeading\": \"About Us\",\n                \"aboutText\": \"We are a passionate team building next-generation technology solutions.\",\n                \"aboutImage\": \"/assets/images/about-team.jpg\",\n                \"servicesHeading\": \"Our Services\",\n                \"service1\": \"Custom Web Development\",\n                \"service2\": \"Mobile App Solutions\",\n                \"service3\": \"AI-Powered Automation\",\n                \"testimonialsHeading\": \"What Our Clients Say\",\n                \"testimonial1Name\": \"Sarah Johnson\",\n                \"testimonial1Text\": \"This platform transformed how we manage our business online!\",\n                \"testimonial2Name\": \"David Lee\",\n                \"testimonial2Text\": \"Great experience, easy to use and very effective.\",\n                \"pricingHeading\": \"Choose Your Plan\",\n                \"pricingBasic\": \"Basic - $19/month\",\n                \"pricingPro\": \"Pro - $49/month\",\n                \"pricingEnterprise\": \"Enterprise - $99/month\",\n                \"faqHeading\": \"Frequently Asked Questions\",\n                \"faq1Q\": \"How do I get started?\",\n                \"faq1A\": \"Simply sign up and start exploring within minutes.\",\n                \"footerText\": \"© 2025 Example Inc. All rights reserved.\"\n            }\n        },\n        \"mediaSet\": {\n            \"id\": 9,\n            \"media\": [\n                {\n                    \"id\": 52,\n                    \"name\": \"clifton-56-passenger-charter-bus.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/clifton-56-passenger-charter-bus.jpeg\"\n                },\n                {\n                    \"id\": 53,\n                    \"name\": \"sprinter-limo-rental-interior-clifton.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/sprinter-limo-rental-interior-clifton.jpeg\"\n                },\n                {\n                    \"id\": 54,\n                    \"name\": \"party-bus-rental-interior-clifton.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/party-bus-rental-interior-clifton.jpeg\"\n                },\n                {\n                    \"id\": 55,\n                    \"name\": \"clifton-government-and-military-bus-rentals.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/clifton-government-and-military-bus-rentals.jpeg\"\n                },\n                {\n                    \"id\": 56,\n                    \"name\": \"clifton-prom-and-homecoming-party-bus-rentals.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/clifton-prom-and-homecoming-party-bus-rentals.jpeg\"\n                },\n                {\n                    \"id\": 57,\n                    \"name\": \"clifton-wine-tour-and-pub-crawl-bus-rentals.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/clifton-wine-tour-and-pub-crawl-bus-rentals.jpeg\"\n                },\n                {\n                    \"id\": 58,\n                    \"name\": \"clifton-construction-site-shuttle-services.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/clifton-construction-site-shuttle-services.jpeg\"\n                },\n                {\n                    \"id\": 59,\n                    \"name\": \"clifton-emergency-transportation-service.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/clifton-emergency-transportation-service.jpeg\"\n                },\n                {\n                    \"id\": 60,\n                    \"name\": \"clifton-hospital-and-healthcare-shuttles.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/clifton-hospital-and-healthcare-shuttles.jpeg\"\n                },\n                {\n                    \"id\": 61,\n                    \"name\": \"clifton-religious-charter-bus-minibus-rentals.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/clifton-religious-charter-bus-minibus-rentals.jpeg\"\n                },\n                {\n                    \"id\": 62,\n                    \"name\": \"clifton-summer-camp-transportation-and-bus-rentals.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/clifton-summer-camp-transportation-and-bus-rentals.jpeg\"\n                },\n                {\n                    \"id\": 63,\n                    \"name\": \"clifton-vacation-bus-rentals-for-family-trips.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/clifton-vacation-bus-rentals-for-family-trips.jpeg\"\n                },\n                {\n                    \"id\": 64,\n                    \"name\": \"clifton-bus-rentals-for-travel-agents.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/clifton-bus-rentals-for-travel-agents.jpeg\"\n                },\n                {\n                    \"id\": 65,\n                    \"name\": \"clifton-private-event-transportation.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/clifton-private-event-transportation.jpeg\"\n                },\n                {\n                    \"id\": 66,\n                    \"name\": \"clifton-airport-shuttles.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/clifton-airport-shuttles.jpeg\"\n                },\n                {\n                    \"id\": 67,\n                    \"name\": \"clifton-school-event-transportation.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/clifton-school-event-transportation.jpeg\"\n                },\n                {\n                    \"id\": 68,\n                    \"name\": \"clifton-sporting-event-transportation.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/clifton-sporting-event-transportation.jpeg\"\n                },\n                {\n                    \"id\": 69,\n                    \"name\": \"clifton-wedding-transportation.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/clifton-wedding-transportation.jpeg\"\n                },\n                {\n                    \"id\": 70,\n                    \"name\": \"clifton-corporate-bus-rental.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/clifton-corporate-bus-rental.jpeg\"\n                },\n                {\n                    \"id\": 71,\n                    \"name\": \"sprinter-limo-rental-clifton.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/sprinter-limo-rental-clifton.jpeg\"\n                },\n                {\n                    \"id\": 72,\n                    \"name\": \"party-bus-rental-clifton.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/party-bus-rental-clifton.jpeg\"\n                },\n                {\n                    \"id\": 73,\n                    \"name\": \"school-bus-rental-interior-clifton.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/school-bus-rental-interior-clifton.jpeg\"\n                },\n                {\n                    \"id\": 74,\n                    \"name\": \"school-bus-rental-clifton.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/school-bus-rental-clifton.jpeg\"\n                },\n                {\n                    \"id\": 75,\n                    \"name\": \"sprinter-van-with-driver-interior-clifton.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/sprinter-van-with-driver-interior-clifton.jpeg\"\n                },\n                {\n                    \"id\": 76,\n                    \"name\": \"sprinter-van-with-driver-clifton.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/sprinter-van-with-driver-clifton.jpeg\"\n                },\n                {\n                    \"id\": 77,\n                    \"name\": \"56-passenger-charter-bus-interior-clifton.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/56-passenger-charter-bus-interior-clifton.jpeg\"\n                },\n                {\n                    \"id\": 78,\n                    \"name\": \"56-passenger-charter-bus-clifton.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/56-passenger-charter-bus-clifton.jpeg\"\n                },\n                {\n                    \"id\": 79,\n                    \"name\": \"55-passenger-charter-bus-interior-clifton.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/55-passenger-charter-bus-interior-clifton.jpeg\"\n                },\n                {\n                    \"id\": 80,\n                    \"name\": \"55-passenger-charter-bus-clifton.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/55-passenger-charter-bus-clifton.jpeg\"\n                },\n                {\n                    \"id\": 81,\n                    \"name\": \"54-passenger-charter-bus-interior-clifton.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/54-passenger-charter-bus-interior-clifton.jpeg\"\n                },\n                {\n                    \"id\": 82,\n                    \"name\": \"54-passenger-charter-bus-clifton.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/54-passenger-charter-bus-clifton.jpeg\"\n                },\n                {\n                    \"id\": 83,\n                    \"name\": \"50-passenger-charter-bus-interior-clifton.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/50-passenger-charter-bus-interior-clifton.jpeg\"\n                },\n                {\n                    \"id\": 84,\n                    \"name\": \"50-passenger-charter-bus-clifton.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/50-passenger-charter-bus-clifton.jpeg\"\n                },\n                {\n                    \"id\": 85,\n                    \"name\": \"35-passenger-minibus-interior-clifton.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/35-passenger-minibus-interior-clifton.jpeg\"\n                },\n                {\n                    \"id\": 86,\n                    \"name\": \"35-passenger-minibus-clifton.jpeg\",\n                    \"url\": \"https://testbucket1234456768.s3.ap-south-1.amazonaws.com/template/8/35-passenger-minibus-clifton.jpeg\"\n                }\n            ]\n        }\n    }\n}"}]}, {"name": "GetAll", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/templateversions", "host": ["{{URL}}"], "path": ["templateversions"]}}, "response": []}, {"name": "UpdateOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n  \"version\": 6194574091354112,\n  \"pageVersionIds\": \"JpLUyq9QdFSMn6KfKeR1\",\n  \"pageData\": \"CnVheSAz4dXk5oUxvzs1\",\n  \"pageCount\": 3489357793067008,\n  \"content\": \"9jjCXlr6BQQtOWzi9FBs\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/templateversions/:id", "host": ["{{URL}}"], "path": ["templateversions", ":id"], "variable": [{"key": "id"}]}}, "response": []}, {"name": "DeleteOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/templateversions/:id", "host": ["{{URL}}"], "path": ["templateversions", ":id"], "variable": [{"key": "id"}]}}, "response": []}]}, {"name": "websites", "item": [{"name": "Create", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"name\": \"My Website\",\n  \"templateId\": 2,\n  \"templateVersionId\": 8,\n  \"primaryColor\": \"#123456\",\n  \"secondaryColor\": \"#abcdef\",\n  \"backgroundColor\": \"#ffffff\",\n  \"textColor\": \"#000000\",\n  \"borderColor\": \"#cccccc\",\n  \"cardBackgroundColor\": \"#f5f5f5\",\n  \"dividerColor\": \"#e0e0e0\",\n  \"websitevariables\": {\n  \"brandName\": \"Example Brand\",\n  \"sftpDetails\": [\n    { \"ip\": \"***********\" },\n    { \"path\": \"/var/www\" },\n    { \"username\": \"deploy\" }\n  ],\n  \"customSettings\": [\n    { \"showNavbar\": \"true\" },\n    { \"footerText\": \"Powered by Dream Builder\" }\n  ]\n},\n  \"websiteDynamicPageData\": {\n    \"home\": {\n      \"sitemapLabel\": \"Home\",\n      \"navigationType\": \"Single Column\",\n      \"sections\": [\n        {\n          \"sectionLabel\": \"Header\",\n          \"sectionItems\": [\n            {\n              \"label\": \"Logo\",\n              \"slug\": \"logo\",\n              \"extra\": {\n                \"url\": \"https://example.com/logo.png\"\n              }\n            }\n          ]\n        }\n      ],\n      \"columnCount\": 1\n    }\n  },\n  \"content\": {\n    \"welcomeText\": \"Welcome to My Website\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/websites", "host": ["{{URL}}"], "path": ["admin", "websites"]}}, "response": []}, {"name": "GetOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/websites/:id", "host": ["{{URL}}"], "path": ["websites", ":id"], "variable": [{"key": "id", "value": "1"}]}}, "response": []}, {"name": "add media", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "media", "type": "file", "src": ["/home/<USER>/Downloads/clifton-images-special/Individual Service Page/clifton-government-and-military-bus-rentals.jpeg", "/home/<USER>/Downloads/clifton-images-special/Individual Service Page/clifton-construction-site-shuttle-bus-service.jpeg"]}, {"key": "removeMediaIds", "value": "87,88,89,91", "type": "text"}]}, "url": {"raw": "{{URL}}/admin/websites/media?id=5&type=website", "host": ["{{URL}}"], "path": ["admin", "websites", "media"], "query": [{"key": "id", "value": "5"}, {"key": "type", "value": "website"}]}}, "response": []}, {"name": "add media content-team", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "media", "type": "file", "src": ["/home/<USER>/Downloads/clifton-images-special/Individual Buses Page/clifton-56-passenger-charter-bus-inside.jpeg", "/home/<USER>/Downloads/clifton-images-special/Individual Buses Page/clifton-56-passenger-charter-bus-interior.jpeg", "/home/<USER>/Downloads/clifton-images-special/Individual Buses Page/clifton-56-passenger-charter-bus-rental.jpeg"]}, {"key": "removeMediaIds", "value": "87,88,89,91", "type": "text"}]}, "url": {"raw": "{{URL}}/websites/media?id=7&type=website", "host": ["{{URL}}"], "path": ["websites", "media"], "query": [{"key": "id", "value": "7"}, {"key": "type", "value": "website"}]}}, "response": []}, {"name": "GetAll", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/websites", "host": ["{{URL}}"], "path": ["websites"], "query": [{"key": "limit", "value": "1", "disabled": true}, {"key": "page", "value": "2", "disabled": true}, {"key": "search", "value": "ba", "disabled": true}]}}, "response": []}, {"name": "UpdateOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n//   \"name\": \"My Website\",\n//   \"ip\":\"**********\",\n//   \"path\":\"home/tst/server\",\n//   \"username\":\"<PERSON><PERSON><PERSON><PERSON>\",\n//   \"sshKey\":\"fafdajkhfdjhjhKHFHahKJHuehiu#7&$34&@H2\"\n//   \"templateId\": 2,\n//   \"templateVersionId\": 8,\n//   \"primaryColor\": \"#787653\",\n//   \"secondaryColor\": \"#zyxabc\"\n//   \"backgroundColor\": \"#ffffff\",\n//   \"textColor\": \"#000000\",\n//   \"borderColor\": \"#cccccc\",\n//   \"cardBackgroundColor\": \"#f5f5f5\",\n//   \"dividerColor\": \"#e0e0e0\"\n//   \"websitevariables\": {\n//   \"brandName\": \"Example Brand\",\n//   \"sftpDetails\": [\n//     { \"ip\": \"***********\" },\n//     { \"path\": \"/var/www\" },\n//     { \"username\": \"deploy\" }\n//   ],\n//   \"customSettings\": [\n//     { \"showNavbar\": \"true\" },\n//     { \"footerText\": \"Powered by Dream Builder\" }\n//   ]\n// },\n//   \"websiteDynamicPageData\": {\n//     \"home\": {\n//       \"sitemapLabel\": \"Home\",\n//       \"navigationType\": \"Single Column\",\n//       \"sections\": [\n//         {\n//           \"sectionLabel\": \"Header\",\n//           \"sectionItems\": [\n//             {\n//               \"label\": \"Logo\",\n//               \"slug\": \"logo\",\n//               \"extra\": {\n//                 \"url\": \"https://example.com/logo.png\"\n//               }\n//             }\n//           ]\n//         }\n//       ],\n//       \"columnCount\": 1\n//     }\n//   },\n//   \"content\": {\n//   \"welcomeText\": \"Welcome to My Website\",\n//   \"siteName\": \"My Awesome Site\",\n//   \"version\": 1.0,\n//   \"isActive\": true,\n//   \"theme\": {\n//     \"primaryColor\": \"#3498db\",\n//     \"secondaryColor\": \"#2ecc71\",\n//     \"darkMode\": false\n//   },\n//   \"socialLinks\": [\n//     { \"platform\": \"Twitter\", \"url\": \"https://twitter.com/mysite\" },\n//     { \"platform\": \"Facebook\", \"url\": \"https://facebook.com/mysite\" },\n//     { \"platform\": \"LinkedIn\", \"url\": \"https://linkedin.com/company/mysite\" }\n//   ],\n//   \"footer\": {\n//     \"text\": \"© 2025 MySite. All rights reserved.\",\n//     \"links\": [\n//       { \"label\": \"Privacy Policy\", \"url\": \"/privacy\" },\n//       { \"label\": \"Terms of Service\", \"url\": \"/terms\" }\n//     ]\n//   },\n//   \"menu\": [\n//     { \"name\": \"Home\", \"url\": \"/\" },\n//     { \"name\": \"About\", \"url\": \"/about\" },\n//     { \"name\": \"Services\", \"url\": \"/services\" },\n//     { \"name\": \"Contact\", \"url\": \"/contact\" }\n//   ],\n//   \"languages\": [\"en\", \"es\", \"fr\", \"de\"],\n//   \"contactInfo\": {\n//     \"email\": \"<EMAIL>\",\n//     \"phone\": \"******-555-0176\",\n//     \"address\": {\n//       \"street\": \"123 Main St\",\n//       \"city\": \"New York\",\n//       \"country\": \"USA\"\n//     }\n//   },\n//   \"features\": [\n//     { \"id\": 1, \"title\": \"Fast\", \"description\": \"Our website loads super fast.\" },\n//     { \"id\": 2, \"title\": \"Secure\", \"description\": \"We use SSL and encryption.\" },\n//     { \"id\": 3, \"title\": \"Reliable\", \"description\": \"99.9% uptime guarantee.\" }\n//   ],\n//   \"testimonials\": [\n//     { \"name\": \"Alice\", \"review\": \"Great service!\", \"rating\": 5 },\n//     { \"name\": \"Bob\", \"review\": \"Very satisfied.\", \"rating\": 4 }\n//   ],\n//   \"faq\": [\n//     { \"question\": \"How do I sign up?\", \"answer\": \"Click the signup button.\" },\n//     { \"question\": \"Is it free?\", \"answer\": \"Yes, basic plan is free.\" }\n//   ],\n//   \"gallery\": [\n//     { \"id\": 1, \"imageUrl\": \"/images/pic1.jpg\" },\n//     { \"id\": 2, \"imageUrl\": \"/images/pic2.jpg\" }\n//   ],\n//   \"blogPosts\": [\n//     { \"title\": \"Post One\", \"author\": \"Admin\", \"date\": \"2025-01-01\" },\n//     { \"title\": \"Post Two\", \"author\": \"Guest\", \"date\": \"2025-02-15\" }\n//   ],\n//   \"stats\": {\n//     \"users\": 10500,\n//     \"subscribers\": 4500,\n//     \"visits\": 250000\n//   },\n//   \"partners\": [\n//     { \"name\": \"Company A\", \"logo\": \"/logos/a.png\" },\n//     { \"name\": \"Company B\", \"logo\": \"/logos/b.png\" }\n//   ],\n//   \"pricingPlans\": [\n//     { \"plan\": \"Free\", \"price\": 0, \"features\": [\"Basic Support\", \"1GB Storage\"] },\n//     { \"plan\": \"Pro\", \"price\": 19.99, \"features\": [\"Priority Support\", \"50GB Storage\"] }\n//   ],\n//   \"team\": [\n//     { \"name\": \"Jane Doe\", \"role\": \"CEO\", \"social\": { \"linkedin\": \"linkedin.com/in/janedoe\" } },\n//     { \"name\": \"John Smith\", \"role\": \"CTO\", \"social\": { \"github\": \"github.com/johnsmith\" } }\n//   ],\n//   \"events\": [\n//     { \"event\": \"Webinar\", \"date\": \"2025-05-10\", \"location\": \"Online\" },\n//     { \"event\": \"Conference\", \"date\": \"2025-07-20\", \"location\": \"San Francisco\" }\n//   ],\n//   \"notificationsEnabled\": true,\n//   \"defaultLanguage\": \"en\",\n//   \"currency\": \"USD\",\n//   \"maxUploadSize\": 10485760,\n//   \"apiEndpoints\": {\n//     \"auth\": \"/api/auth\",\n//     \"posts\": \"/api/posts\",\n//     \"users\": \"/api/users\"\n//   },\n//   \"integrations\": [\n//     { \"service\": \"Stripe\", \"enabled\": true },\n//     { \"service\": \"Google Analytics\", \"enabled\": false }\n//   ],\n//   \"newsletter\": {\n//     \"enabled\": true,\n//     \"signupUrl\": \"/newsletter/signup\"\n//   },\n//   \"cookiesConsent\": {\n//     \"required\": true,\n//     \"message\": \"We use cookies to improve your experience.\"\n//   }\n// }\n\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/websites/:id?mediaChanged=true", "host": ["{{URL}}"], "path": ["admin", "websites", ":id"], "query": [{"key": "mediaChanged", "value": "true"}], "variable": [{"key": "id", "value": "1"}]}}, "response": []}, {"name": "Update content -team", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n//   \"name\": \"My Website\",\n//   \"ip\":\"**********\",\n//   \"path\":\"home/tst/server\",\n//   \"username\":\"<PERSON><PERSON><PERSON><PERSON>\",\n//   \"sshKey\":\"fafdajkhfdjhjhKHFHahKJHuehiu#7&$34&@H2\"\n//   \"templateId\": 2,\n//   \"templateVersionId\": 8,\n//   \"primaryColor\": \"#787653\",\n//   \"secondaryColor\": \"#zyxabc\"\n//   \"backgroundColor\": \"#ffffff\",\n//   \"textColor\": \"#000002\",\n//   \"borderColor\": \"#cccccc\",\n//   \"cardBackgroundColor\": \"#f5f5f5\",\n//   \"dividerColor\": \"#e0e0e0\"\n//   \"websitevariables\": {\n//   \"brandName\": \"Example Brand\",\n//   \"customSettings\": [\n//     { \"showNavbar\": \"true\" },\n//     { \"footerText\": \"Powered by Dream Builder\" }\n//   ]\n// },\n//   \"websiteDynamicPageData\": {\n//     \"home\": {\n//       \"sitemapLabel\": \"Home\",\n//       \"navigationType\": \"Single Column\",\n//       \"sections\": [\n//         {\n//           \"sectionLabel\": \"Header\",\n//           \"sectionItems\": [\n//             {\n//               \"label\": \"Logo\",\n//               \"slug\": \"logo\",\n//               \"extra\": {\n//                 \"url\": \"https://example.com/logo.png\"\n//               }\n//             }\n//           ]\n//         }\n//       ],\n//       \"columnCount\": 1\n//     }\n//   },\n//   \"content\": {\n//   \"welcomeText\": \"Welcome to My Website\",\n//   \"siteName\": \"My Awesome Site\",\n//   \"version\": 1.0,\n//   \"isActive\": true,\n//   \"theme\": {\n//     \"primaryColor\": \"#3498db\",\n//     \"secondaryColor\": \"#2ecc71\",\n//     \"darkMode\": false\n//   },\n//   \"socialLinks\": [\n//     { \"platform\": \"Twitter\", \"url\": \"https://twitter.com/mysite\" },\n//     { \"platform\": \"Facebook\", \"url\": \"https://facebook.com/mysite\" },\n//     { \"platform\": \"LinkedIn\", \"url\": \"https://linkedin.com/company/mysite\" }\n//   ],\n//   \"footer\": {\n//     \"text\": \"© 2025 MySite. All rights reserved.\",\n//     \"links\": [\n//       { \"label\": \"Privacy Policy\", \"url\": \"/privacy\" },\n//       { \"label\": \"Terms of Service\", \"url\": \"/terms\" }\n//     ]\n//   },\n//   \"menu\": [\n//     { \"name\": \"Home\", \"url\": \"/\" },\n//     { \"name\": \"About\", \"url\": \"/about\" },\n//     { \"name\": \"Services\", \"url\": \"/services\" },\n//     { \"name\": \"Contact\", \"url\": \"/contact\" }\n//   ],\n//   \"languages\": [\"en\", \"es\", \"fr\", \"de\"],\n//   \"contactInfo\": {\n//     \"email\": \"<EMAIL>\",\n//     \"phone\": \"******-555-0176\",\n//     \"address\": {\n//       \"street\": \"123 Main St\",\n//       \"city\": \"New York\",\n//       \"country\": \"USA\"\n//     }\n//   },\n//   \"features\": [\n//     { \"id\": 1, \"title\": \"Fast\", \"description\": \"Our website loads super fast.\" },\n//     { \"id\": 2, \"title\": \"Secure\", \"description\": \"We use SSL and encryption.\" },\n//     { \"id\": 3, \"title\": \"Reliable\", \"description\": \"99.9% uptime guarantee.\" }\n//   ],\n//   \"testimonials\": [\n//     { \"name\": \"Alice\", \"review\": \"Great service!\", \"rating\": 5 },\n//     { \"name\": \"Bob\", \"review\": \"Very satisfied.\", \"rating\": 4 }\n//   ],\n//   \"faq\": [\n//     { \"question\": \"How do I sign up?\", \"answer\": \"Click the signup button.\" },\n//     { \"question\": \"Is it free?\", \"answer\": \"Yes, basic plan is free.\" }\n//   ],\n//   \"gallery\": [\n//     { \"id\": 1, \"imageUrl\": \"/images/pic1.jpg\" },\n//     { \"id\": 2, \"imageUrl\": \"/images/pic2.jpg\" }\n//   ],\n//   \"blogPosts\": [\n//     { \"title\": \"Post One\", \"author\": \"Admin\", \"date\": \"2025-01-01\" },\n//     { \"title\": \"Post Two\", \"author\": \"Guest\", \"date\": \"2025-02-15\" }\n//   ],\n//   \"stats\": {\n//     \"users\": 10500,\n//     \"subscribers\": 4500,\n//     \"visits\": 250000\n//   },\n//   \"partners\": [\n//     { \"name\": \"Company A\", \"logo\": \"/logos/a.png\" },\n//     { \"name\": \"Company B\", \"logo\": \"/logos/b.png\" }\n//   ],\n//   \"pricingPlans\": [\n//     { \"plan\": \"Free\", \"price\": 0, \"features\": [\"Basic Support\", \"1GB Storage\"] },\n//     { \"plan\": \"Pro\", \"price\": 19.99, \"features\": [\"Priority Support\", \"50GB Storage\"] }\n//   ],\n//   \"team\": [\n//     { \"name\": \"Jane Doe\", \"role\": \"CEO\", \"social\": { \"linkedin\": \"linkedin.com/in/janedoe\" } },\n//     { \"name\": \"John Smith\", \"role\": \"CTO\", \"social\": { \"github\": \"github.com/johnsmith\" } }\n//   ],\n//   \"events\": [\n//     { \"event\": \"Webinar\", \"date\": \"2025-05-10\", \"location\": \"Online\" },\n//     { \"event\": \"Conference\", \"date\": \"2025-07-20\", \"location\": \"San Francisco\" }\n//   ],\n//   \"notificationsEnabled\": true,\n//   \"defaultLanguage\": \"en\",\n//   \"currency\": \"USD\",\n//   \"maxUploadSize\": 10485760,\n//   \"apiEndpoints\": {\n//     \"auth\": \"/api/auth\",\n//     \"posts\": \"/api/posts\",\n//     \"users\": \"/api/users\"\n//   },\n//   \"integrations\": [\n//     { \"service\": \"Stripe\", \"enabled\": true },\n//     { \"service\": \"Google Analytics\", \"enabled\": false }\n//   ],\n//   \"newsletter\": {\n//     \"enabled\": true,\n//     \"signupUrl\": \"/newsletter/signup\"\n//   },\n//   \"cookiesConsent\": {\n//     \"required\": true,\n//     \"message\": \"We use cookies to improve your experience.\"\n//   }\n// }\n\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/websites/:id?mediaChanged=true", "host": ["{{URL}}"], "path": ["websites", ":id"], "query": [{"key": "mediaChanged", "value": "true"}], "variable": [{"key": "id", "value": "1"}]}}, "response": []}, {"name": "DeleteOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/websites/:id", "host": ["{{URL}}"], "path": ["websites", ":id"], "variable": [{"key": "id"}]}}, "response": []}]}, {"name": "websiteversions", "item": [{"name": "Create", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"primaryColour\": \"8eO2y\",\n  \"secodaryColour\": \"Wytvh\",\n  \"version\": 5806009817759744,\n  \"logoUrl\": \"tHCZW\",\n  \"content\": \"EBxAoQHCe8RLJjEKmTx5\",\n  \"fontFamily\": \"pvNgT\",\n  \"favicon\": \"blvu1\",\n  \"webisteDynamicPageIds\": \"of0OQ3C8QlNcEfrSfljI\",\n  \"websiteDynamicPageData\": \"DQ0LUBtj9sjwPQtEt1JM\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/websiteversions", "host": ["{{URL}}"], "path": ["websiteversions"]}}, "response": []}, {"name": "GetOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/websiteversions/:id", "host": ["{{URL}}"], "path": ["websiteversions", ":id"], "variable": [{"key": "id"}]}}, "response": []}, {"name": "GetAll", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/websiteversions", "host": ["{{URL}}"], "path": ["websiteversions"]}}, "response": []}, {"name": "UpdateOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n  \"primaryColour\": \"7NP1o\",\n  \"secodaryColour\": \"X0NVM\",\n  \"version\": 4735240947892224,\n  \"logoUrl\": \"yZVmA\",\n  \"content\": \"d70arxdkhbOm7RbVF8FF\",\n  \"fontFamily\": \"lhEZ0\",\n  \"favicon\": \"dYWGS\",\n  \"webisteDynamicPageIds\": \"m7v2lUuDlZrnMgu5n7Vu\",\n  \"websiteDynamicPageData\": \"zQS3X2w9i8hNzyVc9MUf\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/websiteversions/:id", "host": ["{{URL}}"], "path": ["websiteversions", ":id"], "variable": [{"key": "id"}]}}, "response": []}, {"name": "DeleteOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/websiteversions/:id", "host": ["{{URL}}"], "path": ["websiteversions", ":id"], "variable": [{"key": "id"}]}}, "response": []}]}, {"name": "deploymenthistory", "item": [{"name": "Create", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"status\": \"vEuUM\",\n  \"ip\": \"vbQAX\",\n  \"path\": \"ZzCp<PERSON>\",\n  \"username\": \"PrzHI\",\n  \"sshKey\": \"LZZ1m\",\n  \"fromVersion\": 6825057342980096,\n  \"toVersion\": 179451082047488\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/deploymenthistory", "host": ["{{URL}}"], "path": ["deploymenthistory"]}}, "response": []}, {"name": "GetOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/deploymenthistory/:id", "host": ["{{URL}}"], "path": ["deploymenthistory", ":id"], "variable": [{"key": "id"}]}}, "response": []}, {"name": "GetAll", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/deploymenthistory", "host": ["{{URL}}"], "path": ["deploymenthistory"]}}, "response": []}, {"name": "UpdateOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n  \"status\": \"IiLNs\",\n  \"ip\": \"4BNlO\",\n  \"path\": \"WJaC8\",\n  \"username\": \"KJBGl\",\n  \"sshKey\": \"2vb6r\",\n  \"fromVersion\": 3226037917843456,\n  \"toVersion\": 7182880509788160\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/deploymenthistory/:id", "host": ["{{URL}}"], "path": ["deploymenthistory", ":id"], "variable": [{"key": "id"}]}}, "response": []}, {"name": "DeleteOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/deploymenthistory/:id", "host": ["{{URL}}"], "path": ["deploymenthistory", ":id"], "variable": [{"key": "id"}]}}, "response": []}]}, {"name": "media", "item": [{"name": "Create", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"webisteVersions\": \"Pac81ktz1XUpuQ0BTign\",\n  \"name\": \"o0RAt\",\n  \"url\": \"9hGUv\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/media", "host": ["{{URL}}"], "path": ["media"]}}, "response": []}, {"name": "GetOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/media/:id", "host": ["{{URL}}"], "path": ["media", ":id"], "variable": [{"key": "id"}]}}, "response": []}, {"name": "GetAll", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/media", "host": ["{{URL}}"], "path": ["media"]}}, "response": []}, {"name": "UpdateOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n  \"webisteVersions\": \"QZnnBkECCKspytGo3RVk\",\n  \"name\": \"87g66\",\n  \"url\": \"qJM0t\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/media/:id", "host": ["{{URL}}"], "path": ["media", ":id"], "variable": [{"key": "id"}]}}, "response": []}, {"name": "DeleteOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/media/:id", "host": ["{{URL}}"], "path": ["media", ":id"], "variable": [{"key": "id"}]}}, "response": []}]}, {"name": "globalsettings", "item": [{"name": "Create", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"variables\": \"city, phone, state, email, zipcode, country, address, supportNumber, website\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/admin/globalsettings", "host": ["{{URL}}"], "path": ["admin", "globalsettings"]}}, "response": []}, {"name": "GetOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/globalsettings/:id", "host": ["{{URL}}"], "path": ["globalsettings", ":id"], "variable": [{"key": "id"}]}}, "response": []}, {"name": "GetAll", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/globalsettings", "host": ["{{URL}}"], "path": ["globalsettings"]}}, "response": []}, {"name": "UpdateOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n  \"variables\": \"4HeBu\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/globalsettings/:id", "host": ["{{URL}}"], "path": ["globalsettings", ":id"], "variable": [{"key": "id"}]}}, "response": []}, {"name": "DeleteOne", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{JWT}}", "type": "string"}]}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/globalsettings/:id", "host": ["{{URL}}"], "path": ["globalsettings", ":id"], "variable": [{"key": "id"}]}}, "response": []}]}]}