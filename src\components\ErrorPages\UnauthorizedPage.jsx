import React from "react";
import { Result, <PERSON><PERSON>, <PERSON>, Typography } from "antd";
import { useNavigate } from "react-router-dom";
import { HomeOutlined, LoginOutlined, LockOutlined } from "@ant-design/icons";

const { Title, Paragraph } = Typography;

const UnauthorizedPage = () => {
  const navigate = useNavigate();

  const handleGoHome = () => {
    navigate("/");
  };

  const handleLogin = () => {
    navigate("/login");
  };

  const handleContactAdmin = () => {
    // You can implement contact admin functionality here
    console.log("Contact admin clicked");
  };

  return (
    <div className="tw-min-h-screen tw-flex tw-items-center tw-justify-center tw-bg-gradient-to-br tw-from-yellow-50 tw-to-orange-100">
      <div className="tw-max-w-2xl tw-w-full tw-mx-4">
        <Card className="tw-shadow-lg tw-border-0">
          <Result
            icon={<LockOutlined className="tw-text-6xl tw-text-orange-500" />}
            title="403"
            subTitle="Access Denied"
            extra={[
              <Button
                type="primary"
                icon={<HomeOutlined />}
                onClick={handleGoHome}
                className="tw-mr-2"
                key="home"
              >
                Back Home
              </Button>,
              <Button
                icon={<LoginOutlined />}
                onClick={handleLogin}
                className="tw-mr-2"
                key="login"
              >
                Login
              </Button>,
              <Button
                icon={<LockOutlined />}
                onClick={handleContactAdmin}
                key="contact"
              >
                Contact Admin
              </Button>,
            ]}
          />

          <div className="tw-mt-6 tw-text-center">
            <Title level={4} className="tw-text-gray-600">
              You don't have permission to access this resource
            </Title>
            <Paragraph className="tw-text-gray-500">
              This could be because:
            </Paragraph>
            <ul className="tw-text-left tw-text-gray-500 tw-mt-4">
              <li>Your session has expired</li>
              <li>You don't have the required permissions</li>
              <li>Your account has been suspended or blocked</li>
              <li>You need to log in with a different account</li>
            </ul>
            <Paragraph className="tw-text-sm tw-text-gray-400 tw-mt-4">
              If you believe this is an error, please contact your
              administrator.
            </Paragraph>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default UnauthorizedPage;
