import React from "react";
import { Result, Button } from "antd";
import { useNavigate } from "react-router-dom";
import { HomeOutlined, ReloadOutlined } from "@ant-design/icons";

const Error404 = () => {
  const navigate = useNavigate();

  const handleGoHome = () => {
    navigate("/");
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  return (
    <div className="tw-min-h-screen tw-flex tw-items-center tw-justify-center tw-bg-gray-50">
      <div className="tw-max-w-md tw-w-full tw-mx-4">
        <Result
          status="404"
          title="404"
          subTitle="Sorry, the page you visited does not exist."
          extra={[
            <Button
              type="primary"
              icon={<HomeOutlined />}
              onClick={handleGoHome}
              className="tw-mr-2"
              key="home"
            >
              Back Home
            </Button>,
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              key="refresh"
            >
              Refresh
            </Button>,
          ]}
        />
      </div>
    </div>
  );
};

export default Error404;
