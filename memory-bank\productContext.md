# Product Context

## 1. Problem Statement

Building static websites for multiple clients or departments manually is time-consuming and requires development skills. This project aims to solve this by providing a scalable and templated solution that saves time, allows branding customization, and enables team collaboration.

## 2. Vision

The vision is to create a Static Site Generator (SSG) that enables non-technical users to create, customise, and deploy HTML websites using a drag-and-drop interface with pre-built components, branding options, and content injection via structured data.

## 3. User Experience Goals

The user experience should be simple and intuitive, allowing users without development skills to easily build and manage websites. The platform should feel powerful and fast, enabling efficient creation and deployment.

## 4. Key Features & Functionality

- Drag-and-drop page builder from categorized components.
- Dynamic content insertion via sheet or JSON (key-value).
- Version control for components, pages, templates, and websites.
- HTML export and SFTP-based deployment with rollback history.
- Auto image upload, storage, and content mapping.
- Role-based access (Admin, Content Team).
- System pages: 404, 500, Maintenance, Unauthorized Access.
