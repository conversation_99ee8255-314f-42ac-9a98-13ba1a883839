import React from "react";
import { Navigate } from "react-router-dom";
import Login from "../components/Auth/Login";
import Dashboard from "../components/Dashboard/Dashboard";
import Layout from "../components/Layout/Layout";
import FeatureLayout from "../components/Layout/FeatureLayout";
import PageBuilder from "../components/Pages/PageBuilder";
import DynamicPages from "../components/FeaturePage/DynamicPages";
import TemplateManager from "../components/Templates/TemplateManager";
import TemplateEditorWrapper from "../components/Templates/TemplateEditorWrapper";
import UserManager from "../components/Users/<USER>";
import WebsiteManager from "../components/Websites/WebsiteManager";
import { AuthProvider, useAuth } from "../contexts/AuthContext";
import ComponentManager from "../components/Components/ComponentManager";
import CategoryManager from "../components/Categories/CategoryManager";
import ActivityLogs from "../components/ActivityLogs/ActivityLogs";
import { Spin } from "antd";
import GlobalVariable from "../components/GlobalVariable";
// Error Pages
import Error404 from "../components/ErrorPages/Error404";
import Error500 from "../components/ErrorPages/Error500";
import MaintenancePage from "../components/ErrorPages/MaintenancePage";
import InternetLossPage from "../components/ErrorPages/InternetLossPage";
import UnauthorizedPage from "../components/ErrorPages/UnauthorizedPage";
import RouterWithMonitor from "../components/ErrorPages/RouterWithMonitor";
// import DynamicPages from "../FeaturePage/DynamicPages";

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="tw-flex tw-items-center tw-justify-center tw-min-h-screen">
        <Spin />
      </div>
    );
  }

  return user ? <>{children}</> : <Navigate to="/login" replace />;
};

export const ROUTES = {
  dashboard: "dashboard",
  pages: "pages",
  dynamicPages: "dynamic-pages",
  categories: "categories",
  components: "components",
  templates: "templates",
  websites: "websites",
  activityLogs: "activity-logs",
  globalVariable: "global-variable",
  users: "users",
  // Error routes
  error404: "404",
  error500: "500",
  maintenance: "maintenance",
  internetLoss: "internet",
  unauthorized: "unauthorized",
  blockuser: "blockuser",
  suspendeduser: "suspendeduser",
};

export const ROLES = {
  ADMIN: "admin",
  CONTENT: "content-team",
};

export const appRoot = "/";

export const adminRole = "admin";
export const contentRole = "content-team";

// Get layout mode from environment
const getLayoutComponent = () => {
  const layoutMode = import.meta.env.VITE_LAYOUT_MODE;
  return Layout;
  // return layoutMode === "feature" ? Layout : FeatureLayout;
};

const LOGIN_ROUTES = [
  {
    index: true,
    path: "/login",
    element: <Login />,
  },
];

const ALL_ROUTES = (appProps) => {
  const LayoutComponent = getLayoutComponent();

  return [
    // Root route with RouterWithMonitor wrapper
    ...LOGIN_ROUTES,
    {
      path: "/",
      element: (
        <ProtectedRoute>
          <LayoutComponent {...appProps} />
        </ProtectedRoute>
      ),
      // element: <RouterWithMonitor />,
      children: [
        // Redirect root to dashboard

        {
          index: true,
          element: <Navigate to={ROUTES.dashboard} {...appProps} />,
        },
        {
          path: `${ROUTES.dashboard}`,
          element: <Dashboard {...appProps} />,
          Role: [ROLES.ADMIN, ROLES.CONTENT],
        },
        {
          path: ROUTES.pages,
          element: <PageBuilder {...appProps} />,
          Role: [ROLES.ADMIN],
          children: [
            {
              path: "add",
              element: <PageBuilder {...appProps} />,
              Role: [ROLES.ADMIN],
            },
            {
              path: ":id",
              element: <PageBuilder {...appProps} />,
              Role: [ROLES.ADMIN],
            },
          ],
        },
        {
          path: ROUTES.dynamicPages,
          element: <DynamicPages {...appProps} />,
          Role: [ROLES.ADMIN],
        },
        {
          path: "categories",
          element: <CategoryManager {...appProps} />,
          Role: [ROLES.ADMIN],
          children: [
            {
              path: "add",
              element: <CategoryManager {...appProps} />,
              Role: [ROLES.ADMIN],
            },
            {
              path: ":id",
              element: <CategoryManager {...appProps} />,
              Role: [ROLES.ADMIN],
            },
          ],
        },
        {
          path: ROUTES.components,
          element: <ComponentManager {...appProps} />,
          Role: [ROLES.ADMIN],
          children: [
            {
              path: "add",
              element: <ComponentManager {...appProps} />,
              Role: [ROLES.ADMIN],
            },
            {
              path: ":id",
              element: <ComponentManager {...appProps} />,
              Role: [ROLES.ADMIN],
            },
          ],
        },
        {
          path: ROUTES.templates,
          element: <TemplateManager {...appProps} />,
          Role: [ROLES.ADMIN],
          children: [
            {
              path: "add",
              element: <TemplateEditorWrapper {...appProps} />,
              Role: [ROLES.ADMIN],
            },
            {
              path: ":id",
              element: <TemplateEditorWrapper {...appProps} />,
              Role: [ROLES.ADMIN],
            },
          ],
        },
        {
          path: ROUTES.websites,
          element: <WebsiteManager {...appProps} />,
        },
        {
          path: ROUTES.activityLogs,
          element: <ActivityLogs {...appProps} />,
          Role: [ROLES.ADMIN],
        },
        {
          path: ROUTES.globalVariable,
          element: <GlobalVariable {...appProps} />,
          Role: [ROLES.ADMIN],
        },
        {
          path: ROUTES.users,
          element: <UserManager {...appProps} />,
          Role: [ROLES.ADMIN],
        },

        // Login route (separate from protected routes)
        // {
        //   path: "login",
        //   element: <Login />,
        // },

        // Protected routes with Layout
        // {
        //   path: `${ROUTES.dashboard}`,
        //   element: (
        //     <Dashboard {...appProps} />
        //   ),
        //   Role: [ROLES.ADMIN, ROLES.CONTENT],
        //   children: [
        //     {
        //       index: true,
        //       element: <Dashboard {...appProps} />,
        //     },
        //     {
        //       path: ROUTES.pages,
        //       element: <PageBuilder {...appProps} />,
        //       children: [
        //         {
        //           path: "add",
        //           element: <PageBuilder {...appProps} />,
        //         },
        //         {
        //           path: ":id",
        //           element: <PageBuilder {...appProps} />,
        //         },
        //       ],
        //     },
        //     {
        //       path: ROUTES.dynamicPages,
        //       element: <DynamicPages {...appProps} />,
        //     },
        //     {
        //       path: "categories",
        //       element: <CategoryManager {...appProps} />,
        //       children: [
        //         {
        //           path: "add",
        //           element: <CategoryManager {...appProps} />,
        //         },
        //         {
        //           path: ":id",
        //           element: <CategoryManager {...appProps} />,
        //         },
        //       ],
        //     },
        //     {
        //       path: ROUTES.components,
        //       element: <ComponentManager {...appProps} />,
        //       children: [
        //         {
        //           path: "add",
        //           element: <ComponentManager {...appProps} />,
        //         },
        //         {
        //           path: ":id",
        //           element: <ComponentManager {...appProps} />,
        //         },
        //       ],
        //     },
        //     {
        //       path: ROUTES.templates,
        //       element: <TemplateManager {...appProps} />,
        //       children: [
        //         {
        //           path: "add",
        //           element: <TemplateEditorWrapper {...appProps} />,
        //         },
        //         {
        //           path: ":id",
        //           element: <TemplateEditorWrapper {...appProps} />,
        //         },
        //       ],
        //     },
        //     {
        //       path: ROUTES.websites,
        //       element: <WebsiteManager {...appProps} />,
        //     },
        //     {
        //       path: ROUTES.activityLogs,
        //       element: <ActivityLogs {...appProps} />,
        //     },
        //     {
        //       path: ROUTES.globalVariable,
        //       element: <GlobalVariable {...appProps} />,
        //     },
        //     {
        //       path: ROUTES.users,
        //       element: <UserManager {...appProps} />,
        //     },
        //   ],
        // },
      ],
    },
    // Error pages (public routes)
    {
      path: `/${ROUTES.error404}`,
      element: <Error404 />,
    },
    {
      path: `/${ROUTES.error500}`,
      element: <Error500 />,
    },
    {
      path: `/${ROUTES.maintenance}`,
      element: <MaintenancePage />,
    },
    {
      path: `/${ROUTES.internetLoss}`,
      element: <InternetLossPage />,
    },
    {
      path: `/${ROUTES.unauthorized}`,
      element: <UnauthorizedPage />,
    },
    {
      path: `/${ROUTES.blockuser}`,
      element: <UnauthorizedPage />,
    },
    {
      path: `/${ROUTES.suspendeduser}`,
      element: <UnauthorizedPage />,
    },

    // Fallback route - show 404 instead of redirecting to login
    {
      path: "*",
      element: <Navigate to="/error" replace />,
    },
  ];
};

export default ALL_ROUTES;
