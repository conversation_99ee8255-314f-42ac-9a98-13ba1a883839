import { useState, useEffect } from "react";
import { Typography } from "antd";
import { <PERSON><PERSON><PERSON>, Eye, Globe2 } from "lucide-react";
import DynamicTab from "./components/DynamicTab";
import BradingTab from "./components/BradingTab";
import useStorage from "../../hooks/use-storage";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { dynamicContentForm } from "./components/function";
// import TemplateJSON from "../utils/homeNew_03_09";
import TemplateJSON from "../../utils/homeNewVersion_08-09";
import GlobalVariiableTab from "./components/GlobalVariiableTab";
import { busAmenities, createBusList } from "../../utils/ContentFileArray";
import { getSvg } from "../../utils/svg";
import { createEmptyObj, getCompanySlug } from "../../util/functions";
import { buses<PERSON><PERSON><PERSON>, service<PERSON><PERSON><PERSON><PERSON><PERSON>, service<PERSON><PERSON><PERSON> } from "../../util/content";
import TabList from "../common/TabList";

const { Title, Text } = Typography;
// import home from "../utils/home.json";
// import home from "../utils/homeNew.json";======
// import home from "../utils/template_31_08_latest.json";

// import template from "../utils/MainTemplateFile.json";
// import template from "../utils/template.json";

// Default page structure creator function
const createDefaultPage = (type = "Single Column") => ({
  sitemapLabel: "",
  navigationType: type,
  sections: [],
  columnCount: 0,
  // type.includes("Multi Column") ? 2 : undefined,
});

const home = TemplateJSON();

const DynamicPages = () => {
  const [previewMode, setPreviewMode] = useState("dynamic_pages");
  const [templateObj, setTemplateObj] = useState(null);
  const [dynamicForms, setdynamicForms] = useState(
    {}
    // JSON.parse(JSON.stringify(dynamicContentForm)) || dynamicContentForm
  );
  const [components, setComponents] = useState([]);
  const [saving, setSaving] = useState(false);
  const api = useStorage();
  // console.log(dynamicForms, "templatePage");
  const [bradingDetails, setBradingDetails] = useState({
    //clifton
    // primaryColor: "#d5232b",
    // secondaryColor: "#C41421",
    // backgroundColor: "#ffffff",
    // textColor: "#333333",
    // borderColor: "#e0e0e0",
    // cardBackgroundColor: "#fafafa",
    // dividerColor: "#eee",
    // grand-island
    primaryColor: "#38b6ff",
    secondaryColor: "#004e7a",
    backgroundColor: "#ffffff",
    textColor: "#333333",
    borderColor: "#e0e0e0",
    cardBackgroundColor: "#fafafa",
    dividerColor: "#eee",
  });

  useEffect(() => {
    let alive = true;

    const setBC = (root, keys, { current, anchors }) => {
      const bc = createEmptyObj(root, [...keys, "breadcrumb"]);
      bc.current_page = current;
      bc.anchorTag_item = anchors;
    };

    // const getBreadcrumbLabel = (label, key) =>
    //   key === serviceAreaDKey ? `${label} Charter Bus Rental` : label;

    (async () => {
      try {
        //https://dreambuilder.wooffer.io
        const res = await fetch("https://dreambuilder.wooffer.io/api/assets");
        const data = await res.json();

        const companyName = home?.GVariable?.company_name ?? "";
        const defaultCity = home?.GVariable?.default_city ?? "";
        const websiteBase = home?.GVariable?.website_url ?? "/";
        // console.log(websiteBase);
        const defaultAnchor = {
          title: companyName,
          slug: websiteBase + "index",
        };

        // --- Build FileList additions (tight loop, no map side-effects)
        const imgVar = {};
        const files = data?.files || [];
        for (let i = 0; i < files.length; i++) {
          const fileName = files[i];
          const fileKey = fileName.split(".")[0];
          imgVar[`_img-${fileKey}`] = {
            url: `/assets/${fileName}`,
            name: fileName,
            path: `/assets/${fileName}`,
          };
        }

        // --- make a new top-level ref (no deep clone)
        const next = {
          ...home,
          FileList: { ...(home?.FileList || {}), ...imgVar },
          contentJSON: { ...(home?.contentJSON || {}) },
          templateContentJSON: { ...(home?.templateContentJSON || {}) },
        };

        // ---------- Precompute busesList once ----------
        const busesSections = dynamicForms?.[busesDKey] || [];
        const busesAnchor = {
          title: busesSections?.breadcrumbName,
          slug: `/buses`,
        };
        // ------------- Static Buses page breadcrumb ------------------
        setBC(next.templateContentJSON, ["Buses"], {
          current: busesSections?.breadcrumbName,
          anchors: [defaultAnchor],
        });
        // console.log(busesSections?.sections, "busesSections");
        // ------------- Dynamic Buses page breadcrumb ------------------
        const sortedBusSections = busesSections?.sections
          ?.slice()
          ?.sort((a, b) =>
            (a?.sectionLabel || "")?.localeCompare(b?.sectionLabel || "")
          );

        const companySplit = companyName?.split(" ")?.slice(0, 3)?.join(" ");

        const busesList = [];
        if (sortedBusSections?.length) {
          for (const section of sortedBusSections) {
            const items = section?.sectionItems || [];
            for (let i = 0; i < items?.length; i++) {
              const item = items?.[i];
              // console.log(item, "item", items, i);
              busesList.push({
                ...item,
                // breadcrumbName: section?.breadcrumbName,
                // breadcrumbURL: section?.breadcrumbURL,
                sectionLabel: section?.sectionLabel,
                companyName: companySplit,
              });
            }
          }
        }

        // ---------- Global: ensure per-item breadcrumbs under each form key ----------
        // const keys = Object.keys(dynamicForms || {});
        // console.log(keys, "keys");
        // for (let ki = 0; ki < keys.length; ki++) {
        //   const key = keys[ki];
        //   const sections = dynamicForms[key]?.sections || [];
        //   for (const section of sections) {
        //     const items = section?.sectionItems || [];
        //     for (const item of items) {
        //       createEmptyObj(next.contentJSON, [item?.label]);
        //       setBC(next.contentJSON, [item?.label], {
        //         current: item?.extra?.breadcrumbName || item?.label,
        //         anchors: [
        //           defaultAnchor,
        //           {
        //             title: section?.breadcrumbName,
        //             slug: `${section?.breadcrumbURL}`,
        //           },
        //         ],
        //       });
        //     }
        //   }
        // }

        // ---------- Buses pages ----------
        // console.log(busesList, "busesList");
        if (busesList.length) {
          for (const item of busesList) {
            const page = createEmptyObj(next.contentJSON, [item?.label]);

            // bus_grid_list -> ${label}_busesList
            const grid = createEmptyObj(next.contentJSON, [
              item?.label,
              "bus_grid_list",
            ]);
            grid[`${item?.label}_busesList`] = createBusList(
              busesList,
              item?.slug,
              "prefix"
            );

            // bus_amenities
            const amenities = createEmptyObj(next.contentJSON, [
              item?.label,
              "bus_amenities",
            ]);
            const amenitiesArr = busAmenities?.[item?.slug] || [];
            amenities[`${item?.label}_specific_bus_amenities_list`] =
              amenitiesArr;
            for (let i = 0; i < amenitiesArr.length; i++) {
              const a = amenitiesArr[i];
              amenities[`${a?.amenities_label}_icon`] = getSvg(
                a?.["bus-amenities-image"]
              );
            }

            // Interior images
            page["bus_interior_view"] = {
              "bus-interior-image-1":
                item?.extra?.["bus-interior-image-1"] || null,
              "bus-interior-image-2":
                item?.extra?.["bus-interior-image-2"] || null,
              "bus-interior-image-3":
                item?.extra?.["bus-interior-image-3"] || null,
            };
            // console.log(page, "page", item);
            // Bus page details
            page["buspage_bus_details"] = {
              "bus-image": item?.extra?.["bus-image"] || null,
            };

            // Bus page breadcrumb (override to “Our <city> Fleet” path)
            setBC(next.contentJSON, [item?.label], {
              current: item?.extra?.breadcrumbName || item?.label,
              anchors: [defaultAnchor, busesAnchor],
            });
          }

          // Buses hub page grid
          const hubGrid = createEmptyObj(next.contentJSON, [
            "Buses",
            "bus_grid_list",
          ]);
          hubGrid.busesList = createBusList(busesList);
        }

        // ---------- Services pages ----------
        {
          const sectionData = dynamicForms?.[serviceDKey] || [];
          const servicesAnchor = {
            title: sectionData?.breadcrumbName,
            slug: `/group-transportation-services`,
          };
          // ------------- Static Services page breadcrumb ------------------
          setBC(next.templateContentJSON, ["Services"], {
            current: sectionData?.breadcrumbName,
            anchors: [defaultAnchor],
          });
          // ------------- Dynamic Services page breadcrumb ------------------
          if (sectionData?.sections?.length) {
            for (const section of sectionData?.sections) {
              const items = section?.sectionItems || [];
              for (const item of items) {
                const page = createEmptyObj(next.contentJSON, [item?.label]);

                page["content_image1"] = {
                  "content-image": item?.extra?.content_image1 || null,
                };
                page["content_image2"] = {
                  "content-image": item?.extra?.content_image2 || null,
                };
                page["h2_content_title8"] = {
                  content_title: item?.extra?.content_title || null,
                };
                page["event_cards_wrapper"] = {
                  event_card: item?.extra?.event_card || null,
                };

                setBC(next.contentJSON, [item?.label], {
                  current: item?.extra?.breadcrumbName || item?.label,
                  anchors: [defaultAnchor, servicesAnchor],
                });
              }
            }
          }
        }

        // ---------- Service-area pages ----------
        {
          const sectionData = dynamicForms?.[serviceAreaDKey] || [];
          const svcSections = (sectionData?.sections || [])
            ?.slice()
            ?.sort((a, b) =>
              (a?.sectionLabel || "")?.localeCompare(b?.sectionLabel || "")
            );
          // ------------- Static Service-area page breadcrumb ------------------
          setBC(next?.templateContentJSON, ["services-area"], {
            current: sectionData?.breadcrumbName,
            anchors: [defaultAnchor],
          });
          // ------------- Dynamic Service-area page breadcrumb ------------------
          for (const section of svcSections) {
            const items = section?.sectionItems || [];
            for (const item of items) {
              const page = createEmptyObj(next.contentJSON, [item?.label]);

              // hero_section_home_page
              const hero = createEmptyObj(next.contentJSON, [
                item?.label,
                "hero_section_home_page",
              ]);
              hero.current_page = item?.extra?.breadcrumbName;
              hero.anchorTag_item = [
                defaultAnchor,
                {
                  title: sectionData?.breadcrumbName,
                  slug: `/service-area`,
                },
              ];

              // city grid
              const grid = createEmptyObj(next.contentJSON, [
                item?.label,
                "bus_grid_list",
              ]);
              grid.busesList = createBusList(
                busesList,
                null,
                "suffix",
                item?.label,
                true
              );
            }
          }
        }

        if (alive) setTemplateObj(next);
      } catch (e) {
        console.error("build content failed:", e);
      }
    })();

    return () => {
      alive = false;
    };
  }, [dynamicForms]);

  useEffect(() => {
    // JSON storage calls
    // setTimeout(() => {
    if (Object.keys(dynamicForms)?.length > 0) return;
    templateObj?.pages?.forEach((page) => {
      if (page.type == "dynamic") {
        setdynamicForms((prev) => ({
          ...prev,
          [page.name]: createDefaultPage(),
        }));
      }
    });

    // api.sendRequest(CONSTANTS.API.templates.get, (res) => {
    //   console.log("Templates fetched from storage:", res);
    //   // Try to find the specific template, or use the first available template for testing
    //   let template = res.find((t) => t.id === "memu53w7j3u1pftta4m");

    //   //now i wan to template inside pages array type is dynamic the store in setpages
    //   template?.pages?.forEach((page) => {
    //     console.log("Processing page:", page);
    //     if (page.type === "dynamic") {
    //       console.log("Adding dynamic page:", page.name);
    //       setPages((prev) => ({
    //         ...prev,
    //         [page.name]: createDefaultPage(),
    //       }));
    //     }
    //   });

    //   // For testing, let's add some default dynamic pages if none exist
    //   if (!template?.pages?.some((page) => page.type === "dynamic")) {
    //     console.log("No dynamic pages found, adding test pages");
    //     setPages({
    //       City: createDefaultPage(),
    //       Service: createDefaultPage(),
    //     });
    //   }

    //   setTemplatePage(template);
    // });
    // }, 1000);
  }, [templateObj]); // Remove formInstancesRef.current from dependency array

  // console.log(dynamicForms, "dynamicForms");
  useEffect(() => {
    // Fetch components for repeated component functionality
    api.sendRequest(CONSTANTS.API.components.get, (res) => {
      // console.log("Components fetched for template editor:", res);
      setComponents(res);
    });
  }, []);

  const tabList = {
    global_Variable: {
      key: "global_Variable",
      label: "Global Variables",
      icon: <Globe2 className="tw-w-4 tw-h-4 tw-mr-2" />,
      content: (
        <div className=" tw-pt-4">
          <GlobalVariiableTab
            templatePage={templateObj}
            setTemplatePage={setTemplateObj}
          />
        </div>
      ),
    },
    dynamic_pages: {
      key: "dynamic_pages",
      label: "Dynamic Pages",
      icon: <Settings className="tw-w-4 tw-h-4 tw-mr-2" />,
      content: (
        <div className=" tw-pt-4">
          <DynamicTab
            templatePage={templateObj}
            // setTemplatePage={setTemplatePage}
            pages={dynamicForms}
            setPages={setdynamicForms}
            components={components}
          />
        </div>
      ),
    },
    branding: {
      key: "branding",
      label: "Branding & Content Preview",
      icon: <Eye className="tw-w-4 tw-h-4 tw-mr-2" />,
      content: (
        <div className=" tw-pt-4">
          <BradingTab
            templateObj={templateObj}
            setTemplateObj={setTemplateObj}
            components={components}
            saving={saving}
            dynamicContentForm={dynamicForms}
            bradingDetails={bradingDetails}
            setBradingDetails={setBradingDetails}
            // dynamicContentForm={dynamicContentForm}
          />
        </div>
      ),
    },
  };

  return (
    <div className="tw-min-h-screen tw-bg-gray-50 tw-p-6">
      <div className="tw-max-w-7xl tw-mx-auto">
        {/* Header */}
        <div className="tw-mb-0">
          <Title level={2} className="!tw-mb-2 !tw-text-gray-900">
            Dream Builder v.0.1
          </Title>
          <Text type="secondary" className="tw-text-base">
            Configure your website settings and content
          </Text>
        </div>

        <div className="tw-p-6 !tw-px-0  tw-rounded-xl ">
          {/* Tab Navigation using project's TabList */}
          <TabList
            tabContents={tabList}
            setPreviewMode={setPreviewMode}
            previewMode={previewMode}
          />
        </div>
      </div>
    </div>
  );
};

export default DynamicPages;

//===================old logic=======================
// useEffect(() => {
//   // setTemplateObj(home);
//   // get all file list from public/asset folder
//   // https://dreambuilder.wooffer.io
//   fetch("https://dreambuilder.wooffer.io/api/assets") // Fetch the list of files from the backend
//     .then((response) => response.json())
//     ?.then((data) => {
//       // console.log(data, "data");
//       const imgVar = {};
//       const companyName = home?.GVariable?.company_name;
//       const defaultCity = home?.GVariable?.default_city;
//       data?.files?.map((fileName) => {
//         const fileKey = fileName?.split(".")[0];
//         return (imgVar[`_img-${fileKey}`] = {
//           url: `/assets/${fileName}`,
//           name: fileName,
//           path: `/assets/${fileName}`,
//         });
//       });
//       const getBreadcrumbLabel = (label, key) => {
//         if (key == serviceAreaDKey) {
//           return label + " Charter Bus Rental";
//         }
//         // else if (key == busesDKey) {
//         //   return label + " Rental";
//         // }
//         return label;
//       };
//       Object?.keys(dynamicForms)?.forEach((key) => {
//         dynamicForms[key].sections?.map((section) => {
//           section?.sectionItems?.map((item) => {
//             if (!(item?.label in home?.contentJSON)) {
//               home.contentJSON[item?.label] = {};
//             }
//             // create bus_grid_list empty in page content object if not exist
//             if (!(`breadcrumb` in home?.contentJSON[item?.label])) {
//               home.contentJSON[item?.label]["breadcrumb"] = {};
//             }
//             home.contentJSON[item?.label]["breadcrumb"][`current_page`] =
//               getBreadcrumbLabel(item?.label, key);
//             home.contentJSON[item?.label]["breadcrumb"][`anchorTag_item`] = [
//               {
//                 title: "Bus Rental Company " + companyName,
//                 slug: home?.GVariable?.website_url + "index",
//               },
//               {
//                 title: section?.breadcrumbName,
//                 slug: `${section?.breadcrumbURL}`,
//               },
//             ];
//           });
//         });
//       });
//       if (!(`breadcrumb` in home?.templateContentJSON["Buses"])) {
//         home.templateContentJSON["Buses"]["breadcrumb"] = {};
//       }
//       home.templateContentJSON["Buses"]["breadcrumb"][`current_page`] =
//         "Our " + defaultCity + " Fleet";
//       home.templateContentJSON["Buses"]["breadcrumb"][`anchorTag_item`] = [
//         {
//           title: companyName,
//           slug: home?.GVariable?.website_url + "index",
//         },
//       ];
//       if (!(`breadcrumb` in home?.templateContentJSON["Services"])) {
//         home.templateContentJSON["Services"]["breadcrumb"] = {};
//       }
//       home.templateContentJSON["Services"]["breadcrumb"][`current_page`] =
//         defaultCity + " Group Transportation Services";
//       home.templateContentJSON["Services"]["breadcrumb"][`anchorTag_item`] = [
//         {
//           title: companyName,
//           slug: home?.GVariable?.website_url + "index",
//         },
//       ];
//       if (!(`breadcrumb` in home?.templateContentJSON["services-area"])) {
//         home.templateContentJSON["services-area"]["breadcrumb"] = {};
//       }
//       home.templateContentJSON["services-area"]["breadcrumb"][
//         `current_page`
//       ] = "Cities We Serve";
//       home.templateContentJSON["services-area"]["breadcrumb"][
//         `anchorTag_item`
//       ] = [
//         {
//           title: companyName,
//           slug: home?.GVariable?.website_url + "index",
//         },
//       ];
//       const busesList = [];
//       const comapnyNameSplit = home?.GVariable?.company_name
//         ?.split(" ")
//         ?.slice(0, 3)
//         ?.join(" ");
//       if (dynamicForms?.[busesDKey]?.sections?.length) {
//         JSON.parse(JSON.stringify(dynamicForms?.[busesDKey]?.sections))
//           ?.sort((a, b) => a?.sectionLabel?.localeCompare(b?.sectionLabel))
//           ?.map((section) => {
//             section?.sectionItems?.map((item) => {
//               busesList.push({
//                 ...item,
//                 breadcrumbName: section?.breadcrumbName,
//                 breadcrumbURL: section?.breadcrumbURL,
//                 sectionLabel: section?.sectionLabel,
//                 companyName: comapnyNameSplit,
//               });
//               // home.templateContentJSON[item?.label] = busesListBusesPage?.filter((item) => item.bus_title != item?.label) || []
//             });
//           });
//         busesList?.map((item) => {
//           // create empty page content object
//           if (!(item?.label in home?.contentJSON)) {
//             home.contentJSON[item?.label] = {};
//           }
//           // create bus_grid_list empty in page content object if not exist
//           if (!(`bus_grid_list` in home?.contentJSON[item?.label])) {
//             home.contentJSON[item?.label]["bus_grid_list"] = {};
//           }
//           // create busesList empty in bus_grid_list if not exist
//           if (
//             !(
//               `${item?.label}_busesList` in
//               home?.contentJSON[item?.label]["bus_grid_list"]
//             )
//           ) {
//             home.contentJSON[item?.label]["bus_grid_list"][
//               `${item?.label}_busesList`
//             ] = [];
//           }
//           // create bus_amenities empty in page content object if not exist
//           if (!(`bus_amenities` in home?.contentJSON[item?.label])) {
//             home.contentJSON[item?.label]["bus_amenities"] = {};
//           }
//           // create specific_bus_amenities_list empty in bus_amenities if not exist
//           if (
//             !(
//               `${item?.label}_specific_bus_amenities_list` in
//               home?.contentJSON[item?.label]["bus_amenities"]
//             )
//           ) {
//             home.contentJSON[item?.label]["bus_amenities"][
//               `${item?.label}_specific_bus_amenities_list`
//             ] = [];
//           }
//           const amenitiesArr = busAmenities[item?.slug] || [];
//           home.contentJSON[item?.label]["bus_amenities"][
//             `${item?.label}_specific_bus_amenities_list`
//           ] = amenitiesArr;
//           amenitiesArr?.map((amenity) => {
//             home.contentJSON[item?.label]["bus_amenities"][
//               amenity?.amenities_label + "_icon"
//             ] = getSvg(amenity?.["bus-amenities-image"]);
//           });

//           createEmptyObj(home?.contentJSON, [
//             item?.label,
//             "bus_interior_view",
//           ]);
//           home.contentJSON[item?.label]["bus_interior_view"] = {
//             "bus-interior-image-1":
//               item?.extra?.["bus-interior-image-1"] || null,
//             "bus-interior-image-2":
//               item?.extra?.["bus-interior-image-2"] || null,
//             "bus-interior-image-3":
//               item?.extra?.["bus-interior-image-3"] || null,
//           };
//           createEmptyObj(home?.contentJSON, [
//             item?.label,
//             "buspage_bus_details",
//           ]);
//           home.contentJSON[item?.label]["buspage_bus_details"] = {
//             "bus-image": item?.extra?.["bus-image"] || null,
//           };
//           home.contentJSON[item?.label]["bus_grid_list"][
//             `${item?.label}_busesList`
//           ] = createBusList(busesList, item?.slug, "prefix");

//           createEmptyObj(home?.contentJSON, [
//             item?.label,
//             "breadcrumb",
//             "anchorTag_item",
//           ]);
//           home.contentJSON[item?.label]["breadcrumb"][`current_page`] =
//             getBreadcrumbLabel(
//               item?.extra?.breadcrumbName
//                 ? item?.extra?.breadcrumbName
//                 : item?.label,
//               busesDKey
//             );
//           home.contentJSON[item?.label]["breadcrumb"][`anchorTag_item`] = [
//             {
//               title: companyName,
//               slug: home?.GVariable?.website_url + "index",
//             },
//             {
//               title: "Our " + defaultCity + " Fleet",
//               slug: `${item?.breadcrumbURL}`,
//             },
//           ];
//         });

//         createEmptyObj(home?.contentJSON, ["Buses", "bus_grid_list"]);
//         home.contentJSON["Buses"]["bus_grid_list"][`busesList`] =
//           createBusList(busesList);
//       }
//       if (dynamicForms?.[serviceDKey]?.sections?.length) {
//         const servicesList = [];
//         dynamicForms?.[serviceDKey]?.sections?.map((section) => {
//           section?.sectionItems?.map((item) => {
//             servicesList.push(item);
//           });
//         });
//         servicesList?.map((item) => {
//           if (!(item?.label in home?.contentJSON)) {
//             home.contentJSON[item?.label] = {};
//           }
//           home.contentJSON[item?.label]["content_image1"] = {
//             "content-image": item?.extra?.content_image1 || null,
//           };
//           home.contentJSON[item?.label]["content_image2"] = {
//             "content-image": item?.extra?.content_image2 || null,
//           };
//           home.contentJSON[item?.label]["h2_content_title8"] = {
//             content_title: item?.extra?.content_title || null,
//           };
//           home.contentJSON[item?.label]["event_cards_wrapper"] = {
//             event_card: item?.extra?.event_card || null,
//           };
//           createEmptyObj(home?.contentJSON, [
//             item?.label,
//             "breadcrumb",
//             "anchorTag_item",
//           ]);
//           home.contentJSON[item?.label]["breadcrumb"][`current_page`] =
//             getBreadcrumbLabel(item?.extra?.breadcrumbName, serviceDKey);
//           home.contentJSON[item?.label]["breadcrumb"][`anchorTag_item`] = [
//             {
//               title: companyName,
//               slug: home?.GVariable?.website_url + "index",
//             },
//             {
//               title: companyName + ` Group Transportation Services`,
//               slug: `/group-transportation-services`,
//             },
//           ];
//         });
//       }
//       if (dynamicForms?.[serviceAreaDKey]?.sections?.length) {
//         // const busesList = [];
//         dynamicForms?.[serviceAreaDKey]?.sections
//           ?.sort((a, b) => a?.sectionLabel?.localeCompare(b?.sectionLabel))
//           ?.map((section) => {
//             section?.sectionItems?.map((item) => {
//               if (!(item?.label in home?.contentJSON)) {
//                 home.contentJSON[item?.label] = {};
//               }
//               // create bus_grid_list empty in page content object if not exist
//               if (
//                 !(`hero_section_home_page` in home?.contentJSON[item?.label])
//               ) {
//                 home.contentJSON[item?.label]["hero_section_home_page"] = {};
//               }
//               createEmptyObj(home?.contentJSON, [
//                 `${item?.label}`,
//                 "bus_grid_list",
//               ]);
//               home.contentJSON[item?.label]["bus_grid_list"][`busesList`] =
//                 createBusList(busesList, null, "suffix", item?.label, true);
//               home.contentJSON[item?.label]["hero_section_home_page"][
//                 `current_page`
//               ] = getBreadcrumbLabel(item?.label, serviceAreaDKey);
//               home.contentJSON[item?.label]["hero_section_home_page"][
//                 `anchorTag_item`
//               ] = [
//                 {
//                   title: companyName,
//                   slug: home?.GVariable?.website_url + "index",
//                 },
//                 {
//                   title: section?.breadcrumbName,
//                   slug: `${section?.breadcrumbURL}`,
//                 },
//               ];
//             });
//           });
//       }
//       // console.log(home, "home...");
//       home.FileList = { ...home?.FileList, ...imgVar };
//       setTemplateObj(home);
//       // console.log(imgVar, "imgVar");
//     });
// }, [dynamicForms]);
