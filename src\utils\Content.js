import { busesD<PERSON>ey, serviceAreaD<PERSON>ey, serviceDKey } from "../util/content";
import { formatImageName, replaceArray } from "../util/functions";
import { AllPagesTitleFile } from "./AllPagesTitleFile";
import { createBusList, weOfferListHomePage } from "./ContentFileArray";

// random fucntion for get return + or - 10% of argument
export const random = (num) => {
  const r = Math.floor(Math.random() * 10) + 1;
  const isPositive = Math.random() < 0.5;
  const multiplier = isPositive ? 1 : -1;
  return (num + ((multiplier * num * r) / 100))?.toFixed(0);
};

export const randomPrice = (num) => {
  const variation = Math.random() * (10 - 5) + 5; // Random number between 5 and 10
  const r = Math.floor(Math.random() * 10) + 1;
  const isPositive = Math.random() < 0.5;
  const multiplier = isPositive ? 1 : -1;
  return (num + ((multiplier * num * r) / 100) + variation)?.toFixed(0); // Add variation to the result
};

export const getRandomTitle = (pageKey) => {
  const titles = AllPagesTitleFile[pageKey];
  if (!titles || titles.length === 0) {
    return `No titles found for page: ${pageKey}`;
  }
  const randomIndex = Math.floor(Math.random() * titles.length);
  return titles[randomIndex];
}

const cityListServe = (dynamicContentForm) => dynamicContentForm?.[serviceAreaDKey]?.sections?.[0]?.sectionItems
  ?.map(item => ({
    city_name: `Bus Rental ${item?.label}`,
    city_url: `/${item?.slug}.html`,
  }))


const serviceAreaDynamic = (pageName, data, dynamicContentForm, testimonialData) => {
  const sectionItems = dynamicContentForm?.[serviceAreaDKey]?.sections?.[0]?.sectionItems;
  const city_list_serve = cityListServe(dynamicContentForm)
    ?.filter(item => item?.city_name && item?.city_name?.toLowerCase() !== pageName?.toLowerCase());
  const itemData = sectionItems?.find(item => item?.label?.toLowerCase() == pageName?.toLowerCase())
  // const testimonialData = replaceArray(testimonialHomePage, ["testimonial", "user_name", "rating"], data?.["sec20"], ["text", "author", "rating",],);
  if (itemData?.isCustom) {
    return {};
  }
  return {
    // [pageName]: {
    "hero_section_home_page": {
      "hero_section_title": getRandomTitle(serviceAreaDKey) || null,
      // "hero_section_title": data?.["sec1"]?.["h2"] || null,
      [`hero_section_description`]: data?.["sec1"]?.["p"] || null,
      "state": itemData?.extra?.state || null,
    },
    "grid_bg_content_image": {
      // "content_heading": data?.["sec2"]?.["h2"] || null,
      [`content_description`]: data?.["sec2"]?.["p"] || null,

    },
    // "bus_grid_list": {
    //   "buses_list_heading": "Our ${slug} Bus Rental Options",
    // "busesList": createBusList(busesList)
    // },
    // "h2_content_title": {
    //   "content_title": data?.["sec3"]?.["h2"] || null,
    // },
    "service_1": {
      "service_title": data?.["sec3"]?.["h2"] || null,
      [`service_description`]: data?.["sec3"]?.["p"] || null,
    },
    "service_2": {
      "service_title": data?.["sec4"]?.["h2"] || null,
      [`service_description`]: data?.["sec4"]?.["p"] || null,
    },
    "service_3": {
      "service_title": data?.["sec5"]?.["h2"] || null,
      [`service_description`]: data?.["sec5"]?.["p"] || null,
    },
    "service_4": {
      "service_title": data?.["sec6"]?.["h2"] || null,
      [`service_description`]: data?.["sec6"]?.["p"] || null,
    },
    "service_5": {
      "service_title": data?.["sec7"]?.["h2"] || null,
      [`service_description`]: data?.["sec7"]?.["p"] || null,
    },
    "service_6": {
      "service_title": data?.["sec8"]?.["h2"] || null,
      [`service_description`]: data?.["sec8"]?.["p"] || null,
    },

    "service_7": {
      "service_title": data?.["sec9"]?.["h2"] || null,
      [`service_description`]: data?.["sec9"]?.["p"] || null,
    },
    "service_8": {
      "service_title": data?.["sec10"]?.["h2"] || null,
      [`service_description`]: data?.["sec10"]?.["p"] || null,
    },

    "service_9": {
      "service_title": data?.["sec11"]?.["h2"] || null,
      [`service_description`]: data?.["sec11"]?.["p"] || null,
    },
    "service_10": {
      "service_title": data?.["sec12"]?.["h2"] || null,
      [`service_description`]: data?.["sec12"]?.["p"] || null,
    },

    "service_11": {
      "service_title": data?.["sec13"]?.["h2"] || null,
      [`service_description`]: data?.["sec13"]?.["p"] || null,
    },
    "service_12": {
      "service_title": data?.["sec14"]?.["h2"] || null,
      [`service_description`]: data?.["sec14"]?.["p"] || null,
    },
    "service_13": {
      "service_title": data?.["sec15"]?.["h2"] || null,
      [`service_description`]: data?.["sec15"]?.["p"] || null,
    },
    "service_14": {
      "service_title": data?.["sec16"]?.["h2"] || null,
      [`service_description`]: data?.["sec16"]?.["p"] || null,
    },
    "service_15": {
      "service_title": data?.["sec17"]?.["h2"] || null,
      [`service_description`]: data?.["sec17"]?.["p"] || null,
    },
    "service_16": {
      "service_title": data?.["sec18"]?.["h2"] || null,
      [`service_description`]: data?.["sec18"]?.["p"] || null,
    },
    "table1": {
      "table_title": "${slug} Charter Bus Rental Prices",
      "column1_header": "Bus Type",
      "column2_header": "Per Hour",
      "column3_header": "Per Day",
      "column4_header": "Per Mile",
      "table_rows": [
        {
          "column1_Input": "50 to 56 Passenger Charter Bus",
          "column2_Input": `$${random(180)} – $${random(500)}`,
          "column3_Input": `$${random(1800)} – $${random(3800)}`,
          "column4_Input": `$${random(6.00)} – $${random(9.95)}`
        },
        {
          "column1_Input": "40 to 50 Passenger Charter Bus",
          "column2_Input": `$${random(170)} – $${random(480)}`,
          "column3_Input": `$${random(1700)} – $${random(3565)}`,
          "column4_Input": `$${random(6.00)} – $${random(9.95)}`
        },
        {
          "column1_Input": "25 to 35 Passenger Minibus",
          "column2_Input": `$${random(150)} – $${random(450)}`,
          "column3_Input": `$${random(1610)} – $${random(3465)}`,
          "column4_Input": `$${random(4.00)} – $${random(9.95)}`
        },
        {
          "column1_Input": "20 to 24 Passenger Minibus",
          "column2_Input": `$${random(150)} – $${random(440)}`,
          "column3_Input": `$${random(1610)} – $${random(3365)}`,
          "column4_Input": `$${random(4.00)} – $${random(8.95)}`
        },
        {
          "column1_Input": "15 to 18 Passenger Minibus",
          "column2_Input": `$${random(150)} – $${random(430)}`,
          "column3_Input": `$${random(1520)} – $${random(3255)}`,
          "column4_Input": `$${random(4.00)} – $${random(7.95)}`
        },
        {
          "column1_Input": "Entertainer Coach",
          "column2_Input": `$${random(315)} – $${random(550)}`,
          "column3_Input": `$${random(3000)} – $${random(5000)}`,
          "column4_Input": `$${random(6.00)} – $${random(9.95)}`
        },
        {
          "column1_Input": "Yellow School Bus",
          "column2_Input": `$${random(145)} – $${random(450)}`,
          "column3_Input": `$${random(1520)} – $${random(3655)}`,
          "column4_Input": `$${random(6.00)} – $${random(9.95)}`
        },
        {
          "column1_Input": "Shuttle Bus",
          "column2_Input": `$${random(155)} – $${random(450)}`,
          "column3_Input": `$${random(1520)} – $${random(3655)}`,
          "column4_Input": `$${random(6.00)} – $${random(9.95)}`
        }
      ]
    },
    "table2": {
      "table_title": "${slug} Party Bus Rental Prices",
      "column1_header": "Type of Party Bus",
      "column2_header": "Cost Per Hour Weekdays",
      "column3_header": "Cost Per Hour Weekends",
      "column4_header": "Cost Per Day",
      "table_rows": [
        {
          "column1_Input": "Small Party Bus (10 to 20 Passengers)",
          "column2_Input": `$${random(180)} - $${random(450)}`,
          "column3_Input": `$${random(200)} - $${random(470)}`,
          "column4_Input": `$${random(1000)} - $${random(2900)}`
        },
        {
          "column1_Input": "Medium Party Bus (20 to 40 Passengers)",
          "column2_Input": `$${random(200)} - $${random(500)}`,
          "column3_Input": `$${random(220)} - $${random(500)}`,
          "column4_Input": `$${random(1200)} - $${random(3500)}`
        },
        {
          "column1_Input": "Large Party Bus Rental (40 to 50 Passengers)",
          "column2_Input": `$${random(250)} - $${random(500)}`,
          "column3_Input": `$${random(240)} - $${random(520)}`,
          "column4_Input": `$${random(1500)} - $${random(3900)}`
        },
        {
          "column1_Input": "Mercedes Sprinter Party Bus",
          "column2_Input": `$${random(160)} - $${random(400)}`,
          "column3_Input": `$${random(180)} - $${random(450)}`,
          "column4_Input": `$${random(1000)} - $${random(3900)}`
        }
      ]
    },
    "city_list_serve_warp": {
      "city_list_serve": city_list_serve,
    },
    "faqs": {
      "faqs_questions": data?.["sec19"]?.map((item) => ({
        "faq_question": item?.["h3"] || null,
        "faq_answer": item?.["p"] || null,
      })) || [],
    },
    ...testimonialData,
    //   [`${pageName}_faqs_questions`]: data?.["sec19"]?.map((item) => ({
    //     "faq_question": item?.["h3"] || null,
    //     "faq_answer": item?.["p"] || null,
    //   })) || [],
    // },
    // "testimonial": {
    //   "testimonial_cards": testimonialData
    // },
    // },
  };
}

const serviceDynamic = (pageName, data, dynamicContentForm, testimonialData) => {
  // Collect and sort section keys like "sec1", "sec2", ...
  const secKeys = Object.keys(data)
    .filter(k => /^sec\d+$/.test(k))
    .sort((a, b) => parseInt(a.slice(3), 10) - parseInt(b.slice(3), 10));
  // Middle = all except first 3 and last 2
  const middleKeys = secKeys.length > 5 ? secKeys.slice(3, -1) : [];

  // Penultimate (second-last)
  const penultimateKey = secKeys.length >= 2 ? secKeys[secKeys.length - 1] : null;
  return {
    "hero_section_content": {
      [`hero_section_title`]: getRandomTitle(pageName) || data?.["sec1"]?.["h2"] || null,
      [`hero_section_description`]: data?.["sec1"]?.["p"] || null,
    },
    "h2_content_title1": {
      [`content_title`]: data?.["sec2"]?.["h2"] || null,
    },
    "content_description1": {
      [`content_description`]: data?.["sec2"]?.["p"] || null,
    },
    "h2_content_title2": {
      [`content_title`]: data?.["sec3"]?.["h2"] || null,
    },
    "content_description2": {
      [`content_description`]: data?.["sec3"]?.["p"] || null,
    },
    "repeated_content_warp": {
      "repeated_content": middleKeys?.map(k => ({
        content_title: data[k]?.h2 ?? null,
        content_description: data[k]?.p ?? null,
      })),
      // [`${pageName}_repeated_content`]: middleKeys?.map(k => ({
      //   content_title: data[k]?.h2 ?? null,
      //   content_description: data[k]?.p ?? null,
      // })),
    },
    "h2_content_title8": {
      [`content_title`]: penultimateKey ? (data[penultimateKey]?.h2 ?? null) : null,
    },
    "content_description8": {
      [`content_description`]: penultimateKey ? (data[penultimateKey]?.p ?? null) : null,
    },
    ...testimonialData,
    // "testimonial": {
    //   "testimonial_cards": {
    //     "testimonial_cards": data?.[secKeys[secKeys.length - 1]]?.map(el => {
    //       return {
    //         "user-image": el?.["user-image"] || null,
    //         "user_name": el?.["author"] || null,
    //         "testimonial": el?.["text"] || null,
    //         "rating": el?.["rating"] || null,
    //       };
    //     })
    //   }
    // }

  }
}

const busesDynamic = (pageName, data, dynamicContentForm, testimonialData) => {

  return {
    "hero_section_content": {
      // "hero_section_title": getRandomTitle(busesDKey) || data?.["sec1"]?.["h2"] || null,
      // "hero_section_title": data?.["sec1"]?.["h2"] || null,
      "hero_section_title": "${slug}",
      "hero_section_description": data?.["sec1"]?.["p"] || null,
    },
    "buspage_bus_details": {
      // [`bus_title`]: data?.["sec2"]?.["h2"] || null,
      "bus_title": "More about our ${slug}",
      "bus_description": data?.["sec2"]?.["p"] || null,
    },
    ...testimonialData,
    // "bus_grid_list": {
    //   [`busesList`]: busesListBusesPage?.filter((item) => item.bus_title != pageName) || []
    // },
    // "testimonial": {
    //   "testimonial_cards": data?.["sec3"]?.map(el => {
    //     return {
    //       "user-image": testimonialImages[el?.["author"]] || null,
    //       "user_name": el?.["author"] || null,
    //       "testimonial": el?.["text"] || null,
    //       "rating": el?.["rating"] || null,
    //     };
    //   })
    // }
  }
}




export const prePareContent = (data, dynamicContentForm = {}) => {
  const homeKey = "Home";
  const servicesKey = "Services";
  const busesKey = "Buses";
  const heroSection = "sec1";
  const faqSection = "sec19";
  const testimonialKey = "Testimonial"
  const partBusKey = "Party bus";

  // buses page
  const busSection = "sec3";
  const testimonialSection = "sec20";
  const bustestimonialSection = "sec17";
  const testimonialAboutusSection = "sec11";

  const weOfferHome = weOfferListHomePage?.map((item, index) => {
    const secData = data?.[homeKey]?.[`sec${index + 3}`];
    return {
      ...item,
      // service_name: secData?.h2 || null,
      service_description: secData?.p || null,
    };
  });



  let dynamicPages = {};

  function findDynamicKey(label) {
    // console.log(Object.keys(dynamicContentForm)?.length, dynamicContentForm, "dynamicContentForm", label, Object.entries(dynamicContentForm).find(([_, form]) =>
    //   form.sections.some(section =>
    //     section?.sectionItems.some(item => item?.label?.toLowerCase() == label?.toLowerCase())
    //   )
    // ));
    // if (Object.keys(dynamicContentForm)?.length) return null;
    const found = Object.entries(dynamicContentForm).find(([_, form]) =>
      form.sections.some(section =>
        section?.sectionItems.some(item => item?.label?.toLowerCase() == label?.toLowerCase())
      )
    );
    // console.log(found, "found");
    return found?.[0];
  }

  const testimonialData = {
    "testimonial": {
      "background-pattern-bg-image": "background-pattern",
      "testimonial_title": "${company_name} Reviews",
      "testimonial_cards": data?.[testimonialKey]?.["sec1"]?.map(el => {
        return {
          "user-image": formatImageName(el?.["author"]) || null,
          "user_name": el?.["author"] || null,
          "testimonial": el?.["text"] || null,
          "rating": el?.["rating"] || null,
        };
      })
    }
  };
  for (const [label, value] of Object.entries(data)) {
    const key = findDynamicKey(label);
    const capitalizeLabel = label?.replace(/\b\w/g, char => char?.toUpperCase())
    // console.log(key, label, capitalizeLabel, value)
    if (key == serviceAreaDKey) {
      dynamicPages[capitalizeLabel] = serviceAreaDynamic(capitalizeLabel, value, dynamicContentForm, testimonialData);
    } else if (key == busesDKey) {
      dynamicPages[capitalizeLabel] = busesDynamic(capitalizeLabel, value, dynamicContentForm, testimonialData);
    } else if (key == serviceDKey) {
      dynamicPages[capitalizeLabel] = serviceDynamic(capitalizeLabel, value, dynamicContentForm, testimonialData);
    }
  }
  const busesList = [];
  if (dynamicContentForm?.[busesDKey]?.sections?.length) {
    JSON.parse(JSON.stringify(dynamicContentForm?.[busesDKey]?.sections))
      ?.sort((a, b) => a?.sectionLabel?.localeCompare(b?.sectionLabel))
      ?.map((section) => {
        section?.sectionItems?.map((item) => {
          busesList.push(item);
          // home.templateContentJSON[item?.label] = busesListBusesPage?.filter((item) => item.bus_title != item?.label) || []
        });
      });
  }
  // console.log(data?.[busesKey], createBusList(busesList), "createBusList");
  const sectionOrder = [
    // first 3 → sec9, sec10, sec11
    "sec9", "sec10", "sec11", "sec12",
    // next 7 → sec2 → sec8
    "sec2", "sec3", "sec4", "sec5", "sec6", "sec7", "sec8",
    // last 4 → sec12, sec13, sec14, sec15 (adjust if you have more/less)
    "sec13", "sec14", "sec15", "sec16"
  ];
  // 50 to 56 || 0 to 3 || sec9 to sec12 ======= 15 to 35 || 4 to 10 || sec2 to sec8 ============ 
  const buesListBues = createBusList(busesList)?.map((item, index) => {
    // const secData = data?.[busesKey]?.[`sec${index + 2}`];
    const secKey = sectionOrder[index]; // pick custom sec order
    // console.log(secKey, "secKey", busesKey, index);
    const secData = data?.[busesKey]?.[secKey];
    return {
      ...item,
      // bus_title: secData?.h2 || null,
      bus_description: secData?.p || null,

    };
  });

  // console.log("dynamicPages", buesListBues);


  // const testimonialData = replaceArray(testimonialHomePage, ["testimonial"], data?.home?.[testimonialSection], ["p"],);
  // const testimonialBuesData = replaceArray(testimonialHomePage, ["testimonial"], data?.home?.[bustestimonialSection], ["p"],);
  // const testimonialAboutupData = replaceArray(testimonialHomePage, ["testimonial"], data?.home?.[testimonialAboutusSection], ["p"],);
  // replaceArray(weOfferList, ["service_name", "service_description"], data?.home?.sec3, ["h3", "p"],);
  return {
    ...dynamicPages,
    // "Party Bus": {
    //   "hero_section_home_page": {
    //     // "hero_section_title": "Party Bus Rental ${default_city}, ${default_state}",
    //     "hero_section_description": data?.[partBusKey]?.["sec1"]?.["p"] || null,
    //   },
    //   "grid_bg_content_image": {
    //     "content_description": data?.[partBusKey]?.["sec2"]?.["p"] || null,
    //   },
    //   "buses_grid_title": {
    //     "buses_list_description": data?.[partBusKey]?.["sec3"]?.["p"] || null,
    //   },
    //   "bus_amenities": {
    //     "amenities_description": data?.[partBusKey]?.["sec4"]?.["p"] || null,
    //   },
    //   "h2_content_title1": {
    //     // "content_title": data?.[partBusKey]?.["sec5"]?.["h2"] || null,
    //     "content_description": data?.[partBusKey]?.["sec5"]?.["p"] || null,
    //   },
    //   "h2_content_title2": {
    //     // "content_title": "Popular Uses for Party Bus Rentals in ${default_city}",
    //     "content_description": data?.[partBusKey]?.["sec6"]?.["p"] || null,
    //   },
    //   "testimonial": {
    //     // "background-pattern-bg-image": "background-pattern",
    //     // "testimonial_title": "Choose one of the most recommended ${default_city} Party Bus Companies",
    //     "testimonial_cards": testimonialData?.["testimonial"]?.["testimonial_cards"] || []
    //   },
    //   "faqs": {
    //     "faqs_title": "Learn More About Bus Rental Company ${slug}",
    //     "faqs_questions": data?.[partBusKey]?.["sec7"]?.map((item) => ({
    //       "faq_question": item?.["h3"] || null,
    //       "faq_answer": item?.["p"] || null,
    //     })) || []
    //   }
    // },
    "Home": {
      "hero_section_home_page": {
        "hero_section_title": getRandomTitle("Home") || null,
        // "hero_section_title": data?.[homeKey]?.[heroSection]?.["h2"] || null,
        "hero_section_description": data?.[homeKey]?.[heroSection]?.["p"] || null,
      },

      "why_section_home_page": {
        // "why_section_heading": data?.[homeKey]?.["sec2"]?.["h2"] || null,
        "why_section_description": data?.[homeKey]?.["sec2"]?.["p"] || null,
      },
      "we_offer_list": {
        // "we_offer_services_title": "Group Transportation Services We Offer at Bus Rental Company Clifton",
        "we_offer_services_list": weOfferHome
      },
      "charter_bus": {
        "table_title": "${default_city} Charter Bus Rental Prices",
        "column1_header": "Bus Type",
        "column2_header": "Per Hour",
        "column3_header": "Per Day",
        "column4_header": "Per Mile",
        "table_rows": [
          {
            "column1_Input": "50 to 56 Passenger Charter Bus",
            "column2_Input": `$${random(180)} – $${random(500)}`,
            "column3_Input": `$${random(1800)} – $${random(3800)}`,
            "column4_Input": `$${random(6.00)} – $${random(9.95)}`
          },
          {
            "column1_Input": "40 to 50 Passenger Charter Bus",
            "column2_Input": `$${random(170)} – $${random(480)}`,
            "column3_Input": `$${random(1700)} – $${random(3565)}`,
            "column4_Input": `$${random(6.00)} – $${random(9.95)}`
          },
          {
            "column1_Input": "25 to 35 Passenger Minibus",
            "column2_Input": `$${random(150)} – $${random(450)}`,
            "column3_Input": `$${random(1610)} – $${random(3465)}`,
            "column4_Input": `$${random(4.00)} – $${random(9.95)}`
          },
          {
            "column1_Input": "20 to 24 Passenger Minibus",
            "column2_Input": `$${random(150)} – $${random(440)}`,
            "column3_Input": `$${random(1610)} – $${random(3365)}`,
            "column4_Input": `$${random(4.00)} – $${random(8.95)}`
          },
          {
            "column1_Input": "15 to 18 Passenger Minibus",
            "column2_Input": `$${random(150)} – $${random(430)}`,
            "column3_Input": `$${random(1520)} – $${random(3255)}`,
            "column4_Input": `$${random(4.00)} – $${random(7.95)}`
          },
          {
            "column1_Input": "Entertainer Coach",
            "column2_Input": `$${random(315)} – $${random(550)}+`,
            "column3_Input": `$${random(3000)} – $${random(5000)}`,
            "column4_Input": `$${random(6.00)} – $${random(9.95)}`
          },
          {
            "column1_Input": "Yellow School Bus",
            "column2_Input": `$${random(145)} – $${random(450)}+`,
            "column3_Input": `$${random(1520)} – $${random(3655)}`,
            "column4_Input": `$${random(6.00)} – $${random(9.95)}`
          },
          {
            "column1_Input": "Shuttle Bus",
            "column2_Input": `$${random(155)} – $${random(450)}+`,
            "column3_Input": `$${random(1520)} – $${random(3655)}`,
            "column4_Input": `$${random(6.00)} – $${random(9.95)}`
          }
        ]
      },
      "party_bus": {
        "table_title": "${default_city} Party Bus Rental Prices",
        "column1_header": "Type of Party Bus",
        "column2_header": "Cost Per Hour Weekdays",
        "column3_header": "Cost Per Hour Weekends",
        "column4_header": "Cost Per Day",
        "table_rows": [
          {
            "column1_Input": "Small Party Bus (10 to 20 Passengers)",
            "column2_Input": `$${random(180)} - $${random(450)}+`,
            "column3_Input": `$${random(200)} - $${random(470)}+`,
            "column4_Input": `$${random(1000)} - $${random(2900)}`
          },
          {
            "column1_Input": "Medium Party Bus (20 to 40 Passengers)",
            "column2_Input": `$${random(200)} - $${random(500)}+`,
            "column3_Input": `$${random(220)} - $${random(500)}+`,
            "column4_Input": `$${random(1200)} - $${random(3500)}`
          },
          {
            "column1_Input": "Large Party Bus Rental (40 to 50 Passengers)",
            "column2_Input": `$${random(250)} - $${random(500)}+`,
            "column3_Input": `$${random(240)} - $${random(520)}+`,
            "column4_Input": `$${random(1500)} - $${random(3900)}`
          },
          {
            "column1_Input": "Mercedes Sprinter Party Bus",
            "column2_Input": `$${random(160)} - $${random(400)}+`,
            "column3_Input": `$${random(180)} - $${random(450)}+`,
            "column4_Input": `$${random(1000)} - $${random(3900)}`
          }
        ]
      },
      ...testimonialData,
      // "testimonial": {
      //   "background-pattern-bg-image": "background-pattern",
      //   "testimonial_title": "What Our Customers Say",
      //   "testimonial_cards": testimonialData
      // },
      "faqs": {
        "faqs_title": "Learn More About ${company_name}",
        "faqs_questions": data?.[homeKey]?.[faqSection]?.map((item) => ({
          "faq_question": item?.["h3"] || null,
          "faq_answer": item?.["p"] || null,
        })) || []
      },
    },
    "Services": {
      "content_title": {
        "content_title": getRandomTitle("Services") || null,
        // "content_title": data?.[servicesKey]?.["sec1"]?.["h2"] || null,
      },
      "services_1": {
        // "service_title": data?.[servicesKey]?.["sec2"]?.["h2"] || null,
        "service_description": data?.[servicesKey]?.["sec3"]?.["p"] || null,
      },
      "services_2": {
        // "service_title": data?.[servicesKey]?.["sec3"]?.["h2"] || null,
        "service_description": data?.[servicesKey]?.["sec5"]?.["p"] || null,
      },
      "services_3": {
        // "service_title": data?.[servicesKey]?.["sec4"]?.["h2"] || null,
        "service_description": data?.[servicesKey]?.["sec4"]?.["p"] || null,
      },
      "services_4": {
        // "service_title": data?.[servicesKey]?.["sec5"]?.["h2"] || null,
        "service_description": data?.[servicesKey]?.["sec1"]?.["p"] || null,
      },
      "services_5": {
        // "service_title": data?.[servicesKey]?.["sec6"]?.["h2"] || null,
        "service_description": data?.[servicesKey]?.["sec2"]?.["p"] || null,
      },
      "services_6": {
        // "service_title": data?.[servicesKey]?.["sec7"]?.["h2"] || null,
        "service_description": data?.[servicesKey]?.["sec6"]?.["p"] || null,
      },
      "services_7": {
        // "service_title": data?.[servicesKey]?.["sec8"]?.["h2"] || null,
        "service_description": data?.[servicesKey]?.["sec7"]?.["p"] || null,
      },
      "services_8": {
        // "service_title": data?.[servicesKey]?.["sec9"]?.["h2"] || null,
        "service_description": data?.[servicesKey]?.["sec8"]?.["p"] || null,
      },
      "services_9": {
        // "service_title": data?.[servicesKey]?.["sec10"]?.["h2"] || null,
        "service_description": data?.[servicesKey]?.["sec9"]?.["p"] || null,
      },
      "services_10": {
        // "service_title": data?.[servicesKey]?.["sec11"]?.["h2"] || null,
        "service_description": data?.[servicesKey]?.["sec10"]?.["p"] || null,
      },
      "services_11": {
        // "service_title": data?.[servicesKey]?.["sec12"]?.["h2"] || null,
        "service_description": data?.[servicesKey]?.["sec11"]?.["p"] || null,
      },
      "services_12": {
        // "service_title": data?.[servicesKey]?.["sec13"]?.["h2"] || null,
        "service_description": data?.[servicesKey]?.["sec12"]?.["p"] || null,
      },
      "services_13": {
        // "service_title": data?.[servicesKey]?.["sec14"]?.["h2"] || null,
        "service_description": data?.[servicesKey]?.["sec13"]?.["p"] || null,
      },
      "services_14": {
        // "service_title": data?.[servicesKey]?.["sec15"]?.["h2"] || null,
        "service_description": data?.[servicesKey]?.["sec14"]?.["p"] || null,
      },
      "services_15": {
        // "service_title": data?.[servicesKey]?.["sec16"]?.["h2"] || null,
        "service_description": data?.[servicesKey]?.["sec15"]?.["p"] || null,
      },
      "services_16": {
        // "service_title": data?.[servicesKey]?.["sec17"]?.["h2"] || null,
        "service_description": data?.[servicesKey]?.["sec16"]?.["p"] || null,
      }
    },
    "Buses": {
      "hero_section_content": {
        "hero_section_title": getRandomTitle("Buses") || null,
        // "hero_section_title": data?.[busesKey]?.[heroSection]?.["h2"] || "",
        "hero_section_description": data?.[busesKey]?.[heroSection]?.["p"] || "",
      },
      "bus_grid_list": {
        "busesList": buesListBues
      },
      ...testimonialData,
      // "testimonial": {
      //   // "img-background-pattern-bg-image": "/assets/background-pattern.png",
      //   // "testimonial_title": "What Our Customers Say",
      //   "testimonial_cards": testimonialBuesData

      // }
    },
    "services-area": {
      "hero_section_content": {
        "hero_section_title": getRandomTitle("services-area") || null,
        // "hero_section_title": data?.["services-area"]?.["sec1"]?.["h2"] || null,
        "hero_section_description": data?.["services-area"]?.["sec1"]?.["p"] || null,
      },
      "city_list_serve_warp": {
        "city_list_serve": cityListServe(dynamicContentForm),
      },
    },
    // "services": {
    //   "content_title": {
    //     "content_title": data?.["services"]?.["sec1"]?.["h2"] || null,
    //   },
    //   "services_1": {
    //     "service_title": data?.["services"]?.["sec2"]?.["h2"] || null,
    //     "service-image": "clifton-corporate-bus-rental",
    //     "service_description": data?.["services"]?.["sec2"]?.["p"] || null,
    //     "service_page_url": "/services/clifton-corporate-bus-rental/"
    //   },
    //   "services_2": {
    //     "service_title": data?.["services"]?.["sec3"]?.["h2"] || null,
    //     "service-image": "clifton-private-event-transportation",
    //     "service_description": data?.["services"]?.["sec3"]?.["p"] || null,
    //     "service_page_url": "/services/clifton-event-transportation-services/"
    //   },
    //   "services_3": {
    //     "service_title": data?.["services"]?.["sec4"]?.["h2"] || null,
    //     "service-image": "clifton-school-event-transportation",
    //     "service_description": data?.["services"]?.["sec4"]?.["p"] || null,
    //     "service_page_url": "/services/clifton-school-event-bus-rental/"
    //   },
    //   "services_4": {
    //     "service_title": data?.["services"]?.["sec5"]?.["h2"] || null,
    //     "service-image": "clifton-sporting-event-transportation",
    //     "service_description": data?.["services"]?.["sec5"]?.["p"] || null,
    //     "service_page_url": "/services/clifton-sports-team-transportation/"
    //   },
    //   "services_5": {
    //     "service_title": data?.["services"]?.["sec6"]?.["h2"] || null,
    //     "service-image": "clifton-wedding-transportation",
    //     "service_description": data?.["services"]?.["sec6"]?.["p"] || null,
    //     "service_page_url": "/services/clifton-wedding-bus-rental/"
    //   },
    //   "services_6": {
    //     "service_title": data?.["services"]?.["sec7"]?.["h2"] || null,
    //     "service-image": "clifton-airport-shuttles",
    //     "service_description": data?.["services"]?.["sec7"]?.["p"] || null,
    //     "service_page_url": "/services/clifton-airport-shuttles/"
    //   },
    //   "services_7": {
    //     "service_title": data?.["services"]?.["sec8"]?.["h2"] || null,
    //     "service-image": "clifton-bus-rentals-for-travel-agents",
    //     "service_description": data?.["services"]?.["sec8"]?.["p"] || null,
    //     "service_page_url": "/services/clifton-bus-rentals-for-travel-agents/"
    //   },
    //   "services_8": {
    //     "service_title": data?.["services"]?.["sec9"]?.["h2"] || null,
    //     "service-image": "clifton-vacation-bus-rentals-for-family-trips",
    //     "service_description": data?.["services"]?.["sec9"]?.["p"] || null,
    //     "service_page_url": "/services/clifton-family-vacation-bus-rentals/"
    //   },
    //   "services_9": {
    //     "service_title": data?.["services"]?.["sec10"]?.["h2"] || null,
    //     "service-image": "clifton-summer-camp-transportation-and-bus-rentals",
    //     "service_description": data?.["services"]?.["sec10"]?.["p"] || null,
    //     "service_page_url": "/services/clifton-camp-bus-rentals/"
    //   },
    //   "services_10": {
    //     "service_title": data?.["services"]?.["sec11"]?.["h2"] || null,
    //     "service-image": "clifton-religious-charter-bus-minibus-rentals",
    //     "service_description": data?.["services"]?.["sec11"]?.["p"] || null,
    //     "service_page_url": "/services/clifton-church-bus-rental/"
    //   },
    //   "services_11": {
    //     "service_title": data?.["services"]?.["sec12"]?.["h2"] || null,
    //     "service-image": "clifton-hospital-and-healthcare-shuttles",
    //     "service_description": data?.["services"]?.["sec12"]?.["p"] || null,
    //     "service_page_url": "/services/clifton-hospital-shuttles/"
    //   },
    //   "services_12": {
    //     "service_title": data?.["services"]?.["sec13"]?.["h2"] || null,
    //     "service-image": "clifton-emergency-transportation-service",
    //     "service_description": data?.["services"]?.["sec13"]?.["p"] || null,
    //     "service_page_url": "/services/clifton-emergency-response-bus-rentals/"
    //   },
    //   "services_13": {
    //     "service_title": data?.["services"]?.["sec14"]?.["h2"] || null,
    //     "service-image": "clifton-government-and-military-bus-rentals",
    //     "service_description": data?.["services"]?.["sec14"]?.["p"] || null,
    //     "service_page_url": "/services/clifton-government-bus-rentals/"
    //   },
    //   "services_14": {
    //     "service_title": data?.["services"]?.["sec15"]?.["h2"] || null,
    //     "service-image": "clifton-construction-site-shuttle-services",
    //     "service_description": data?.["services"]?.["sec15"]?.["p"] || null,
    //     "service_page_url": "/services/clifton-construction-site-shuttle-bus-rental/"
    //   },
    //   "services_15": {
    //     "service_title": data?.["services"]?.["sec16"]?.["h2"] || null,
    //     "service-image": "clifton-wine-tour-and-pub-crawl-bus-rentals",
    //     "service_description": data?.["services"]?.["sec16"]?.["p"] || null,
    //     "service_page_url": "/services/clifton-wine-tour-pub-crawl-bus-rentals/"
    //   },
    //   "services_16": {
    //     "service_title": data?.["services"]?.["sec17"]?.["h2"] || null,
    //     "service-image": "clifton-prom-and-homecoming-party-bus-rentals",
    //     "service_description": data?.["services"]?.["sec17"]?.["p"] || null,
    //     "service_page_url": "/services/clifton-prom-bus-rental/"
    //   }
    // },
    "prices": {
      "hero_section_content": {
        "hero_section_title": getRandomTitle("prices") || null,
        // "hero_section_title": data?.["prices"]?.["sec1"]?.["h2"] || null,
        "hero_section_description": data?.["prices"]?.["sec1"]?.["p"] || null,
      },
      "charter_bus": {
        "table_title": "${default_city} Charter Bus Rental Prices",
        "column1_header": "Bus Type",
        "column2_header": "Per Hour",
        "column3_header": "Per Day",
        "column4_header": "Per Mile",
        "table_rows": [
          {
            "column1_Input": "50 to 56 Passenger Charter Bus",
            "column2_Input": `$${random(180)} - $${random(500)}+`,
            "column3_Input": `$${random(1800)} - $${random(3800)}`,
            "column4_Input": `$${random(6)} - $${random(9.95)}`
          },
          {
            "column1_Input": "40 to 50 Passenger Charter Bus",
            "column2_Input": `$${random(170)} - $${random(480)}+`,
            "column3_Input": `$${random(1700)} - $${random(3565)}`,
            "column4_Input": `$${random(6)} - $${random(9.95)}`
          },
          {
            "column1_Input": "25 to 35 Passenger Minibus",
            "column2_Input": `$${random(150)} - $${random(450)}+`,
            "column3_Input": `$${random(1610)} - $${random(3465)}`,
            "column4_Input": `$${random(4)} - $${random(9.95)}`
          },
          {
            "column1_Input": "20 to 24 Passenger Minibus",
            "column2_Input": `$${random(150)} - $${random(440)}+`,
            "column3_Input": `$${random(1610)} - $${random(3365)}`,
            "column4_Input": `$${random(4)} - $${random(8.95)}`
          },
          {
            "column1_Input": "15 to 18 Passenger Minibus",
            "column2_Input": `$${random(150)} - $${random(430)}+`,
            "column3_Input": `$${random(1520)} - $${random(3255)}`,
            "column4_Input": `$${random(4)} - $${random(7.95)}`
          },
          {
            "column1_Input": "Entertainer Coach",
            "column2_Input": `$${random(315)} - $${random(550)}+`,
            "column3_Input": `$${random(3000)} - $${random(5000)}+`,
            "column4_Input": `$${random(6)} - $${random(9.95)}`
          },
          {
            "column1_Input": "Yellow School Bus",
            "column2_Input": `$${random(145)} - $${random(450)}+`,
            "column3_Input": `$${random(1520)} - $${random(3655)}`,
            "column4_Input": `$${random(6)} - $${random(9.95)}`
          },
          {
            "column1_Input": "Shuttle Bus",
            "column2_Input": `$${random(155)} - $${random(450)}+`,
            "column3_Input": `$${random(1520)} - $${random(3655)}`,
            "column4_Input": `$${random(6)} - $${random(9.95)}`
          }
        ]
      },
      "party_bus": {
        "table_title": "${default_city} Party Bus Rental Prices",
        "column1_header": "Type of Party Bus",
        "column2_header": "Cost Per Hour Weekdays",
        "column3_header": "Cost Per Hour Weekends",
        "column4_header": "Cost Per Day",
        "table_rows": [
          {
            "column1_Input": "Small Party Bus (10 to 20 Passengers)",
            "column2_Input": `$${random(180)} - $${random(450)}+`,
            "column3_Input": `$${random(200)} - $${random(470)}+`,
            "column4_Input": `$${random(1000)} - $${random(2900)}+`
          },
          {
            "column1_Input": "Medium Party Bus (20 to 40 Passengers)",
            "column2_Input": `$${random(200)} - $${random(500)}+`,
            "column3_Input": `$${random(220)} - $${random(500)}+`,
            "column4_Input": `$${random(1200)} - $${random(3500)}+`
          },
          {
            "column1_Input": "Large Party Bus Rental (40 to 50 Passengers)",
            "column2_Input": `$${random(250)} - $${random(500)}+`,
            "column3_Input": `$${random(240)} - $${random(520)}+`,
            "column4_Input": `$${random(1500)} - $${random(3900)}+`
          },
          {
            "column1_Input": "Mercedes Sprinter Party Bus",
            "column2_Input": `$${random(160)} - $${random(400)}+`,
            "column3_Input": `$${random(180)} - $${random(450)}+`,
            "column4_Input": `$${random(1000)} - $${random(3900)}+`
          }
        ]
      },
      "h2_content_title1": {
        "content_title": data?.["prices"]?.["sec2"]?.["h2"] || null,
      },
      "content_description1": {
        "content_description": data?.["prices"]?.["sec2"]?.["p"] || null,
      },
      "h2_content_title2": {
        "content_title": data?.["prices"]?.["sec3"]?.["h2"] || null,
      },
      "content_description2": {
        "content_description": data?.["prices"]?.["sec3"]?.["p"] || null,
      },
      "h2_content_title3": {
        "content_title": data?.["prices"]?.["sec4"]?.["h2"] || null,
      },
      "content_description3": {
        "content_description": data?.["prices"]?.["sec4"]?.["p"] || null,
      },
      "h2_content_title4": {
        "content_title": data?.["prices"]?.["sec5"]?.["h2"] || null,
      },
      "content_description4": {
        "content_description": data?.["prices"]?.["sec5"]?.["p"] || null,
      },
      "h2_content_title5": {
        "content_title": data?.["prices"]?.["sec6"]?.["h2"] || null,
      },
      "content_description5": {
        "content_description": data?.["prices"]?.["sec6"]?.["p"] || null,
      },
      "repeated_content_warp": {
        "repeated_content": Object.keys(data?.["prices"] || {})
          ?.filter(sec => Number(sec?.replace("sec", "")) >= 7)
          ?.map(section => ({
            content_title: data?.["prices"]?.[section]?.h2 ?? null,
            content_description: data?.["prices"]?.[section]?.p ?? null,
          })),
      },
      // "h2_content_title6": {
      //   "content_title": data?.["prices"]?.["sec7"]?.["h2"] || null
      // },
      // "content_description6": {
      //   "content_description": data?.["prices"]?.["sec7"]?.["p"] || null
      // },
      // "h2_content_title7": {
      //   "content_title": data?.["prices"]?.["sec8"]?.["h2"] || null
      // },
      // "content_description7": {
      //   "content_description": data?.["prices"]?.["sec8"]?.["p"] || null
      // },
      // "h2_content_title8": {
      //   "content_title": data?.["prices"]?.["sec9"]?.["h2"] || null
      // },
      // "content_description8": {
      //   "content_description": data?.["prices"]?.["sec9"]?.["p"] || null
      // },
      // "h2_content_title9": {
      //   "content_title": data?.["prices"]?.["sec10"]?.["h2"] || null
      // },
      // "content_description9": {
      //   "content_description": data?.["prices"]?.["sec10"]?.["p"] || null
      // },
      // "h2_content_title10": {
      //   "content_title": data?.["prices"]?.["sec11"]?.["h2"] || null
      // },
      // "content_description10": {
      //   "content_description": data?.["prices"]?.["sec11"]?.["p"] || null
      // },
      // "h2_content_title11": {
      //   "content_title": data?.["prices"]?.["sec12"]?.["h2"] || null
      // },
      // "content_description11": {
      //   "content_description": data?.["prices"]?.["sec12"]?.["p"] || null
      // },
      // "h2_content_title12": {
      //   "content_title": data?.["prices"]?.["sec13"]?.["h2"] || null
      // },
      // "content_description12": {
      //   "content_description": data?.["prices"]?.["sec13"]?.["p"] || null
      // },
      // "h2_content_title13": { "content_title": data?.["prices"]?.["sec14"]?.["h2"] || null },
      // "content_description13": {
      //   "content_description": data?.["prices"]?.["sec14"]?.["p"] || null
      // },

    },
    "contact-us": {
      "hero_section_content": {
        "hero_section_title": getRandomTitle("contact-us") || null,
        // "hero_section_title": data?.["contact-us"]?.["sec1"]?.["h2"] || null,
        "hero_section_description": data?.["contact-us"]?.["sec1"]?.["p"] || null,
      },
    },
    "about-us": {
      "hero_section_content": {
        "hero_section_title": getRandomTitle("about-us") || null,
        // "hero_section_title": data?.["about-us"]?.["sec1"]?.["h2"] || null,
        "hero_section_description": data?.["about-us"]?.["sec1"]?.["p"] || null,
      },
      // "happy_to_serve_wrapper": {
      //   "happy_to_serve_title": data?.["about-us"]?.["sec2"]?.["h2"] || null,
      // },
      "h2_content_title1": { "content_title": data?.["about-us"]?.["sec2"]?.["h2"] || null },
      "content_description1": {
        "content_description": data?.["about-us"]?.["sec2"]?.["p"] || null
      },
      "h2_content_title2": {
        "content_title": data?.["about-us"]?.["sec3"]?.["h2"] || null
      },
      "content_description2": {
        "content_description": data?.["about-us"]?.["sec3"]?.["p"] || null
      },
      "h2_content_title3": { "content_title": data?.["about-us"]?.["sec4"]?.["h2"] || null },
      "content_description3": {
        "content_description": data?.["about-us"]?.["sec4"]?.["p"] || null
      },
      "h2_content_title4": { "content_title": data?.["about-us"]?.["sec5"]?.["h2"] || null },
      "content_description4": {
        "content_description": data?.["about-us"]?.["sec5"]?.["p"] || null
      },
      "h2_content_title5": { "content_title": data?.["about-us"]?.["sec6"]?.["h2"] || null },
      "content_description5": {
        "content_description": data?.["about-us"]?.["sec6"]?.["p"] || null
      },
      // "h2_content_title6": { "content_title": data?.["about-us"]?.["sec7"]?.["h2"] || null },
      // "content_description6": {
      //   "content_description": data?.["about-us"]?.["sec7"]?.["p"] || null
      // },
      // "h2_content_title7": {
      //   "content_title": data?.["about-us"]?.["sec8"]?.["h2"] || null
      // },
      // "content_description7": {
      //   "content_description": data?.["about-us"]?.["sec8"]?.["p"] || null
      // },
      "repeated_content_warp": {
        "repeated_content": Object.keys(data?.["about-us"] || {})
          ?.filter(sec => Number(sec?.replace("sec", "")) >= 7)
          ?.map(section => ({
            content_title: data?.["about-us"]?.[section]?.h2 ?? null,
            content_description: data?.["about-us"]?.[section]?.p ?? null,
          })),
      },
      // "h2_content_title8": { "content_title": data?.["about-us"]?.["sec9"]?.["h2"] || null },
      // "content_description8": {
      //   "content_description": data?.["about-us"]?.["sec9"]?.["p"] || null
      // },
      ...testimonialData,
      // "testimonial": {
      //   // "background-pattern-bg-image": "background-pattern",
      //   // "testimonial_title": "What Our Customers Say",
      //   "testimonial_cards": testimonialAboutupData
      // },
    },
  }
}
