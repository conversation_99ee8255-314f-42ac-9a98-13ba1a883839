# Error Handling System

This directory contains a comprehensive error handling system for the Dream Builder application, including error pages, enhanced internet connectivity monitoring, and global error management.

## Components

### Error Pages

- **Error404.jsx** - 404 Not Found page
- **Error500.jsx** - 500 Internal Server Error page
- **MaintenancePage.jsx** - Under maintenance page
- **InternetLossPage.jsx** - No internet connection page
- **UnauthorizedPage.jsx** - Access denied/unauthorized page
- **ErrorBoundary.jsx** - React error boundary for catching JavaScript errors

### Contexts

- **ErrorContext.jsx** - Global error state management
- **InternetContext.jsx** - Internet connectivity monitoring

### Hooks

- **useInternetConnection.js** - Enhanced internet connection hook with retry mechanisms

### Utilities

- **InternetConnectionStatus.jsx** - Component to display connection status
- **InternetConnectionMonitor.jsx** - Global internet connection monitor (automatically redirects to internet loss page)
- **RouterWithMonitor.jsx** - Router wrapper that includes the internet connection monitor
- **InternetConnectionTest.jsx** - Test component for debugging internet connection issues

## Usage Examples

### 1. Using Error Context

```jsx
import { useError } from "../contexts/ErrorContext";

function MyComponent() {
  const { addError, handleHttpError, setMaintenance } = useError();

  // Add a custom error
  const handleCustomError = () => {
    addError({
      type: "validation",
      message: "Please check your input",
      showNotification: true,
    });
  };

  // Set maintenance mode
  const enableMaintenance = () => {
    setMaintenance(true, "Scheduled maintenance for 2 hours");
  };

  return (
    <div>
      <button onClick={handleCustomError}>Trigger Error</button>
      <button onClick={enableMaintenance}>Enable Maintenance</button>
    </div>
  );
}
```

### 2. Using Internet Connection Hook

```jsx
import { useInternetConnection } from "../hooks/useInternetConnection";

function MyComponent() {
  const { isOnline, connectionQuality, manualRetry, getConnectionStatus } =
    useInternetConnection({
      redirectOnOffline: true,
      redirectUrl: "/internet-loss",
      autoRetry: true,
      maxRetries: 3,
    });

  const handleRetry = async () => {
    const success = await manualRetry();
    if (success) {
      console.log("Connection restored!");
    }
  };

  return (
    <div>
      <p>Status: {isOnline ? "Online" : "Offline"}</p>
      <p>Quality: {connectionQuality}</p>
      {!isOnline && <button onClick={handleRetry}>Retry Connection</button>}
    </div>
  );
}
```

### 3. Using Internet Context

```jsx
import { useInternet } from "../contexts/InternetContext";

function ConnectionMonitor() {
  const { isOnline, connectionQuality, getConnectionStats } = useInternet();

  const stats = getConnectionStats();

  return (
    <div>
      <h3>Connection Status</h3>
      <p>Online: {isOnline ? "Yes" : "No"}</p>
      <p>Quality: {connectionQuality}</p>
      <p>Uptime: {stats.uptime}%</p>
      <p>Total Connections: {stats.totalConnections}</p>
    </div>
  );
}
```

### 4. Using Internet Connection Status Component

```jsx
import InternetConnectionStatus from "./InternetConnectionStatus";

function Header() {
  return (
    <div className="header">
      <h1>My App</h1>
      <InternetConnectionStatus showDetails={false} />
    </div>
  );
}
```

## Error Handling Flow

### HTTP Errors

1. **Network Errors** (no response) → Redirect to `/internet-loss`
2. **500+ Server Errors** → Redirect to `/500`
3. **404 Not Found** → Redirect to `/404`
4. **403 Forbidden** → Redirect to `/unauthorized`
5. **401 Unauthorized** → Redirect to `/unauthorized`
6. **Account Suspended** → Redirect to `/suspendeduser`
7. **Account Blocked** → Redirect to `/blockuser`

### Internet Connectivity

1. **Connection Lost** → Automatic retry with exponential backoff
2. **Connection Restored** → Return to previous page
3. **Quality Monitoring** → Real-time connection quality assessment
4. **Statistics Tracking** → Connection history and uptime statistics

## Configuration

### Internet Connection Hook Options

```jsx
const options = {
  redirectOnOffline: true, // Redirect to error page when offline
  redirectUrl: "/internet-loss", // URL to redirect to when offline
  autoRetry: true, // Automatically retry connection
  maxRetries: 3, // Maximum retry attempts
  retryDelay: 2000, // Delay between retries (ms)
  showNotifications: true, // Show connection notifications
};
```

### Error Context Methods

- `addError(error)` - Add custom error
- `removeError(id)` - Remove error by ID
- `clearErrors()` - Clear all errors
- `handleHttpError(error, response)` - Handle HTTP errors
- `handleNetworkError(error)` - Handle network errors
- `setMaintenance(isMaintenance, message)` - Set maintenance mode

## Integration

The error handling system is automatically integrated into the application through:

1. **App.jsx** - Wraps the entire app with ErrorBoundary, ErrorProvider, InternetProvider
2. **Route.jsx** - Includes RouterWithMonitor wrapper and all error page routes
3. **RouterWithMonitor.jsx** - Wraps all routes with InternetConnectionMonitor for global connection monitoring
4. **use-http.js** - Enhanced with automatic error page routing
5. **Global Context** - Available throughout the application
6. **InternetConnectionMonitor** - Automatically monitors connection and redirects to internet loss page when offline

## Testing Internet Connection

To test the internet connection monitoring:

1. **Using Browser Dev Tools:**

   - Open Dev Tools (F12)
   - Go to Network tab
   - Check "Offline" checkbox
   - You should be automatically redirected to `/internet-loss` page
   - Uncheck "Offline" to restore connection
   - You should be redirected back to the previous page

2. **Using Test Component:**
   - Import and use `InternetConnectionTest` component
   - Provides real-time connection status and testing buttons

## Features

- ✅ Modern, responsive error pages using Ant Design
- ✅ Advanced internet connectivity monitoring
- ✅ Automatic retry mechanisms with exponential backoff
- ✅ Connection quality assessment
- ✅ Global error state management
- ✅ React error boundary for JavaScript errors
- ✅ Seamless integration with existing HTTP error handling
- ✅ Real-time connection status display
- ✅ Connection statistics and history tracking
- ✅ Maintenance mode support
- ✅ Customizable error handling and notifications
