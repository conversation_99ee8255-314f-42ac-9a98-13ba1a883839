import React, { useState, useCallback, useEffect, useMemo } from "react";
import {
  Button,
  Upload,
  Card,
  Row,
  Col,
  Popconfirm,
  Typography,
  Empty,
  Spin,
  message,
  Tag,
} from "antd";
import {
  DeleteOutlined,
  CloudUploadOutlined,
  FileImageOutlined,
} from "@ant-design/icons";
import useMediaManager from "../../../hooks/useMediaManager";
import SearchBar from "../../common/SearchBar";
import useHttp from "../../../hooks/use-http";
import { CONSTANTS } from "../../../util/constant/CONSTANTS";
import { useParams } from "react-router-dom";
import useDebounce from "../../../hooks/useDebounce";
import { imageObj } from "../../../util/functions";

const { Text, Title } = Typography;

const safeKeyFromName = (name = "") => name.trim();

const MediaBar = ({
  fileList,
  setFileList,
  formData,
  setFormData,
  templateId = null, // null for create mode, templateId for edit mode
  newMediaFiles,
  setNewMediaFiles,
  removeMediaIds,
  setRemoveMediaIds,
}) => {
  const api = useHttp();
  const { uploading, uploadFile, deleteFile, getFileUrl } = useMediaManager();
  const [searchTerm, setSearchTerm] = useState("");
  const searchDebounce = useDebounce(searchTerm, 300);
  const [existingMedia, setExistingMedia] = useState([]);
  // const [newMediaFiles, setNewMediaFiles] = useState(
  //   formData?.newMediaFiles || []
  // );
  // const [removeMediaIds, setRemoveMediaIds] = useState(
  //   formData?.removeMediaIds || []
  // );
  const [loadingExistingMedia, setLoadingExistingMedia] = useState(false);
  const { id } = useParams();

  // const uploadData = async (existingMediaList, fileList) => {
  //   return await uploadFile(existingMediaList, fileList);
  // };
  console.log(
    newMediaFiles,
    removeMediaIds,
    "callinnggjdhsujdufbjdsbf",
    formData,
    fileList
  );
  useEffect(() => {
    if (
      templateId &&
      templateId !== "new_template" &&
      formData?.mediaSet?.length
    ) {
      const loadMedia = async () => {
        setLoadingExistingMedia(true);
        try {
          const existingMediaList = formData.mediaSet || [];
          console.log(existingMediaList, "existingMediaList");
          // setExistingMedia(existingMediaList);

          let mediaFileList = {};
          mediaFileList = imageObj(existingMediaList);
          // existingMediaList.forEach((media, index) => {
          //   const originalName =
          //     media?.fileName || media?.name || `Media ${index + 1}`;
          //   const key = safeKeyFromName(originalName);

          //   mediaFileList[key] = {
          //     id: media?.id,
          //     originalName,
          //     url: media?.url || media?.path,
          //     type: "existing",
          //     size: media?.size || 0,
          //   };
          // });
          // console.log(mediaFileList, "mediaFileList");
          // Only call if needed
          // const uploadedFile = await uploadFile(existingMediaList, fileList);
          // console.log(
          //   existingMediaList,
          //   uploadedFile,
          //   "uploadedFile",
          //   mediaFileList,
          //   fileList
          // );

          // update fileList once
          setFileList((prev) => ({
            // ...prev,
            ...mediaFileList,
          }));

          // reset flag only once
          setFormData((prev) => ({ ...prev, mediaChanged: false }));
        } catch (error) {
          console.error("Error loading existing media:", error);
          message.error("Failed to load existing media");
        } finally {
          setLoadingExistingMedia(false);
        }
      };

      loadMedia();
    }
    // ✅ Only run when templateId or mediaSet changes
  }, [templateId, formData]);

  // Load existing media for edit mode
  // useEffect(() => {
  //   if (
  //     templateId &&
  //     templateId !== "new_template" &&
  //     formData?.mediaSet?.length
  //   ) {
  //     setLoadingExistingMedia(true);
  //     try {
  //       // Parse existing media from API response
  //       const existingMediaList = formData?.mediaSet || [];
  //       setExistingMedia(existingMediaList);

  //       // Convert to fileList format for display with clean keys (no prefixes)
  //       const mediaFileList = {};
  //       existingMediaList?.forEach((media, index) => {
  //         const originalName =
  //           media?.fileName || media?.name || `Media ${index + 1}`;
  //         const key = safeKeyFromName(originalName);

  //         mediaFileList[key] = {
  //           id: media?.id,
  //           originalName,
  //           url: media?.url || media?.path,
  //           type: "existing",
  //           size: media?.size || 0,
  //         };
  //       });
  //       const uploadedFile = async () =>
  //         await uploadFile(existingMediaList, fileList);
  //       console.log(uploadedFile, "uploadedFile", mediaFileList);
  //       setFileList((prev) => ({
  //         // ...prev,
  //         ...mediaFileList,
  //       }));

  //       // Reset mediaChanged on initial load in edit mode
  //       if (setFormData) {
  //         setFormData((prev) => ({ ...prev, mediaChanged: false }));
  //       }
  //     } catch (error) {
  //       console.error("Error loading existing media:", error);
  //       message.error("Failed to load existing media");
  //     } finally {
  //       setLoadingExistingMedia(false);
  //     }
  //   }
  // }, [templateId, formData, setFileList, setFormData]);

  // Update formData with media changes (+ mediaChanged flag in edit mode)
  useEffect(() => {
    if (!setFormData) return;
    // console.log(newMediaFiles, removeMediaIds, "callinnggjdhsujdufbjdsbf");
    const changedInEditMode =
      !!templateId &&
      templateId !== "new_template" &&
      ((newMediaFiles?.length ?? 0) > 0 || (removeMediaIds?.length ?? 0) > 0);

    // Only update formData if values have actually changed to prevent circular updates
    setFormData((prev) => {
      const newMediaFilesChanged =
        JSON.stringify(prev.newMediaFiles) !== JSON.stringify(newMediaFiles);
      const removeMediaIdsChanged =
        JSON.stringify(prev.removeMediaIds) !== JSON.stringify(removeMediaIds);

      if (newMediaFilesChanged || removeMediaIdsChanged) {
        return {
          ...prev,
          newMediaFiles,
          removeMediaIds,
          mediaChanged: changedInEditMode,
        };
      }
      return prev; // No change needed
    });
  }, [newMediaFiles, removeMediaIds, setFormData, templateId]);

  // Reset local state when formData is reset (after successful media operations)
  useEffect(() => {
    if (formData?.mediaResetTrigger) {
      setNewMediaFiles([]);
      setRemoveMediaIds([]);
    }
  }, [formData?.mediaResetTrigger]);

  // Sync with formData changes to ensure state consistency (without infinite loops)
  useEffect(() => {
    // Only update if the values are actually different to prevent infinite loops
    if (
      formData?.newMediaFiles !== undefined &&
      JSON.stringify(formData.newMediaFiles) !== JSON.stringify(newMediaFiles)
    ) {
      setNewMediaFiles(formData.newMediaFiles);
    }
    if (
      formData?.removeMediaIds !== undefined &&
      JSON.stringify(formData.removeMediaIds) !== JSON.stringify(removeMediaIds)
    ) {
      setRemoveMediaIds(formData.removeMediaIds);
    }
  }, [formData?.newMediaFiles, formData?.removeMediaIds]); // Removed newMediaFiles and removeMediaIds from deps

  // Entries filtered for search (keep as entries for consistent rendering)
  const filteredEntries = useMemo(() => {
    const entries = Object.entries(fileList || {});
    if (!searchDebounce) return entries;
    const q = searchDebounce?.toLowerCase();
    return entries.filter(([, file]) =>
      file?.originalName?.toLowerCase()?.includes(q)
    );
  }, [searchDebounce, fileList]);

  // Handle search input
  const handleSearch = useCallback((value) => {
    setSearchTerm(value);
  }, []);

  // Enhanced file upload handler for both create and edit modes
  // helpers
  const keyFromName = (name) =>
    name?.includes("_img-") ? name : `_img-${name}`;

  // optional: to avoid URL leaks (call when removing/replacing previews)
  const revokeIfObjectUrl = (url) => {
    try {
      if (url && url.startsWith("blob:")) URL.revokeObjectURL(url);
    } catch {}
  };

  const handleUpload = async (file, fileLists) => {
    // If you must only run once per batch, prefer a flag from your uploader.
    // Otherwise, the following is idempotent for the entire batch.

    try {
      if (templateId && templateId !== "new_template") {
        // ===== Edit mode =====
        // Expecting uploadFile to return an object { key: mediaObj } OR array; normalize to array:
        const uploadedMapOrArray = await uploadFile(fileLists, fileList);
        const staged = Array.isArray(uploadedMapOrArray)
          ? uploadedMapOrArray
          : Object.values(uploadedMapOrArray || []);

        // Derive all changes first
        let nextRemoveIds = [];
        let addOrReplaceEntries = {}; // key -> mediaObj(new)
        let toRemoveNewByOrigName = new Set();

        for (const nf of staged) {
          const k = keyFromName(nf?.originalName);
          addOrReplaceEntries[k] = {
            ...nf,
            url: URL.createObjectURL(nf.file), // preview
            type: "new",
          };
        }

        // Commit in one batch of updates
        setFileList((prev) => {
          const next = { ...prev };

          // For each replacement, decide if we’re replacing existing (with id) or a prior "new"
          Object.entries(addOrReplaceEntries).forEach(([k, newEntry]) => {
            const prevEntry = next[k];

            // If there is a previous preview URL, clean it up
            if (prevEntry?.url && prevEntry.url !== newEntry.url) {
              revokeIfObjectUrl(prevEntry.url);
            }

            if (prevEntry?.id) {
              // replacing a stored media — mark for removal
              nextRemoveIds.push(prevEntry.id);
            } else if (prevEntry?.type === "new" && !prevEntry?.id) {
              // replacing a pending "new" file with same originalName
              if (prevEntry?.originalName) {
                toRemoveNewByOrigName.add(prevEntry.originalName);
              }
            }

            next[k] = newEntry;
          });

          return next;
        });

        // Update remove IDs (merged)
        if (nextRemoveIds.length) {
          setRemoveMediaIds((prevIds) => {
            const set = new Set(prevIds);
            nextRemoveIds.forEach((id) => set.add(id));
            return Array.from(set);
          });
        }

        // Update new files list: remove any previous "new" with same originalName, then add current staged
        setNewMediaFiles((prevNew) => {
          const filtered = prevNew.filter(
            (f) => !toRemoveNewByOrigName.has(f?.originalName)
          );

          // Avoid duplicates if same originalName appears multiple times
          const byName = new Map(filtered.map((f) => [f.originalName, f]));
          for (const nf of staged) byName.set(nf.originalName, nf);
          return Array.from(byName.values());
        });

        message.success(`${fileLists?.length ?? 0} file(s) added for upload`);
      } else {
        // ===== Create mode =====
        const uploadedMapOrArray = await uploadFile(fileLists, fileList);
        const uploaded = Array.isArray(uploadedMapOrArray)
          ? uploadedMapOrArray.reduce((acc, it) => {
              acc[keyFromName(it.originalName)] = it;
              return acc;
            }, {})
          : uploadedMapOrArray;

        setFileList((prev) => ({
          ...prev,
          ...Object.fromEntries(
            Object.entries(uploaded || {}).map(([k, nf]) => [
              k,
              { ...nf, url: URL.createObjectURL(nf.file), type: "new" },
            ])
          ),
        }));

        setNewMediaFiles((prev) => {
          const staged = Array.isArray(uploadedMapOrArray)
            ? uploadedMapOrArray
            : Object.values(uploadedMapOrArray || {});
          // merge by originalName
          const byName = new Map(prev.map((f) => [f.originalName, f]));
          for (const nf of staged) byName.set(nf.originalName, nf);
          return Array.from(byName.values());
        });

        message.success("File(s) uploaded successfully");
      }
    } catch (error) {
      console.error("Error handling file upload:", error);
      message.error("Failed to process file upload");
    }

    // Prevent default Upload behavior
    return false;
  };

  // const handleUpload = async (file, fileLists) => {
  //   // Only run once when the last file of the batch arrives
  //   // console.log(
  //   //   templateId,
  //   //   "templateId",
  //   //   file,
  //   //   fileLists,
  //   //   fileLists.indexOf(file) === fileLists.length - 1
  //   // );
  //   if (fileLists.indexOf(file) === fileLists.length - 1) {
  //     try {
  //       if (templateId && templateId !== "new_template") {
  //         // ===== Edit mode =====
  //         const uploadedFile = await uploadFile(fileLists, fileList);
  //         const staged = Object.values(uploadedFile);
  //         // fileLists.map((f) => ({
  //         //   file: f,
  //         //   originalName: f.name,
  //         //   size: f.size,
  //         //   type: "new",
  //         // }));
  //         // console.log(uploadedFile, staged);

  //         // Stage for API
  //         // setNewMediaFiles((prev) => [...prev, ...staged]);

  //         // Update preview (fileList) — key by exact originalName; new overwrites existing
  //         setFileList((prev) => {
  //           const next = JSON.parse(JSON.stringify(prev));
  //           for (const nf of staged) {
  //             // const k = safeKeyFromName(nf.originalName);
  //             const k = nf?.originalName?.includes("_img-")
  //               ? nf?.originalName
  //               : `_img-${nf?.originalName}`;
  //             // safeKeyFromName(nf?.originalName);
  //             console.log(
  //               k,
  //               "djhgfjudfjdbf",
  //               next,
  //               staged,
  //               prev,
  //               next[k]?.type === "new" && !next[k]?.id,
  //               next[k]
  //             );
  //             // If replacing an existing item with same name, mark it for removal
  //             if (
  //               (next[k]?.type === "existing" || next[k]?.type === "new") &&
  //               next[k]?.id
  //             ) {
  //               setRemoveMediaIds((prevIds) =>
  //                 next[k]?.id && !prevIds.includes(next?.[k]?.id)
  //                   ? [...prevIds, next?.[k]?.id]
  //                   : prevIds
  //               );
  //             }
  //             if (next[k]?.type === "new" && !next[k]?.id) {
  //               // Remove from new files list by originalName
  //               console.log("sec call.....");
  //               setNewMediaFiles((next) => {
  //                 const updated = next.filter(
  //                   (f) => f?.originalName != next?.[k]?.originalName
  //                 );
  //                 console.log([...updated, nf], "updatedddddd");
  //                 return [...updated, nf];
  //               });
  //             }

  //             next[k] = {
  //               ...nf,
  //               url: URL.createObjectURL(nf.file), // preview
  //               type: "new",
  //             };
  //           }
  //           return next;
  //         });

  //         message.success(`${fileLists.length} file(s) added for upload`);
  //       } else {
  //         // ===== Create mode =====
  //         // console.log(fileLists, fileList);
  //         const uploadedFile = await uploadFile(fileLists, fileList);
  //         // console.log(uploadedFile, "uploadedFile");
  //         if (uploadedFile) {
  //           setFileList((pr) => ({
  //             ...pr,
  //             ...uploadedFile,
  //           }));
  //           message.success("File(s) uploaded successfully");
  //         }
  //       }
  //     } catch (error) {
  //       console.error("Error handling file upload:", error);
  //       message.error("Failed to process file upload");
  //     }
  //   }
  //   return false; // Prevent default Upload behavior
  // };

  // Enhanced file deletion handler for both create and edit modes
  const handleDelete = async (fileKey) => {
    try {
      const file = fileList[fileKey];
      console.log(file, "filesdsdsd", removeMediaIds, newMediaFiles);
      if (templateId && templateId !== "new_template") {
        // ===== Edit mode =====
        if (file?.type === "existing") {
          // Existing file: Add to removal list (dedupe)
          setRemoveMediaIds((prev) =>
            file?.id && !prev.includes(file.id) ? [...prev, file.id] : prev
          );
          message.success("File marked for removal");
        } else if (file?.type === "new") {
          // New file: Remove from new files list by originalName
          setNewMediaFiles((prev) =>
            prev.filter((f) => f.originalName !== file.originalName)
          );
          message.success("File removed");
        }

        // Remove from preview
        setFileList((prev) => {
          const updated = { ...prev };
          delete updated[fileKey];
          return updated;
        });
      } else {
        // ===== Create mode =====
        await deleteFile(fileKey);
        setFileList((prev) => {
          const updated = { ...prev };
          delete updated[fileKey];
          return updated;
        });
      }
    } catch (error) {
      console.error("Error handling file deletion:", error);
      message.error("Failed to delete file");
    }
  };

  return (
    <div className="tw-h-full tw-flex tw-flex-col tw-overflow-hidden">
      {/* Header Section - Fixed */}
      <div className="tw-flex-shrink-0 tw-px-4">
        <div className="tw-flex tw-items-center tw-justify-between tw-mb-4">
          <Title level={4} className="!tw-m-0 tw-flex tw-items-center">
            Media
          </Title>

          <Upload
            beforeUpload={handleUpload}
            showUploadList={false}
            accept="image/*"
            multiple
            disabled={uploading}
          >
            <Button
              type="primary"
              size="large"
              title=""
              loading={uploading}
              className="tw-px-6 tw-h-10 tw-flex tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
            >
              Upload
            </Button>
          </Upload>
        </div>

        {/* Search Bar */}
        <SearchBar type="page" handleSearch={handleSearch} />
      </div>

      {/* Scrollable Media Files List */}
      <div className="tw-flex-1 tw-overflow-y-auto tw-p-4">
        {(uploading || loadingExistingMedia) && (
          <div className="tw-text-center tw-py-4">
            <Spin size="large" />
            <Text className="tw-block tw-mt-2 tw-text-gray-500">
              {loadingExistingMedia ? "Loading media..." : "Uploading..."}
            </Text>
          </div>
        )}

        {filteredEntries.length === 0 &&
        !(uploading || loadingExistingMedia) ? (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={
              searchTerm
                ? `No files found matching "${searchTerm}"`
                : "No media files uploaded yet"
            }
            className="tw-py-8"
          />
        ) : (
          <Row gutter={[16, 16]}>
            {filteredEntries.map(([imageKey, file]) => {
              const displayUrl = getFileUrl(file); // Get blob URL or fallback to path
              // console.log(displayUrl, "displayUrl");
              return (
                <Col xs={24} sm={12} md={12} lg={12} key={imageKey}>
                  <Card
                    className="tw-h-full tw-border tw-border-gray-200 hover:tw-border-blue-300 tw-transition-all tw-duration-200"
                    styles={{ body: { padding: "8px 12px" } }}
                  >
                    <div className="tw-space-y-3">
                      {/* Image Preview */}
                      {displayUrl && (
                        <div className="tw-flex tw-justify-center tw-mb-3">
                          <img
                            src={displayUrl}
                            alt={file.originalName}
                            className="tw-w-full tw-h-24 tw-object-cover tw-rounded-lg tw-border tw-border-gray-200"
                            onError={(e) => {
                              // Fallback if blob URL fails
                              e.target.style.display = "none";
                            }}
                          />
                        </div>
                      )}

                      {/* File Info Header */}
                      <div className="tw-flex tw-items-center tw-justify-between">
                        <div className="tw-flex-1 tw-min-w-0">
                          <Text
                            className="tw-block tw-text-sm tw-font-medium tw-text-gray-900 tw-truncate"
                            title={file.originalName}
                          >
                            {file.originalName}
                          </Text>

                          {/* File Status Tags */}
                          <div className="tw-flex tw-items-center tw-space-x-1 tw-mt-1">
                            {file.type === "existing" && (
                              <Tag color="blue" size="small">
                                <FileImageOutlined className="tw-mr-1" />
                                Existing
                              </Tag>
                            )}
                            {file.type === "new" && (
                              <Tag color="green" size="small">
                                <CloudUploadOutlined className="tw-mr-1" />
                                New
                              </Tag>
                            )}
                            {removeMediaIds.includes(file.id) && (
                              <Tag color="red" size="small">
                                <DeleteOutlined className="tw-mr-1" />
                                To Remove
                              </Tag>
                            )}
                          </div>
                        </div>

                        <Popconfirm
                          title={
                            file.type === "existing"
                              ? "Mark this file for removal?"
                              : "Delete this file?"
                          }
                          description={
                            file.type === "existing"
                              ? "File will be removed when template is saved."
                              : "This action cannot be undone."
                          }
                          onConfirm={() => handleDelete(imageKey)}
                          okText={
                            file.type === "existing"
                              ? "Mark for Removal"
                              : "Delete"
                          }
                          cancelText="Cancel"
                          okButtonProps={{ danger: true }}
                        >
                          <Button
                            type="text"
                            danger
                            size="small"
                            icon={<DeleteOutlined />}
                            disabled={
                              file.type === "existing" &&
                              file?.id &&
                              removeMediaIds.includes(file.id)
                            }
                          />
                        </Popconfirm>
                      </div>
                    </div>
                  </Card>
                </Col>
              );
            })}
          </Row>
        )}
      </div>
    </div>
  );
};

export default MediaBar;
