import {
  Form,
  Input,
  Tooltip,
  Select,
  Button,
  Divider,
  message,
  Tag,
} from "antd";
import React, { useRef, useState, useEffect, useMemo } from "react";
import { useDrag, useDrop } from "react-dnd";
import { DND_TYPES } from "../../../util/content";
import { autoDetectRepeatPlaceHolder } from "../../../util/functions";
import {
  ChevronLeft,
  GripVertical,
  Trash2,
  Search,
  ChevronDown,
  ChevronUp,
  Loader2,
  X,
  Edit,
  Pencil,
} from "lucide-react";
import { useDebounce } from "../../../hooks/useDebounce";
import useHttp from "../../../hooks/use-http";
import { CONSTANTS } from "../../../util/constant/CONSTANTS";
import { apiGenerator } from "../../../util/functions";
import runtimeCache from "../../../utils/runtimeCache";
import SelectComponentModal from "./SelectComponentModal";
import Paragraph from "antd/es/typography/Paragraph";
import SearchBar from "../../common/SearchBar";
import { useParams } from "react-router-dom";

const StructureItem = React.memo(function StructureItem({
  index,
  component,
  onRemove,
  moveComponent,
  onComponentChange,
  allComponents,
  pageData,
  setPageData,
  onVersionChangeLoading,
}) {
  const ref = useRef(null);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [showSelectModal, setShowSelectModal] = useState(false);
  const [isVersionLoading, setIsVersionLoading] = useState(false);
  const [cssClassValue, setCssClassValue] = useState(component?.class || "");
  const [nameValue, setNameValue] = useState(component?.name || "");
  const api = useHttp();
  const { id } = useParams();

  // Debounce CSS class changes to prevent preview flickering
  const debouncedCssClass = useDebounce(cssClassValue, 300);
  const debouncedName = useDebounce(nameValue, 300);

  // Update component when debounced values change
  useEffect(() => {
    if (debouncedCssClass !== component?.class) {
      handleFieldChange("class", debouncedCssClass);
    }
  }, [debouncedCssClass]);

  useEffect(() => {
    if (debouncedName !== component?.name) {
      handleFieldChange("name", debouncedName);
    }
  }, [debouncedName]);

  // Update local state when component prop changes
  useEffect(() => {
    setCssClassValue(component?.class?.trim() || "");
    setNameValue(component?.name?.trim() || "");
  }, [component?.class, component?.name]);

  const [{ isOver }, drop] = useDrop({
    accept: DND_TYPES.STRUCT_ITEM,
    hover: (item, monitor) => {
      if (!ref.current) return;
      const dragIndex = item.index;
      const hoverIndex = index;
      if (dragIndex === hoverIndex) return;

      const rect = ref.current.getBoundingClientRect();
      const middleY = (rect.bottom - rect.top) / 2;
      const clientOffset = monitor.getClientOffset();
      if (!clientOffset) return;
      const hoverY = clientOffset.y - rect.top;
      if (dragIndex < hoverIndex && hoverY < middleY) return;
      if (dragIndex > hoverIndex && hoverY > middleY) return;

      moveComponent(dragIndex, hoverIndex);
      item.index = hoverIndex;
    },
    collect: (monitor) => ({ isOver: monitor.isOver() }),
  });

  const [{ isDragging }, drag] = useDrag({
    type: DND_TYPES.STRUCT_ITEM,
    item: () => ({ index }),
    collect: (monitor) => ({ isDragging: monitor.isDragging() }),
  });

  drag(drop(ref));

  const handleFieldChange = (field, value) => {
    onComponentChange(index, { ...component, [field]: value });
  };

  const handleManyFieldsChange = (obj) => {
    onComponentChange(index, { ...component, ...obj });
  };

  const componentOptions = useMemo(
    () =>
      (allComponents || []).map((comp) => ({
        value: comp.id,
        label: comp.name,
      })),
    [allComponents]
  );

  return (
    <div
      ref={ref}
      className={`tw-bg-gray-100 tw-border tw-border-[#D9DBDF] tw-rounded-2xl tw-p-3 tw-pt-2 tw-transition-all tw-duration-200 tw-cursor-move ${
        isDragging
          ? "tw-shadow-lg tw-scale-105 tw-bg-blue-50"
          : isOver
          ? "tw-bg-yellow-50"
          : "tw-hover:tw-bg-gray-200"
      }`}
      style={{ opacity: isDragging ? 0.8 : 1 }}
    >
      <div className="tw-flex tw-items-center tw-justify-between">
        <div className="tw-flex tw-items-center tw-gap-3">
          {/* <GripVertical className="tw-w-4 tw-h-4 tw-text-gray-400" /> */}
          <span className="tw-text-gray-500 tw-font-normal tw-text-sm">
            Position {index + 1}
          </span>
        </div>
        <div className="tw-flex tw-items-center">
          <Button
            type="text"
            danger
            icon={<Trash2 className="tw-w-4 tw-h-4" />}
            onClick={(e) => {
              e.stopPropagation();
              onRemove(index);
            }}
            className="tw-text-gray-400 tw-hover:tw-text-red-500 tw-transition-colors tw-rounded-lg tw-hover:tw-bg-white"
          />
          <Button
            type="text"
            icon={
              isCollapsed ? (
                <ChevronDown className="tw-w-4 tw-h-4 tw-text-gray-400" />
              ) : (
                <ChevronUp className="tw-w-4 tw-h-4 tw-text-gray-400" />
              )
            }
            onClick={(e) => {
              e.stopPropagation();
              setIsCollapsed((v) => !v);
            }}
            className="tw-w-full tw-bg-transparent tw-border-0 tw-cursor-pointer"
          />
        </div>
      </div>

      {!isCollapsed && (
        <div className=" tw-space-y-2">
          <div className="">
            {!id ? (
              <Select
                size="large"
                placeholder="v1"
                value={component?.componentVersionId}
                onChange={(value) => {
                  handleFieldChange("componentVersionId", value);

                  // Find the selected version
                  const selectedVersion = component?.componentversions?.find(
                    (v) => v.id === value
                  );
                  if (selectedVersion) {
                    // Check cache first
                    const cachedComponent = runtimeCache.getCachedComponent(
                      component?.id,
                      selectedVersion?.version
                    );

                    if (cachedComponent && cachedComponent?.html) {
                      // Update component with cached data
                      onComponentChange(index, {
                        ...component,
                        ...cachedComponent,
                        componentVersionId: value,
                      });
                      return;
                    }

                    // Set loading state and notify parent
                    setIsVersionLoading(true);
                    if (onVersionChangeLoading) {
                      onVersionChangeLoading(true);
                    }

                    // Fetch from API
                    api.sendRequest(
                      apiGenerator(CONSTANTS.API.componentVersions.getById, {
                        id: value,
                      }),
                      (res) => {
                        const fullComponentData = res?.data?.[0] || res;
                        runtimeCache.cacheComponent(fullComponentData);
                        onComponentChange(index, {
                          ...component,
                          ...fullComponentData,
                          componentVersionId: value,
                        });
                        // Clear loading state
                        setIsVersionLoading(false);
                        if (onVersionChangeLoading) {
                          onVersionChangeLoading(false);
                        }
                      },
                      null,
                      null,
                      (error) => {
                        console.error(
                          "Error fetching component version:",
                          error
                        );
                        // Clear loading state on error
                        setIsVersionLoading(false);
                        if (onVersionChangeLoading) {
                          onVersionChangeLoading(false);
                        }
                      }
                    );
                  }
                }}
                onMouseDown={(e) => e.stopPropagation()}
                className="tw-w-12 tw-h-3 tw-text-sm version-class"
                style={{
                  width: 55,
                  height: 30,
                  borderRadius: "100px",
                  fontSize: 12,
                }}
                options={
                  component?.componentversions?.map((v) => ({
                    value: v?.id,
                    label: `v${v?.version}`,
                  })) || []
                }
              />
            ) : (
              <Tag color="default" className="tw-rounded-xl ">
                v{component?.componentVersionId || 0}
              </Tag>
            )}
          </div>

          <div className="">
            <Input
              size="large"
              placeholder="Component Name"
              value={nameValue}
              onChange={(e) => setNameValue(e.target.value)}
              onMouseDown={(e) => e.stopPropagation()}
              className="tw-rounded-xl tw-text-gray-900"
            />
          </div>

          <div className="tw-space-y-2">
            <Input
              size="large"
              placeholder="Enter class"
              value={cssClassValue}
              onChange={(e) => setCssClassValue(e.target.value)}
              onMouseDown={(e) => e.stopPropagation()}
              className="tw-rounded-xl tw-text-gray-900"
            />

            <Select
              size="large"
              placeholder="Repeat"
              value={component?.type}
              onChange={(value) => {
                if (value === "single") {
                  handleManyFieldsChange({
                    repeatComponents: [],
                    type: value,
                  });
                } else if (value == "repeat") {
                  const isKeyExist = autoDetectRepeatPlaceHolder(
                    component?.html
                  );
                  if (!isKeyExist.length) {
                    message.error(
                      "Please add {{key}} in html to use repeat component"
                    );
                    return;
                  }
                }
                handleFieldChange("type", value);
              }}
              onMouseDown={(e) => e.stopPropagation()}
              className="tw-w-full"
              style={{ borderRadius: 16 }}
              options={[
                { value: "single", label: "Single" },
                { value: "repeat", label: "Repeat" },
              ]}
            />

            {component?.type === "repeat" && (
              <div className="tw-space-y-2">
                {!component?.repeatComponents ||
                component?.repeatComponents?.length == 0 ? (
                  <Button
                    type="default"
                    size="large"
                    onClick={() => setShowSelectModal(true)}
                    className="tw-w-full tw-rounded-xl tw-h-10 tw-border-gray-300 hover:tw-border-gray-400"
                  >
                    {"Select Component"}
                  </Button>
                ) : (
                  <div className="tw-bg-white tw-border tw-border-gray-200 tw-rounded-xl  tw-gap-2 tw-flex tw-flex-col">
                    <div className="tw-flex tw-justify-between tw-items-center tw-p-3 tw-pb-0">
                      <p>Components</p>
                      <Button
                        type="text"
                        size="small"
                        icon={
                          <Pencil className="tw-w-4 tw-h-4 !tw-text-gray-600 tw-mr-1" />
                        }
                        onClick={() => setShowSelectModal(true)}
                        className="tw-w-full tw-border-0 hover:!tw-bg-transparent tw-rounded-xl tw-h-6 tw-border-gray-300 hover:tw-border-gray-400"
                      />
                    </div>
                    <Divider
                      size="large"
                      className="!tw-my-0  tw-text-gray-300 tw-border-[0.5px] "
                    />
                    <div className="tw-p-3 tw-pt-0">
                      {component?.repeatComponents?.map((rc, idx) => (
                        <>
                          <div
                            className="tw-flex tw-justify-between tw-items-center tw-gap-x-1"
                            key={idx}
                          >
                            <Paragraph
                              ellipsis={{
                                rows: 1,
                                expandable: false,
                                tooltip: true,
                              }}
                              className="tw-text-gray-900 tw-text-sm !tw-mb-0"
                            >
                              {rc?.componentName}
                            </Paragraph>
                            {/* <p>{rc?.componentName}</p> */}
                            <p className="tw-text-sm tw-border-[1px] tw-border-gray-300 tw-border-solid tw-px-[9px] tw-py-[2px] tw-rounded-[14px] tw-bg-white">
                              v{rc?.version}
                            </p>
                          </div>
                        </>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Select Component Modal */}
      <SelectComponentModal
        visible={showSelectModal}
        onClose={() => setShowSelectModal(false)}
        component={component}
        onSave={(updatedData) => {
          // Update the component with the selected repeat configuration
          handleManyFieldsChange({
            repeatComponents: updatedData?.repeatComponents,
          });
        }}
        pageData={pageData}
        setPageData={setPageData}
      />
    </div>
  );
});

const PageStructure = ({
  isStructureOpen,
  setIsStructureOpen,
  pageData,
  components,
  removeComponentFromPage,
  moveComponent,
  setPageData,
  onVersionChangeLoading,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  const [isSearching, setIsSearching] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);

  useEffect(() => {
    setIsSearching(searchTerm !== debouncedSearchTerm);
  }, [searchTerm, debouncedSearchTerm]);

  useEffect(() => {
    const onResize = () => {
      const w = window.innerWidth;
      setIsMobile(w < 768);
      setIsTablet(w >= 768 && w < 1024);
    };
    onResize();
    window.addEventListener("resize", onResize);
    return () => window.removeEventListener("resize", onResize);
  }, []);

  const handleComponentChange = (index, updatedComponent) => {
    const newComponents = [...(pageData?.componentData || [])];
    newComponents[index] = updatedComponent;
    setPageData({ ...pageData, componentData: newComponents });
  };
  const visibleItems = useMemo(() => {
    const comps = pageData?.componentData || [];
    if (!debouncedSearchTerm) return comps;
    const lower = debouncedSearchTerm?.toLowerCase();
    return comps?.filter((pc) => {
      // const comp = (components || []).find((c) => c.id === pc.id);
      return pc?.name?.toLowerCase()?.includes(lower);
    });
  }, [pageData, debouncedSearchTerm]);
  console.log(pageData, "pageData");
  return (
    <div
      className={`tw-bg-white tw-border-l tw-border-gray-200 tw-flex tw-flex-col tw-transition-all tw-duration-300 tw-ease-in-out ${
        isStructureOpen
          ? isMobile
            ? "tw-fixed tw-inset-0 tw-z-50 tw-w-full"
            : isTablet
            ? "tw-w-64"
            : "tw-w-[18rem]"
          : "tw-w-0 tw-overflow-hidden"
      } ${isMobile && isStructureOpen ? "tw-shadow-2xl" : ""}`}
    >
      {isStructureOpen && (
        <>
          <div className=" tw-border-b tw-border-gray-200 tw-flex tw-items-center tw-justify-between">
            <div className="tw-p-3 md:tw-p-4 tw-flex tw-items-start tw-justify-between">
              <div className="tw-flex tw-flex-col tw-justify-start tw-text-start">
                <h3 className="tw-text-base md:tw-text-lg tw-font-semibold tw-text-gray-900">
                  Page Structure
                </h3>
                <p className="tw-text-sm tw-text-gray-600 tw-mt-1 tw-hidden md:tw-block">
                  {pageData?.componentData?.length || 0} components
                </p>
              </div>
              {isMobile && (
                <button
                  onClick={() => setIsStructureOpen(false)}
                  className="tw-p-2 tw-text-gray-400 tw-hover:tw-text-gray-600 tw-rounded-lg tw-ml-2"
                >
                  <X className="tw-w-5 tw-h-5" />
                </button>
              )}
            </div>
            <div className="tw-flex tw-h-full tw-items-center tw-justify-center tw-border-l tw-border-gray-200">
              <Tooltip
                title={
                  isStructureOpen
                    ? "Hide Page Structure"
                    : "Show Page Structure"
                }
              >
                <button
                  onClick={() => setIsStructureOpen((v) => !v)}
                  className="tw-text-gray-600 tw-px-3 tw-flex tw-items-center tw-justify-center tw-rounded-lg"
                >
                  <ChevronLeft
                    size={30}
                    className={`${!isStructureOpen ? "" : "tw-rotate-180 "}`}
                  />
                </button>
              </Tooltip>
            </div>
          </div>

          <div className="tw-p-3 md:tw-p-4">
            <SearchBar
              placeholder="Search..."
              type="page"
              handleSearch={(e) => setSearchTerm(e)}
            />
            {/* <Input
              size="middle"
              placeholder="Search components..."
              prefix={
                isSearching ? (
                  <Loader2 className="tw-w-4 tw-h-4 tw-text-blue-500 tw-animate-spin" />
                ) : (
                  <Search className="tw-w-4 tw-h-8 tw-text-gray-400" />
                )
              }
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="tw-rounded-lg"
              allowClear
            /> */}
          </div>
        </>
      )}
      <div className="tw-flex-1 tw-overflow-y-auto tw-p-3  md:tw-p-4 !tw-pt-0">
        {visibleItems?.length > 0 ? (
          <div className="tw-space-y-2">
            {visibleItems
              ?.sort((a, b) => a?.index - b?.index)
              .map((component, index) => (
                <StructureItem
                  key={component?.uniqueId || `pc-${component?.id}-${index}`}
                  index={index}
                  component={component}
                  onRemove={removeComponentFromPage}
                  moveComponent={moveComponent}
                  onComponentChange={handleComponentChange}
                  allComponents={components}
                  pageData={pageData}
                  setPageData={setPageData}
                  onVersionChangeLoading={onVersionChangeLoading}
                />
              ))}
          </div>
        ) : (
          <div className="tw-text-center tw-py-8">
            <p className="tw-text-gray-500">No components added</p>
            <p className="tw-text-sm tw-text-gray-400 tw-mt-1">
              Components will appear here as you add them
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PageStructure;
