import { Input } from "antd";
import { generateGlobalPreviewHTML } from '../components/Components/content';

const { TextArea } = Input;

// i want to function for null, "" , undefined, space trim  check
export const isNotNullOrEmpty = (value) => {
    if (value === null || value === undefined || value === "") {
        return false;
    }
    if (typeof value === "string" && value.trim() === "") {
        return false;
    }
    return true;
};


export const apiGenerator = (apiObject, exchangePair = {}, join = null) => {
    const apiObj = { ...apiObject };
    if (Object.keys(exchangePair).length) {
        Object.keys(exchangePair).forEach((el) => {
            apiObj.endpoint = apiObj.endpoint.replace(`:${el}`, exchangePair[el]);
        });
    }

    if (join) {
        apiObj.endpoint = `${apiObj.endpoint}${join}`;
    }
    return apiObj;
};

export const generateRoutes = (routes = [], role) => {
    let Routes = [];

    routes?.forEach((route) => {
        const mainRoute = { ...route };
        // nestedBasePath = nestedBasePath.trim().replace("//", "/");
        // const menuItem = {
        //   id: nestedBasePath,
        //   icon: route?.icon,
        //   label: route?.label,
        //   key: nestedBasePath,
        // };

        if (route?.children && route?.children?.length > 0) {
            // Recursive call for nested children

            const children = generateRoutes(route?.children, role);
            if (children.length) mainRoute.children = children;
        }
        if (!route?.Role || route?.Role?.includes(role)) {
            Routes?.push(mainRoute);
        } else if (
            mainRoute?.children?.length &&
            (!route?.Role || route?.Role?.includes(role))
        ) {
            Routes?.push(...mainRoute.children);
        }
    });
    return Routes;
};


// export const buildItemList = (data, expandedAll, parentKey, handleDelete, handleAddItem) => {
//     if (Array.isArray(data)) {
//         // If data is an array
//         console.log(data)
//         return data.map((item, index) => ({
//             key: `${parentKey}_pos_${index}`,
//             label: `Position ${index + 1}`,
//             children: React.createElement(JsonContentCollapse, {
//                 expanded: expandedAll,
//                 onDeleteItem: (it) => handleDelete(parentKey, index, it.key),
//                 itemList: buildItemList(item, expandedAll, parentKey, handleDelete, handleAddItem)
//             })
//         }));
//     } else if (typeof data === "object" && data !== null) {
//         // If data is an object
//         return Object.keys(data).map((key) => ({
//             key,
//             label: key,
//             isDelete: true,
//             onDelete: () => handleDelete(parentKey, null, key),
//             children: typeof data[key] === "object"
//                 ? React.createElement(JsonContentCollapse, {
//                     expanded: expandedAll,
//                     itemList: buildItemList(
//                         data[key],
//                         expandedAll,
//                         key,
//                         handleDelete,
//                         handleAddItem
//                     ),
//                 })
//                 : React.createElement(TextArea, { rows: 4, value: data[key], readOnly: true }),
//         }));
//     } else {
//         // Primitive values
//         return [
//             {
//                 key: parentKey,
//                 label: parentKey,
//                 children: React.createElement(TextArea, { rows: 4, value: data, readOnly: true }),
//             },
//         ];
//     }
// };


export const autoDetectRepeatPlaceHolder = (str) => {
    const content = str;
    const regex = /\{\{([^}]+)\}\}/g;
    const matches = [];
    let match;

    while ((match = regex?.exec(content)) !== null) {
        if (!matches?.includes(match[1])) {
            matches?.push(match[1]);
        }
    }

    return [...new Set([...matches])];
};

// export const autoDetectPlaceholders = (str) => {
//     const content = str;
//     if (!content) return;

//     // Matches ${var} or ${_img-${var}} or ${_alt-${var}}
//     // /\$\{(?:_img-\$\{([^}]+)\}|([^}]+))\}/g;
//     const regex = /\$\{(?:_img-\$\{([^}]+)\}|([^}]+))\}/g;
//     // /\$\{(?:_img-|_alt-)?([^}]+)\}/g;
//     const matches = new Set();
//     let match;

//     while ((match = regex.exec(content)) !== null) {
//         // match[1] = variable inside _img-${...}
//         // match[2] = normal variable
//         const variable = match[1] || match[2];
//         if (variable) {
//             matches.add(variable);
//         }
//     }
//     return [...matches];
// };
export const autoDetectPlaceholders = (str) => {
    if (!str) return [];

    // Matches:
    // ${_img-${var}}  -> returns var
    // ${_alt-${var}}  -> returns _alt-var
    // ${var}          -> returns var
    const regex = /\$\{(?:_img-\$\{([^}]+)\}|_alt-\$\{([^}]+)\}|([^}]+))\}/g;

    const matches = new Set();
    let match;

    while ((match = regex.exec(str)) !== null) {
        if (match[1]) {
            // _img-${...} → prefix with "image:"
            matches.add(`${match[1]}`);
        } else if (match[2]) {
            // _alt-${...} -> include _alt- prefix
            // matches.add(`_alt-${match[2]}`);
        } else if (match[3]) {
            // normal ${...}
            matches.add(match[3]);
        }
    }

    return [...matches];
};

export const autoDetectImgPlaceholders = (str) => {
    if (!str) return [];

    // Matches:
    // ${_img-${var}}  -> returns var
    const regex = /\$\{(?:_img-\$\{([^}]+)\})\}/g;
    const matches = new Set();
    let match;

    while ((match = regex.exec(str)) !== null) {
        if (match[1]) {
            // _img-${...} → prefix with "image:"
            matches.add(`${match[1]}`);
        }
    }

    return [...matches];
};


// =================================Collapse functinlity =========================================

export const updateAtPath = (obj, path, updater) => {
    if (path.length === 0) return updater(obj);

    const [head, ...rest] = path;

    if (Array.isArray(obj)) {
        const clone = [...obj];
        clone[head] = updateAtPath(obj[head], rest, updater);
        return clone;
    } else if (typeof obj === "object" && obj !== null) {
        return {
            ...obj,
            [head]: updateAtPath(obj[head], rest, updater),
        };
    }
    return obj;
};
export const addAtPath = (obj, path) =>
    updateAtPath(obj, path, (arr) => {
        if (!Array.isArray(arr) || arr.length === 0) {
            message.warning("Cannot add item: reference structure not found.");
            return arr;
        }
        const referenceKeys = Object.keys(arr[0] || {});
        const newItem = referenceKeys.reduce(
            (acc, k) => ({ ...acc, [k]: "" }),
            {}
        );
        return [...arr, newItem];
    });
export const deleteAtPath = (obj, path, fieldKey = null) =>
    updateAtPath(obj, path, (target) => {
        if (Array.isArray(target)) {
            const index = path[path.length - 1];
            if (typeof index === "number" && index < target.length) {
                return target.filter((_, i) => i !== index);
            }
        } else if (typeof target === "object" && target !== null) {
            if (fieldKey) {
                const { [fieldKey]: _, ...rest } = target;
                return rest;
            }
        }
        return target;
    });




// ================================ Preview & Collapse JSON prepare functinlity =========================================

function extractVariables(str) {
    const results = [];
    const seen = new Set();

    const add = (v) => {
        if (!seen.has(v)) {
            seen.add(v);
            results.push(v);
        }
    };

    function scan(s, from = 0) {
        let i = from;
        while (i < s.length) {
            const start = s.indexOf("${", i);
            if (start === -1) break;

            let j = start + 2; // after "${"
            let depth = 1;

            // walk forward, tracking nested ${...}
            while (j < s.length && depth > 0) {
                if (s[j] === "$" && s[j + 1] === "{") {
                    depth++;
                    j += 2;
                    continue;
                }
                if (s[j] === "}") {
                    depth--;
                    j++;
                    continue;
                }
                j++;
            }

            if (depth === 0) {
                const expr = s.slice(start + 2, j - 1).trim(); // content inside ${...}
                // first, collect inner placeholders inside expr
                scan(expr);
                // then, add the whole expr (e.g., "${slug}_state")
                add(expr);
                i = j;
            } else {
                // unmatched brace; stop
                break;
            }
        }
    }

    scan(str);
    return results;
}

export const findAndReplaceVariable = ({
    str,
    content,
    extraVariable = [],
    isDirectVariable = true,
    mediaFiles = {},
    comVariable = {},
    // isIncludeInComVariable = true,
    isDefaultEmpty = false,
    depth = 0,
    maxDepth = 5,
    isExport = false,
}) => {
    if (!str) return false;
    if (depth > maxDepth) {
        return { str, extraVariable };
    }
    const variables = extractVariables(str);
    // console.log(variables, "variablesffff");
    const directVariable = variables?.filter(
        (variable) => extractVariables(variable)?.length == 0
    );
    // console.log(directVariable, "variablesffff");
    directVariable.map((variable) => {
        if (
            !variable.includes("_img-") &&
            !(variable in comVariable) &&
            variable != "slug"
        ) {
            comVariable[variable] = "";
        }

        if (!isDirectVariable) {
            extraVariable.push(variable);
        }
        if (variable in content && variable != "slug") {
            comVariable[variable] = content[variable];

        }
        if (!(variable in content) && isDefaultEmpty) {
            content[variable] = "";
        }
        // console.log(variable, "variable");
        if (variable.includes("_img-")) {
            // let variableKey = variable.replace(/ /g, "-");
            let variableKey = variable
                .replace(/[^a-zA-Z0-9 _-]/g, "") // keep letters, numbers, spaces, _ and -
                .replace(/\s+/g, "-");           // convert spaces to -
            // console.log(variableKey, "variableKey");
            // if (variableKey.includes("background-pattern")) {
            //     console.log("background-pattern", variableKey, mediaFiles);
            // }
            const variableLowercase = variableKey?.toLowerCase();
            if (!(variableKey in mediaFiles)) {
                variableKey = variableLowercase;
            }
            // if (variableKey?.includes("-logo")) {

            // console.log(variableKey, "variableKey", mediaFiles, variable)
            // }
            if ((variableKey in mediaFiles)) {
                // console.log(mediaFiles[variableKey], "variableKey");
                if (mediaFiles[variableKey] != null && mediaFiles[variableKey] != undefined && mediaFiles[variableKey] != "") {
                    let imgUrl = mediaFiles[variableKey]?.url;
                    if (isExport) {
                        imgUrl = mediaFiles[variableKey]?.path;
                    }
                    str = str?.replace(
                        new RegExp(`\\$\\{${variable}\\}`, "g"),
                        imgUrl
                    );
                    const fileName = mediaFiles[variableKey]?.name?.split(".")[0];

                    if (fileName) {
                        // console.log(str, "str");
                        str = str?.replace(
                            new RegExp(`\\$\\{_alt-${variable?.replace("_img-", "")}\\}`, "g"),
                            fileName?.replaceAll("-", " ")
                        );
                        // console.log(str, "after    str");

                    }
                }
            }

        } else if (variable in content) {
            // if (variable?.includes("bus-interior")) {
            //     console.log(variable, "variable", content);
            // }
            if (content[variable] != null && content[variable] != undefined && (content[variable] != "" || isDefaultEmpty)) {
                if (Array.isArray(content[variable])) {
                    str = content[variable];
                } else {
                    str = str?.replace(
                        new RegExp(`\\$\\{${variable}\\}`, "g"),
                        findAndReplacePTag(content[variable])
                    );
                }
            }
        }
    });
    // console.log(variables)
    // if (variables?.includes("term_and_conditions")) {
    //     console.log(str, "term_and_conditions");
    // }
    // console.log(str, "variablesffff", JSON.parse(JSON.stringify(content)));
    if (directVariable?.length != variables.length) {
        return findAndReplaceVariable({
            str,
            content,
            extraVariable,
            isDirectVariable: false,
            mediaFiles,
            comVariable,
            isDefaultEmpty,
            depth: depth + 1,
            maxDepth,
            isExport,
        });
    }
    return { str, extraVariable };
};

const isKeyExist = (key, obj, pageName, comName) => {
    if (obj && pageName in obj && comName in obj[pageName] && key in obj[pageName][comName]) {
        return true;

    } else {
        return false;
    }
}

const filterPageComponents = ({ component, pageName }) => {
    if (pageName?.toLowerCase() == ("school bus") && component?.name?.toLowerCase() == "bus_amenities") {
        return false;
    }
    return true;
}

const findAndReplacePTag = (str) => {
    // check str is string
    if (typeof str != "string") return str;
    const matches = str?.match(/<p>(?=[^/])/g) || [];
    if (matches?.length == 1) {
        str = str?.replaceAll("<p>", "");
        str = str?.replaceAll("</p>", "");
    }
    return str;
}

export const replaceTemplateContent = ({
    page,
    tempContent = {},
    content = {},
    slug,
    mediaFiles,
    allVariables,
    isExport,
    templateObj = {},
    dynamicContentForm = {},
    bradingDetails = {},
}) => {
    // console.log(mediaFiles, "mediaFilesdddd");
    const pageVariable = {};
    const pageImgVariable = {};
    const pageName = slug?.label || page?.name;
    const extraKeys = {
        default_city_small: getCompanySlug(templateObj?.GVariable?.default_city),
        default_state_small: getCompanySlug(templateObj?.GVariable?.default_state),
    }
    page.oldSlug = page?.urlSlug;
    page.slug = getCompanySlug(slug?.slug || page?.name);
    if (page == undefined || page == null) return page;
    page.oldName = page?.name;
    if (page?.type == "dynamic") {
        if (page.name == "services-d") {
            page.url = `/${getCompanySlug(templateObj?.GVariable?.default_city)}-` + slug?.slug;
        } else {
            page.url = "/" + slug?.slug;
        }

    }
    page.sitemapLabel = page?.type == "dynamic" ? slug?.sitemapLabel : (page?.siteMapLabel || page?.name);
    // page.components = page?.components?.filter((component) => filterPageComponents({ component, pageName }))?.map((component) => {
    page.componentData = page?.componentData?.filter((component) => filterPageComponents({ component, pageName }))?.map((component) => {
        const comVariable = {};
        const comImgvariable = {};
        // get _img- variable and store in pageImgVariable
        const imgPlaceholder = autoDetectImgPlaceholders(component?.html);
        imgPlaceholder?.map((item) => {
            comImgvariable[item] = true;
        });
        component.html_withValue = component.html;
        // const extraVariable = [];
        // console.log(component, "placeHolderValue");
        component?.placeholders?.map((placeholder) => {
            let placeHolderValue = "";
            if (isKeyExist(placeholder, allVariables, pageName, component?.name)) {
                placeHolderValue = allVariables?.[pageName]?.[component?.name]?.[placeholder];
            } else if (isKeyExist(placeholder, content, pageName, component?.name)) {
                placeHolderValue = content?.[pageName]?.[component?.name]?.[placeholder];
            } else if (isKeyExist(placeholder, content, page?.name, component?.name)) {
                placeHolderValue = content?.[page?.name]?.[component?.name]?.[placeholder];
            } else if (isKeyExist(placeholder, tempContent, page?.name, component?.name)) {
                placeHolderValue = tempContent?.[page?.name]?.[component?.name]?.[placeholder];
            }
            // console.log(placeHolderValue, allVariables, tempContent, "jdhjdfd")
            if (placeHolderValue != null && placeHolderValue != undefined && placeHolderValue != "") {

                component.html_withValue =
                    component?.html_withValue.replace(
                        new RegExp(`\\$\\{${placeholder}\\}`, "g"),
                        findAndReplacePTag(placeHolderValue)
                    );
            }
            // }

            comVariable[placeholder] = placeHolderValue;
        });
        if (component?.repeatedPlaceholder && component?.repeatedPlaceholder.length) {
            // get _img- and store in pageImgVariable
            component?.repeatedPlaceholder?.map((placeholder) => {
                let placeHolderValue = [];
                let imgPlaceholders = []
                if (isKeyExist(placeholder, allVariables, pageName, component?.name)) {
                    placeHolderValue = allVariables?.[pageName]?.[component?.name]?.[placeholder];
                } else if (isKeyExist(placeholder, content, pageName, component?.name)) {
                    placeHolderValue = content?.[pageName]?.[component?.name]?.[placeholder];
                } else if (isKeyExist(placeholder, content, page?.name, component?.name)) {
                    placeHolderValue = content?.[page?.name]?.[component?.name]?.[placeholder];
                } else if (isKeyExist(placeholder, tempContent, page?.name, component?.name)) {
                    placeHolderValue = tempContent?.[page?.name]?.[component?.name]?.[placeholder];
                }
                // console.log(placeHolderValue, "repeatedCom", placeholder, allVariables, content, page, component);
                if (placeHolderValue != undefined && placeHolderValue != null && !Array.isArray(placeHolderValue)) {
                    const variables = extractVariables(placeHolderValue);
                    const directVariable = variables?.filter(
                        (variable) => extractVariables(variable)?.length == 0
                    );
                    if (variables.length != directVariable.length) {
                        const placeHolderKey = findAndReplaceVariable({
                            str: placeHolderValue,
                            content: { ...(templateObj?.GVariable || {}), ...(content?.[page?.name]?.[component?.name] || {}), ...(content?.[pageName]?.[component?.name] || {}), slug_key: slug?.slug, slug: slug?.label, ...extraKeys },
                            mediaFiles,
                            isDefaultEmpty: true,
                            isExport,
                            // comVariable,
                        });
                        placeHolderValue = placeHolderKey.str;
                        // console.log(placeHolderValue, "placeHolderValue_newwww", placeHolderKey);
                    }
                }
                if (Array.isArray(placeHolderValue) && placeHolderValue?.length) {
                    // console.log(placeHolderValue, "placeHolderValue_newwww");
                    const repeatedCom = placeHolderValue?.map((item) => {
                        const newItm = JSON.parse(JSON.stringify(item));
                        // Add null check for repeatComponents
                        if (!component?.repeatComponents?.length) {
                            console.warn(`repeatComponents is undefined for component: ${component?.name}`);
                            return { str: '' }; // Return empty string if repeatComponents is undefined
                        }
                        // component?.repeatComponents?.map((rc) => {
                        //     const htmlContent = JSON.parse(JSON.stringify(rc));

                        //     return findAndReplaceVariable({
                        //         str: htmlContent?.html,
                        //         content: { ...(content?.[pageName]?.[component?.name] || {}), ...templateObj?.GVariable, ...newItm, ...extraKeys },
                        //         mediaFiles,
                        //         isExport,
                        //         isDefaultEmpty: true,
                        //         // isExport
                        //         // comVariable,
                        //     });
                        // });
                        const htmlContent = JSON.parse(JSON.stringify(component?.repeatComponents?.[0]));
                        const imgPlaceholder = autoDetectImgPlaceholders(htmlContent?.html);
                        // imgPlaceholders?.push(...imgPlaceholder);
                        // imgPlaceholders insdie add  imgPlaceholder as a object using reduce
                        const imgPlaceholdersArr = imgPlaceholder?.reduce((acc, cur) => {
                            acc[cur] = true;
                            return acc;
                        }, {});
                        imgPlaceholders?.push(imgPlaceholdersArr)
                        // imgPlaceholder?.map((item) => {
                        //     comImgvariable[item] = true;
                        // });
                        return findAndReplaceVariable({
                            str: htmlContent?.html,
                            content: { ...(content?.[pageName]?.[component?.name] || {}), ...templateObj?.GVariable, ...newItm, ...extraKeys },
                            mediaFiles,
                            isExport,
                            isDefaultEmpty: true,
                            // isExport
                            // comVariable,
                        });
                    });
                    component.html_withValue =
                        component?.html_withValue.replace(
                            new RegExp(`{{${placeholder}}}`, "g"),
                            repeatedCom?.map((item) => item?.str).join("")
                        );
                }
                // imgPlaceholders?.map((item) => {
                //     comImgvariable[item] = true;
                // });
                comImgvariable[placeholder] = imgPlaceholders;
                comVariable[placeholder] = placeHolderValue;
            });
        }

        let pageContent = {
            ...(content?.[page.name]?.[component.name] || {}),
            ...(allVariables?.[pageName]?.[component.name] || {}),
        };
        // console.log(pageContent, "pageContent", component, allVariables, content);
        const response = findAndReplaceVariable({
            str: component?.html_withValue,
            content: { ...(templateObj?.GVariable || {}), ...pageContent, slug_key: slug?.slug, slug: slug?.label, ...extraKeys },
            mediaFiles,
            comVariable,
            isExport,
        });
        if (response) {
            component.html_withValue = response.str;
            if (response?.extraVariable?.length) {
                // extraVariable?.concat(response.extraVariable);
                component.extraPlaceholders = response?.extraVariable;
            }
        }
        if (Object.keys(comVariable).length) {
            pageVariable[component.name] = comVariable;
        }
        if (Object.keys(comImgvariable).length) {
            pageImgVariable[component.name] = comImgvariable;
        }
        // pageVariable[component.name] = comVariable;
        return component;
    });
    const extraDetail = {}
    page.full_page_content = generateGlobalPreviewHTML({
        data: page?.componentData,
        // components: page?.components,
        pageData: { ...page, name: pageName },
        isHtml: true,
        extraDetail,
        templateObj,
        dynamicContentForm,
        GVariable: templateObj?.GVariable,
        // mediaFiles: templateObj?.FileList,
        mediaFiles: mediaFiles,
        bradingDetails,
        isExport,
    });

    page.variable = pageVariable;
    page.imgVariable = pageImgVariable;
    return page;
};


// ========================================== Import Doc logic =================================================
function isTestimonialsHeader(s) {
    const t = (s || "").trim().toLowerCase();
    return t === "testimonials" || t === "testimonial";
}
export function parseContent(html) {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, "text/html");
    // console.log(doc, "doc");

    const result = {};
    let currentPage = null;
    let sectionCounter = 0;
    let currentSectionKey = null;
    // Mode per-section: "normal" | "faq" | "testimonials"
    let currentMode = null;

    // For FAQs: track the last Q being answered
    let lastFaqItem = null;

    // For Testimonials: hold a pending item until we see the author/rating line
    let pendingTestimonial = null;

    // NEW: capture loose <li> when they appear outside UL/OL (rare), wrap later
    let pendingLooseLis = [];

    // NEW FLAGS: to implement H1-after-H1-as-section behavior
    let lastTagWasH1 = false;
    let hadContentSinceLastH1 = false;

    // Include lists so content between h2s is complete HTML
    const nodes = Array.from(doc.body.querySelectorAll("h1, h2, h3, p, ul, ol, li"));

    for (const el of nodes) {
        const tag = el.tagName.toLowerCase();
        const text = cleanText(el.textContent);
        if (!text) continue;

        // ---------------- H1 HANDLING (modified) ----------------
        if (tag === "h1") {
            // finalize any pending testimonial / loose li on section/page boundary
            flushLooseLisIntoCurrentSection();
            flushPendingTestimonial();

            const pageName = cleanPageName(text);
            const testimonialsH1 = isTestimonialsHeader(text);

            if (!currentPage) {
                // First H1 in the document → start a new page
                currentPage = pageName;
                result[currentPage] = {};
                sectionCounter = 0;
                currentSectionKey = null;
                currentMode = null;
                lastFaqItem = null;
                pendingLooseLis = [];

                // NEW: if this first H1 is "Testimonials", immediately create sec1 as testimonials array
                if (testimonialsH1) {
                    sectionCounter += 1;
                    currentSectionKey = `sec${sectionCounter}`;
                    currentMode = "testimonials";
                    result[currentPage][currentSectionKey] = []; // array of testimonials
                    pendingTestimonial = null;
                }

                lastTagWasH1 = true;
                hadContentSinceLastH1 = false;
                continue;
            }

            // We already have a page:
            if (lastTagWasH1 && !hadContentSinceLastH1) {
                // Case: H1 immediately after H1 (no content between) → treat as a new section on current page
                sectionCounter += 1;
                currentSectionKey = `sec${sectionCounter}`;
                if (testimonialsH1) {
                    // NEW: consecutive H1 == "Testimonials" → open testimonials section
                    currentMode = "testimonials";
                    result[currentPage][currentSectionKey] = []; // array of testimonials
                    pendingTestimonial = null;
                } else {
                    // Existing behavior for H1→H1: treat as a normal section (stored like an H2)
                    currentMode = "normal";
                    result[currentPage][currentSectionKey] = { h2: el.innerText || text };
                }
                pendingLooseLis = [];

                lastTagWasH1 = true;
                hadContentSinceLastH1 = false;
                continue;
            }

            // Case: H1 after some content (e.g., h1, p, h1 OR h1, h2, h1 etc.) → start a NEW page
            currentPage = pageName;
            result[currentPage] = {};
            sectionCounter = 0;
            currentSectionKey = null;
            currentMode = null;
            lastFaqItem = null;
            pendingLooseLis = [];

            // NEW: if this H1 is "Testimonials", create sec1 as testimonials array immediately
            if (testimonialsH1) {
                sectionCounter += 1;
                currentSectionKey = `sec${sectionCounter}`;
                currentMode = "testimonials";
                result[currentPage][currentSectionKey] = []; // array of testimonials
                pendingTestimonial = null;
            }

            lastTagWasH1 = true;
            hadContentSinceLastH1 = false;
            continue;
        }

        // No <h1> yet → ignore everything
        if (!currentPage) continue;

        // ---------------- H2 HANDLING ----------------
        if (tag === "h2") {
            // finalize any pending testimonial or loose lis when switching sections
            flushLooseLisIntoCurrentSection();
            flushPendingTestimonial(true);

            // new section
            sectionCounter += 1;
            currentSectionKey = `sec${sectionCounter}`;
            const h2text = text.toLowerCase();

            if (h2text === "faqs" || h2text === "faq") {
                currentMode = "faq";
                result[currentPage][currentSectionKey] = []; // array of {h3, p}
                lastFaqItem = null;
                pendingLooseLis = [];
            } else if (h2text === "testimonials" || h2text === "testimonial") {
                currentMode = h2text;
                result[currentPage][currentSectionKey] = []; // array of testimonials
                pendingTestimonial = null;
                pendingLooseLis = [];
            } else {
                currentMode = "normal";
                result[currentPage][currentSectionKey] = { h2: el.innerText || text };
                // p will be built as a concatenated HTML string of everything until next H2
                pendingLooseLis = [];
            }

            // Any H2 counts as content between H1s
            lastTagWasH1 = false;
            hadContentSinceLastH1 = true;
            continue;
        }

        // ---------------- H3 HANDLING ----------------
        if (tag === "h3") {
            if (currentMode === "faq" && currentSectionKey) {
                // Start a new Q
                lastFaqItem = { h3: stripInline(text) };
                result[currentPage][currentSectionKey].push(lastFaqItem);

                lastTagWasH1 = false;
                hadContentSinceLastH1 = true;
                continue;
            }
            // console.log(currentMode, "currentMode");
            // Fixed condition: only check testimonials mode (typo removed)
            if ((currentMode === "testimonials" || currentMode == "testimonial") && currentSectionKey) {
                // If you want H3 to indicate a new testimonial header like "Name (4)"
                flushPendingTestimonial();
                // console.log(text, "sjkdhfkdhfkd")
                const meta = parseTestimonialMeta(text); // supports "Name (4)" and older lines
                pendingTestimonial = {
                    author: meta?.author ?? null,
                    rating: isFiniteNumber(meta?.rating) ? meta.rating : null,
                    text: ""
                };

                lastTagWasH1 = false;
                hadContentSinceLastH1 = true;
                continue;
            }
            // h3 in other modes is ignored
            lastTagWasH1 = false;
            hadContentSinceLastH1 = true;
            continue;
        }

        // ---------------- CONTENT HANDLING ----------------

        // NORMAL MODE: build sec.p as HTML string (append outerHTML for p/ul/ol and wrapped <li>)
        if (currentMode === "normal" && currentSectionKey) {
            const sec = result[currentPage][currentSectionKey];

            if (tag === "p" || tag === "ul" || tag === "ol") {
                // before appending block tags, close any stray <li>s
                flushLooseLisIntoCurrentSection();
                if (tag === "p") {
                    // ensure break between multiple <p> in same section
                    if (sec.p && sec.p.trim().length > 0) {
                        sec.p += "<br/>";
                    }
                }
                sec.p = (sec.p || "") + el.outerHTML;

                lastTagWasH1 = false;
                hadContentSinceLastH1 = true;
                continue;
            }

            if (tag === "li") {
                // If this <li> is inside UL/OL, skip (the parent list will be appended via its outerHTML)
                const parentTag = el.parentElement?.tagName;
                const parentIsList = parentTag === "UL" || parentTag === "OL";
                if (parentIsList) {
                    lastTagWasH1 = false;
                    hadContentSinceLastH1 = true;
                    continue;
                }

                // Otherwise, capture loose <li> to wrap into a <ul> later
                pendingLooseLis.push(el.outerHTML);

                lastTagWasH1 = false;
                hadContentSinceLastH1 = true;
                continue;
            }
        }

        // FAQ MODE: keep text-based behavior, also allow list items as bullets
        if (currentMode === "faq" && currentSectionKey) {
            if (tag === "p") {
                if (!lastFaqItem) {
                    // If an answer comes before a question, start a blank question (rare)
                    lastFaqItem = { h3: "", p: stripInline(el.innerText || text) };
                    result[currentPage][currentSectionKey].push(lastFaqItem);
                } else {
                    const ans = stripInline(el.innerText || text);
                    lastFaqItem.p = lastFaqItem.p ? `${lastFaqItem.p} ${ans}` : ans;
                }

                lastTagWasH1 = false;
                hadContentSinceLastH1 = true;
                continue;
            }
            if (tag === "li") {
                const liText = stripInline(el.innerText || el.textContent || "");
                if (liText) {
                    const bullet = `• ${liText}`;
                    if (!lastFaqItem) {
                        lastFaqItem = { h3: "", p: bullet };
                        result[currentPage][currentSectionKey].push(lastFaqItem);
                    } else {
                        lastFaqItem.p = lastFaqItem.p ? `${lastFaqItem.p}\n${bullet}` : bullet;
                    }
                }

                lastTagWasH1 = false;
                hadContentSinceLastH1 = true;
                continue;
            }
        }

        // TESTIMONIALS MODE: collect text in p; optional H3 handled above
        if ((currentMode === "testimonials" || currentMode === "testimonial") && currentSectionKey) {
            if (tag === "p") {
                const val = stripInline(el.innerText || text);
                if (val) {
                    if (!pendingTestimonial) {
                        // Rare: description before header → still collect
                        pendingTestimonial = { author: null, rating: null, text: val };
                    } else {
                        pendingTestimonial.text = pendingTestimonial.text
                            ? `${pendingTestimonial.text} ${val}`
                            : val;
                    }
                }

                lastTagWasH1 = false;
                hadContentSinceLastH1 = true;
                continue;
            }
        }
    }

    // End of doc: flush any unfinished testimonial and pending loose <li>
    flushLooseLisIntoCurrentSection();
    flushPendingTestimonial(true);

    return result;

    // ---- helpers bound to closure ----
    function flushLooseLisIntoCurrentSection() {
        if (
            currentMode === "normal" &&
            currentSectionKey &&
            pendingLooseLis.length > 0 &&
            result[currentPage] &&
            result[currentPage][currentSectionKey]
        ) {
            const sec = result[currentPage][currentSectionKey];
            sec.p = (sec.p || "") + `<ul>${pendingLooseLis.join("")}</ul>`;
            pendingLooseLis = [];
        }
    }

    function flushPendingTestimonial(force = false) {
        if (
            pendingTestimonial &&
            currentSectionKey &&
            result[currentPage] &&
            result[currentPage][currentSectionKey]
        ) {
            // If we’re switching sections or ending, persist whatever we have
            if (force || currentMode === "testimonials" || currentMode === "testimonial") {
                const trimmed = cleanText(pendingTestimonial.text || "");
                if (trimmed) {
                    result[currentPage][currentSectionKey].push({
                        author: pendingTestimonial.author,
                        rating: isFiniteNumber(pendingTestimonial.rating)
                            ? pendingTestimonial.rating
                            : null,
                        text: trimmed
                    });
                }
            }
            pendingTestimonial = null;
        }
        // Reset FAQ carryover when leaving a section/page
        lastFaqItem = null;
    }
}


// helpers
function cleanText(s) {
    return (s || "").replace(/\s+/g, " ").trim();
}

function cleanPageName(text) {
    // return text.replace(/[^\w\s]/g, "").trim()?.toLowerCase();
    return text?.trim()
    // ?.toLowerCase();

}

// Strip inline markup influence (we prefer clean text for JSON)
function stripInline(s) {
    return cleanText(s);
}

/**
 * Parse testimonial header lines.
 * Supports:
 *   1) "Jessica P. (4)"                → {author: "Jessica P.", rating: 4}
 *   2) "- Jessica P. - ★★★★☆ (4/5)"    → legacy fallback
 *   3) "Jessica P." (no rating)        → {author: "Jessica P.", rating: null}
 */
function parseTestimonialMeta(line) {
    const raw = cleanText(line);

    // NEW primary pattern: "Name (4)" or "Name (4.5)"
    const mNew = raw.match(/^(.+?)\s*\(\s*(\d+(?:\.\d+)?)\s*\)\s*$/);
    if (mNew) {
        const author = cleanText(mNew[1]);
        const rating = parseFloat(mNew[2]);
        return { author: author || null, rating: isFiniteNumber(rating) ? rating : null };
    }

    // Legacy: rating in parentheses e.g. (4/5) or (4.5/5)
    const ratingParen = raw.match(/(\d+(?:\.\d+)?)\s*\/\s*5/);
    const legacyRating = ratingParen ? parseFloat(ratingParen[1]) : null;

    // Legacy: author between hyphens: "- Name - ..."
    let author = null;
    const authorHyphen = raw.match(/-\s*([^–—-][^-–—]+?)\s*-\s*/);
    if (authorHyphen && authorHyphen[1]) {
        author = authorHyphen[1].trim();
    } else {
        // fallback: before the first '(' or star symbol
        const cut = raw.split(/\(|★/)[0].replace(/^[-–—\s]+/, "").trim();
        author = cut || null;
    }

    return {
        author,
        rating: isFiniteNumber(legacyRating) ? legacyRating : null,
    };
}

/**
 * Parse testimonial author + stars from a line like:
 * "- Jessica P. - ★★★★☆ (4/5 Stars)"
 * Returns { author, rating }
 */
// function parseTestimonialMeta(line) {
//     const raw = cleanText(line);

//     // rating in parentheses e.g. (4/5) or (4.5/5)
//     const ratingParen = raw.match(/(\d+(?:\.\d+)?)\s*\/\s*5/);
//     const rating = ratingParen ? parseFloat(ratingParen[1]) : null;

//     // author between hyphens: "- Name - ..." (robust to extra spaces/dots)
//     let author = null;
//     const authorHyphen = raw.match(/-\s*([^–—-][^-–—]+?)\s*-\s*/);
//     if (authorHyphen && authorHyphen[1]) {
//         author = authorHyphen[1].trim();
//     } else {
//         // fallback: before the first '(' or star symbol chunk
//         const cut = raw.split(/\(|★/)[0].replace(/^[-–—\s]+/, "").trim();
//         author = cut || null;
//     }

//     return {
//         author,
//         rating: isFiniteNumber(rating) ? rating : null,
//     };
// }

function isFiniteNumber(n) {
    return typeof n === "number" && Number.isFinite(n);
}

export const replaceArray = (arr1, arrKeys, arr2, arr2Keys) => {
    return arr1?.map((item, index) => {
        const newItem = { ...item };
        arrKeys?.forEach((key, i) => {
            newItem[key] = arr2?.[index]?.[arr2Keys?.[i]] || item?.[key];
        });
        return newItem;
    });
};


// export function parseContent(html) {
//     const parser = new DOMParser();
//     const doc = parser.parseFromString(html, "text/html");
//     console.log(doc, JSON.parse(JSON.stringify(html)), "htmldddddd", html);
//  * - sections as arrays of objects with h2, p, and h3 (for FAQs) properties
//  * - some sections are marked as "testimonials" or "faqs" and contain a
//  *   special structure
//  * - the JSON is "clean" in that it does not contain any HTML tags or
//  *   whitespace not intended for display
//  *
//  * This function is very specific to the structure of the HTML pages in
//  * this project. It is not a general-purpose HTML parser.
//  *
//  * @param {string} html - the HTML content as a string
//  * @returns {Object} the parsed JSON
//  */
//     const result = {};
//     let currentPage = null;
//     let sectionCounter = 0;
//     // console.log(doc, JSON.parse(JSON.stringify(html)), "htmldddddd", html);
//     let currentSectionKey = null;
//     // Mode per-section: "normal" | "faq" | "testimonials"
//     let currentMode = null;

//     // For FAQs: track the last Q being answered
//     let lastFaqItem = null;

//     // For Testimonials: hold a pending item until we see the author/rating line
//     let pendingTestimonial = null;
//     // const matches = [...html.matchAll(/<(h1|h2|p)>(.*?)<\/\1>/gi)];

//     // const arr = matches.map(m => ({
//     //     type: m[1],
//     //     content: m[2].trim()
//     // }));

//     // // Use JSON.parse(JSON.stringify(...)) to make it plain JSON
//     // const results = JSON.parse(JSON.stringify(arr));


//     // Walk in order: only h1, h2, p
//     const nodes = Array.from(doc.body.querySelectorAll("h1, h2, h3, p"));
//     console.log(nodes);

//     for (const el of nodes) {
//         const tag = el.tagName.toLowerCase();
//     // console.log(nodes);
//         const text = cleanText(el.textContent);
//         if (!text) continue;

//         if (tag === "h1") {
//             // finalize any pending testimonial on section end
//             flushPendingTestimonial();

//             currentPage = cleanPageName(text);
//             result[currentPage] = {};
//             sectionCounter = 0;
//             currentSectionKey = null;
//             currentMode = null;
//             lastFaqItem = null;

//             continue;
//         }

//         if (!currentPage) {
//             // No <h1> yet → ignore
//             continue;
//         }

//         if (tag === "h2") {
//             // finalize any pending testimonial when switching sections
//             flushPendingTestimonial();
//             // new section
//             sectionCounter += 1;
//             currentSectionKey = `sec${sectionCounter}`;
//             const h2text = text.toLowerCase();

//             if (h2text === "faqs" || h2text === "faq") {
//                 currentMode = "faq";
//                 result[currentPage][currentSectionKey] = []; // array of {h3, p}
//                 lastFaqItem = null;
//             } else if (h2text === "testimonials" || h2text === "testimonial") {
//                 currentMode = "testimonials";
//                 result[currentPage][currentSectionKey] = []; // array of testimonials
//                 pendingTestimonial = null;
//             }
//             else {
//                 currentMode = "normal";
//                 result[currentPage][currentSectionKey] = { h2: text };
//             }
//             continue;
//             // result[currentPage][currentSectionKey] = { h2: text };
//             // continue;
//         }

//         if (tag === "h3") {
//             if (currentMode === "faq" && currentSectionKey) {
//                 // Start a new Q
//                 lastFaqItem = { h3: stripInline(text) };
//                 result[currentPage][currentSectionKey].push(lastFaqItem);
//             }
//             // h3 in other modes is ignored
//             continue;
//         }

//         if (tag === "p") {
//             if (!currentSectionKey) {
//                 // Paragraph before any h2 in this page → skip
//                 continue;
//             }
//             if (currentMode === "normal") {
//                 const sec = result[currentPage][currentSectionKey];
//                 sec.p = sec.p ? `${sec.p} ${stripInline(el.innerText || text)}` : stripInline(el.innerText || text);
//             } else if (currentMode === "faq") {
//                 if (!lastFaqItem) {
//                     // If an answer comes before a question, start a blank question (rare)
//                     lastFaqItem = { h3: "", p: stripInline(el.innerText || text) };
//                     result[currentPage][currentSectionKey].push(lastFaqItem);
//                 } else {
//                     // Append/assign answer
//                     const ans = stripInline(el.innerText || text);
//                     lastFaqItem.p = lastFaqItem.p ? `${lastFaqItem.p} ${ans}` : ans;
//                 }
//             }
//             else if (currentMode === "testimonials") {
//                 const val = el.innerText || text;
//                 // Pattern: description <p> then author/rating <p>
//                 if (!pendingTestimonial) {
//                     pendingTestimonial = { text: stripInline(val) };
//                 } else {
//                     // This should be the author/rating line
//                     const meta = parseTestimonialMeta(val);
//                     const item = {
//                         text: pendingTestimonial.text,
//                         author: meta.author,
//                         rating: meta.rating,
//                         // ratingMax: meta.ratingMax,
//                         // starsDisplay: meta.starsDisplay,
//                         // rawMeta: meta.rawMeta
//                     };
//                     result[currentPage][currentSectionKey].push(item);
//                     pendingTestimonial = null;
//                 }
//             }

//             // const sec = result[currentPage][currentSectionKey];
//             // sec.p = sec.p ? `${sec.p} ${text}` : text;
//         }
//     }

//     // End of doc: flush any unfinished testimonial
//     flushPendingTestimonial();

//     return result;

//     // ---- helpers bound to closure for flushing with current section ----
//     function flushPendingTestimonial() {
//         if (currentMode === "testimonials" && pendingTestimonial && currentSectionKey && result[currentPage][currentSectionKey]) {
//             // If author line never arrived, still push with text only
//             result[currentPage][currentSectionKey].push({
//                 text: pendingTestimonial.text,
//                 author: null,
//                 rating: null,
//                 ratingMax: 5,
//                 starsDisplay: null,
//                 rawMeta: null
//             });
//             pendingTestimonial = null;
//         }
//         // reset FAQ carryover when leaving a section/page
//         lastFaqItem = null;
//         lastWeOfferItem = null;
//     }
// }

// // helpers
// function cleanText(s) {
//     return (s || "").replace(/\s+/g, " ").trim();
// }

// function cleanPageName(text) {
//     return text.replace(/[^\w\s]/g, "").trim();
// }

// // Strip inline markup influence (we prefer clean text for JSON)
// function stripInline(s) {
//     return cleanText(s);
// }

// /**
//  * Parse testimonial author + stars from a line like:
//  * "- Jessica P. - ★★★★☆ (4/5 Stars)"
//  * Returns { author, rating, ratingMax: 5, starsDisplay, rawMeta }
//  */
// function parseTestimonialMeta(line) {
//     const raw = cleanText(line);

//     // stars by unicode ★ count
//     // const starsDisplayMatch = raw.match(/★+☆*/g);
//     // const starsDisplay = starsDisplayMatch ? starsDisplayMatch[0] : null;
//     // const starCountByGlyph = starsDisplay ? (starsDisplay.match(/★/g) || []).length : null;

//     // rating in parentheses e.g. (4/5) or (4.5/5)
//     const ratingParen = raw.match(/(\d+(?:\.\d+)?)\s*\/\s*5/);
//     const rating = ratingParen ? parseFloat(ratingParen[1]) : null;

//     // author between hyphens: "- Name - ..." (robust to extra spaces/dots)
//     let author = null;
//     const authorHyphen = raw.match(/-\s*([^–—-][^-–—]+?)\s*-\s*/);
//     if (authorHyphen && authorHyphen[1]) {
//         author = authorHyphen[1].trim();
//     } else {
//         // fallback: before the first '(' or star symbol chunk
//         const cut = raw.split(/\(|★/)[0].replace(/^[-–—\s]+/, "").trim();
//         author = cut || null;
//     }

//     return {
//         author,
//         rating: isFiniteNumber(rating) ? rating : null,
//         // ratingMax: 5,
//         // starsDisplay,
//         // rawMeta: raw
//     };
// }

// function isFiniteNumber(n) {
//     return typeof n === "number" && Number.isFinite(n);
// }

// export const replaceArray = (arr1, arrKeys, arr2, arr2Keys) => {

//     return arr1.map((item, index) => {
//         const newItem = { ...item };
//         arrKeys.forEach((key, i) => {
//             newItem[key] = arr2[index]?.[arr2Keys[i]] || item[key];
//         });
//         return newItem;
//     });
// }


// utils/deepBlank.js
export const deepBlank = (input) => {
    if (input === null || input === undefined) return "";

    if (typeof input === "string") return "";
    if (typeof input === "number") return "";
    if (typeof input === "boolean") return false;

    if (Array.isArray(input)) {
        // preserve array length but empty values inside
        return input.map((item) => deepBlank(item));
    }

    if (typeof input === "object") {
        const out = {};
        Object.keys(input).forEach((key) => {
            out[key] = deepBlank(input[key]);
        });
        return out;
    }

    return ""; // fallback for other types
};

export function isEmptyValue(v) {
    return v === "" || v === null || v === undefined;
}

export function filterEmptyFields(input) {
    if (Array.isArray(input)) {
        const next = input
            .map(filterEmptyFields)
            .filter(v => {
                if (v == null) return false;
                if (v === undefined) return false;
                if (v && Array.isArray(v)) return v.length > 0;
                if (v && typeof v === "object") return Object.keys(v).length > 0;
                return true;
            });
        return next.length > 0 ? next : undefined;
    }

    if (input && typeof input === "object") {
        const out = {};
        for (const [key, val] of Object.entries(input)) {
            if (key?.includes("_alt-")) continue;
            if (isEmptyValue(val)) {
                out[key] = val;
                continue;
            }
            const child = filterEmptyFields(val);
            if (child !== undefined) {
                if (Array.isArray(child)) {
                    if (child.length > 0) out[key] = child;
                } else if (child && typeof child === "object") {
                    if (Object.keys(child).length > 0) out[key] = child;
                } else {
                    out[key] = child;
                }
            }
        }
        return Object.keys(out).length > 0 ? out : undefined;
    }

    return isEmptyValue(input) ? input : undefined;
}

// JSON.stringify drops undefined — this preserves it visually
export function stringifyPreservingUndefined(obj) {
    const replacer = (_, v) => (v === undefined ? "__undefined__" : v);
    return JSON.stringify(obj, replacer, 2);
}

export const getCompanySlug = (name) => name?.replace(/ /g, "-")?.toLowerCase()
export const createEmptyObj = (data, keys) => {

    if (keys?.length == 0) return data;
    const key = keys?.[0];
    if (!(key in data)) data[key] = {};
    return createEmptyObj(data?.[key], keys?.slice(1));

}

export const getOrCreatePath = (data, keys) => {
    if (!data || typeof data !== "object" || !Array.isArray(keys) || keys.length === 0) {
        return data;
    }

    const [key, ...rest] = keys;
    const isLast = rest.length === 0;

    if (!(key in data)) {
        data[key] = isLast ? "" : {};
    }

    if (isLast) {
        return data[key];
    }

    return getOrCreatePath(data[key], rest);
};


export const formatImageName = (name) => {
    return name
        ?.trim()
        ?.replace(/\./g, "")       // remove dot
        ?.replace(/\s+/, "-")      // replace first space with -
        ?.toLowerCase();           // optional: make it lowercase
}

// remove all spaces from string and object===========================================
export const removeSpacesDeep = (input) => {
    if (typeof input === "string") {
        // remove ALL spaces
        return input.trim();
        // OR only trim edges: return input.trim();
    }

    if (Array?.isArray(input)) {
        return input?.map(item => removeSpacesDeep(item));
    }

    if (input && typeof input === "object") {
        return Object?.fromEntries(
            Object?.entries(input)?.map(([key, value]) => [key, removeSpacesDeep(value)])
        );
    }

    // if it's not string/object/array, return as is (number, boolean, null, etc.)
    return input;
}

//============================content tab search functinlity========================

export const searchKeysDeep = (obj, searchTerm) => {
    if (typeof obj !== "object" || obj === null) return null;

    let result = Array.isArray(obj) ? [] : {};

    for (let key in obj) {
        if (key.toLowerCase().includes(searchTerm.toLowerCase())) {
            result[key] = obj[key];
        }

        if (typeof obj[key] === "object") {
            let nested = searchKeysDeep(obj[key], searchTerm);
            if (nested && Object.keys(nested).length > 0) {
                result[key] = nested;
            }
        }
    }

    return result;
}

export const imageObj = (fileList) => {
    const fileObj = {};
    if (Array.isArray(fileList)) {
        fileList?.map((file, index) => {
            const originalName =
                (file?.originalName || file?.name || `Media ${index + 1}`)?.trim();
            fileObj[file?.name?.includes("_img-") ? file?.name : `_img-${file?.name}`] = {
                ...file,
                url: file?.blobUrl || file?.url,
                name: file?.name || file?.originalName,
                originalName: originalName,
                path: "/assets/" + file?.originalName,
                type: file?.type || "existing"
            };
        });
    } else {
        Object.keys(fileList)?.map((key, index) => {
            const originalName =
                (fileList[key]?.originalName || fileList[key]?.name || `Media ${index + 1}`)?.trim();
            fileObj[key?.includes("_img-") ? key : `_img-${key}`] = {
                ...fileList[key],
                url: fileList[key]?.blobUrl || fileList[key]?.url,
                name: fileList[key]?.name || fileList[key]?.originalName,
                originalName: originalName,
                path: "/assets/" + fileList[key]?.originalName,
                type: fileList[key]?.type
            };
        });
    }

    return fileObj;
}