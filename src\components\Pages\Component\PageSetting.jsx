import {
  Modal,
  Form,
  Input,
  But<PERSON>,
  Row,
  Col,
  Typography,
  Space,
  Tooltip,
} from "antd";
import { X, Settings, Info } from "lucide-react";
import React, { useEffect } from "react";
import FormFields from "../../common/FormFields";
import { CONSTANTS } from "../../../util/constant/CONSTANTS";
import { removeSpacesDeep } from "../../../util/functions";

const { Title, Text } = Typography;
const { TextArea } = Input;

const PageSetting = ({
  showSettings,
  setShowSettings,
  pageData,
  setPageData,
}) => {
  const [form] = Form.useForm();

  // Initialize form with pageData when modal opens
  useEffect(() => {
    if (showSettings && pageData) {
      form.setFieldsValue({
        name: pageData?.name || "",
        urlSlug: pageData?.urlSlug || "",
        metaTitle: pageData?.metaTitle || "",
        metaDescription: pageData?.metaDescription || "",
        siteMapLabel: pageData?.siteMapLabel || "",
        customCss: pageData?.customCss || "",
        customJs: pageData?.customJs || "",
        headers: pageData?.headers || "",
        wrapperClass: pageData?.wrapperClass || "",
      });
    }
  }, [showSettings, pageData, form]);

  // Handle form submission
  const handleFormSubmit = (values) => {
    console.log("Form values:", values);
    const updatedData = removeSpacesDeep(values);
    setPageData((pr) => ({
      ...pr,
      ...updatedData,
    }));
    setShowSettings(false);
  };

  // Handle form value changes
  // const handleValuesChange = (changedValues, allValues) => {
  //   setPageData({
  //     ...pageData,
  //     ...allValues,
  //   });
  // };

  return (
    <Modal
      title={
        <Space className="tw-items-center">
          {/* <Settings className="tw-w-5 tw-h-5 tw-text-blue-600" /> */}
          <Title level={4} className="!tw-mb-0 tw-text-gray-800">
            Page Settings
          </Title>
        </Space>
      }
      open={showSettings}
      onCancel={() => setShowSettings(false)}
      width={800}
      footer={
        <div className="tw-flex tw-justify-end tw-space-x-3 tw-pt-4 tw-border-t tw-border-gray-200">
          <Button
            type="default"
            size="large"
            onClick={() => setShowSettings(false)}
            className="tw-px-6 tw-h-10 tw-rounded-lg tw-font-medium tw-border-gray-300 hover:tw-border-gray-400"
          >
            Cancel
          </Button>
          <Button
            type="primary"
            size="large"
            onClick={() => form.submit()}
            className="tw-px-6 tw-h-10 tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
          >
            Save
          </Button>
        </div>
      }
      className="tw-top-4"
      styles={{
        body: { padding: "6px" },
        header: { borderBottom: "1px solid #f0f0f0", paddingBottom: "8px" },
      }}
      closeIcon={
        <Button
          type="text"
          icon={<X className="tw-w-4 tw-h-4" />}
          className="tw-text-gray-400 hover:tw-text-gray-600 tw-border-0"
        />
      }
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleFormSubmit}
        // onValuesChange={handleValuesChange}
        className="tw-space-y-6"
        size="large"
      >
        <Row gutter={[16, 0]}>
          {CONSTANTS?.FORM?.pageSetting?.map((field) => (
            <Col key={field.name} {...(field.colProps || { xs: 24 })}>
              <FormFields field={field} />
            </Col>
          ))}
        </Row>
      </Form>
    </Modal>
  );
};

export default PageSetting;
