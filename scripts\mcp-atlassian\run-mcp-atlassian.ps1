param(
  [string]$EnvPath = (Join-<PERSON> $PSScriptRoot ".env"),
  [int]$Port = 9000
)

Write-Host "[MCP] Using env file: $EnvPath"
if (!(Test-Path $EnvPath)) {
  Write-Error "Env file not found: $EnvPath"; exit 1
}

Write-Host "[MCP] Starting Atlassian MCP server on http://localhost:$Port/sse ..."
# This runs attached; stop with Ctrl+C
& docker run --rm -p "$Port:$Port" --env-file "$EnvPath" ghcr.io/sooperset/mcp-atlassian:latest --transport sse --port $Port -vv

# If you prefer streamable-http transport, replace the last line with:
#   --transport streamable-http --port $Port -vv

