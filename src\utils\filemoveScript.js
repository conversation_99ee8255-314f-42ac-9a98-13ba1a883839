import fs from 'fs';
import path from 'path';

const srcFolder = '../../public/assets copy'; // path to the source folder
const destFolder = '../../public/assets'; // path to the destination folder

// Function to create a mapping of file names (without extension) to full file names
function createFileMapping() {
    const fileMapping = {};

    // Read the files in the src folder
    const files = fs.readdirSync(srcFolder);

    files.forEach(file => {
        // Extract the file name without extension
        const fileNameWithoutExt = path.parse(file).name;

        // Add the file name and full file path to the mapping object
        fileMapping[fileNameWithoutExt] = file;
    });

    return fileMapping;
}

// Function to move the file from src to dest
function moveFile(fileName) {
    const fileMapping = createFileMapping();

    // Check if the file exists in the file mapping object
    if (fileMapping[fileName]) {
        const srcFilePath = path.join(srcFolder, fileMapping[fileName]);
        const destFilePath = path.join(destFolder, fileMapping[fileName]);

        // Ensure the destination folder exists
        if (!fs.existsSync(destFolder)) {
            fs.mkdirSync(destFolder, { recursive: true });
        }

        // Move the file to the destination folder
        fs.renameSync(srcFilePath, destFilePath);
        console.log(`Moved: ${fileMapping[fileName]}`);
    } else {
        console.log(`File not found: ${fileName}`);
    }
}
const aminities = [
    "clifton-bus-rental",
    "clifton-corporate-bus-rental",
    "clifton-private-event-transportation",
    "clifton-school-event-transportation",
    "clifton-sporting-event-transportation",
    "clifton-wedding-transportation",
    "clifton-airport-shuttles",
    "clifton-bus-rentals-for-travel-agents",
    "clifton-vacation-bus-rentals-for-family-trips",
    "clifton-summer-camp-transportation-and-bus-rentals",
    "clifton-religious-charter-bus-minibus-rentals",
    "clifton-hospital-and-healthcare-shuttles",
    "clifton-emergency-transportation-service",
    "clifton-government-and-military-bus-rentals",
    "clifton-construction-site-shuttle-services",
    "clifton-wine-tour-and-pub-crawl-bus-rentals",
    "clifton-prom-and-homecoming-party-bus-rentals"
]
// Array of file names (without extensions) that need to be moved
const fileNamesToMove = aminities.map(item => item);

// Iterate over each file name in the array and move it if it exists
fileNamesToMove.forEach(fileName => {
    moveFile(fileName);
});
