import { Globe, Component as Components, FileText, Layout, } from "lucide-react";

export const serviceDKey = "services-d";
export const serviceAreaDKey = "service-area-d";
export const busesDKey = "buses-d";

export const predefinedColors = [
    "#3B82F6",
    "#8B5CF6",
    "#10B981",
    "#F59E0B",
    "#EF4444",
    "#06B6D4",
    "#84CC16",
    "#F97316",
];

// Dashboard content ===============================================
export const statCards = [
    {
        name: "Total Websites",
        key: "websites",
        icon: Globe,
        color: "tw-from-blue-500 tw-to-blue-600",
        bgColor: "tw-bg-blue-50",
        iconColor: "tw-text-blue-600",
    },
    {
        name: "Components",
        icon: Components,
        key: "components",
        color: "tw-from-purple-500 tw-to-purple-600",
        bgColor: "tw-bg-purple-50",
        iconColor: "tw-text-purple-600",
    },
    {
        name: "Pages Created",
        icon: FileText,
        key: "pages",
        color: "tw-from-green-500 tw-to-green-600",
        bgColor: "tw-bg-green-50",
        iconColor: "tw-text-green-600",
    },
    {
        name: "Templates",
        icon: Layout,
        key: "templates",
        color: "tw-from-orange-500 tw-to-orange-600",
        bgColor: "tw-bg-orange-50",
        iconColor: "tw-text-orange-600",
    },
];

export const quickActions = [
    {
        name: "Create New Website",
        description: "Start building a new website from template",
        icon: Globe,
        color: "tw-from-blue-500 tw-to-blue-600",
        href: "/websites",
    },
    {
        name: "Build Component",
        description: "Create reusable components",
        icon: Components,
        color: "tw-from-purple-500 tw-to-purple-600",
        href: "/components",
    },
    {
        name: "Design Page",
        description: "Build pages with drag & drop",
        icon: FileText,
        color: "tw-from-green-500 tw-to-green-600",
        href: "/pages",
    },
    {
        name: "Manage Templates",
        description: "Organize page collections",
        icon: Layout,
        color: "tw-from-orange-500 tw-to-orange-600",
        href: "/templates",
    },
];

export const recentActivity = [
    {
        id: 1,
        label: "Create Categories",
        description: "Organize your components into categories",
        color: "blue",
        className: "tw-text-blue-600",

    },
    {
        id: 2,
        label: "Build Components",
        description: "Create reusable HTML components with placeholders",
        color: "purple",
        className: "tw-text-purple-600",
    },
    {
        id: 3,
        label: "Design Pages",
        description: "Drag and drop components to build pages",
        color: "green",
        className: "tw-text-green-600",
    },
    {
        id: 4,
        label: "Create Templates",
        description: "Group pages into reusable templates",
        color: "orange",
        className: "tw-text-orange-600",
    },
    {
        id: 5,
        label: "Generate Websites",
        description: "Create and export complete websites",
        color: "red",
        className: "tw-text-red-600",
    },
];

export const DND_TYPES = {
    LIB_ITEM: "LIB_ITEM",
    STRUCT_ITEM: "STRUCT_ITEM",
};

export const templateData = [
    {
        "name": "testT",
        "full_template_content": "\n      <div class=\"template-container\">\n        <div class=\"template-pages\">\n          \n        <div class=\"template-page\" data-page-id=\"meml1gk01ps1ylfq51x\" data-position=\"0\">\n          <div class=\"page-content\">\n            <!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>test</title>\n  <!-- Tailwind CSS - Generated locally to avoid CDN warnings -->\n  <style>\n      @font-face {\n        font-family: Inter;\n        font-style: normal;\n        font-weight: 400;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n\n      @font-face {\n        font-family: Inter;\n        font-style: normal;\n        font-weight: 500;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n\n      @font-face {\n        font-family: Inter;\n        font-style: normal;\n        font-weight: 700;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n\n      @font-face {\n        font-family: Phudu;\n        font-style: normal;\n        font-weight: 600;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n\n      @font-face {\n        font-family: Phudu;\n        font-style: normal;\n        font-weight: 700;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n    </style>\n\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script src=\"//unpkg.com/alpinejs\" defer></script>\n\n    <script>\n      tailwind.config = {\n        content: [\"./*.html\"],\n        prefix: \"dw-\",\n        theme: {\n          extend: {\n            colors: {\n              body: {\n                bg: \"#ffffff\", // --body-bg-color\n                text: \"#333333\", // --body-text-color\n              },\n              theme: {\n                main: \"#d5232b\", // --theme-main-color\n                mainColorShade: \"#C41421\",\n                hoverBox: \"rgba(82, 164, 206, 0.43)\", // --theme-box-hover-color\n                cardBorder: \"#e0e0e0\",\n                cardBg: \"#fafafa\",\n                divider: \"#eee\",\n                lowOpacityTextColor: \"#111827\",\n              },\n            },\n            fontFamily: {\n              heading: [\"Phudu\", \"sans-serif\"], // --theme-heading-font\n              text: [\"Inter\", \"sans-serif\"], // --theme-text-font\n            },\n            screens: {\n              xs: \"200px\",\n              smx: \"376px\",\n              smm: \"567px\",\n              mdx: \"768px\",\n              mdl: \"993px\",\n              lgx: \"1171px\",\n              lgs: \"1221px\",\n              lgm: \"1281px\",\n              lgl: \"1361px\",\n              xlx: \"1400px\",\n              xl1: \"1441px\",\n              xl2: \"1600px\",\n              xxl: \"1900px\",\n            },\n          },\n        },\n        plugins: [],\n      };\n    </script>\n  <style>\n    /* Base styles */\n    * { box-sizing: border-box; }\n    html {\n      height: 100%;\n    }\n    body {\n      margin: 0;\n      padding: 20px;\n      font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif;\n      line-height: 1.6;\n      background: white;\n      color: #374151;\n    }\n\n    /* Tailwind CSS classes with tw- prefix */\n    .tw-bg-blue-500 { background-color: rgb(59 130 246) !important; }\n    .tw-bg-blue-600 { background-color: rgb(37 99 235) !important; }\n    .tw-bg-purple-600 { background-color: rgb(147 51 234) !important; }\n    .tw-bg-white { background-color: rgb(255 255 255) !important; }\n    .tw-bg-gray-50 { background-color: rgb(249 250 251) !important; }\n    .tw-bg-gray-100 { background-color: rgb(243 244 246) !important; }\n    .tw-bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)) !important; }\n    .tw-from-blue-600 { --tw-gradient-from: rgb(37 99 235); --tw-gradient-to: rgb(37 99 235 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important; }\n    .tw-to-purple-600 { --tw-gradient-to: rgb(147 51 234) !important; }\n\n    .tw-text-white { color: rgb(255 255 255) !important; }\n    .tw-text-blue-600 { color: rgb(37 99 235) !important; }\n    .tw-text-gray-600 { color: rgb(75 85 99) !important; }\n    .tw-text-gray-800 { color: rgb(31 41 55) !important; }\n    .tw-text-gray-900 { color: rgb(17 24 39) !important; }\n\n    .tw-p-4 { padding: 1rem !important; }\n    .tw-p-6 { padding: 1.5rem !important; }\n    .tw-p-8 { padding: 2rem !important; }\n    .tw-px-4 { padding-left: 1rem; padding-right: 1rem !important; }\n    .tw-px-6 { padding-left: 1.5rem; padding-right: 1.5rem !important; }\n    .tw-px-8 { padding-left: 2rem; padding-right: 2rem !important; }\n    .tw-py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem !important; }\n    .tw-py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem !important; }\n    .tw-py-4 { padding-top: 1rem; padding-bottom: 1rem !important; }\n    .tw-py-16 { padding-top: 4rem; padding-bottom: 4rem !important; }\n    .tw-py-20 { padding-top: 5rem; padding-bottom: 5rem !important; }\n\n    .tw-m-4 { margin: 1rem !important; }\n    .tw-mx-auto { margin-left: auto; margin-right: auto !important; }\n    .tw-mt-2 { margin-top: 0.5rem !important; }\n    .tw-mt-4 { margin-top: 1rem !important; }\n    .tw-mb-4 { margin-bottom: 1rem !important; }\n    .tw-mb-6 { margin-bottom: 1.5rem !important; }\n    .tw-mb-8 { margin-bottom: 2rem !important; }\n\n    .tw-rounded { border-radius: 0.25rem !important; }\n    .tw-rounded-lg { border-radius: 0.5rem !important; }\n    .tw-rounded-xl { border-radius: 0.75rem !important; }\n\n    .tw-text-sm { font-size: 0.875rem; line-height: 1.25rem !important; }\n    .tw-text-base { font-size: 1rem; line-height: 1.5rem !important; }\n    .tw-text-lg { font-size: 1.125rem; line-height: 1.75rem !important; }\n    .tw-text-xl { font-size: 1.25rem; line-height: 1.75rem !important; }\n    .tw-text-2xl { font-size: 1.5rem; line-height: 2rem !important; }\n    .tw-text-3xl { font-size: 1.875rem; line-height: 2.25rem !important; }\n    .tw-text-4xl { font-size: 2.25rem; line-height: 2.5rem !important; }\n    .tw-text-5xl { font-size: 3rem; line-height: 1 !important; }\n\n    .tw-font-medium { font-weight: 500 !important; }\n    .tw-font-semibold { font-weight: 600 !important; }\n    .tw-font-bold { font-weight: 700 !important; }\n\n    .tw-text-center { text-align: center !important; }\n    .tw-text-left { text-align: left !important; }\n\n    .tw-flex { display: flex !important; }\n    .tw-grid { display: grid !important; }\n    .tw-block { display: block !important; }\n    .tw-inline-block { display: inline-block !important; }\n    .tw-hidden { display: none !important; }\n\n    .tw-items-center { align-items: center !important; }\n    .tw-justify-center { justify-content: center !important; }\n    .tw-justify-between { justify-content: space-between !important; }\n\n    .tw-w-full { width: 100% !important; }\n    .tw-w-auto { width: auto !important; }\n    .tw-h-8 { height: 2rem !important; }\n    .tw-h-auto { height: auto !important; }\n\n    .tw-container { width: 100%; margin-left: auto; margin-right: auto !important; }\n    @media (min-width: 640px) { .tw-container { max-width: 640px !important; } }\n    @media (min-width: 768px) { .tw-container { max-width: 768px !important; } }\n    @media (min-width: 1024px) { .tw-container { max-width: 1024px !important; } }\n    @media (min-width: 1280px) { .tw-container { max-width: 1280px !important; } }\n\n    .tw-shadow-sm { box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05) !important; }\n    .tw-shadow { box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1) !important; }\n    .tw-shadow-md { box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important; }\n    .tw-shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important; }\n\n    .tw-border { border-width: 1px !important; }\n    .tw-border-gray-200 { border-color: rgb(229 231 235) !important; }\n    .tw-border-gray-300 { border-color: rgb(209 213 219) !important; }\n\n    .tw-transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms !important; }\n    .tw-transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms !important; }\n\n    .tw-hover\\:tw-bg-gray-100:hover { background-color: rgb(243 244 246) !important; }\n    .tw-hover\\:tw-text-blue-600:hover { color: rgb(37 99 235) !important; }\n\n    /* Grid classes */\n    .tw-grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }\n    .tw-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }\n    .tw-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }\n    .tw-gap-4 { gap: 1rem !important; }\n    .tw-gap-6 { gap: 1.5rem !important; }\n    .tw-gap-8 { gap: 2rem !important; }\n\n    @media (min-width: 768px) {\n      .tw-md\\:tw-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }\n      .tw-md\\:tw-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }\n      .tw-md\\:tw-block { display: block !important; }\n    }\n\n    /* Custom component styles */\n    \n  </style>\n</head>\n<body>\n  <div><div data-component=\"hero_section_home_page\"><div\n  class=\"dw-relative dw-z-0 dw-overflow-hidden dw-bg-white dw-bg-[top_right] dw-bg-no-repeat dw-bg-[length:50%_auto] dw-px-[5rem] dw-pt-2 dw-pb-[9rem] xlx:dw-px-[3rem] xlx:dw-pb-[7.3rem] xlx:dw-bg-[length:46%_auto] xlx:dw-bg-none lgl:dw-px-[3rem] lgl:dw-pb-[8.3rem] lgl:dw-bg-[length:46%_auto] lgl:dw-bg-none lgm:dw-px-[2.5rem] lgm:dw-pb-[9.3rem] lgm:dw-bg-[length:46%_auto] lgm:dw-bg-none lgs:dw-px-[3rem] lgs:dw-pb-[9.3rem] lgs:dw-bg-[length:46%_auto] lgs:dw-bg-none lgx:dw-px-[3rem] lgx:dw-pb-[9.3rem] lgx:dw-bg-[length:46%_auto] lgx:dw-bg-none mdl:dw-pl-[2.5rem] mdl:dw-pr-0 mdl:dw-pb-[9rem] mdl:dw-bg-[length:46%_auto] mdl:dw-bg-none mdx:dw-px-[2.5rem] mdx:dw-pb-[9rem] mdx:dw-bg-[length:46%_auto] smm:dw-px-[1rem] smm:dw-pb-[6rem] smm:dw-bg-none smx:dw-px-0 smx:dw-pb-[6rem] smx:dw-bg-none xs:dw-px-0 xs:dw-pb-[6rem] xs:dw-bg-none\"\n>\n  <svg\n    class=\"dw-absolute dw-top-0 dw-left-1/2 dw-hidden mdx:dw-block\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0.03 0 645.94 800\"\n  >\n    <path\n      d=\"M409.731 554.556L418.669 537.316C421.506 531.775 424.147 526.333 426.631 520.781C431.502 509.725 435.484 498.498 437.992 487.406C443.094 465.014 440.439 444.475 426.827 429.266C413.214 413.753 390.738 403.342 367.505 396.086C318.045 381.852 267.467 366.011 218.531 347.431C206.247 342.767 194.022 337.922 181.845 332.759C169.852 327.719 156.848 321.827 144.952 313.85C133.191 305.9 120.856 295.306 114.489 279.064C111.334 271.077 110.506 262.042 111.567 253.834C112.637 245.605 115.317 238.116 118.642 231.442C125.558 217.769 134.044 207.906 141.691 197.325L165.495 166.277L213.603 104.836L295.511 1.79063L233.07 0C233.07 0 90.8422 130.795 27.3563 213.336C-27.8375 285.077 2.30937 348.466 102.153 391.105C186.423 427.109 265.3 454.98 265.3 454.98C278.889 462.017 288.556 474.352 291.77 488.744C294.984 503.148 291.43 518.138 282.017 529.838L64.7484 800H281.519L373.716 623.644L409.731 554.556Z\"\n      class=\"dw-fill-theme-main\"\n      fill-opacity=\"0.239216\"\n    ></path>\n    <path\n      d=\"M618.27 407.848C588.012 357.233 536.216 321.533 476.212 309.928L263.631 265.109C254.767 263.405 247.316 257.78 243.553 249.963C239.791 242.158 240.192 233.109 244.612 225.622L385.928 4.37188L317.097 2.4L230.075 117.512L183.587 179.95L160.733 211.328C153.378 221.884 145.269 232.32 140.8 242.084C136.041 252.178 134.847 261.798 138.281 269.784C141.592 277.967 149.689 285.577 159.455 291.691C169.342 297.948 180.216 302.659 192.27 307.384C204.13 312.072 216.159 316.517 228.275 320.778C276.955 337.884 325.987 351.959 376.712 365.245C402.72 372.989 430.25 383.362 452.18 406.498C462.87 418.077 470.845 433.284 473.877 449.028C476.994 464.784 475.972 480.297 473.244 494.639C470.37 508.97 465.878 522.437 460.605 535.27C457.939 541.65 455.064 547.97 452.119 554.058L443.547 571.628L409.198 641.628L331.256 800H550.984L634.097 573.991C654.298 519.028 648.527 458.463 618.27 407.848Z\"\n      class=\"dw-fill-theme-main\"\n      fill-opacity=\"0.611765\"\n    ></path>\n  </svg>\n\n  <div\n    class=\"dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdx:dw-grid-cols-2\">\n      <div class=\"sub-buses-1 dw-px-1\">\n        <h1\n          class=\"dw-text-[32px] mdx:dw-text-[48px] dw-font-bold dw-mb-4 dw-font-heading\"\n        >\n          ${hero_section_title}\n        </h1>\n        <p class=\"dw-text-base xl1:dw-text-lg dw-mb-6\">\n          ${hero_section_description}\n          <strong>${contact_number}</strong>\n          ${hero_section_extra_text}\n        </p>\n\n        <div\n          class=\"dw-flex lgm:dw-flex-row dw-flex-col dw-gap-5 dw-items-start\"\n        >\n          <a\n            href=\"tel:${contact_number}\"\n            class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center dw-gap-[10px] dw-pe-[10px] lgs:dw-text-[18px] dw-text-[16px] dw-ps-[3px] dw-py-[3px] dw-text-white dw-bg-body-text dw-rounded-full hover:dw-bg-theme-main dw-border-[2px] dw-border-body-text hover:dw-border-theme-main dw-transition\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"42\"\n              height=\"42\"\n              viewBox=\"0 0 36 36\"\n              fill=\"none\"\n            >\n              <rect width=\"36\" height=\"36\" rx=\"18\" class=\"dw-fill-theme-main\" />\n              <path\n                d=\"M27.7388 22.4138C27.5716 23.6841 26.9477 24.8502 25.9837 25.6942C25.0196 26.5382 23.7813 27.0023 22.5 27.0001C15.0563 27.0001 9.00001 20.9438 9.00001 13.5001C8.99771 12.2188 9.4619 10.9805 10.3059 10.0164C11.1499 9.05234 12.3159 8.42847 13.5863 8.2613C13.9075 8.22208 14.2328 8.2878 14.5136 8.44865C14.7944 8.60951 15.0157 8.85687 15.1444 9.1538L17.1244 13.5741V13.5854C17.2229 13.8127 17.2636 14.0608 17.2428 14.3077C17.222 14.5545 17.1404 14.7924 17.0053 15.0001C16.9884 15.0254 16.9706 15.0488 16.9519 15.0722L15 17.386C15.7022 18.8129 17.1947 20.2922 18.6403 20.9963L20.9222 19.0547C20.9446 19.0359 20.9681 19.0184 20.9925 19.0022C21.2 18.8639 21.4387 18.7794 21.687 18.7565C21.9353 18.7336 22.1854 18.7729 22.4147 18.871L22.4269 18.8766L26.8434 20.8557C27.1409 20.9839 27.3889 21.205 27.5503 21.4858C27.7116 21.7667 27.7778 22.0922 27.7388 22.4138Z\"\n                fill=\"white\"\n              ></path>\n            </svg>\n            ${contact_number_button_label}\n          </a>\n\n          <a\n            href=\"${quote_url}\"\n            class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center dw-gap-[6px] lgs:dw-text-[18px] dw-text-[16px] dw-pe-[10px] dw-ps-3 dw-py-[9.5px] dw-text-white dw-border-[2px] dw-border-theme-main dw-rounded-full dw-bg-theme-main\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              id=\"Layer_1\"\n              data-name=\"Layer 1\"\n              viewBox=\"0 0 35.05 60.44\"\n              width=\"30\"\n              height=\"30\"\n              fill=\"white\"\n            >\n              <path\n                d=\"M21.45 7.46c2.09-.11 4.18.1 6.23.44l.13.22c-.51 1.39-2.17.52-3.38.48-9.62-.33-17.23 4.17-21.45 12.76-.11.23-.47 1.43-.53 1.49-.23.26-1.08.01-1.14-.18-.08-.25 1.18-2.92 1.4-3.33C6.35 12.59 13.75 7.86 21.44 7.45Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M22.15 9.65c.52-.02 4.24.09 4.25.48-.74 1.21-.54.76-1.62.75-7.73-.07-14.38 2.32-18.55 9.17-.33.55-1.51 3.39-1.67 3.51-.13.1-1.1-.11-1.14-.26-.04-.13.91-2.17 1.05-2.46C7.73 14.38 14.91 9.88 22.15 9.66Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M.13 29.21c.06-.03.97 0 1.01.04.09.13-.04 2.64 0 3.07.56 6.25 4.19 12.15 9.25 15.75.45.32 2.88 1.55 2.94 1.71.02.05-.36.77-.39.92C5.95 47.64.79 40.17.09 32.59c-.04-.45-.2-3.25.04-3.38\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"M21.71 11.93c1.05-.06 2.11.02 3.16.09.26.1-.47 1.03-.53 1.05-.37.13-1.97-.06-2.54 0-5.7.56-10.71 3.36-13.73 8.29-.29.48-1.09 2.47-1.23 2.63-.21.23-.9.01-1.14-.18 2.33-6.64 8.99-11.48 16.01-11.89Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M2.42 29.21c.07-.03.96 0 1.01.04-.56 6.56 2.81 12.97 8.03 16.8.39.29 2.65 1.47 2.68 1.62 0 .05-.37.76-.39.92-5.39-2.46-9.76-8.03-11.01-13.82-.15-.7-.78-5.36-.31-5.57Z\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"m4.61 29.21 1.01.04c-.47 6.78 3.33 13.04 9.21 16.23l-.31 1.01C8.17 43.38 3.99 36.33 4.61 29.21M2.94 24.56s.54.12.66.13c-.19.44-.08 3.28-.22 3.38-.03.02-.62.02-.66-.04-.02-.46-.01-3.28.22-3.46ZM5.31 25.09l.66.13c-.14.37-.1 2.77-.22 2.85-.03.02-.62.02-.66-.04 0-.98-.06-1.98.22-2.94\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"M35.05 0 25.01 27.68l8.95.26-23.39 32.5c3.39-9.18 7.02-18.39 9.82-27.72l-8.73-.13z\"\n                style=\"fill: #fdfeff\"\n              />\n              <path\n                d=\"M29.52 8.95c.26 0 0 .31-.04.39-4.07 7.61-10.02 14.97-14.3 22.54l-1.71.13z\"\n                style=\"fill: #fefefe\"\n              />\n            </svg>\n            ${get_quote_button_text}\n          </a>\n        </div>\n      </div>\n\n      <div\n        class=\"dw-z-10 dw-pt-10 dw-pb-10 mdl:dw-pt-16 mdl:dw-ps-10 lgm:dw-ps-[60px]\"\n      >\n        <img\n          src=\"${img-hero-section-image}\"\n          class=\"dw-rounded-[16px] dw-w-full dw-h-auto\"\n          alt=\"img-hero-section-image\"\n        />\n      </div>\n    </div>\n  </div>\n</div>\n\n<div class=\"dw-relative dw--mt-[85px] dw-z-[1]\">\n  <div\n    class=\"dw-px-8 lgm:dw-px-0 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdl:dw-grid-cols-3 dw-gap-3 dw-gap-y-12\">\n      \n      <!-- Box 1 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=`${img-advantage-image}`\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${hero_section_card1_content}\n          </p>\n        </div>\n      </div>\n\n      <!-- Box 2 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${hero_section_card2_content}\n          </p>\n        </div>\n      </div>\n\n      <!-- Box 3 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${hero_section_card3_content}\n          </p>\n        </div>\n      </div>\n    </div>\n  </div>\n</div></div></div>\n<div><div data-component=\"why_section_home_page\"><div\n  class=\"dw-bg-theme-cardBg dw-bg-[url('${img-background-pattern-bg-image}')] dw-bg-top dw-bg-no-repeat dw-bg-cover mdl:dw-pt-24 mdl:dw-pb-[15rem] dw-pt-[5rem] dw-pb-[7rem]\"\n>\n  <div\n    class=\"dw-px-8 lgm:dw-px-0 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdl:dw-grid-cols-2 dw-items-center\">\n      <!-- Left Content -->\n      <div class=\"sub-buses-1 dw-px-1\">\n        <h2\n          class=\"dw-font-heading dw-leading-tight dw-text-[26px] mdx:dw-text-[46px] lgx:text-[48px] dw-font-semibold dw-mb-4\"\n        >\n          ${why_section_heading}\n        </h2>\n        <p class=\"xl1:dw-text-lg dw-text-base dw-leading-relaxed\">\n          ${why_section_description}\n        </p>\n      </div>\n\n      <!-- Right Image -->\n      <div\n        class=\"why-rent-image sub-buses-2 dw-pt-[40px] mdl:dw-ps-[40px] lgm:dw-ps-[60px] lgs:dw-pt-0\"\n      >\n        <img\n          src=\"${img-why-section-image}\"\n          alt=\"img-why-section-image\"\n          class=\"dw-w-full dw-h-auto dw-rounded-[16px]\"\n        />\n      </div>\n    </div>\n  </div>\n</div>\n<div class=\"dw-relative dw--mt-[85px] dw-z-[1]\">\n  <div\n    class=\"dw-px-8 lgm:dw-px-0 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdl:dw-grid-cols-3 dw-gap-3 dw-gap-y-12\">\n      \n      <!-- Box 1 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${why_section_card1_content}\n          </p>\n        </div>\n      </div>\n\n      <!-- Box 2 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${why_section_card2_content}\n          </p>\n        </div>\n      </div>\n\n      <!-- Box 3 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${why_section_card3_content}\n          </p>\n        </div>\n      </div>\n    </div>\n  </div>\n</div></div></div>\n<div><div data-component=\"bus_grid_list\"><div\n  class=\"dw-my-16 dw-mx-auto dw-w-full dw-px-6 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgl:dw-px-6 lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdl:dw-px-6 mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smm:dw-px-6 smx:dw-max-w-[700px] smx:dw-px-6\"\n>\n  <div class=\"dw-flex dw-justify-center\">\n    <h2\n      class=\"xl1:dw-mx-[395px] dw-font-semibold lgm:dw-text-[48px] lgx:dw-text-[50px] mdx:dw-text-[46px] dw-text-[26px] xl1:dw-leading-[60px] lgx:dw-leading-[46px] dw-uppercase dw-text-center dw-pb-[25px] dw-font-heading\"\n    >\n      ${buses_list_heading}\n    </h2>\n  </div>\n\n  <div\n    class=\"dw-grid dw-grid-cols-1 mdx:dw-grid-cols-2 lgs:dw-grid-cols-3 mdx:dw-gap-8 dw-gap-4 xl1:dw-gap-[50px] dw-mb-4\"\n  >\n    {{busesList}}\n  </div>\n</div></div></div>\n<div><div data-component=\"bus_amenities\"><div class=\"dw-my-16\">\n  <div\n    class=\"dw-mx-auto dw-w-full dw-px-8 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgl:dw-px-8 lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] lgs:dw-px-8 mdl:dw-max-w-[1080px] mdl:dw-px-8 mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smm:dw-px-8 smx:dw-max-w-[700px] smx:dw-px-8 mdx:dw-px-[132px]\"\n  >\n    <div class=\"dw-text-center dw-pb-[20px]\">\n      <h2\n        class=\"dw-font-heading xlx:dw-text-[24px] lgx:dw-text-[22px] dw-text-[20px] dw-leading-[32px] dw-font-semibold dw-uppercase dw-tracking-[-0.011em] dw-pt-8 dw-pb-5\"\n      >\n        ${amenities_title}\n      </h2>\n    </div>\n    <div\n      class=\"dw-grid dw-grid-cols-1 mdx:dw-grid-cols-2 mdl:dw-grid-cols-3 lgs:dw-grid-cols-4 dw-gap-x-6 dw-gap-y-6\"\n    >\n      {{amenities_cards}}\n    </div>\n  </div>\n</div></div></div>\n<div><div data-component=\"note_sure\"><div\n  class=\"dw-my-16 dw-mx-auto dw-w-full dw-px-6 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgl:dw-px-6 lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdl:dw-px-6 mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smm:dw-px-6 smx:dw-max-w-[700px] smx:dw-px-6\"\n>\n  <div class=\"dw-mt-[80px] dw-mb-[40px]\">\n    <div class=\"dw-w-full\">\n      <!-- Heading -->\n      <p\n        class=\"dw-font-semibold dw-text-[20px] lgx:dw-text-[22px] xl1:dw-text-[24px] dw-uppercase dw-leading-8 dw-tracking-tight dw-pb-5 dw-text-center dw-m-0 dw-font-heading\"\n      >\n        ${note_sure_title}\n      </p>\n\n      <!-- Description -->\n      <p\n        class=\"dw-text-center dw-font-medium dw-text-[16px] lgx:dw-text-[18px] dw-pb-2\"\n      >\n        ${note_sure_description}\n      </p>\n\n      <!-- Buttons -->\n      <div class=\"dw-text-center dw-mt-10\">\n        <div\n          class=\"dw-flex dw-flex-col mdx:dw-flex-row dw-justify-center dw-items-center dw-gap-4\"\n        >\n          <!-- Call Us Button -->\n          <a\n            href=\"tel:${contact_number}\"\n            class=\"dw-inline-flex dw-font-medium dw-items-center mdx:dw-gap-[10px] dw-gap-[35px] dw-pe-[10px] lgs:dw-text-[18px] dw-text-[16px] dw-ps-[3px] dw-py-[3px] dw-text-white dw-bg-body-text dw-rounded-full hover:dw-bg-theme-main dw-border-[2px] dw-border-body-text hover:dw-border-theme-main dw-transition\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"42\"\n              height=\"42\"\n              viewBox=\"0 0 36 36\"\n              fill=\"none\"\n            >\n              <rect width=\"36\" height=\"36\" rx=\"18\" class=\"dw-fill-theme-main\" />\n              <path\n                d=\"M27.7388 22.4138C27.5716 23.6841 26.9477 24.8502 25.9837 25.6942C25.0196 26.5382 23.7813 27.0023 22.5 27.0001C15.0563 27.0001 9.00001 20.9438 9.00001 13.5001C8.99771 12.2188 9.4619 10.9805 10.3059 10.0164C11.1499 9.05234 12.3159 8.42847 13.5863 8.2613C13.9075 8.22208 14.2328 8.2878 14.5136 8.44865C14.7944 8.60951 15.0157 8.85687 15.1444 9.1538L17.1244 13.5741V13.5854C17.2229 13.8127 17.2636 14.0608 17.2428 14.3077C17.222 14.5545 17.1404 14.7924 17.0053 15.0001C16.9884 15.0254 16.9706 15.0488 16.9519 15.0722L15 17.386C15.7022 18.8129 17.1947 20.2922 18.6403 20.9963L20.9222 19.0547C20.9446 19.0359 20.9681 19.0184 20.9925 19.0022C21.2 18.8639 21.4387 18.7794 21.687 18.7565C21.9353 18.7336 22.1854 18.7729 22.4147 18.871L22.4269 18.8766L26.8434 20.8557C27.1409 20.9839 27.3889 21.205 27.5503 21.4858C27.7116 21.7667 27.7778 22.0922 27.7388 22.4138Z\"\n                fill=\"white\"\n              ></path>\n            </svg>\n            ${contact_number_button_label}\n          </a>\n\n          <!-- Get Quote Button -->\n          <a\n            href=\"${quote_url}\"\n            class=\"dw-inline-flex dw-font-medium dw-items-center dw-gap-[6px] lgs:dw-text-[18px] dw-text-[16px] dw-pe-[10px] dw-ps-3 dw-py-[9.5px] dw-text-white dw-border-[2px] dw-border-theme-main dw-rounded-full dw-bg-theme-main\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              id=\"Layer_1\"\n              data-name=\"Layer 1\"\n              viewBox=\"0 0 35.05 60.44\"\n              width=\"30\"\n              height=\"30\"\n              fill=\"white\"\n            >\n              <path\n                d=\"M21.45 7.46c2.09-.11 4.18.1 6.23.44l.13.22c-.51 1.39-2.17.52-3.38.48-9.62-.33-17.23 4.17-21.45 12.76-.11.23-.47 1.43-.53 1.49-.23.26-1.08.01-1.14-.18-.08-.25 1.18-2.92 1.4-3.33C6.35 12.59 13.75 7.86 21.44 7.45Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M22.15 9.65c.52-.02 4.24.09 4.25.48-.74 1.21-.54.76-1.62.75-7.73-.07-14.38 2.32-18.55 9.17-.33.55-1.51 3.39-1.67 3.51-.13.1-1.1-.11-1.14-.26-.04-.13.91-2.17 1.05-2.46C7.73 14.38 14.91 9.88 22.15 9.66Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M.13 29.21c.06-.03.97 0 1.01.04.09.13-.04 2.64 0 3.07.56 6.25 4.19 12.15 9.25 15.75.45.32 2.88 1.55 2.94 1.71.02.05-.36.77-.39.92C5.95 47.64.79 40.17.09 32.59c-.04-.45-.2-3.25.04-3.38\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"M21.71 11.93c1.05-.06 2.11.02 3.16.09.26.1-.47 1.03-.53 1.05-.37.13-1.97-.06-2.54 0-5.7.56-10.71 3.36-13.73 8.29-.29.48-1.09 2.47-1.23 2.63-.21.23-.9.01-1.14-.18 2.33-6.64 8.99-11.48 16.01-11.89Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M2.42 29.21c.07-.03.96 0 1.01.04-.56 6.56 2.81 12.97 8.03 16.8.39.29 2.65 1.47 2.68 1.62 0 .05-.37.76-.39.92-5.39-2.46-9.76-8.03-11.01-13.82-.15-.7-.78-5.36-.31-5.57Z\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"m4.61 29.21 1.01.04c-.47 6.78 3.33 13.04 9.21 16.23l-.31 1.01C8.17 43.38 3.99 36.33 4.61 29.21M2.94 24.56s.54.12.66.13c-.19.44-.08 3.28-.22 3.38-.03.02-.62.02-.66-.04-.02-.46-.01-3.28.22-3.46ZM5.31 25.09l.66.13c-.14.37-.1 2.77-.22 2.85-.03.02-.62.02-.66-.04 0-.98-.06-1.98.22-2.94\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"M35.05 0 25.01 27.68l8.95.26-23.39 32.5c3.39-9.18 7.02-18.39 9.82-27.72l-8.73-.13z\"\n                style=\"fill: #fdfeff\"\n              />\n              <path\n                d=\"M29.52 8.95c.26 0 0 .31-.04.39-4.07 7.61-10.02 14.97-14.3 22.54l-1.71.13z\"\n                style=\"fill: #fefefe\"\n              />\n            </svg>\n            ${get_quote_button_text}\n          </a>\n        </div>\n      </div>\n    </div>\n  </div>\n</div></div></div>\n<div><div data-component=\"book_in_minutes\"><div\n  class=\"dw-my-16 homepage-cta dw-bg-theme-mainColorShade dw-bg-[url('${img-book_in_minutes_cta_bg}')] dw-bg-no-repeat dw-bg-cover dw-py-[64px] mdx:dw-pb-[96px] mdl:dw-px-[160px] lgm:dw-px-[240px] xl1:dw-px-[160px] xl1:dw-py-[80px]\"\n>\n  <div\n    class=\"dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"row\">\n      <div class=\"dw-w-full\">\n        <h2\n          class=\"dw-mx-1 dw-text-white dw-font-heading dw-text-center dw-mb-[30px] xl1:dw-w-1/2 dw-w-full mdl:dw-mx-auto lgm:dw-text-[48px] lgx:dw-text-[50px] mdx:dw-text-[46px] dw-text-[26px] dw-font-semibold xl1:dw-leading-[60px] lgx:dw-leading-[46px] dw-pb-4\"\n        >\n          ${book_in_minutes_title}\n        </h2>\n        <div\n          class=\"dw-flex dw-flex-col mdx:dw-flex-row dw-items-center dw-justify-center dw-gap-4 dw-mt-6\"\n        >\n          <!-- Call Us Button -->\n          <a\n            href=\"tel:${contact_number}\"\n            class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center mdx:dw-gap-[10px] dw-pe-[40px] dw-gap-[30px] mdx:dw-pe-[10px] lgs:dw-text-[18px] dw-text-[16px] dw-ps-[3px] dw-py-[3px] dw-bg-white dw-rounded-full hover:dw-bg-theme-main hover:dw-text-white dw-border-[2px] dw-border-white hover:dw-border-theme-main dw-transition\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"42\"\n              height=\"42\"\n              viewBox=\"0 0 36 36\"\n              fill=\"none\"\n            >\n              <rect width=\"36\" height=\"36\" rx=\"18\" class=\"dw-fill-theme-main\" />\n              <path\n                d=\"M27.7388 22.4138C27.5716 23.6841 26.9477 24.8502 25.9837 25.6942C25.0196 26.5382 23.7813 27.0023 22.5 27.0001C15.0563 27.0001 9.00001 20.9438 9.00001 13.5001C8.99771 12.2188 9.4619 10.9805 10.3059 10.0164C11.1499 9.05234 12.3159 8.42847 13.5863 8.2613C13.9075 8.22208 14.2328 8.2878 14.5136 8.44865C14.7944 8.60951 15.0157 8.85687 15.1444 9.1538L17.1244 13.5741V13.5854C17.2229 13.8127 17.2636 14.0608 17.2428 14.3077C17.222 14.5545 17.1404 14.7924 17.0053 15.0001C16.9884 15.0254 16.9706 15.0488 16.9519 15.0722L15 17.386C15.7022 18.8129 17.1947 20.2922 18.6403 20.9963L20.9222 19.0547C20.9446 19.0359 20.9681 19.0184 20.9925 19.0022C21.2 18.8639 21.4387 18.7794 21.687 18.7565C21.9353 18.7336 22.1854 18.7729 22.4147 18.871L22.4269 18.8766L26.8434 20.8557C27.1409 20.9839 27.3889 21.205 27.5503 21.4858C27.7116 21.7667 27.7778 22.0922 27.7388 22.4138Z\"\n                fill=\"white\"\n              ></path>\n            </svg>\n            ${contact_number_button_label}\n          </a>\n\n          <!-- Get Quote Button -->\n          <a\n            href=\"${quote_url}\"\n            class=\"dw-flex dw-font-medium dw-w-fit dw-items-center dw-gap-[6px] lgs:dw-text-[18px] dw-text-[16px] dw-pe-[10px] dw-border-[2px] hover:dw-bg-theme-main hover:dw-text-white dw-rounded-full dw-bg-white dw-border-white hover:dw-border-theme-main dw-transition\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              id=\"Layer_1\"\n              data-name=\"Layer 1\"\n              viewBox=\"0 0 72 84\"\n              width=\"48\"\n              height=\"48\"\n              class=\"dw-fill-white\"\n            >\n              <circle\n                cx=\"36\"\n                cy=\"42\"\n                r=\"36\"\n                class=\"dw-fill-theme-main\"\n              ></circle>\n              <g transform=\"translate(18, 18) scale(0.8)\">\n                <path\n                  d=\"M21.45 7.46c2.09-.11 4.18.1 6.23.44l.13.22c-.51 1.39-2.17.52-3.38.48-9.62-.33-17.23 4.17-21.45 12.76-.11.23-.47 1.43-.53 1.49-.23.26-1.08.01-1.14-.18-.08-.25 1.18-2.92 1.4-3.33C6.35 12.59 13.75 7.86 21.44 7.45Z\"\n                  class=\"cls-3\"\n                ></path>\n                <path\n                  d=\"M22.15 9.65c.52-.02 4.24.09 4.25.48-.74 1.21-.54.76-1.62.75-7.73-.07-14.38 2.32-18.55 9.17-.33.55-1.51 3.39-1.673.51-.13.1-1.1-.11-1.14-.26-.04-.13.91-2.17 1.05-2.46C7.73 14.38 14.91 9.88 22.15 9.66Z\"\n                  class=\"cls-3\"\n                ></path>\n                <path\n                  d=\"M.13 29.21c.06-.03.97 0 1.01.04.09.13-.04 2.64 0 3.07.56 6.25 4.19 12.15 9.25 15.75.45.32 2.88 1.55 2.94 1.71.02.05-.36.77-.39.92C5.95 47.64.79 40.17.09 32.59c-.04-.45-.2-3.25.04-3.38\"\n                  class=\"cls-1\"\n                ></path>\n                <path\n                  d=\"M21.71 11.93c1.05-.06 2.11.02 3.16.09.26.1-.47 1.03-.53 1.05-.37.13-1.97-.06-2.54 0-5.7.56-10.71 3.36-13.73 8.29-.29.48-1.09 2.47-1.23 2.63-.21.23-.9.01-1.14-.18 2.33-6.64 8.99-11.48 16.01-11.89Z\"\n                  class=\"cls-3\"\n                ></path>\n                <path\n                  d=\"M2.42 29.21c.07-.03.96 0 1.01.04-.56 6.56 2.81 12.97 8.03 16.8.39.29 2.65 1.47 2.68 1.62 0 .05-.37.76-.39.92-5.39-2.46-9.76-8.03-11.01-13.82-.15-.7-.78-5.36-.31-5.57Z\"\n                  class=\"cls-1\"\n                ></path>\n                <path\n                  d=\"m4.61 29.21 1.01.04c-.47 6.78 3.33 13.04 9.21 16.23l-.31 1.01C8.17 43.38 3.99 36.33 4.61 29.21M2.94 24.56s.54.12.66.13c-.19.44-.08 3.28-.22 3.38-.03.02-.62.02-.66-.04-.02-.46-.01-3.28.22-3.46ZM5.31 25.09l.66.13c-.14.37-.1 2.77-.22 2.85-.03.02-.62.02-.66-.04 0-.98-.06-1.98.22-2.94\"\n                  class=\"cls-1\"\n                ></path>\n                <path\n                  d=\"M35.05 0 25.01 27.68l8.95.26-23.39 32.5c3.39-9.18 7.02-18.39 9.82-27.72l-8.73-.13z\"\n                  style=\"fill: #fdfeff\"\n                ></path>\n                <path\n                  d=\"M29.52 8.95c.26 0 0 .31-.04.39-4.07 7.61-10.02 14.97-14.3 22.54l-1.71.13z\"\n                  style=\"fill: #fefefe\"\n                ></path>\n              </g>\n            </svg>\n            ${get_quote_button_text}\n          </a>\n        </div>\n      </div>\n    </div>\n  </div>\n</div></div></div>\n<div><div data-component=\"common_cta_section\"><div\n  class=\"dw-my-16 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px] dw-px-8 dw-hidden mdx:dw-block\"\n>\n  <div class=\"dw-flex dw-w-full dw-rounded-[50px] dw-overflow-hidden\">\n    <!-- Left Column -->\n    <div\n      class=\"dw-w-[70%] dw-bg-theme-mainColorShade lgm:dw-py-[88px] lgm:dw-px-[80px] dw-py-[56px] dw-px-[48px] mdl:dw-py-[72px] mdl:dw-px-[48px]\"\n    >\n      <p\n        class=\"mdl:dw-text-[30px] dw-text-2xl dw-font-heading dw-font-semibold dw-text-white\"\n      >\n        ${cta_title}\n      </p>\n\n      <div class=\"dw-flex dw-flex-col mdl:dw-flex-row dw-gap-4 dw-mt-6\">\n        <!-- Call Us Button -->\n        <a\n          href=\"tel:${contact_number}\"\n          class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center mdx:dw-gap-[10px] dw-gap-[35px] dw-pe-[10px] lgs:dw-text-[18px] dw-text-[16px] dw-ps-[3px] dw-py-[3px] dw-bg-white dw-rounded-full hover:dw-bg-theme-main hover:dw-text-white dw-border-[2px] dw-border-white hover:dw-border-theme-main dw-transition\"\n        >\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            width=\"42\"\n            height=\"42\"\n            viewBox=\"0 0 36 36\"\n            fill=\"none\"\n          >\n            <rect width=\"36\" height=\"36\" rx=\"18\" class=\"dw-fill-theme-main\" />\n            <path\n              d=\"M27.7388 22.4138C27.5716 23.6841 26.9477 24.8502 25.9837 25.6942C25.0196 26.5382 23.7813 27.0023 22.5 27.0001C15.0563 27.0001 9.00001 20.9438 9.00001 13.5001C8.99771 12.2188 9.4619 10.9805 10.3059 10.0164C11.1499 9.05234 12.3159 8.42847 13.5863 8.2613C13.9075 8.22208 14.2328 8.2878 14.5136 8.44865C14.7944 8.60951 15.0157 8.85687 15.1444 9.1538L17.1244 13.5741V13.5854C17.2229 13.8127 17.2636 14.0608 17.2428 14.3077C17.222 14.5545 17.1404 14.7924 17.0053 15.0001C16.9884 15.0254 16.9706 15.0488 16.9519 15.0722L15 17.386C15.7022 18.8129 17.1947 20.2922 18.6403 20.9963L20.9222 19.0547C20.9446 19.0359 20.9681 19.0184 20.9925 19.0022C21.2 18.8639 21.4387 18.7794 21.687 18.7565C21.9353 18.7336 22.1854 18.7729 22.4147 18.871L22.4269 18.8766L26.8434 20.8557C27.1409 20.9839 27.3889 21.205 27.5503 21.4858C27.7116 21.7667 27.7778 22.0922 27.7388 22.4138Z\"\n              fill=\"white\"\n            ></path>\n          </svg>\n          ${contact_number_button_label}\n        </a>\n\n        <!-- Get Quote Button -->\n        <a\n          href=\"${quote_url}\"\n          class=\"dw-flex dw-font-medium dw-w-fit dw-items-center dw-gap-[6px] lgs:dw-text-[18px] dw-text-[16px] dw-pe-[10px] dw-border-[2px] hover:dw-bg-theme-main hover:dw-text-white dw-rounded-full dw-bg-white dw-border-white hover:dw-border-theme-main dw-transition\"\n        >\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            id=\"Layer_1\"\n            data-name=\"Layer 1\"\n            viewBox=\"0 0 72 84\"\n            width=\"48\"\n            height=\"48\"\n            class=\"dw-fill-white\"\n          >\n            <circle cx=\"36\" cy=\"42\" r=\"36\" class=\"dw-fill-theme-main\"></circle>\n            <g transform=\"translate(18, 18) scale(0.8)\">\n              <path\n                d=\"M21.45 7.46c2.09-.11 4.18.1 6.23.44l.13.22c-.51 1.39-2.17.52-3.38.48-9.62-.33-17.23 4.17-21.45 12.76-.11.23-.47 1.43-.53 1.49-.23.26-1.08.01-1.14-.18-.08-.25 1.18-2.92 1.4-3.33C6.35 12.59 13.75 7.86 21.44 7.45Z\"\n                class=\"cls-3\"\n              ></path>\n              <path\n                d=\"M22.15 9.65c.52-.02 4.24.09 4.25.48-.74 1.21-.54.76-1.62.75-7.73-.07-14.38 2.32-18.55 9.17-.33.55-1.51 3.39-1.673.51-.13.1-1.1-.11-1.14-.26-.04-.13.91-2.17 1.05-2.46C7.73 14.38 14.91 9.88 22.15 9.66Z\"\n                class=\"cls-3\"\n              ></path>\n              <path\n                d=\"M.13 29.21c.06-.03.97 0 1.01.04.09.13-.04 2.64 0 3.07.56 6.25 4.19 12.15 9.25 15.75.45.32 2.88 1.55 2.94 1.71.02.05-.36.77-.39.92C5.95 47.64.79 40.17.09 32.59c-.04-.45-.2-3.25.04-3.38\"\n                class=\"cls-1\"\n              ></path>\n              <path\n                d=\"M21.71 11.93c1.05-.06 2.11.02 3.16.09.26.1-.47 1.03-.53 1.05-.37.13-1.97-.06-2.54 0-5.7.56-10.71 3.36-13.73 8.29-.29.48-1.09 2.47-1.23 2.63-.21.23-.9.01-1.14-.18 2.33-6.64 8.99-11.48 16.01-11.89Z\"\n                class=\"cls-3\"\n              ></path>\n              <path\n                d=\"M2.42 29.21c.07-.03.96 0 1.01.04-.56 6.56 2.81 12.97 8.03 16.8.39.29 2.65 1.47 2.68 1.62 0 .05-.37.76-.39.92-5.39-2.46-9.76-8.03-11.01-13.82-.15-.7-.78-5.36-.31-5.57Z\"\n                class=\"cls-1\"\n              ></path>\n              <path\n                d=\"m4.61 29.21 1.01.04c-.47 6.78 3.33 13.04 9.21 16.23l-.31 1.01C8.17 43.38 3.99 36.33 4.61 29.21M2.94 24.56s.54.12.66.13c-.19.44-.08 3.28-.22 3.38-.03.02-.62.02-.66-.04-.02-.46-.01-3.28.22-3.46ZM5.31 25.09l.66.13c-.14.37-.1 2.77-.22 2.85-.03.02-.62.02-.66-.04 0-.98-.06-1.98.22-2.94\"\n                class=\"cls-1\"\n              ></path>\n              <path\n                d=\"M35.05 0 25.01 27.68l8.95.26-23.39 32.5c3.39-9.18 7.02-18.39 9.82-27.72l-8.73-.13z\"\n                style=\"fill: #fdfeff\"\n              ></path>\n              <path\n                d=\"M29.52 8.95c.26 0 0 .31-.04.39-4.07 7.61-10.02 14.97-14.3 22.54l-1.71.13z\"\n                style=\"fill: #fefefe\"\n              ></path>\n            </g>\n          </svg>\n          ${get_quote_button_text}\n        </a>\n      </div>\n\n      <div class=\"dw-mt-6 dw-flex dw-items-center dw-space-x-4\">\n        <img\n          src=\"${img-cta-review-photo}\"\n          alt=\"cta_review_photo\"\n          class=\"dw-w-[20%] dw-h-auto dw-object-contain\"\n        />\n        <p class=\"dw-text-white mdl:dw-text-[16px] dw-text-sm dw-font-medium\">\n          ${available_24_7_text}\n        </p>\n      </div>\n    </div>\n\n    <!-- Right Column -->\n    <div\n      class=\"mdl:dw-w-[30%] dw-w-[35%] dw-bg-theme-main dw-flex dw-items-center\"\n    >\n      <img\n        src=\"${img-cta-bus-rental-image}\"\n        alt=\"cta_bus_rental_image\"\n        class=\"dw-w-full dw-h-auto dw-object-contain\"\n      />\n    </div>\n  </div>\n</div></div></div>\n<div><div data-component=\"faqs\"><div\n      class=\"lgx:dw-pb-[80px] mdx:dw-pb-[64px] dw-pb-[32px] dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n    >\n      <h1\n        class=\"dw-pb-[48px] dw-mb-2 dw-capitalize dw-mx-auto dw-text-[26px] mdx:dw-text-[48px] dw-font-semibold dw-text-heading dw-text-center dw-pb-5 dw-font-heading dw-leading-tight lgx:dw-px-0 dw-px-6\"\n      >\n        ${faqs_title}\n      </h1>\n\n      <div\n        id=\"accordion\"\n        class=\"dw-w-full mdl:dw-w-[60%] mdl:dw-px-0 dw-px-8 dw-mx-auto\"\n      >\n        <div class=\"accordion\">{{faqs_questions}}</div>\n      </div>\n    </div></div></div>\n<div><div\n  class=\"dw-mx-auto dw-w-full dw-px-6 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n>\n  <hr\n    class=\"dw-border-t dw-border-theme-divider dw-w-[60%] dw-mx-auto dw-opacity-100 dw-my-14\"\n  />\n</div></div>\n<div><div\n  class=\"dw-mx-auto dw-w-full dw-px-8 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n>\n  <hr\n    class=\"dw-border-t dw-border-theme-divider dw-mx-auto dw-opacity-100 dw-my-14\"\n  />\n</div></div>\n<div><div data-component=\"worked_with\"><div\n  class=\"dw-my-16 dw-mx-auto dw-w-full dw-px-4 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgl:dw-px-4 lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdl:dw-px-4 mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smm:dw-px-4 smx:dw-max-w-[700px] smx:dw-px-4\"\n>\n  <p\n    class=\"dw-font-heading dw-text-center xlx:dw-text-[24px] lgx:dw-text-[22px] dw-text-[20px] dw-font-semibold\"\n  >\n    ${worked_with_title}\n  </p>\n\n  <div\n    class=\"dw-grid dw-grid-cols-1 smm:dw-grid-cols-2 mdx:dw-grid-cols-6 mdx:dw-gap-6 dw-gap-y-8 dw-justify-center dw-pt-[64px] dw-px-[48px] xl1:dw-pt-[64px] xl1:dw-px-[48px] dw-pb-[30px] lgs:dw-pt-[64px] xlx:dw-px-0 lgx:dw-px-[16px] mdx:dw-px-[32px]\"\n  >\n    {{work_with_list}}\n  </div>\n</div>\n</div></div>\n<div><div data-component=\"testimonial\"><section\n  class=\"dw-my-16 dw-bg-theme-cardBg dw-bg-[url('${img-background-pattern-bg-image}')] dw-bg-top dw-bg-no-repeat dw-bg-cover xlx:dw-py-24 mdx:dw-py-[64px] dw-py-[48px]\"\n>\n  <div\n    class=\"dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px] dw-px-8\"\n  >\n    <div class=\"dw-flex dw-flex-col dw-items-center\">\n      <h2\n        class=\"dw-font-heading dw-text-center mdx:dw-text-[48px] dw-text-[26px] dw-pb-4 dw-pb-6 dw-font-semibold dw-w-full\"\n      >\n        ${testimonial_title}\n      </h2>\n      <div\n        class=\"dw-grid dw-grid-cols-1 mdx:dw-grid-cols-2 mdl:dw-grid-cols-4 dw-gap-[14px] dw-gap-y-[25px] dw-w-full\"\n      >\n        {{testimonial_cards}}\n      </div>\n    </div>\n  </div>\n</section>\n</div></div>\n<div><div data-component=\"we_offer_list\">\n  <div\n    class=\"dw-my-16 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px] dw-px-[2rem]\"\n  >\n    <h2\n      class=\"dw-font-heading dw-text-center mdx:dw-text-[48px] dw-text-[26px] mdx:dw-pb-[48px] dw-pb-[20px] dw-font-semibold dw-w-full lgl:dw-w-[72%] lgs:dw-w-[80%] dw-mx-auto dw-leading-[30px] mdx:dw-leading-[55px] xl1:dw-leading-[60px]\"\n    >\n      ${we_offer_services_title}\n    </h2>\n\n    <div\n      class=\"dw-grid mdl:dw-grid-cols-2 dw-gap-x-[6px] mdx:dw-gap-x-[40px] lgx:dw-gap-x-[80px] mdx:dw-px-0 dw-px-[6px]\"\n    >\n      {{we_offer_services_list}}\n    </div>\n  </div>\n</div>\n</div>\n\n  <script>\n    try {\n       document.addEventListener(\"DOMContentLoaded\", () => {\n        const accordions = document.querySelectorAll(\".accordion-btn\");\n\n        accordions.forEach((btn) => {\n          btn.addEventListener(\"click\", () => {\n            const body = btn.nextElementSibling;\n\n            body.classList.toggle(\"dw-hidden\");\n\n            accordions.forEach((otherBtn) => {\n              if (otherBtn !== btn) {\n                otherBtn.nextElementSibling.classList.add(\"dw-hidden\");\n              }\n            });\n          });\n        });\n      });\n    } catch(e) {\n      console.error('JavaScript error:', e);\n    }\n  </script>\n</body>\n</html>\n          </div>\n        </div>\n      \n        </div>\n      </div>\n    ",
        // "templateComponentList": [
        //     {
        //         "name": "test",
        //         "slug": "test",
        //         "meta_title": "",
        //         "meta_description": "",
        //         "custom_css": "",
        //         "custom_js": "",
        //         "full_page_content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>test</title>\n  <!-- Tailwind CSS - Generated locally to avoid CDN warnings -->\n  <style>\n      @font-face {\n        font-family: Inter;\n        font-style: normal;\n        font-weight: 400;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n\n      @font-face {\n        font-family: Inter;\n        font-style: normal;\n        font-weight: 500;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n\n      @font-face {\n        font-family: Inter;\n        font-style: normal;\n        font-weight: 700;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n\n      @font-face {\n        font-family: Phudu;\n        font-style: normal;\n        font-weight: 600;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n\n      @font-face {\n        font-family: Phudu;\n        font-style: normal;\n        font-weight: 700;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n    </style>\n\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script src=\"//unpkg.com/alpinejs\" defer></script>\n\n    <script>\n      tailwind.config = {\n        content: [\"./*.html\"],\n        prefix: \"dw-\",\n        theme: {\n          extend: {\n            colors: {\n              body: {\n                bg: \"#ffffff\", // --body-bg-color\n                text: \"#333333\", // --body-text-color\n              },\n              theme: {\n                main: \"#d5232b\", // --theme-main-color\n                mainColorShade: \"#C41421\",\n                hoverBox: \"rgba(82, 164, 206, 0.43)\", // --theme-box-hover-color\n                cardBorder: \"#e0e0e0\",\n                cardBg: \"#fafafa\",\n                divider: \"#eee\",\n                lowOpacityTextColor: \"#111827\",\n              },\n            },\n            fontFamily: {\n              heading: [\"Phudu\", \"sans-serif\"], // --theme-heading-font\n              text: [\"Inter\", \"sans-serif\"], // --theme-text-font\n            },\n            screens: {\n              xs: \"200px\",\n              smx: \"376px\",\n              smm: \"567px\",\n              mdx: \"768px\",\n              mdl: \"993px\",\n              lgx: \"1171px\",\n              lgs: \"1221px\",\n              lgm: \"1281px\",\n              lgl: \"1361px\",\n              xlx: \"1400px\",\n              xl1: \"1441px\",\n              xl2: \"1600px\",\n              xxl: \"1900px\",\n            },\n          },\n        },\n        plugins: [],\n      };\n    </script>\n  <style>\n    /* Base styles */\n    * { box-sizing: border-box; }\n    html {\n      height: 100%;\n    }\n    body {\n      margin: 0;\n      padding: 20px;\n      font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif;\n      line-height: 1.6;\n      background: white;\n      color: #374151;\n    }\n\n    /* Tailwind CSS classes with tw- prefix */\n    .tw-bg-blue-500 { background-color: rgb(59 130 246) !important; }\n    .tw-bg-blue-600 { background-color: rgb(37 99 235) !important; }\n    .tw-bg-purple-600 { background-color: rgb(147 51 234) !important; }\n    .tw-bg-white { background-color: rgb(255 255 255) !important; }\n    .tw-bg-gray-50 { background-color: rgb(249 250 251) !important; }\n    .tw-bg-gray-100 { background-color: rgb(243 244 246) !important; }\n    .tw-bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)) !important; }\n    .tw-from-blue-600 { --tw-gradient-from: rgb(37 99 235); --tw-gradient-to: rgb(37 99 235 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important; }\n    .tw-to-purple-600 { --tw-gradient-to: rgb(147 51 234) !important; }\n\n    .tw-text-white { color: rgb(255 255 255) !important; }\n    .tw-text-blue-600 { color: rgb(37 99 235) !important; }\n    .tw-text-gray-600 { color: rgb(75 85 99) !important; }\n    .tw-text-gray-800 { color: rgb(31 41 55) !important; }\n    .tw-text-gray-900 { color: rgb(17 24 39) !important; }\n\n    .tw-p-4 { padding: 1rem !important; }\n    .tw-p-6 { padding: 1.5rem !important; }\n    .tw-p-8 { padding: 2rem !important; }\n    .tw-px-4 { padding-left: 1rem; padding-right: 1rem !important; }\n    .tw-px-6 { padding-left: 1.5rem; padding-right: 1.5rem !important; }\n    .tw-px-8 { padding-left: 2rem; padding-right: 2rem !important; }\n    .tw-py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem !important; }\n    .tw-py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem !important; }\n    .tw-py-4 { padding-top: 1rem; padding-bottom: 1rem !important; }\n    .tw-py-16 { padding-top: 4rem; padding-bottom: 4rem !important; }\n    .tw-py-20 { padding-top: 5rem; padding-bottom: 5rem !important; }\n\n    .tw-m-4 { margin: 1rem !important; }\n    .tw-mx-auto { margin-left: auto; margin-right: auto !important; }\n    .tw-mt-2 { margin-top: 0.5rem !important; }\n    .tw-mt-4 { margin-top: 1rem !important; }\n    .tw-mb-4 { margin-bottom: 1rem !important; }\n    .tw-mb-6 { margin-bottom: 1.5rem !important; }\n    .tw-mb-8 { margin-bottom: 2rem !important; }\n\n    .tw-rounded { border-radius: 0.25rem !important; }\n    .tw-rounded-lg { border-radius: 0.5rem !important; }\n    .tw-rounded-xl { border-radius: 0.75rem !important; }\n\n    .tw-text-sm { font-size: 0.875rem; line-height: 1.25rem !important; }\n    .tw-text-base { font-size: 1rem; line-height: 1.5rem !important; }\n    .tw-text-lg { font-size: 1.125rem; line-height: 1.75rem !important; }\n    .tw-text-xl { font-size: 1.25rem; line-height: 1.75rem !important; }\n    .tw-text-2xl { font-size: 1.5rem; line-height: 2rem !important; }\n    .tw-text-3xl { font-size: 1.875rem; line-height: 2.25rem !important; }\n    .tw-text-4xl { font-size: 2.25rem; line-height: 2.5rem !important; }\n    .tw-text-5xl { font-size: 3rem; line-height: 1 !important; }\n\n    .tw-font-medium { font-weight: 500 !important; }\n    .tw-font-semibold { font-weight: 600 !important; }\n    .tw-font-bold { font-weight: 700 !important; }\n\n    .tw-text-center { text-align: center !important; }\n    .tw-text-left { text-align: left !important; }\n\n    .tw-flex { display: flex !important; }\n    .tw-grid { display: grid !important; }\n    .tw-block { display: block !important; }\n    .tw-inline-block { display: inline-block !important; }\n    .tw-hidden { display: none !important; }\n\n    .tw-items-center { align-items: center !important; }\n    .tw-justify-center { justify-content: center !important; }\n    .tw-justify-between { justify-content: space-between !important; }\n\n    .tw-w-full { width: 100% !important; }\n    .tw-w-auto { width: auto !important; }\n    .tw-h-8 { height: 2rem !important; }\n    .tw-h-auto { height: auto !important; }\n\n    .tw-container { width: 100%; margin-left: auto; margin-right: auto !important; }\n    @media (min-width: 640px) { .tw-container { max-width: 640px !important; } }\n    @media (min-width: 768px) { .tw-container { max-width: 768px !important; } }\n    @media (min-width: 1024px) { .tw-container { max-width: 1024px !important; } }\n    @media (min-width: 1280px) { .tw-container { max-width: 1280px !important; } }\n\n    .tw-shadow-sm { box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05) !important; }\n    .tw-shadow { box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1) !important; }\n    .tw-shadow-md { box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important; }\n    .tw-shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important; }\n\n    .tw-border { border-width: 1px !important; }\n    .tw-border-gray-200 { border-color: rgb(229 231 235) !important; }\n    .tw-border-gray-300 { border-color: rgb(209 213 219) !important; }\n\n    .tw-transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms !important; }\n    .tw-transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms !important; }\n\n    .tw-hover\\:tw-bg-gray-100:hover { background-color: rgb(243 244 246) !important; }\n    .tw-hover\\:tw-text-blue-600:hover { color: rgb(37 99 235) !important; }\n\n    /* Grid classes */\n    .tw-grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }\n    .tw-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }\n    .tw-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }\n    .tw-gap-4 { gap: 1rem !important; }\n    .tw-gap-6 { gap: 1.5rem !important; }\n    .tw-gap-8 { gap: 2rem !important; }\n\n    @media (min-width: 768px) {\n      .tw-md\\:tw-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }\n      .tw-md\\:tw-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }\n      .tw-md\\:tw-block { display: block !important; }\n    }\n\n    /* Custom component styles */\n    \n  </style>\n</head>\n<body>\n  <div><div data-component=\"hero_section_home_page\"><div\n  class=\"dw-relative dw-z-0 dw-overflow-hidden dw-bg-white dw-bg-[top_right] dw-bg-no-repeat dw-bg-[length:50%_auto] dw-px-[5rem] dw-pt-2 dw-pb-[9rem] xlx:dw-px-[3rem] xlx:dw-pb-[7.3rem] xlx:dw-bg-[length:46%_auto] xlx:dw-bg-none lgl:dw-px-[3rem] lgl:dw-pb-[8.3rem] lgl:dw-bg-[length:46%_auto] lgl:dw-bg-none lgm:dw-px-[2.5rem] lgm:dw-pb-[9.3rem] lgm:dw-bg-[length:46%_auto] lgm:dw-bg-none lgs:dw-px-[3rem] lgs:dw-pb-[9.3rem] lgs:dw-bg-[length:46%_auto] lgs:dw-bg-none lgx:dw-px-[3rem] lgx:dw-pb-[9.3rem] lgx:dw-bg-[length:46%_auto] lgx:dw-bg-none mdl:dw-pl-[2.5rem] mdl:dw-pr-0 mdl:dw-pb-[9rem] mdl:dw-bg-[length:46%_auto] mdl:dw-bg-none mdx:dw-px-[2.5rem] mdx:dw-pb-[9rem] mdx:dw-bg-[length:46%_auto] smm:dw-px-[1rem] smm:dw-pb-[6rem] smm:dw-bg-none smx:dw-px-0 smx:dw-pb-[6rem] smx:dw-bg-none xs:dw-px-0 xs:dw-pb-[6rem] xs:dw-bg-none\"\n>\n  <svg\n    class=\"dw-absolute dw-top-0 dw-left-1/2 dw-hidden mdx:dw-block\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0.03 0 645.94 800\"\n  >\n    <path\n      d=\"M409.731 554.556L418.669 537.316C421.506 531.775 424.147 526.333 426.631 520.781C431.502 509.725 435.484 498.498 437.992 487.406C443.094 465.014 440.439 444.475 426.827 429.266C413.214 413.753 390.738 403.342 367.505 396.086C318.045 381.852 267.467 366.011 218.531 347.431C206.247 342.767 194.022 337.922 181.845 332.759C169.852 327.719 156.848 321.827 144.952 313.85C133.191 305.9 120.856 295.306 114.489 279.064C111.334 271.077 110.506 262.042 111.567 253.834C112.637 245.605 115.317 238.116 118.642 231.442C125.558 217.769 134.044 207.906 141.691 197.325L165.495 166.277L213.603 104.836L295.511 1.79063L233.07 0C233.07 0 90.8422 130.795 27.3563 213.336C-27.8375 285.077 2.30937 348.466 102.153 391.105C186.423 427.109 265.3 454.98 265.3 454.98C278.889 462.017 288.556 474.352 291.77 488.744C294.984 503.148 291.43 518.138 282.017 529.838L64.7484 800H281.519L373.716 623.644L409.731 554.556Z\"\n      class=\"dw-fill-theme-main\"\n      fill-opacity=\"0.239216\"\n    ></path>\n    <path\n      d=\"M618.27 407.848C588.012 357.233 536.216 321.533 476.212 309.928L263.631 265.109C254.767 263.405 247.316 257.78 243.553 249.963C239.791 242.158 240.192 233.109 244.612 225.622L385.928 4.37188L317.097 2.4L230.075 117.512L183.587 179.95L160.733 211.328C153.378 221.884 145.269 232.32 140.8 242.084C136.041 252.178 134.847 261.798 138.281 269.784C141.592 277.967 149.689 285.577 159.455 291.691C169.342 297.948 180.216 302.659 192.27 307.384C204.13 312.072 216.159 316.517 228.275 320.778C276.955 337.884 325.987 351.959 376.712 365.245C402.72 372.989 430.25 383.362 452.18 406.498C462.87 418.077 470.845 433.284 473.877 449.028C476.994 464.784 475.972 480.297 473.244 494.639C470.37 508.97 465.878 522.437 460.605 535.27C457.939 541.65 455.064 547.97 452.119 554.058L443.547 571.628L409.198 641.628L331.256 800H550.984L634.097 573.991C654.298 519.028 648.527 458.463 618.27 407.848Z\"\n      class=\"dw-fill-theme-main\"\n      fill-opacity=\"0.611765\"\n    ></path>\n  </svg>\n\n  <div\n    class=\"dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdx:dw-grid-cols-2\">\n      <div class=\"sub-buses-1 dw-px-1\">\n        <h1\n          class=\"dw-text-[32px] mdx:dw-text-[48px] dw-font-bold dw-mb-4 dw-font-heading\"\n        >\n          ${hero_section_title}\n        </h1>\n        <p class=\"dw-text-base xl1:dw-text-lg dw-mb-6\">\n          ${hero_section_description}\n          <strong>${contact_number}</strong>\n          ${hero_section_extra_text}\n        </p>\n\n        <div\n          class=\"dw-flex lgm:dw-flex-row dw-flex-col dw-gap-5 dw-items-start\"\n        >\n          <a\n            href=\"tel:${contact_number}\"\n            class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center dw-gap-[10px] dw-pe-[10px] lgs:dw-text-[18px] dw-text-[16px] dw-ps-[3px] dw-py-[3px] dw-text-white dw-bg-body-text dw-rounded-full hover:dw-bg-theme-main dw-border-[2px] dw-border-body-text hover:dw-border-theme-main dw-transition\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"42\"\n              height=\"42\"\n              viewBox=\"0 0 36 36\"\n              fill=\"none\"\n            >\n              <rect width=\"36\" height=\"36\" rx=\"18\" class=\"dw-fill-theme-main\" />\n              <path\n                d=\"M27.7388 22.4138C27.5716 23.6841 26.9477 24.8502 25.9837 25.6942C25.0196 26.5382 23.7813 27.0023 22.5 27.0001C15.0563 27.0001 9.00001 20.9438 9.00001 13.5001C8.99771 12.2188 9.4619 10.9805 10.3059 10.0164C11.1499 9.05234 12.3159 8.42847 13.5863 8.2613C13.9075 8.22208 14.2328 8.2878 14.5136 8.44865C14.7944 8.60951 15.0157 8.85687 15.1444 9.1538L17.1244 13.5741V13.5854C17.2229 13.8127 17.2636 14.0608 17.2428 14.3077C17.222 14.5545 17.1404 14.7924 17.0053 15.0001C16.9884 15.0254 16.9706 15.0488 16.9519 15.0722L15 17.386C15.7022 18.8129 17.1947 20.2922 18.6403 20.9963L20.9222 19.0547C20.9446 19.0359 20.9681 19.0184 20.9925 19.0022C21.2 18.8639 21.4387 18.7794 21.687 18.7565C21.9353 18.7336 22.1854 18.7729 22.4147 18.871L22.4269 18.8766L26.8434 20.8557C27.1409 20.9839 27.3889 21.205 27.5503 21.4858C27.7116 21.7667 27.7778 22.0922 27.7388 22.4138Z\"\n                fill=\"white\"\n              ></path>\n            </svg>\n            ${contact_number_button_label}\n          </a>\n\n          <a\n            href=\"${quote_url}\"\n            class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center dw-gap-[6px] lgs:dw-text-[18px] dw-text-[16px] dw-pe-[10px] dw-ps-3 dw-py-[9.5px] dw-text-white dw-border-[2px] dw-border-theme-main dw-rounded-full dw-bg-theme-main\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              id=\"Layer_1\"\n              data-name=\"Layer 1\"\n              viewBox=\"0 0 35.05 60.44\"\n              width=\"30\"\n              height=\"30\"\n              fill=\"white\"\n            >\n              <path\n                d=\"M21.45 7.46c2.09-.11 4.18.1 6.23.44l.13.22c-.51 1.39-2.17.52-3.38.48-9.62-.33-17.23 4.17-21.45 12.76-.11.23-.47 1.43-.53 1.49-.23.26-1.08.01-1.14-.18-.08-.25 1.18-2.92 1.4-3.33C6.35 12.59 13.75 7.86 21.44 7.45Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M22.15 9.65c.52-.02 4.24.09 4.25.48-.74 1.21-.54.76-1.62.75-7.73-.07-14.38 2.32-18.55 9.17-.33.55-1.51 3.39-1.67 3.51-.13.1-1.1-.11-1.14-.26-.04-.13.91-2.17 1.05-2.46C7.73 14.38 14.91 9.88 22.15 9.66Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M.13 29.21c.06-.03.97 0 1.01.04.09.13-.04 2.64 0 3.07.56 6.25 4.19 12.15 9.25 15.75.45.32 2.88 1.55 2.94 1.71.02.05-.36.77-.39.92C5.95 47.64.79 40.17.09 32.59c-.04-.45-.2-3.25.04-3.38\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"M21.71 11.93c1.05-.06 2.11.02 3.16.09.26.1-.47 1.03-.53 1.05-.37.13-1.97-.06-2.54 0-5.7.56-10.71 3.36-13.73 8.29-.29.48-1.09 2.47-1.23 2.63-.21.23-.9.01-1.14-.18 2.33-6.64 8.99-11.48 16.01-11.89Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M2.42 29.21c.07-.03.96 0 1.01.04-.56 6.56 2.81 12.97 8.03 16.8.39.29 2.65 1.47 2.68 1.62 0 .05-.37.76-.39.92-5.39-2.46-9.76-8.03-11.01-13.82-.15-.7-.78-5.36-.31-5.57Z\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"m4.61 29.21 1.01.04c-.47 6.78 3.33 13.04 9.21 16.23l-.31 1.01C8.17 43.38 3.99 36.33 4.61 29.21M2.94 24.56s.54.12.66.13c-.19.44-.08 3.28-.22 3.38-.03.02-.62.02-.66-.04-.02-.46-.01-3.28.22-3.46ZM5.31 25.09l.66.13c-.14.37-.1 2.77-.22 2.85-.03.02-.62.02-.66-.04 0-.98-.06-1.98.22-2.94\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"M35.05 0 25.01 27.68l8.95.26-23.39 32.5c3.39-9.18 7.02-18.39 9.82-27.72l-8.73-.13z\"\n                style=\"fill: #fdfeff\"\n              />\n              <path\n                d=\"M29.52 8.95c.26 0 0 .31-.04.39-4.07 7.61-10.02 14.97-14.3 22.54l-1.71.13z\"\n                style=\"fill: #fefefe\"\n              />\n            </svg>\n            ${get_quote_button_text}\n          </a>\n        </div>\n      </div>\n\n      <div\n        class=\"dw-z-10 dw-pt-10 dw-pb-10 mdl:dw-pt-16 mdl:dw-ps-10 lgm:dw-ps-[60px]\"\n      >\n        <img\n          src=\"${img-hero-section-image}\"\n          class=\"dw-rounded-[16px] dw-w-full dw-h-auto\"\n          alt=\"img-hero-section-image\"\n        />\n      </div>\n    </div>\n  </div>\n</div>\n\n<div class=\"dw-relative dw--mt-[85px] dw-z-[1]\">\n  <div\n    class=\"dw-px-8 lgm:dw-px-0 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdl:dw-grid-cols-3 dw-gap-3 dw-gap-y-12\">\n      \n      <!-- Box 1 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=`${img-advantage-image}`\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${hero_section_card1_content}\n          </p>\n        </div>\n      </div>\n\n      <!-- Box 2 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${hero_section_card2_content}\n          </p>\n        </div>\n      </div>\n\n      <!-- Box 3 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${hero_section_card3_content}\n          </p>\n        </div>\n      </div>\n    </div>\n  </div>\n</div></div></div>\n<div><div data-component=\"why_section_home_page\"><div\n  class=\"dw-bg-theme-cardBg dw-bg-[url('${img-background-pattern-bg-image}')] dw-bg-top dw-bg-no-repeat dw-bg-cover mdl:dw-pt-24 mdl:dw-pb-[15rem] dw-pt-[5rem] dw-pb-[7rem]\"\n>\n  <div\n    class=\"dw-px-8 lgm:dw-px-0 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdl:dw-grid-cols-2 dw-items-center\">\n      <!-- Left Content -->\n      <div class=\"sub-buses-1 dw-px-1\">\n        <h2\n          class=\"dw-font-heading dw-leading-tight dw-text-[26px] mdx:dw-text-[46px] lgx:text-[48px] dw-font-semibold dw-mb-4\"\n        >\n          ${why_section_heading}\n        </h2>\n        <p class=\"xl1:dw-text-lg dw-text-base dw-leading-relaxed\">\n          ${why_section_description}\n        </p>\n      </div>\n\n      <!-- Right Image -->\n      <div\n        class=\"why-rent-image sub-buses-2 dw-pt-[40px] mdl:dw-ps-[40px] lgm:dw-ps-[60px] lgs:dw-pt-0\"\n      >\n        <img\n          src=\"${img-why-section-image}\"\n          alt=\"img-why-section-image\"\n          class=\"dw-w-full dw-h-auto dw-rounded-[16px]\"\n        />\n      </div>\n    </div>\n  </div>\n</div>\n<div class=\"dw-relative dw--mt-[85px] dw-z-[1]\">\n  <div\n    class=\"dw-px-8 lgm:dw-px-0 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdl:dw-grid-cols-3 dw-gap-3 dw-gap-y-12\">\n      \n      <!-- Box 1 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${why_section_card1_content}\n          </p>\n        </div>\n      </div>\n\n      <!-- Box 2 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${why_section_card2_content}\n          </p>\n        </div>\n      </div>\n\n      <!-- Box 3 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${why_section_card3_content}\n          </p>\n        </div>\n      </div>\n    </div>\n  </div>\n</div></div></div>\n<div><div data-component=\"bus_grid_list\"><div\n  class=\"dw-my-16 dw-mx-auto dw-w-full dw-px-6 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgl:dw-px-6 lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdl:dw-px-6 mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smm:dw-px-6 smx:dw-max-w-[700px] smx:dw-px-6\"\n>\n  <div class=\"dw-flex dw-justify-center\">\n    <h2\n      class=\"xl1:dw-mx-[395px] dw-font-semibold lgm:dw-text-[48px] lgx:dw-text-[50px] mdx:dw-text-[46px] dw-text-[26px] xl1:dw-leading-[60px] lgx:dw-leading-[46px] dw-uppercase dw-text-center dw-pb-[25px] dw-font-heading\"\n    >\n      ${buses_list_heading}\n    </h2>\n  </div>\n\n  <div\n    class=\"dw-grid dw-grid-cols-1 mdx:dw-grid-cols-2 lgs:dw-grid-cols-3 mdx:dw-gap-8 dw-gap-4 xl1:dw-gap-[50px] dw-mb-4\"\n  >\n    {{busesList}}\n  </div>\n</div></div></div>\n<div><div data-component=\"bus_amenities\"><div class=\"dw-my-16\">\n  <div\n    class=\"dw-mx-auto dw-w-full dw-px-8 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgl:dw-px-8 lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] lgs:dw-px-8 mdl:dw-max-w-[1080px] mdl:dw-px-8 mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smm:dw-px-8 smx:dw-max-w-[700px] smx:dw-px-8 mdx:dw-px-[132px]\"\n  >\n    <div class=\"dw-text-center dw-pb-[20px]\">\n      <h2\n        class=\"dw-font-heading xlx:dw-text-[24px] lgx:dw-text-[22px] dw-text-[20px] dw-leading-[32px] dw-font-semibold dw-uppercase dw-tracking-[-0.011em] dw-pt-8 dw-pb-5\"\n      >\n        ${amenities_title}\n      </h2>\n    </div>\n    <div\n      class=\"dw-grid dw-grid-cols-1 mdx:dw-grid-cols-2 mdl:dw-grid-cols-3 lgs:dw-grid-cols-4 dw-gap-x-6 dw-gap-y-6\"\n    >\n      {{amenities_cards}}\n    </div>\n  </div>\n</div></div></div>\n<div><div data-component=\"note_sure\"><div\n  class=\"dw-my-16 dw-mx-auto dw-w-full dw-px-6 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgl:dw-px-6 lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdl:dw-px-6 mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smm:dw-px-6 smx:dw-max-w-[700px] smx:dw-px-6\"\n>\n  <div class=\"dw-mt-[80px] dw-mb-[40px]\">\n    <div class=\"dw-w-full\">\n      <!-- Heading -->\n      <p\n        class=\"dw-font-semibold dw-text-[20px] lgx:dw-text-[22px] xl1:dw-text-[24px] dw-uppercase dw-leading-8 dw-tracking-tight dw-pb-5 dw-text-center dw-m-0 dw-font-heading\"\n      >\n        ${note_sure_title}\n      </p>\n\n      <!-- Description -->\n      <p\n        class=\"dw-text-center dw-font-medium dw-text-[16px] lgx:dw-text-[18px] dw-pb-2\"\n      >\n        ${note_sure_description}\n      </p>\n\n      <!-- Buttons -->\n      <div class=\"dw-text-center dw-mt-10\">\n        <div\n          class=\"dw-flex dw-flex-col mdx:dw-flex-row dw-justify-center dw-items-center dw-gap-4\"\n        >\n          <!-- Call Us Button -->\n          <a\n            href=\"tel:${contact_number}\"\n            class=\"dw-inline-flex dw-font-medium dw-items-center mdx:dw-gap-[10px] dw-gap-[35px] dw-pe-[10px] lgs:dw-text-[18px] dw-text-[16px] dw-ps-[3px] dw-py-[3px] dw-text-white dw-bg-body-text dw-rounded-full hover:dw-bg-theme-main dw-border-[2px] dw-border-body-text hover:dw-border-theme-main dw-transition\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"42\"\n              height=\"42\"\n              viewBox=\"0 0 36 36\"\n              fill=\"none\"\n            >\n              <rect width=\"36\" height=\"36\" rx=\"18\" class=\"dw-fill-theme-main\" />\n              <path\n                d=\"M27.7388 22.4138C27.5716 23.6841 26.9477 24.8502 25.9837 25.6942C25.0196 26.5382 23.7813 27.0023 22.5 27.0001C15.0563 27.0001 9.00001 20.9438 9.00001 13.5001C8.99771 12.2188 9.4619 10.9805 10.3059 10.0164C11.1499 9.05234 12.3159 8.42847 13.5863 8.2613C13.9075 8.22208 14.2328 8.2878 14.5136 8.44865C14.7944 8.60951 15.0157 8.85687 15.1444 9.1538L17.1244 13.5741V13.5854C17.2229 13.8127 17.2636 14.0608 17.2428 14.3077C17.222 14.5545 17.1404 14.7924 17.0053 15.0001C16.9884 15.0254 16.9706 15.0488 16.9519 15.0722L15 17.386C15.7022 18.8129 17.1947 20.2922 18.6403 20.9963L20.9222 19.0547C20.9446 19.0359 20.9681 19.0184 20.9925 19.0022C21.2 18.8639 21.4387 18.7794 21.687 18.7565C21.9353 18.7336 22.1854 18.7729 22.4147 18.871L22.4269 18.8766L26.8434 20.8557C27.1409 20.9839 27.3889 21.205 27.5503 21.4858C27.7116 21.7667 27.7778 22.0922 27.7388 22.4138Z\"\n                fill=\"white\"\n              ></path>\n            </svg>\n            ${contact_number_button_label}\n          </a>\n\n          <!-- Get Quote Button -->\n          <a\n            href=\"${quote_url}\"\n            class=\"dw-inline-flex dw-font-medium dw-items-center dw-gap-[6px] lgs:dw-text-[18px] dw-text-[16px] dw-pe-[10px] dw-ps-3 dw-py-[9.5px] dw-text-white dw-border-[2px] dw-border-theme-main dw-rounded-full dw-bg-theme-main\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              id=\"Layer_1\"\n              data-name=\"Layer 1\"\n              viewBox=\"0 0 35.05 60.44\"\n              width=\"30\"\n              height=\"30\"\n              fill=\"white\"\n            >\n              <path\n                d=\"M21.45 7.46c2.09-.11 4.18.1 6.23.44l.13.22c-.51 1.39-2.17.52-3.38.48-9.62-.33-17.23 4.17-21.45 12.76-.11.23-.47 1.43-.53 1.49-.23.26-1.08.01-1.14-.18-.08-.25 1.18-2.92 1.4-3.33C6.35 12.59 13.75 7.86 21.44 7.45Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M22.15 9.65c.52-.02 4.24.09 4.25.48-.74 1.21-.54.76-1.62.75-7.73-.07-14.38 2.32-18.55 9.17-.33.55-1.51 3.39-1.67 3.51-.13.1-1.1-.11-1.14-.26-.04-.13.91-2.17 1.05-2.46C7.73 14.38 14.91 9.88 22.15 9.66Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M.13 29.21c.06-.03.97 0 1.01.04.09.13-.04 2.64 0 3.07.56 6.25 4.19 12.15 9.25 15.75.45.32 2.88 1.55 2.94 1.71.02.05-.36.77-.39.92C5.95 47.64.79 40.17.09 32.59c-.04-.45-.2-3.25.04-3.38\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"M21.71 11.93c1.05-.06 2.11.02 3.16.09.26.1-.47 1.03-.53 1.05-.37.13-1.97-.06-2.54 0-5.7.56-10.71 3.36-13.73 8.29-.29.48-1.09 2.47-1.23 2.63-.21.23-.9.01-1.14-.18 2.33-6.64 8.99-11.48 16.01-11.89Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M2.42 29.21c.07-.03.96 0 1.01.04-.56 6.56 2.81 12.97 8.03 16.8.39.29 2.65 1.47 2.68 1.62 0 .05-.37.76-.39.92-5.39-2.46-9.76-8.03-11.01-13.82-.15-.7-.78-5.36-.31-5.57Z\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"m4.61 29.21 1.01.04c-.47 6.78 3.33 13.04 9.21 16.23l-.31 1.01C8.17 43.38 3.99 36.33 4.61 29.21M2.94 24.56s.54.12.66.13c-.19.44-.08 3.28-.22 3.38-.03.02-.62.02-.66-.04-.02-.46-.01-3.28.22-3.46ZM5.31 25.09l.66.13c-.14.37-.1 2.77-.22 2.85-.03.02-.62.02-.66-.04 0-.98-.06-1.98.22-2.94\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"M35.05 0 25.01 27.68l8.95.26-23.39 32.5c3.39-9.18 7.02-18.39 9.82-27.72l-8.73-.13z\"\n                style=\"fill: #fdfeff\"\n              />\n              <path\n                d=\"M29.52 8.95c.26 0 0 .31-.04.39-4.07 7.61-10.02 14.97-14.3 22.54l-1.71.13z\"\n                style=\"fill: #fefefe\"\n              />\n            </svg>\n            ${get_quote_button_text}\n          </a>\n        </div>\n      </div>\n    </div>\n  </div>\n</div></div></div>\n<div><div data-component=\"book_in_minutes\"><div\n  class=\"dw-my-16 homepage-cta dw-bg-theme-mainColorShade dw-bg-[url('${img-book_in_minutes_cta_bg}')] dw-bg-no-repeat dw-bg-cover dw-py-[64px] mdx:dw-pb-[96px] mdl:dw-px-[160px] lgm:dw-px-[240px] xl1:dw-px-[160px] xl1:dw-py-[80px]\"\n>\n  <div\n    class=\"dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"row\">\n      <div class=\"dw-w-full\">\n        <h2\n          class=\"dw-mx-1 dw-text-white dw-font-heading dw-text-center dw-mb-[30px] xl1:dw-w-1/2 dw-w-full mdl:dw-mx-auto lgm:dw-text-[48px] lgx:dw-text-[50px] mdx:dw-text-[46px] dw-text-[26px] dw-font-semibold xl1:dw-leading-[60px] lgx:dw-leading-[46px] dw-pb-4\"\n        >\n          ${book_in_minutes_title}\n        </h2>\n        <div\n          class=\"dw-flex dw-flex-col mdx:dw-flex-row dw-items-center dw-justify-center dw-gap-4 dw-mt-6\"\n        >\n          <!-- Call Us Button -->\n          <a\n            href=\"tel:${contact_number}\"\n            class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center mdx:dw-gap-[10px] dw-pe-[40px] dw-gap-[30px] mdx:dw-pe-[10px] lgs:dw-text-[18px] dw-text-[16px] dw-ps-[3px] dw-py-[3px] dw-bg-white dw-rounded-full hover:dw-bg-theme-main hover:dw-text-white dw-border-[2px] dw-border-white hover:dw-border-theme-main dw-transition\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"42\"\n              height=\"42\"\n              viewBox=\"0 0 36 36\"\n              fill=\"none\"\n            >\n              <rect width=\"36\" height=\"36\" rx=\"18\" class=\"dw-fill-theme-main\" />\n              <path\n                d=\"M27.7388 22.4138C27.5716 23.6841 26.9477 24.8502 25.9837 25.6942C25.0196 26.5382 23.7813 27.0023 22.5 27.0001C15.0563 27.0001 9.00001 20.9438 9.00001 13.5001C8.99771 12.2188 9.4619 10.9805 10.3059 10.0164C11.1499 9.05234 12.3159 8.42847 13.5863 8.2613C13.9075 8.22208 14.2328 8.2878 14.5136 8.44865C14.7944 8.60951 15.0157 8.85687 15.1444 9.1538L17.1244 13.5741V13.5854C17.2229 13.8127 17.2636 14.0608 17.2428 14.3077C17.222 14.5545 17.1404 14.7924 17.0053 15.0001C16.9884 15.0254 16.9706 15.0488 16.9519 15.0722L15 17.386C15.7022 18.8129 17.1947 20.2922 18.6403 20.9963L20.9222 19.0547C20.9446 19.0359 20.9681 19.0184 20.9925 19.0022C21.2 18.8639 21.4387 18.7794 21.687 18.7565C21.9353 18.7336 22.1854 18.7729 22.4147 18.871L22.4269 18.8766L26.8434 20.8557C27.1409 20.9839 27.3889 21.205 27.5503 21.4858C27.7116 21.7667 27.7778 22.0922 27.7388 22.4138Z\"\n                fill=\"white\"\n              ></path>\n            </svg>\n            ${contact_number_button_label}\n          </a>\n\n          <!-- Get Quote Button -->\n          <a\n            href=\"${quote_url}\"\n            class=\"dw-flex dw-font-medium dw-w-fit dw-items-center dw-gap-[6px] lgs:dw-text-[18px] dw-text-[16px] dw-pe-[10px] dw-border-[2px] hover:dw-bg-theme-main hover:dw-text-white dw-rounded-full dw-bg-white dw-border-white hover:dw-border-theme-main dw-transition\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              id=\"Layer_1\"\n              data-name=\"Layer 1\"\n              viewBox=\"0 0 72 84\"\n              width=\"48\"\n              height=\"48\"\n              class=\"dw-fill-white\"\n            >\n              <circle\n                cx=\"36\"\n                cy=\"42\"\n                r=\"36\"\n                class=\"dw-fill-theme-main\"\n              ></circle>\n              <g transform=\"translate(18, 18) scale(0.8)\">\n                <path\n                  d=\"M21.45 7.46c2.09-.11 4.18.1 6.23.44l.13.22c-.51 1.39-2.17.52-3.38.48-9.62-.33-17.23 4.17-21.45 12.76-.11.23-.47 1.43-.53 1.49-.23.26-1.08.01-1.14-.18-.08-.25 1.18-2.92 1.4-3.33C6.35 12.59 13.75 7.86 21.44 7.45Z\"\n                  class=\"cls-3\"\n                ></path>\n                <path\n                  d=\"M22.15 9.65c.52-.02 4.24.09 4.25.48-.74 1.21-.54.76-1.62.75-7.73-.07-14.38 2.32-18.55 9.17-.33.55-1.51 3.39-1.673.51-.13.1-1.1-.11-1.14-.26-.04-.13.91-2.17 1.05-2.46C7.73 14.38 14.91 9.88 22.15 9.66Z\"\n                  class=\"cls-3\"\n                ></path>\n                <path\n                  d=\"M.13 29.21c.06-.03.97 0 1.01.04.09.13-.04 2.64 0 3.07.56 6.25 4.19 12.15 9.25 15.75.45.32 2.88 1.55 2.94 1.71.02.05-.36.77-.39.92C5.95 47.64.79 40.17.09 32.59c-.04-.45-.2-3.25.04-3.38\"\n                  class=\"cls-1\"\n                ></path>\n                <path\n                  d=\"M21.71 11.93c1.05-.06 2.11.02 3.16.09.26.1-.47 1.03-.53 1.05-.37.13-1.97-.06-2.54 0-5.7.56-10.71 3.36-13.73 8.29-.29.48-1.09 2.47-1.23 2.63-.21.23-.9.01-1.14-.18 2.33-6.64 8.99-11.48 16.01-11.89Z\"\n                  class=\"cls-3\"\n                ></path>\n                <path\n                  d=\"M2.42 29.21c.07-.03.96 0 1.01.04-.56 6.56 2.81 12.97 8.03 16.8.39.29 2.65 1.47 2.68 1.62 0 .05-.37.76-.39.92-5.39-2.46-9.76-8.03-11.01-13.82-.15-.7-.78-5.36-.31-5.57Z\"\n                  class=\"cls-1\"\n                ></path>\n                <path\n                  d=\"m4.61 29.21 1.01.04c-.47 6.78 3.33 13.04 9.21 16.23l-.31 1.01C8.17 43.38 3.99 36.33 4.61 29.21M2.94 24.56s.54.12.66.13c-.19.44-.08 3.28-.22 3.38-.03.02-.62.02-.66-.04-.02-.46-.01-3.28.22-3.46ZM5.31 25.09l.66.13c-.14.37-.1 2.77-.22 2.85-.03.02-.62.02-.66-.04 0-.98-.06-1.98.22-2.94\"\n                  class=\"cls-1\"\n                ></path>\n                <path\n                  d=\"M35.05 0 25.01 27.68l8.95.26-23.39 32.5c3.39-9.18 7.02-18.39 9.82-27.72l-8.73-.13z\"\n                  style=\"fill: #fdfeff\"\n                ></path>\n                <path\n                  d=\"M29.52 8.95c.26 0 0 .31-.04.39-4.07 7.61-10.02 14.97-14.3 22.54l-1.71.13z\"\n                  style=\"fill: #fefefe\"\n                ></path>\n              </g>\n            </svg>\n            ${get_quote_button_text}\n          </a>\n        </div>\n      </div>\n    </div>\n  </div>\n</div></div></div>\n<div><div data-component=\"common_cta_section\"><div\n  class=\"dw-my-16 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px] dw-px-8 dw-hidden mdx:dw-block\"\n>\n  <div class=\"dw-flex dw-w-full dw-rounded-[50px] dw-overflow-hidden\">\n    <!-- Left Column -->\n    <div\n      class=\"dw-w-[70%] dw-bg-theme-mainColorShade lgm:dw-py-[88px] lgm:dw-px-[80px] dw-py-[56px] dw-px-[48px] mdl:dw-py-[72px] mdl:dw-px-[48px]\"\n    >\n      <p\n        class=\"mdl:dw-text-[30px] dw-text-2xl dw-font-heading dw-font-semibold dw-text-white\"\n      >\n        ${cta_title}\n      </p>\n\n      <div class=\"dw-flex dw-flex-col mdl:dw-flex-row dw-gap-4 dw-mt-6\">\n        <!-- Call Us Button -->\n        <a\n          href=\"tel:${contact_number}\"\n          class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center mdx:dw-gap-[10px] dw-gap-[35px] dw-pe-[10px] lgs:dw-text-[18px] dw-text-[16px] dw-ps-[3px] dw-py-[3px] dw-bg-white dw-rounded-full hover:dw-bg-theme-main hover:dw-text-white dw-border-[2px] dw-border-white hover:dw-border-theme-main dw-transition\"\n        >\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            width=\"42\"\n            height=\"42\"\n            viewBox=\"0 0 36 36\"\n            fill=\"none\"\n          >\n            <rect width=\"36\" height=\"36\" rx=\"18\" class=\"dw-fill-theme-main\" />\n            <path\n              d=\"M27.7388 22.4138C27.5716 23.6841 26.9477 24.8502 25.9837 25.6942C25.0196 26.5382 23.7813 27.0023 22.5 27.0001C15.0563 27.0001 9.00001 20.9438 9.00001 13.5001C8.99771 12.2188 9.4619 10.9805 10.3059 10.0164C11.1499 9.05234 12.3159 8.42847 13.5863 8.2613C13.9075 8.22208 14.2328 8.2878 14.5136 8.44865C14.7944 8.60951 15.0157 8.85687 15.1444 9.1538L17.1244 13.5741V13.5854C17.2229 13.8127 17.2636 14.0608 17.2428 14.3077C17.222 14.5545 17.1404 14.7924 17.0053 15.0001C16.9884 15.0254 16.9706 15.0488 16.9519 15.0722L15 17.386C15.7022 18.8129 17.1947 20.2922 18.6403 20.9963L20.9222 19.0547C20.9446 19.0359 20.9681 19.0184 20.9925 19.0022C21.2 18.8639 21.4387 18.7794 21.687 18.7565C21.9353 18.7336 22.1854 18.7729 22.4147 18.871L22.4269 18.8766L26.8434 20.8557C27.1409 20.9839 27.3889 21.205 27.5503 21.4858C27.7116 21.7667 27.7778 22.0922 27.7388 22.4138Z\"\n              fill=\"white\"\n            ></path>\n          </svg>\n          ${contact_number_button_label}\n        </a>\n\n        <!-- Get Quote Button -->\n        <a\n          href=\"${quote_url}\"\n          class=\"dw-flex dw-font-medium dw-w-fit dw-items-center dw-gap-[6px] lgs:dw-text-[18px] dw-text-[16px] dw-pe-[10px] dw-border-[2px] hover:dw-bg-theme-main hover:dw-text-white dw-rounded-full dw-bg-white dw-border-white hover:dw-border-theme-main dw-transition\"\n        >\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            id=\"Layer_1\"\n            data-name=\"Layer 1\"\n            viewBox=\"0 0 72 84\"\n            width=\"48\"\n            height=\"48\"\n            class=\"dw-fill-white\"\n          >\n            <circle cx=\"36\" cy=\"42\" r=\"36\" class=\"dw-fill-theme-main\"></circle>\n            <g transform=\"translate(18, 18) scale(0.8)\">\n              <path\n                d=\"M21.45 7.46c2.09-.11 4.18.1 6.23.44l.13.22c-.51 1.39-2.17.52-3.38.48-9.62-.33-17.23 4.17-21.45 12.76-.11.23-.47 1.43-.53 1.49-.23.26-1.08.01-1.14-.18-.08-.25 1.18-2.92 1.4-3.33C6.35 12.59 13.75 7.86 21.44 7.45Z\"\n                class=\"cls-3\"\n              ></path>\n              <path\n                d=\"M22.15 9.65c.52-.02 4.24.09 4.25.48-.74 1.21-.54.76-1.62.75-7.73-.07-14.38 2.32-18.55 9.17-.33.55-1.51 3.39-1.673.51-.13.1-1.1-.11-1.14-.26-.04-.13.91-2.17 1.05-2.46C7.73 14.38 14.91 9.88 22.15 9.66Z\"\n                class=\"cls-3\"\n              ></path>\n              <path\n                d=\"M.13 29.21c.06-.03.97 0 1.01.04.09.13-.04 2.64 0 3.07.56 6.25 4.19 12.15 9.25 15.75.45.32 2.88 1.55 2.94 1.71.02.05-.36.77-.39.92C5.95 47.64.79 40.17.09 32.59c-.04-.45-.2-3.25.04-3.38\"\n                class=\"cls-1\"\n              ></path>\n              <path\n                d=\"M21.71 11.93c1.05-.06 2.11.02 3.16.09.26.1-.47 1.03-.53 1.05-.37.13-1.97-.06-2.54 0-5.7.56-10.71 3.36-13.73 8.29-.29.48-1.09 2.47-1.23 2.63-.21.23-.9.01-1.14-.18 2.33-6.64 8.99-11.48 16.01-11.89Z\"\n                class=\"cls-3\"\n              ></path>\n              <path\n                d=\"M2.42 29.21c.07-.03.96 0 1.01.04-.56 6.56 2.81 12.97 8.03 16.8.39.29 2.65 1.47 2.68 1.62 0 .05-.37.76-.39.92-5.39-2.46-9.76-8.03-11.01-13.82-.15-.7-.78-5.36-.31-5.57Z\"\n                class=\"cls-1\"\n              ></path>\n              <path\n                d=\"m4.61 29.21 1.01.04c-.47 6.78 3.33 13.04 9.21 16.23l-.31 1.01C8.17 43.38 3.99 36.33 4.61 29.21M2.94 24.56s.54.12.66.13c-.19.44-.08 3.28-.22 3.38-.03.02-.62.02-.66-.04-.02-.46-.01-3.28.22-3.46ZM5.31 25.09l.66.13c-.14.37-.1 2.77-.22 2.85-.03.02-.62.02-.66-.04 0-.98-.06-1.98.22-2.94\"\n                class=\"cls-1\"\n              ></path>\n              <path\n                d=\"M35.05 0 25.01 27.68l8.95.26-23.39 32.5c3.39-9.18 7.02-18.39 9.82-27.72l-8.73-.13z\"\n                style=\"fill: #fdfeff\"\n              ></path>\n              <path\n                d=\"M29.52 8.95c.26 0 0 .31-.04.39-4.07 7.61-10.02 14.97-14.3 22.54l-1.71.13z\"\n                style=\"fill: #fefefe\"\n              ></path>\n            </g>\n          </svg>\n          ${get_quote_button_text}\n        </a>\n      </div>\n\n      <div class=\"dw-mt-6 dw-flex dw-items-center dw-space-x-4\">\n        <img\n          src=\"${img-cta-review-photo}\"\n          alt=\"cta_review_photo\"\n          class=\"dw-w-[20%] dw-h-auto dw-object-contain\"\n        />\n        <p class=\"dw-text-white mdl:dw-text-[16px] dw-text-sm dw-font-medium\">\n          ${available_24_7_text}\n        </p>\n      </div>\n    </div>\n\n    <!-- Right Column -->\n    <div\n      class=\"mdl:dw-w-[30%] dw-w-[35%] dw-bg-theme-main dw-flex dw-items-center\"\n    >\n      <img\n        src=\"${img-cta-bus-rental-image}\"\n        alt=\"cta_bus_rental_image\"\n        class=\"dw-w-full dw-h-auto dw-object-contain\"\n      />\n    </div>\n  </div>\n</div></div></div>\n<div><div data-component=\"faqs\"><div\n      class=\"lgx:dw-pb-[80px] mdx:dw-pb-[64px] dw-pb-[32px] dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n    >\n      <h1\n        class=\"dw-pb-[48px] dw-mb-2 dw-capitalize dw-mx-auto dw-text-[26px] mdx:dw-text-[48px] dw-font-semibold dw-text-heading dw-text-center dw-pb-5 dw-font-heading dw-leading-tight lgx:dw-px-0 dw-px-6\"\n      >\n        ${faqs_title}\n      </h1>\n\n      <div\n        id=\"accordion\"\n        class=\"dw-w-full mdl:dw-w-[60%] mdl:dw-px-0 dw-px-8 dw-mx-auto\"\n      >\n        <div class=\"accordion\">{{faqs_questions}}</div>\n      </div>\n    </div></div></div>\n<div><div\n  class=\"dw-mx-auto dw-w-full dw-px-6 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n>\n  <hr\n    class=\"dw-border-t dw-border-theme-divider dw-w-[60%] dw-mx-auto dw-opacity-100 dw-my-14\"\n  />\n</div></div>\n<div><div\n  class=\"dw-mx-auto dw-w-full dw-px-8 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n>\n  <hr\n    class=\"dw-border-t dw-border-theme-divider dw-mx-auto dw-opacity-100 dw-my-14\"\n  />\n</div></div>\n<div><div data-component=\"worked_with\"><div\n  class=\"dw-my-16 dw-mx-auto dw-w-full dw-px-4 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgl:dw-px-4 lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdl:dw-px-4 mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smm:dw-px-4 smx:dw-max-w-[700px] smx:dw-px-4\"\n>\n  <p\n    class=\"dw-font-heading dw-text-center xlx:dw-text-[24px] lgx:dw-text-[22px] dw-text-[20px] dw-font-semibold\"\n  >\n    ${worked_with_title}\n  </p>\n\n  <div\n    class=\"dw-grid dw-grid-cols-1 smm:dw-grid-cols-2 mdx:dw-grid-cols-6 mdx:dw-gap-6 dw-gap-y-8 dw-justify-center dw-pt-[64px] dw-px-[48px] xl1:dw-pt-[64px] xl1:dw-px-[48px] dw-pb-[30px] lgs:dw-pt-[64px] xlx:dw-px-0 lgx:dw-px-[16px] mdx:dw-px-[32px]\"\n  >\n    {{work_with_list}}\n  </div>\n</div>\n</div></div>\n<div><div data-component=\"testimonial\"><section\n  class=\"dw-my-16 dw-bg-theme-cardBg dw-bg-[url('${img-background-pattern-bg-image}')] dw-bg-top dw-bg-no-repeat dw-bg-cover xlx:dw-py-24 mdx:dw-py-[64px] dw-py-[48px]\"\n>\n  <div\n    class=\"dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px] dw-px-8\"\n  >\n    <div class=\"dw-flex dw-flex-col dw-items-center\">\n      <h2\n        class=\"dw-font-heading dw-text-center mdx:dw-text-[48px] dw-text-[26px] dw-pb-4 dw-pb-6 dw-font-semibold dw-w-full\"\n      >\n        ${testimonial_title}\n      </h2>\n      <div\n        class=\"dw-grid dw-grid-cols-1 mdx:dw-grid-cols-2 mdl:dw-grid-cols-4 dw-gap-[14px] dw-gap-y-[25px] dw-w-full\"\n      >\n        {{testimonial_cards}}\n      </div>\n    </div>\n  </div>\n</section>\n</div></div>\n<div><div data-component=\"we_offer_list\">\n  <div\n    class=\"dw-my-16 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px] dw-px-[2rem]\"\n  >\n    <h2\n      class=\"dw-font-heading dw-text-center mdx:dw-text-[48px] dw-text-[26px] mdx:dw-pb-[48px] dw-pb-[20px] dw-font-semibold dw-w-full lgl:dw-w-[72%] lgs:dw-w-[80%] dw-mx-auto dw-leading-[30px] mdx:dw-leading-[55px] xl1:dw-leading-[60px]\"\n    >\n      ${we_offer_services_title}\n    </h2>\n\n    <div\n      class=\"dw-grid mdl:dw-grid-cols-2 dw-gap-x-[6px] mdx:dw-gap-x-[40px] lgx:dw-gap-x-[80px] mdx:dw-px-0 dw-px-[6px]\"\n    >\n      {{we_offer_services_list}}\n    </div>\n  </div>\n</div>\n</div>\n\n  <script>\n    try {\n       document.addEventListener(\"DOMContentLoaded\", () => {\n        const accordions = document.querySelectorAll(\".accordion-btn\");\n\n        accordions.forEach((btn) => {\n          btn.addEventListener(\"click\", () => {\n            const body = btn.nextElementSibling;\n\n            body.classList.toggle(\"dw-hidden\");\n\n            accordions.forEach((otherBtn) => {\n              if (otherBtn !== btn) {\n                otherBtn.nextElementSibling.classList.add(\"dw-hidden\");\n              }\n            });\n          });\n        });\n      });\n    } catch(e) {\n      console.error('JavaScript error:', e);\n    }\n  </script>\n</body>\n</html>",

        //         "components": [
        //             {
        //                 "name": "hero_section_home_page",
        //                 "category_id": "cat2",
        //                 "html_content": "<div data-component=\"hero_section_home_page\"><div\n  class=\"dw-relative dw-z-0 dw-overflow-hidden dw-bg-white dw-bg-[top_right] dw-bg-no-repeat dw-bg-[length:50%_auto] dw-px-[5rem] dw-pt-2 dw-pb-[9rem] xlx:dw-px-[3rem] xlx:dw-pb-[7.3rem] xlx:dw-bg-[length:46%_auto] xlx:dw-bg-none lgl:dw-px-[3rem] lgl:dw-pb-[8.3rem] lgl:dw-bg-[length:46%_auto] lgl:dw-bg-none lgm:dw-px-[2.5rem] lgm:dw-pb-[9.3rem] lgm:dw-bg-[length:46%_auto] lgm:dw-bg-none lgs:dw-px-[3rem] lgs:dw-pb-[9.3rem] lgs:dw-bg-[length:46%_auto] lgs:dw-bg-none lgx:dw-px-[3rem] lgx:dw-pb-[9.3rem] lgx:dw-bg-[length:46%_auto] lgx:dw-bg-none mdl:dw-pl-[2.5rem] mdl:dw-pr-0 mdl:dw-pb-[9rem] mdl:dw-bg-[length:46%_auto] mdl:dw-bg-none mdx:dw-px-[2.5rem] mdx:dw-pb-[9rem] mdx:dw-bg-[length:46%_auto] smm:dw-px-[1rem] smm:dw-pb-[6rem] smm:dw-bg-none smx:dw-px-0 smx:dw-pb-[6rem] smx:dw-bg-none xs:dw-px-0 xs:dw-pb-[6rem] xs:dw-bg-none\"\n>\n  <svg\n    class=\"dw-absolute dw-top-0 dw-left-1/2 dw-hidden mdx:dw-block\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0.03 0 645.94 800\"\n  >\n    <path\n      d=\"M409.731 554.556L418.669 537.316C421.506 531.775 424.147 526.333 426.631 520.781C431.502 509.725 435.484 498.498 437.992 487.406C443.094 465.014 440.439 444.475 426.827 429.266C413.214 413.753 390.738 403.342 367.505 396.086C318.045 381.852 267.467 366.011 218.531 347.431C206.247 342.767 194.022 337.922 181.845 332.759C169.852 327.719 156.848 321.827 144.952 313.85C133.191 305.9 120.856 295.306 114.489 279.064C111.334 271.077 110.506 262.042 111.567 253.834C112.637 245.605 115.317 238.116 118.642 231.442C125.558 217.769 134.044 207.906 141.691 197.325L165.495 166.277L213.603 104.836L295.511 1.79063L233.07 0C233.07 0 90.8422 130.795 27.3563 213.336C-27.8375 285.077 2.30937 348.466 102.153 391.105C186.423 427.109 265.3 454.98 265.3 454.98C278.889 462.017 288.556 474.352 291.77 488.744C294.984 503.148 291.43 518.138 282.017 529.838L64.7484 800H281.519L373.716 623.644L409.731 554.556Z\"\n      class=\"dw-fill-theme-main\"\n      fill-opacity=\"0.239216\"\n    ></path>\n    <path\n      d=\"M618.27 407.848C588.012 357.233 536.216 321.533 476.212 309.928L263.631 265.109C254.767 263.405 247.316 257.78 243.553 249.963C239.791 242.158 240.192 233.109 244.612 225.622L385.928 4.37188L317.097 2.4L230.075 117.512L183.587 179.95L160.733 211.328C153.378 221.884 145.269 232.32 140.8 242.084C136.041 252.178 134.847 261.798 138.281 269.784C141.592 277.967 149.689 285.577 159.455 291.691C169.342 297.948 180.216 302.659 192.27 307.384C204.13 312.072 216.159 316.517 228.275 320.778C276.955 337.884 325.987 351.959 376.712 365.245C402.72 372.989 430.25 383.362 452.18 406.498C462.87 418.077 470.845 433.284 473.877 449.028C476.994 464.784 475.972 480.297 473.244 494.639C470.37 508.97 465.878 522.437 460.605 535.27C457.939 541.65 455.064 547.97 452.119 554.058L443.547 571.628L409.198 641.628L331.256 800H550.984L634.097 573.991C654.298 519.028 648.527 458.463 618.27 407.848Z\"\n      class=\"dw-fill-theme-main\"\n      fill-opacity=\"0.611765\"\n    ></path>\n  </svg>\n\n  <div\n    class=\"dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdx:dw-grid-cols-2\">\n      <div class=\"sub-buses-1 dw-px-1\">\n        <h1\n          class=\"dw-text-[32px] mdx:dw-text-[48px] dw-font-bold dw-mb-4 dw-font-heading\"\n        >\n          ${hero_section_title}\n        </h1>\n        <p class=\"dw-text-base xl1:dw-text-lg dw-mb-6\">\n          ${hero_section_description}\n          <strong>${contact_number}</strong>\n          ${hero_section_extra_text}\n        </p>\n\n        <div\n          class=\"dw-flex lgm:dw-flex-row dw-flex-col dw-gap-5 dw-items-start\"\n        >\n          <a\n            href=\"tel:${contact_number}\"\n            class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center dw-gap-[10px] dw-pe-[10px] lgs:dw-text-[18px] dw-text-[16px] dw-ps-[3px] dw-py-[3px] dw-text-white dw-bg-body-text dw-rounded-full hover:dw-bg-theme-main dw-border-[2px] dw-border-body-text hover:dw-border-theme-main dw-transition\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"42\"\n              height=\"42\"\n              viewBox=\"0 0 36 36\"\n              fill=\"none\"\n            >\n              <rect width=\"36\" height=\"36\" rx=\"18\" class=\"dw-fill-theme-main\" />\n              <path\n                d=\"M27.7388 22.4138C27.5716 23.6841 26.9477 24.8502 25.9837 25.6942C25.0196 26.5382 23.7813 27.0023 22.5 27.0001C15.0563 27.0001 9.00001 20.9438 9.00001 13.5001C8.99771 12.2188 9.4619 10.9805 10.3059 10.0164C11.1499 9.05234 12.3159 8.42847 13.5863 8.2613C13.9075 8.22208 14.2328 8.2878 14.5136 8.44865C14.7944 8.60951 15.0157 8.85687 15.1444 9.1538L17.1244 13.5741V13.5854C17.2229 13.8127 17.2636 14.0608 17.2428 14.3077C17.222 14.5545 17.1404 14.7924 17.0053 15.0001C16.9884 15.0254 16.9706 15.0488 16.9519 15.0722L15 17.386C15.7022 18.8129 17.1947 20.2922 18.6403 20.9963L20.9222 19.0547C20.9446 19.0359 20.9681 19.0184 20.9925 19.0022C21.2 18.8639 21.4387 18.7794 21.687 18.7565C21.9353 18.7336 22.1854 18.7729 22.4147 18.871L22.4269 18.8766L26.8434 20.8557C27.1409 20.9839 27.3889 21.205 27.5503 21.4858C27.7116 21.7667 27.7778 22.0922 27.7388 22.4138Z\"\n                fill=\"white\"\n              ></path>\n            </svg>\n            ${contact_number_button_label}\n          </a>\n\n          <a\n            href=\"${quote_url}\"\n            class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center dw-gap-[6px] lgs:dw-text-[18px] dw-text-[16px] dw-pe-[10px] dw-ps-3 dw-py-[9.5px] dw-text-white dw-border-[2px] dw-border-theme-main dw-rounded-full dw-bg-theme-main\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              id=\"Layer_1\"\n              data-name=\"Layer 1\"\n              viewBox=\"0 0 35.05 60.44\"\n              width=\"30\"\n              height=\"30\"\n              fill=\"white\"\n            >\n              <path\n                d=\"M21.45 7.46c2.09-.11 4.18.1 6.23.44l.13.22c-.51 1.39-2.17.52-3.38.48-9.62-.33-17.23 4.17-21.45 12.76-.11.23-.47 1.43-.53 1.49-.23.26-1.08.01-1.14-.18-.08-.25 1.18-2.92 1.4-3.33C6.35 12.59 13.75 7.86 21.44 7.45Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M22.15 9.65c.52-.02 4.24.09 4.25.48-.74 1.21-.54.76-1.62.75-7.73-.07-14.38 2.32-18.55 9.17-.33.55-1.51 3.39-1.67 3.51-.13.1-1.1-.11-1.14-.26-.04-.13.91-2.17 1.05-2.46C7.73 14.38 14.91 9.88 22.15 9.66Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M.13 29.21c.06-.03.97 0 1.01.04.09.13-.04 2.64 0 3.07.56 6.25 4.19 12.15 9.25 15.75.45.32 2.88 1.55 2.94 1.71.02.05-.36.77-.39.92C5.95 47.64.79 40.17.09 32.59c-.04-.45-.2-3.25.04-3.38\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"M21.71 11.93c1.05-.06 2.11.02 3.16.09.26.1-.47 1.03-.53 1.05-.37.13-1.97-.06-2.54 0-5.7.56-10.71 3.36-13.73 8.29-.29.48-1.09 2.47-1.23 2.63-.21.23-.9.01-1.14-.18 2.33-6.64 8.99-11.48 16.01-11.89Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M2.42 29.21c.07-.03.96 0 1.01.04-.56 6.56 2.81 12.97 8.03 16.8.39.29 2.65 1.47 2.68 1.62 0 .05-.37.76-.39.92-5.39-2.46-9.76-8.03-11.01-13.82-.15-.7-.78-5.36-.31-5.57Z\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"m4.61 29.21 1.01.04c-.47 6.78 3.33 13.04 9.21 16.23l-.31 1.01C8.17 43.38 3.99 36.33 4.61 29.21M2.94 24.56s.54.12.66.13c-.19.44-.08 3.28-.22 3.38-.03.02-.62.02-.66-.04-.02-.46-.01-3.28.22-3.46ZM5.31 25.09l.66.13c-.14.37-.1 2.77-.22 2.85-.03.02-.62.02-.66-.04 0-.98-.06-1.98.22-2.94\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"M35.05 0 25.01 27.68l8.95.26-23.39 32.5c3.39-9.18 7.02-18.39 9.82-27.72l-8.73-.13z\"\n                style=\"fill: #fdfeff\"\n              />\n              <path\n                d=\"M29.52 8.95c.26 0 0 .31-.04.39-4.07 7.61-10.02 14.97-14.3 22.54l-1.71.13z\"\n                style=\"fill: #fefefe\"\n              />\n            </svg>\n            ${get_quote_button_text}\n          </a>\n        </div>\n      </div>\n\n      <div\n        class=\"dw-z-10 dw-pt-10 dw-pb-10 mdl:dw-pt-16 mdl:dw-ps-10 lgm:dw-ps-[60px]\"\n      >\n        <img\n          src=\"${img-hero-section-image}\"\n          class=\"dw-rounded-[16px] dw-w-full dw-h-auto\"\n          alt=\"img-hero-section-image\"\n        />\n      </div>\n    </div>\n  </div>\n</div>\n\n<div class=\"dw-relative dw--mt-[85px] dw-z-[1]\">\n  <div\n    class=\"dw-px-8 lgm:dw-px-0 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdl:dw-grid-cols-3 dw-gap-3 dw-gap-y-12\">\n      \n      <!-- Box 1 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=`${img-advantage-image}`\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${hero_section_card1_content}\n          </p>\n        </div>\n      </div>\n\n      <!-- Box 2 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${hero_section_card2_content}\n          </p>\n        </div>\n      </div>\n\n      <!-- Box 3 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${hero_section_card3_content}\n          </p>\n        </div>\n      </div>\n    </div>\n  </div>\n</div></div>",
        //                 "css_content": "",
        //                 "js_content": "",
        //                 "placeholders": [
        //                     "hero_section_title",
        //                     "hero_section_description",
        //                     "contact_number",
        //                     "hero_section_extra_text",
        //                     "contact_number_button_label",
        //                     "quote_url",
        //                     "get_quote_button_text",
        //                     "img-hero-section-image",
        //                     "img-advantage-image",
        //                     "hero_section_card1_content",
        //                     "hero_section_card2_content",
        //                     "hero_section_card3_content"
        //                 ],
        //                 "preview_image": "",
        //                 "version": 1,
        //                 "status": "draft",
        //                 "tags": [],
        //                 "thumbnail_url": "",
        //                 "id": "mejxa6hnolket8gfqqg",
        //                 "created_at": "2025-08-20T12:02:02.459Z",
        //                 "updated_at": "2025-08-21T11:19:53.780Z",
        //                 "category_name": "Content",
        //                 "category_color": "#10B981",
        //                 "order": 0,
        //                 "repeator": "single",
        //                 "cssClass": "",
        //                 "uniqueId": "mejxa6hnolket8gfqqg-1755852044433-n3nr3fxos"
        //             },
        //             {
        //                 "name": "why_section_home_page",
        //                 "category_id": "cat2",
        //                 "html_content": "<div data-component=\"why_section_home_page\"><div\n  class=\"dw-bg-theme-cardBg dw-bg-[url('${img-background-pattern-bg-image}')] dw-bg-top dw-bg-no-repeat dw-bg-cover mdl:dw-pt-24 mdl:dw-pb-[15rem] dw-pt-[5rem] dw-pb-[7rem]\"\n>\n  <div\n    class=\"dw-px-8 lgm:dw-px-0 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdl:dw-grid-cols-2 dw-items-center\">\n      <!-- Left Content -->\n      <div class=\"sub-buses-1 dw-px-1\">\n        <h2\n          class=\"dw-font-heading dw-leading-tight dw-text-[26px] mdx:dw-text-[46px] lgx:text-[48px] dw-font-semibold dw-mb-4\"\n        >\n          ${why_section_heading}\n        </h2>\n        <p class=\"xl1:dw-text-lg dw-text-base dw-leading-relaxed\">\n          ${why_section_description}\n        </p>\n      </div>\n\n      <!-- Right Image -->\n      <div\n        class=\"why-rent-image sub-buses-2 dw-pt-[40px] mdl:dw-ps-[40px] lgm:dw-ps-[60px] lgs:dw-pt-0\"\n      >\n        <img\n          src=\"${img-why-section-image}\"\n          alt=\"img-why-section-image\"\n          class=\"dw-w-full dw-h-auto dw-rounded-[16px]\"\n        />\n      </div>\n    </div>\n  </div>\n</div>\n<div class=\"dw-relative dw--mt-[85px] dw-z-[1]\">\n  <div\n    class=\"dw-px-8 lgm:dw-px-0 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdl:dw-grid-cols-3 dw-gap-3 dw-gap-y-12\">\n      \n      <!-- Box 1 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${why_section_card1_content}\n          </p>\n        </div>\n      </div>\n\n      <!-- Box 2 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${why_section_card2_content}\n          </p>\n        </div>\n      </div>\n\n      <!-- Box 3 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${why_section_card3_content}\n          </p>\n        </div>\n      </div>\n    </div>\n  </div>\n</div></div>",
        //                 "css_content": "",
        //                 "js_content": "",
        //                 "placeholders": [
        //                     "img-background-pattern-bg-image",
        //                     "why_section_heading",
        //                     "why_section_description",
        //                     "img-why-section-image",
        //                     "img-advantage-image",
        //                     "why_section_card1_content",
        //                     "why_section_card2_content",
        //                     "why_section_card3_content"
        //                 ],
        //                 "preview_image": "",
        //                 "version": 1,
        //                 "status": "draft",
        //                 "tags": [],
        //                 "thumbnail_url": "",
        //                 "id": "mejxkuo2q6g7dim380s",
        //                 "created_at": "2025-08-20T12:10:20.354Z",
        //                 "updated_at": "2025-08-21T11:21:58.734Z",
        //                 "category_name": "Content",
        //                 "category_color": "#10B981",
        //                 "order": 1,
        //                 "repeator": "single",
        //                 "cssClass": "",
        //                 "uniqueId": "mejxkuo2q6g7dim380s-1755852046724-wf8wnll0e"
        //             },
        //             {
        //                 "name": "bus_grid_list",
        //                 "category_id": "cat2",
        //                 "html_content": "<div data-component=\"bus_grid_list\"><div\n  class=\"dw-my-16 dw-mx-auto dw-w-full dw-px-6 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgl:dw-px-6 lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdl:dw-px-6 mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smm:dw-px-6 smx:dw-max-w-[700px] smx:dw-px-6\"\n>\n  <div class=\"dw-flex dw-justify-center\">\n    <h2\n      class=\"xl1:dw-mx-[395px] dw-font-semibold lgm:dw-text-[48px] lgx:dw-text-[50px] mdx:dw-text-[46px] dw-text-[26px] xl1:dw-leading-[60px] lgx:dw-leading-[46px] dw-uppercase dw-text-center dw-pb-[25px] dw-font-heading\"\n    >\n      ${buses_list_heading}\n    </h2>\n  </div>\n\n  <div\n    class=\"dw-grid dw-grid-cols-1 mdx:dw-grid-cols-2 lgs:dw-grid-cols-3 mdx:dw-gap-8 dw-gap-4 xl1:dw-gap-[50px] dw-mb-4\"\n  >\n    {{busesList}}\n  </div>\n</div></div>",
        //                 "css_content": "",
        //                 "js_content": "",
        //                 "placeholders": [
        //                     "buses_list_heading"
        //                 ],
        //                 "preview_image": "",
        //                 "version": 1,
        //                 "status": "draft",
        //                 "tags": [],
        //                 "thumbnail_url": "",
        //                 "id": "mejz65z1z7hhj4rxbr",
        //                 "created_at": "2025-08-20T12:54:54.397Z",
        //                 "updated_at": "2025-08-21T11:23:11.906Z",
        //                 "category_name": "Content",
        //                 "category_color": "#10B981",
        //                 "order": 2,
        //                 "repeator": "repeat",
        //                 "cssClass": "",
        //                 "uniqueId": "mejz65z1z7hhj4rxbr-1755852048083-gnecsryuy",
        //                 "repeatedComponentId": "melbj7f65tmd8ocep4a",
        //                 "repeatedComponentName": [
        //                     "busesList"
        //                 ],
        //                 "repeatedComponent": {
        //                     "name": "bud_grid_card",
        //                     "category_id": "cat2",
        //                     "html_content": "<div data-component=\"bud_grid_card\"><div\n  class=\"dw-border dw-border-theme-cardBorder dw-rounded-[32px] dw-bg-theme-cardBg xl1:dw-p-[3rem_2rem_1.5rem] dw-p-[2.5rem_1rem_1rem] dw-min-h-full dw-relative\"\n>\n  <span\n    class=\"dw-absolute dw-z-10 dw-bg-body-bg dw-text-theme-lowOpacityTextColor dw-font-normal dw-border dw-shadow-sm dw-text-[14px] dw-px-[15px] dw-py-[8px] dw-ml-[10px] dw--translate-y-[20px] dw-rounded-full\"\n  >\n    ${bus_price_range}\n  </span>\n\n  <img\n    src=\"${img-bus-image}\"\n    class=\"dw-rounded-[16px] dw-w-full dw-h-auto\"\n    alt=\"img-busImage\"\n  />\n  <img\n    src=\"${img-bus-interior}\"\n    class=\"dw-rounded-[16px] dw-w-full dw-h-auto dw-mt-4\"\n    alt=\"img-busInterior\"\n  />\n\n  <h3\n    class=\"dw-font-semibold lgm:dw-text-[24px] lgx:dw-text-[22px] dw-text-[20px] dw-leading-[32px] dw-uppercase dw-tracking-[-0.176px] dw-text-center dw-pt-[32px] dw-pb-[25px] dw-font-heading dw-mb-2\"\n  >\n    <a href=\"${view_Bus_Url}\"> ${bus_title} </a>\n  </h3>\n\n  <div class=\"dw-flex dw-justify-center dw-gap-4 dw-pb-[5px]\">\n    <a\n      href=\"${view_Bus_Url}\"\n      class=\"dw-bg-body-text dw-text-white dw-border-2 dw-border-body-text dw-rounded-full dw-px-[16px] dw-py-[13px] dw-text-[18px] dw-leading-[24px] dw-font-medium dw-tracking-[-0.24px] hover:dw-border-theme-main hover:dw-bg-theme-main\"\n    >\n      ${bus_view_button_text}\n    </a>\n    <a\n      href=\"${quote_url}\"\n      class=\"dw-bg-theme-main dw-text-white dw-border-2 dw-border-theme-main dw-rounded-full dw-px-[16px] dw-py-[13px] dw-inline-flex dw-items-center dw-justify-center dw-text-[18px] dw-leading-[24px] dw-font-medium dw-tracking-[-0.24px]\"\n    >\n      ${get_quote_button_text}\n    </a>\n  </div>\n</div>\n</div>",
        //                     "css_content": "",
        //                     "js_content": "",
        //                     "placeholders": [
        //                         "bus_price_range",
        //                         "img-bus-image",
        //                         "img-bus-interior",
        //                         "view_Bus_Url",
        //                         "bus_title",
        //                         "bus_view_button_text",
        //                         "quote_url",
        //                         "get_quote_button_text"
        //                     ],
        //                     "preview_image": "",
        //                     "version": 1,
        //                     "status": "draft",
        //                     "tags": [],
        //                     "thumbnail_url": "",
        //                     "id": "melbj7f65tmd8ocep4a",
        //                     "created_at": "2025-08-21T11:28:44.370Z",
        //                     "updated_at": "2025-08-22T05:03:22.688Z",
        //                     "category_name": "Content",
        //                     "category_color": "#10B981"
        //                 }
        //             },
        //             {
        //                 "name": "bus_amenities",
        //                 "category_id": "cat2",
        //                 "html_content": "<div data-component=\"bus_amenities\"><div class=\"dw-my-16\">\n  <div\n    class=\"dw-mx-auto dw-w-full dw-px-8 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgl:dw-px-8 lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] lgs:dw-px-8 mdl:dw-max-w-[1080px] mdl:dw-px-8 mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smm:dw-px-8 smx:dw-max-w-[700px] smx:dw-px-8 mdx:dw-px-[132px]\"\n  >\n    <div class=\"dw-text-center dw-pb-[20px]\">\n      <h2\n        class=\"dw-font-heading xlx:dw-text-[24px] lgx:dw-text-[22px] dw-text-[20px] dw-leading-[32px] dw-font-semibold dw-uppercase dw-tracking-[-0.011em] dw-pt-8 dw-pb-5\"\n      >\n        ${amenities_title}\n      </h2>\n    </div>\n    <div\n      class=\"dw-grid dw-grid-cols-1 mdx:dw-grid-cols-2 mdl:dw-grid-cols-3 lgs:dw-grid-cols-4 dw-gap-x-6 dw-gap-y-6\"\n    >\n      {{amenities_cards}}\n    </div>\n  </div>\n</div></div>",
        //                 "css_content": "",
        //                 "js_content": "",
        //                 "placeholders": [
        //                     "amenities_title"
        //                 ],
        //                 "preview_image": "",
        //                 "version": 1,
        //                 "status": "draft",
        //                 "tags": [],
        //                 "thumbnail_url": "",
        //                 "id": "mel05d6jebm3z3va507",
        //                 "created_at": "2025-08-21T06:10:02.875Z",
        //                 "updated_at": "2025-08-21T11:29:41.045Z",
        //                 "category_name": "Content",
        //                 "category_color": "#10B981",
        //                 "order": 3,
        //                 "repeator": "repeat",
        //                 "cssClass": "",
        //                 "uniqueId": "mel05d6jebm3z3va507-1755852061728-3pnoa23ny",
        //                 "repeatedComponentId": "melbmuizp786kngqmks",
        //                 "repeatedComponentName": [
        //                     "amenities_cards"
        //                 ],
        //                 "repeatedComponent": {
        //                     "name": "amenities_card",
        //                     "category_id": "cat2",
        //                     "html_content": "<div data-component=\"amenities_card\"><div\n  class=\"dw-flex dw-flex-col dw-justify-center dw-items-center dw-text-center dw-border dw-border-gray-200 dw-rounded-[16px] dw-min-h-[180px] dw-shadow-sm dw-px-4 dw-py-6\"\n>\n  <img src=\"${img-amenities_image}\" alt=\"amenities Image\" class=\"dw-mx-auto\" />\n  <p class=\"dw-pt-5 dw-font-medium dw-text-center xl1:dw-text-lg dw-text-base\">\n    ${amenities_text}\n  </p>\n</div>\n</div>",
        //                     "css_content": "",
        //                     "js_content": "",
        //                     "placeholders": [
        //                         "img-amenities_image",
        //                         "amenities_text"
        //                     ],
        //                     "preview_image": "",
        //                     "version": 1,
        //                     "status": "draft",
        //                     "tags": [],
        //                     "thumbnail_url": "",
        //                     "id": "melbmuizp786kngqmks",
        //                     "created_at": "2025-08-21T11:31:34.283Z",
        //                     "updated_at": "2025-08-21T11:31:34.283Z",
        //                     "category_name": "Content",
        //                     "category_color": "#10B981"
        //                 }
        //             },
        //             {
        //                 "name": "note_sure",
        //                 "category_id": "cat2",
        //                 "html_content": "<div data-component=\"note_sure\"><div\n  class=\"dw-my-16 dw-mx-auto dw-w-full dw-px-6 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgl:dw-px-6 lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdl:dw-px-6 mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smm:dw-px-6 smx:dw-max-w-[700px] smx:dw-px-6\"\n>\n  <div class=\"dw-mt-[80px] dw-mb-[40px]\">\n    <div class=\"dw-w-full\">\n      <!-- Heading -->\n      <p\n        class=\"dw-font-semibold dw-text-[20px] lgx:dw-text-[22px] xl1:dw-text-[24px] dw-uppercase dw-leading-8 dw-tracking-tight dw-pb-5 dw-text-center dw-m-0 dw-font-heading\"\n      >\n        ${note_sure_title}\n      </p>\n\n      <!-- Description -->\n      <p\n        class=\"dw-text-center dw-font-medium dw-text-[16px] lgx:dw-text-[18px] dw-pb-2\"\n      >\n        ${note_sure_description}\n      </p>\n\n      <!-- Buttons -->\n      <div class=\"dw-text-center dw-mt-10\">\n        <div\n          class=\"dw-flex dw-flex-col mdx:dw-flex-row dw-justify-center dw-items-center dw-gap-4\"\n        >\n          <!-- Call Us Button -->\n          <a\n            href=\"tel:${contact_number}\"\n            class=\"dw-inline-flex dw-font-medium dw-items-center mdx:dw-gap-[10px] dw-gap-[35px] dw-pe-[10px] lgs:dw-text-[18px] dw-text-[16px] dw-ps-[3px] dw-py-[3px] dw-text-white dw-bg-body-text dw-rounded-full hover:dw-bg-theme-main dw-border-[2px] dw-border-body-text hover:dw-border-theme-main dw-transition\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"42\"\n              height=\"42\"\n              viewBox=\"0 0 36 36\"\n              fill=\"none\"\n            >\n              <rect width=\"36\" height=\"36\" rx=\"18\" class=\"dw-fill-theme-main\" />\n              <path\n                d=\"M27.7388 22.4138C27.5716 23.6841 26.9477 24.8502 25.9837 25.6942C25.0196 26.5382 23.7813 27.0023 22.5 27.0001C15.0563 27.0001 9.00001 20.9438 9.00001 13.5001C8.99771 12.2188 9.4619 10.9805 10.3059 10.0164C11.1499 9.05234 12.3159 8.42847 13.5863 8.2613C13.9075 8.22208 14.2328 8.2878 14.5136 8.44865C14.7944 8.60951 15.0157 8.85687 15.1444 9.1538L17.1244 13.5741V13.5854C17.2229 13.8127 17.2636 14.0608 17.2428 14.3077C17.222 14.5545 17.1404 14.7924 17.0053 15.0001C16.9884 15.0254 16.9706 15.0488 16.9519 15.0722L15 17.386C15.7022 18.8129 17.1947 20.2922 18.6403 20.9963L20.9222 19.0547C20.9446 19.0359 20.9681 19.0184 20.9925 19.0022C21.2 18.8639 21.4387 18.7794 21.687 18.7565C21.9353 18.7336 22.1854 18.7729 22.4147 18.871L22.4269 18.8766L26.8434 20.8557C27.1409 20.9839 27.3889 21.205 27.5503 21.4858C27.7116 21.7667 27.7778 22.0922 27.7388 22.4138Z\"\n                fill=\"white\"\n              ></path>\n            </svg>\n            ${contact_number_button_label}\n          </a>\n\n          <!-- Get Quote Button -->\n          <a\n            href=\"${quote_url}\"\n            class=\"dw-inline-flex dw-font-medium dw-items-center dw-gap-[6px] lgs:dw-text-[18px] dw-text-[16px] dw-pe-[10px] dw-ps-3 dw-py-[9.5px] dw-text-white dw-border-[2px] dw-border-theme-main dw-rounded-full dw-bg-theme-main\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              id=\"Layer_1\"\n              data-name=\"Layer 1\"\n              viewBox=\"0 0 35.05 60.44\"\n              width=\"30\"\n              height=\"30\"\n              fill=\"white\"\n            >\n              <path\n                d=\"M21.45 7.46c2.09-.11 4.18.1 6.23.44l.13.22c-.51 1.39-2.17.52-3.38.48-9.62-.33-17.23 4.17-21.45 12.76-.11.23-.47 1.43-.53 1.49-.23.26-1.08.01-1.14-.18-.08-.25 1.18-2.92 1.4-3.33C6.35 12.59 13.75 7.86 21.44 7.45Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M22.15 9.65c.52-.02 4.24.09 4.25.48-.74 1.21-.54.76-1.62.75-7.73-.07-14.38 2.32-18.55 9.17-.33.55-1.51 3.39-1.67 3.51-.13.1-1.1-.11-1.14-.26-.04-.13.91-2.17 1.05-2.46C7.73 14.38 14.91 9.88 22.15 9.66Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M.13 29.21c.06-.03.97 0 1.01.04.09.13-.04 2.64 0 3.07.56 6.25 4.19 12.15 9.25 15.75.45.32 2.88 1.55 2.94 1.71.02.05-.36.77-.39.92C5.95 47.64.79 40.17.09 32.59c-.04-.45-.2-3.25.04-3.38\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"M21.71 11.93c1.05-.06 2.11.02 3.16.09.26.1-.47 1.03-.53 1.05-.37.13-1.97-.06-2.54 0-5.7.56-10.71 3.36-13.73 8.29-.29.48-1.09 2.47-1.23 2.63-.21.23-.9.01-1.14-.18 2.33-6.64 8.99-11.48 16.01-11.89Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M2.42 29.21c.07-.03.96 0 1.01.04-.56 6.56 2.81 12.97 8.03 16.8.39.29 2.65 1.47 2.68 1.62 0 .05-.37.76-.39.92-5.39-2.46-9.76-8.03-11.01-13.82-.15-.7-.78-5.36-.31-5.57Z\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"m4.61 29.21 1.01.04c-.47 6.78 3.33 13.04 9.21 16.23l-.31 1.01C8.17 43.38 3.99 36.33 4.61 29.21M2.94 24.56s.54.12.66.13c-.19.44-.08 3.28-.22 3.38-.03.02-.62.02-.66-.04-.02-.46-.01-3.28.22-3.46ZM5.31 25.09l.66.13c-.14.37-.1 2.77-.22 2.85-.03.02-.62.02-.66-.04 0-.98-.06-1.98.22-2.94\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"M35.05 0 25.01 27.68l8.95.26-23.39 32.5c3.39-9.18 7.02-18.39 9.82-27.72l-8.73-.13z\"\n                style=\"fill: #fdfeff\"\n              />\n              <path\n                d=\"M29.52 8.95c.26 0 0 .31-.04.39-4.07 7.61-10.02 14.97-14.3 22.54l-1.71.13z\"\n                style=\"fill: #fefefe\"\n              />\n            </svg>\n            ${get_quote_button_text}\n          </a>\n        </div>\n      </div>\n    </div>\n  </div>\n</div></div>",
        //                 "css_content": "",
        //                 "js_content": "",
        //                 "placeholders": [
        //                     "note_sure_title",
        //                     "note_sure_description",
        //                     "contact_number",
        //                     "contact_number_button_label",
        //                     "quote_url",
        //                     "get_quote_button_text"
        //                 ],
        //                 "preview_image": "",
        //                 "version": 1,
        //                 "status": "draft",
        //                 "tags": [],
        //                 "thumbnail_url": "",
        //                 "id": "mel0t7zfjeg1zxrr0m",
        //                 "created_at": "2025-08-21T06:28:35.883Z",
        //                 "updated_at": "2025-08-22T05:04:09.666Z",
        //                 "category_name": "Content",
        //                 "category_color": "#10B981",
        //                 "order": 4,
        //                 "repeator": "single",
        //                 "cssClass": "",
        //                 "uniqueId": "mel0t7zfjeg1zxrr0m-1755852073254-6fdl28lr0"
        //             },
        //             {
        //                 "name": "book_in_minutes",
        //                 "category_id": "cat2",
        //                 "html_content": "<div data-component=\"book_in_minutes\"><div\n  class=\"dw-my-16 homepage-cta dw-bg-theme-mainColorShade dw-bg-[url('${img-book_in_minutes_cta_bg}')] dw-bg-no-repeat dw-bg-cover dw-py-[64px] mdx:dw-pb-[96px] mdl:dw-px-[160px] lgm:dw-px-[240px] xl1:dw-px-[160px] xl1:dw-py-[80px]\"\n>\n  <div\n    class=\"dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"row\">\n      <div class=\"dw-w-full\">\n        <h2\n          class=\"dw-mx-1 dw-text-white dw-font-heading dw-text-center dw-mb-[30px] xl1:dw-w-1/2 dw-w-full mdl:dw-mx-auto lgm:dw-text-[48px] lgx:dw-text-[50px] mdx:dw-text-[46px] dw-text-[26px] dw-font-semibold xl1:dw-leading-[60px] lgx:dw-leading-[46px] dw-pb-4\"\n        >\n          ${book_in_minutes_title}\n        </h2>\n        <div\n          class=\"dw-flex dw-flex-col mdx:dw-flex-row dw-items-center dw-justify-center dw-gap-4 dw-mt-6\"\n        >\n          <!-- Call Us Button -->\n          <a\n            href=\"tel:${contact_number}\"\n            class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center mdx:dw-gap-[10px] dw-pe-[40px] dw-gap-[30px] mdx:dw-pe-[10px] lgs:dw-text-[18px] dw-text-[16px] dw-ps-[3px] dw-py-[3px] dw-bg-white dw-rounded-full hover:dw-bg-theme-main hover:dw-text-white dw-border-[2px] dw-border-white hover:dw-border-theme-main dw-transition\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"42\"\n              height=\"42\"\n              viewBox=\"0 0 36 36\"\n              fill=\"none\"\n            >\n              <rect width=\"36\" height=\"36\" rx=\"18\" class=\"dw-fill-theme-main\" />\n              <path\n                d=\"M27.7388 22.4138C27.5716 23.6841 26.9477 24.8502 25.9837 25.6942C25.0196 26.5382 23.7813 27.0023 22.5 27.0001C15.0563 27.0001 9.00001 20.9438 9.00001 13.5001C8.99771 12.2188 9.4619 10.9805 10.3059 10.0164C11.1499 9.05234 12.3159 8.42847 13.5863 8.2613C13.9075 8.22208 14.2328 8.2878 14.5136 8.44865C14.7944 8.60951 15.0157 8.85687 15.1444 9.1538L17.1244 13.5741V13.5854C17.2229 13.8127 17.2636 14.0608 17.2428 14.3077C17.222 14.5545 17.1404 14.7924 17.0053 15.0001C16.9884 15.0254 16.9706 15.0488 16.9519 15.0722L15 17.386C15.7022 18.8129 17.1947 20.2922 18.6403 20.9963L20.9222 19.0547C20.9446 19.0359 20.9681 19.0184 20.9925 19.0022C21.2 18.8639 21.4387 18.7794 21.687 18.7565C21.9353 18.7336 22.1854 18.7729 22.4147 18.871L22.4269 18.8766L26.8434 20.8557C27.1409 20.9839 27.3889 21.205 27.5503 21.4858C27.7116 21.7667 27.7778 22.0922 27.7388 22.4138Z\"\n                fill=\"white\"\n              ></path>\n            </svg>\n            ${contact_number_button_label}\n          </a>\n\n          <!-- Get Quote Button -->\n          <a\n            href=\"${quote_url}\"\n            class=\"dw-flex dw-font-medium dw-w-fit dw-items-center dw-gap-[6px] lgs:dw-text-[18px] dw-text-[16px] dw-pe-[10px] dw-border-[2px] hover:dw-bg-theme-main hover:dw-text-white dw-rounded-full dw-bg-white dw-border-white hover:dw-border-theme-main dw-transition\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              id=\"Layer_1\"\n              data-name=\"Layer 1\"\n              viewBox=\"0 0 72 84\"\n              width=\"48\"\n              height=\"48\"\n              class=\"dw-fill-white\"\n            >\n              <circle\n                cx=\"36\"\n                cy=\"42\"\n                r=\"36\"\n                class=\"dw-fill-theme-main\"\n              ></circle>\n              <g transform=\"translate(18, 18) scale(0.8)\">\n                <path\n                  d=\"M21.45 7.46c2.09-.11 4.18.1 6.23.44l.13.22c-.51 1.39-2.17.52-3.38.48-9.62-.33-17.23 4.17-21.45 12.76-.11.23-.47 1.43-.53 1.49-.23.26-1.08.01-1.14-.18-.08-.25 1.18-2.92 1.4-3.33C6.35 12.59 13.75 7.86 21.44 7.45Z\"\n                  class=\"cls-3\"\n                ></path>\n                <path\n                  d=\"M22.15 9.65c.52-.02 4.24.09 4.25.48-.74 1.21-.54.76-1.62.75-7.73-.07-14.38 2.32-18.55 9.17-.33.55-1.51 3.39-1.673.51-.13.1-1.1-.11-1.14-.26-.04-.13.91-2.17 1.05-2.46C7.73 14.38 14.91 9.88 22.15 9.66Z\"\n                  class=\"cls-3\"\n                ></path>\n                <path\n                  d=\"M.13 29.21c.06-.03.97 0 1.01.04.09.13-.04 2.64 0 3.07.56 6.25 4.19 12.15 9.25 15.75.45.32 2.88 1.55 2.94 1.71.02.05-.36.77-.39.92C5.95 47.64.79 40.17.09 32.59c-.04-.45-.2-3.25.04-3.38\"\n                  class=\"cls-1\"\n                ></path>\n                <path\n                  d=\"M21.71 11.93c1.05-.06 2.11.02 3.16.09.26.1-.47 1.03-.53 1.05-.37.13-1.97-.06-2.54 0-5.7.56-10.71 3.36-13.73 8.29-.29.48-1.09 2.47-1.23 2.63-.21.23-.9.01-1.14-.18 2.33-6.64 8.99-11.48 16.01-11.89Z\"\n                  class=\"cls-3\"\n                ></path>\n                <path\n                  d=\"M2.42 29.21c.07-.03.96 0 1.01.04-.56 6.56 2.81 12.97 8.03 16.8.39.29 2.65 1.47 2.68 1.62 0 .05-.37.76-.39.92-5.39-2.46-9.76-8.03-11.01-13.82-.15-.7-.78-5.36-.31-5.57Z\"\n                  class=\"cls-1\"\n                ></path>\n                <path\n                  d=\"m4.61 29.21 1.01.04c-.47 6.78 3.33 13.04 9.21 16.23l-.31 1.01C8.17 43.38 3.99 36.33 4.61 29.21M2.94 24.56s.54.12.66.13c-.19.44-.08 3.28-.22 3.38-.03.02-.62.02-.66-.04-.02-.46-.01-3.28.22-3.46ZM5.31 25.09l.66.13c-.14.37-.1 2.77-.22 2.85-.03.02-.62.02-.66-.04 0-.98-.06-1.98.22-2.94\"\n                  class=\"cls-1\"\n                ></path>\n                <path\n                  d=\"M35.05 0 25.01 27.68l8.95.26-23.39 32.5c3.39-9.18 7.02-18.39 9.82-27.72l-8.73-.13z\"\n                  style=\"fill: #fdfeff\"\n                ></path>\n                <path\n                  d=\"M29.52 8.95c.26 0 0 .31-.04.39-4.07 7.61-10.02 14.97-14.3 22.54l-1.71.13z\"\n                  style=\"fill: #fefefe\"\n                ></path>\n              </g>\n            </svg>\n            ${get_quote_button_text}\n          </a>\n        </div>\n      </div>\n    </div>\n  </div>\n</div></div>",
        //                 "css_content": "",
        //                 "js_content": "",
        //                 "placeholders": [
        //                     "img-book_in_minutes_cta_bg",
        //                     "book_in_minutes_title",
        //                     "contact_number",
        //                     "contact_number_button_label",
        //                     "quote_url",
        //                     "get_quote_button_text"
        //                 ],
        //                 "preview_image": "",
        //                 "version": 1,
        //                 "status": "draft",
        //                 "tags": [],
        //                 "thumbnail_url": "",
        //                 "id": "mel11vphae1ej7eq137",
        //                 "created_at": "2025-08-21T06:35:19.877Z",
        //                 "updated_at": "2025-08-21T12:08:09.560Z",
        //                 "category_name": "Content",
        //                 "category_color": "#10B981",
        //                 "order": 5,
        //                 "repeator": "single",
        //                 "cssClass": "",
        //                 "uniqueId": "mel11vphae1ej7eq137-1755852075658-xxen4bk19"
        //             },
        //             {
        //                 "name": "common_cta_section",
        //                 "category_id": "cat2",
        //                 "html_content": "<div data-component=\"common_cta_section\"><div\n  class=\"dw-my-16 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px] dw-px-8 dw-hidden mdx:dw-block\"\n>\n  <div class=\"dw-flex dw-w-full dw-rounded-[50px] dw-overflow-hidden\">\n    <!-- Left Column -->\n    <div\n      class=\"dw-w-[70%] dw-bg-theme-mainColorShade lgm:dw-py-[88px] lgm:dw-px-[80px] dw-py-[56px] dw-px-[48px] mdl:dw-py-[72px] mdl:dw-px-[48px]\"\n    >\n      <p\n        class=\"mdl:dw-text-[30px] dw-text-2xl dw-font-heading dw-font-semibold dw-text-white\"\n      >\n        ${cta_title}\n      </p>\n\n      <div class=\"dw-flex dw-flex-col mdl:dw-flex-row dw-gap-4 dw-mt-6\">\n        <!-- Call Us Button -->\n        <a\n          href=\"tel:${contact_number}\"\n          class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center mdx:dw-gap-[10px] dw-gap-[35px] dw-pe-[10px] lgs:dw-text-[18px] dw-text-[16px] dw-ps-[3px] dw-py-[3px] dw-bg-white dw-rounded-full hover:dw-bg-theme-main hover:dw-text-white dw-border-[2px] dw-border-white hover:dw-border-theme-main dw-transition\"\n        >\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            width=\"42\"\n            height=\"42\"\n            viewBox=\"0 0 36 36\"\n            fill=\"none\"\n          >\n            <rect width=\"36\" height=\"36\" rx=\"18\" class=\"dw-fill-theme-main\" />\n            <path\n              d=\"M27.7388 22.4138C27.5716 23.6841 26.9477 24.8502 25.9837 25.6942C25.0196 26.5382 23.7813 27.0023 22.5 27.0001C15.0563 27.0001 9.00001 20.9438 9.00001 13.5001C8.99771 12.2188 9.4619 10.9805 10.3059 10.0164C11.1499 9.05234 12.3159 8.42847 13.5863 8.2613C13.9075 8.22208 14.2328 8.2878 14.5136 8.44865C14.7944 8.60951 15.0157 8.85687 15.1444 9.1538L17.1244 13.5741V13.5854C17.2229 13.8127 17.2636 14.0608 17.2428 14.3077C17.222 14.5545 17.1404 14.7924 17.0053 15.0001C16.9884 15.0254 16.9706 15.0488 16.9519 15.0722L15 17.386C15.7022 18.8129 17.1947 20.2922 18.6403 20.9963L20.9222 19.0547C20.9446 19.0359 20.9681 19.0184 20.9925 19.0022C21.2 18.8639 21.4387 18.7794 21.687 18.7565C21.9353 18.7336 22.1854 18.7729 22.4147 18.871L22.4269 18.8766L26.8434 20.8557C27.1409 20.9839 27.3889 21.205 27.5503 21.4858C27.7116 21.7667 27.7778 22.0922 27.7388 22.4138Z\"\n              fill=\"white\"\n            ></path>\n          </svg>\n          ${contact_number_button_label}\n        </a>\n\n        <!-- Get Quote Button -->\n        <a\n          href=\"${quote_url}\"\n          class=\"dw-flex dw-font-medium dw-w-fit dw-items-center dw-gap-[6px] lgs:dw-text-[18px] dw-text-[16px] dw-pe-[10px] dw-border-[2px] hover:dw-bg-theme-main hover:dw-text-white dw-rounded-full dw-bg-white dw-border-white hover:dw-border-theme-main dw-transition\"\n        >\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            id=\"Layer_1\"\n            data-name=\"Layer 1\"\n            viewBox=\"0 0 72 84\"\n            width=\"48\"\n            height=\"48\"\n            class=\"dw-fill-white\"\n          >\n            <circle cx=\"36\" cy=\"42\" r=\"36\" class=\"dw-fill-theme-main\"></circle>\n            <g transform=\"translate(18, 18) scale(0.8)\">\n              <path\n                d=\"M21.45 7.46c2.09-.11 4.18.1 6.23.44l.13.22c-.51 1.39-2.17.52-3.38.48-9.62-.33-17.23 4.17-21.45 12.76-.11.23-.47 1.43-.53 1.49-.23.26-1.08.01-1.14-.18-.08-.25 1.18-2.92 1.4-3.33C6.35 12.59 13.75 7.86 21.44 7.45Z\"\n                class=\"cls-3\"\n              ></path>\n              <path\n                d=\"M22.15 9.65c.52-.02 4.24.09 4.25.48-.74 1.21-.54.76-1.62.75-7.73-.07-14.38 2.32-18.55 9.17-.33.55-1.51 3.39-1.673.51-.13.1-1.1-.11-1.14-.26-.04-.13.91-2.17 1.05-2.46C7.73 14.38 14.91 9.88 22.15 9.66Z\"\n                class=\"cls-3\"\n              ></path>\n              <path\n                d=\"M.13 29.21c.06-.03.97 0 1.01.04.09.13-.04 2.64 0 3.07.56 6.25 4.19 12.15 9.25 15.75.45.32 2.88 1.55 2.94 1.71.02.05-.36.77-.39.92C5.95 47.64.79 40.17.09 32.59c-.04-.45-.2-3.25.04-3.38\"\n                class=\"cls-1\"\n              ></path>\n              <path\n                d=\"M21.71 11.93c1.05-.06 2.11.02 3.16.09.26.1-.47 1.03-.53 1.05-.37.13-1.97-.06-2.54 0-5.7.56-10.71 3.36-13.73 8.29-.29.48-1.09 2.47-1.23 2.63-.21.23-.9.01-1.14-.18 2.33-6.64 8.99-11.48 16.01-11.89Z\"\n                class=\"cls-3\"\n              ></path>\n              <path\n                d=\"M2.42 29.21c.07-.03.96 0 1.01.04-.56 6.56 2.81 12.97 8.03 16.8.39.29 2.65 1.47 2.68 1.62 0 .05-.37.76-.39.92-5.39-2.46-9.76-8.03-11.01-13.82-.15-.7-.78-5.36-.31-5.57Z\"\n                class=\"cls-1\"\n              ></path>\n              <path\n                d=\"m4.61 29.21 1.01.04c-.47 6.78 3.33 13.04 9.21 16.23l-.31 1.01C8.17 43.38 3.99 36.33 4.61 29.21M2.94 24.56s.54.12.66.13c-.19.44-.08 3.28-.22 3.38-.03.02-.62.02-.66-.04-.02-.46-.01-3.28.22-3.46ZM5.31 25.09l.66.13c-.14.37-.1 2.77-.22 2.85-.03.02-.62.02-.66-.04 0-.98-.06-1.98.22-2.94\"\n                class=\"cls-1\"\n              ></path>\n              <path\n                d=\"M35.05 0 25.01 27.68l8.95.26-23.39 32.5c3.39-9.18 7.02-18.39 9.82-27.72l-8.73-.13z\"\n                style=\"fill: #fdfeff\"\n              ></path>\n              <path\n                d=\"M29.52 8.95c.26 0 0 .31-.04.39-4.07 7.61-10.02 14.97-14.3 22.54l-1.71.13z\"\n                style=\"fill: #fefefe\"\n              ></path>\n            </g>\n          </svg>\n          ${get_quote_button_text}\n        </a>\n      </div>\n\n      <div class=\"dw-mt-6 dw-flex dw-items-center dw-space-x-4\">\n        <img\n          src=\"${img-cta-review-photo}\"\n          alt=\"cta_review_photo\"\n          class=\"dw-w-[20%] dw-h-auto dw-object-contain\"\n        />\n        <p class=\"dw-text-white mdl:dw-text-[16px] dw-text-sm dw-font-medium\">\n          ${available_24_7_text}\n        </p>\n      </div>\n    </div>\n\n    <!-- Right Column -->\n    <div\n      class=\"mdl:dw-w-[30%] dw-w-[35%] dw-bg-theme-main dw-flex dw-items-center\"\n    >\n      <img\n        src=\"${img-cta-bus-rental-image}\"\n        alt=\"cta_bus_rental_image\"\n        class=\"dw-w-full dw-h-auto dw-object-contain\"\n      />\n    </div>\n  </div>\n</div></div>",
        //                 "css_content": "",
        //                 "js_content": "",
        //                 "placeholders": [
        //                     "cta_title",
        //                     "contact_number",
        //                     "contact_number_button_label",
        //                     "quote_url",
        //                     "get_quote_button_text",
        //                     "img-cta-review-photo",
        //                     "available_24_7_text",
        //                     "img-cta-bus-rental-image"
        //                 ],
        //                 "preview_image": "",
        //                 "version": 1,
        //                 "status": "draft",
        //                 "tags": [],
        //                 "thumbnail_url": "",
        //                 "id": "mel2ma1dkrz5ju2ar8j",
        //                 "created_at": "2025-08-21T07:19:11.185Z",
        //                 "updated_at": "2025-08-22T04:17:02.112Z",
        //                 "category_name": "Content",
        //                 "category_color": "#10B981",
        //                 "order": 6,
        //                 "repeator": "single",
        //                 "cssClass": "",
        //                 "uniqueId": "mel2ma1dkrz5ju2ar8j-1755852080376-vbqugjqf6"
        //             },
        //             {
        //                 "name": "faqs",
        //                 "category_id": "cat2",
        //                 "html_content": "<div data-component=\"faqs\"><div\n      class=\"lgx:dw-pb-[80px] mdx:dw-pb-[64px] dw-pb-[32px] dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n    >\n      <h1\n        class=\"dw-pb-[48px] dw-mb-2 dw-capitalize dw-mx-auto dw-text-[26px] mdx:dw-text-[48px] dw-font-semibold dw-text-heading dw-text-center dw-pb-5 dw-font-heading dw-leading-tight lgx:dw-px-0 dw-px-6\"\n      >\n        ${faqs_title}\n      </h1>\n\n      <div\n        id=\"accordion\"\n        class=\"dw-w-full mdl:dw-w-[60%] mdl:dw-px-0 dw-px-8 dw-mx-auto\"\n      >\n        <div class=\"accordion\">{{faqs_questions}}</div>\n      </div>\n    </div></div>",
        //                 "css_content": "",
        //                 "js_content": " document.addEventListener(\"DOMContentLoaded\", () => {\n        const accordions = document.querySelectorAll(\".accordion-btn\");\n\n        accordions.forEach((btn) => {\n          btn.addEventListener(\"click\", () => {\n            const body = btn.nextElementSibling;\n\n            body.classList.toggle(\"dw-hidden\");\n\n            accordions.forEach((otherBtn) => {\n              if (otherBtn !== btn) {\n                otherBtn.nextElementSibling.classList.add(\"dw-hidden\");\n              }\n            });\n          });\n        });\n      });",
        //                 "placeholders": [
        //                     "faqs_title"
        //                 ],
        //                 "preview_image": "",
        //                 "version": 1,
        //                 "status": "draft",
        //                 "tags": [],
        //                 "thumbnail_url": "",
        //                 "id": "mel3dogap4t0oax8x8g",
        //                 "created_at": "2025-08-21T07:40:29.578Z",
        //                 "updated_at": "2025-08-22T04:20:06.258Z",
        //                 "category_name": "Content",
        //                 "category_color": "#10B981",
        //                 "order": 7,
        //                 "repeator": "repeat",
        //                 "cssClass": "",
        //                 "uniqueId": "mel3dogap4t0oax8x8g-1755852086182-bcsbfmz1n",
        //                 "repeatedComponentId": "meldzsff2p93tgx5017",
        //                 "repeatedComponentName": [
        //                     "faqs_questions"
        //                 ],
        //                 "repeatedComponent": {
        //                     "name": "faq_question",
        //                     "category_id": "cat2",
        //                     "html_content": "<div data-component=\"faq_question\"><div class=\"dw-border-t dw-border-[#ddd] dw-py-[15px]\">\n  <button\n    type=\"button\"\n    class=\"accordion-btn dw-w-full dw-text-left dw-flex dw-justify-between dw-items-center dw-font-heading dw-font-semibold dw-text-lg dw-mb-2 dw-leading-tight\"\n  >\n    ${faq_question}\n  </button>\n  <div class=\"dw-mt-2 accordion-body dw-hidden\">\n    <p class=\"dw-text-base\">${faq_answer}</p>\n  </div>\n</div>\n</div>",
        //                     "css_content": "",
        //                     "js_content": "",
        //                     "placeholders": [
        //                         "faq_question",
        //                         "faq_answer"
        //                     ],
        //                     "preview_image": "",
        //                     "version": 1,
        //                     "status": "draft",
        //                     "tags": [],
        //                     "thumbnail_url": "",
        //                     "id": "meldzsff2p93tgx5017",
        //                     "created_at": "2025-08-21T12:37:37.323Z",
        //                     "updated_at": "2025-08-21T12:37:37.323Z",
        //                     "category_name": "Content",
        //                     "category_color": "#10B981"
        //                 }
        //             },
        //             {
        //                 "name": "divider_60",
        //                 "category_id": "cat2",
        //                 "html_content": "<div\n  class=\"dw-mx-auto dw-w-full dw-px-6 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n>\n  <hr\n    class=\"dw-border-t dw-border-theme-divider dw-w-[60%] dw-mx-auto dw-opacity-100 dw-my-14\"\n  />\n</div>",
        //                 "css_content": "",
        //                 "js_content": "",
        //                 "placeholders": [],
        //                 "preview_image": "",
        //                 "version": 1,
        //                 "status": "draft",
        //                 "tags": [],
        //                 "thumbnail_url": "",
        //                 "id": "mel64wfe2bdmijws9vo",
        //                 "created_at": "2025-08-21T08:57:38.858Z",
        //                 "updated_at": "2025-08-21T08:57:38.858Z",
        //                 "category_name": "Content",
        //                 "category_color": "#10B981",
        //                 "order": 8,
        //                 "repeator": "single",
        //                 "cssClass": "",
        //                 "uniqueId": "mel64wfe2bdmijws9vo-1755852098284-4i7wuvfki"
        //             },
        //             {
        //                 "name": "divider_100",
        //                 "category_id": "cat2",
        //                 "html_content": "<div\n  class=\"dw-mx-auto dw-w-full dw-px-8 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n>\n  <hr\n    class=\"dw-border-t dw-border-theme-divider dw-mx-auto dw-opacity-100 dw-my-14\"\n  />\n</div>",
        //                 "css_content": "",
        //                 "js_content": "",
        //                 "placeholders": [],
        //                 "preview_image": "",
        //                 "version": 1,
        //                 "status": "draft",
        //                 "tags": [],
        //                 "thumbnail_url": "",
        //                 "id": "mel65imuhz888tp56ts",
        //                 "created_at": "2025-08-21T08:58:07.638Z",
        //                 "updated_at": "2025-08-21T08:58:07.638Z",
        //                 "category_name": "Content",
        //                 "category_color": "#10B981",
        //                 "order": 9,
        //                 "repeator": "single",
        //                 "cssClass": "",
        //                 "uniqueId": "mel65imuhz888tp56ts-1755852099574-asb41r557"
        //             },
        //             {
        //                 "name": "worked_with",
        //                 "category_id": "cat2",
        //                 "html_content": "<div data-component=\"worked_with\"><div\n  class=\"dw-my-16 dw-mx-auto dw-w-full dw-px-4 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgl:dw-px-4 lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdl:dw-px-4 mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smm:dw-px-4 smx:dw-max-w-[700px] smx:dw-px-4\"\n>\n  <p\n    class=\"dw-font-heading dw-text-center xlx:dw-text-[24px] lgx:dw-text-[22px] dw-text-[20px] dw-font-semibold\"\n  >\n    ${worked_with_title}\n  </p>\n\n  <div\n    class=\"dw-grid dw-grid-cols-1 smm:dw-grid-cols-2 mdx:dw-grid-cols-6 mdx:dw-gap-6 dw-gap-y-8 dw-justify-center dw-pt-[64px] dw-px-[48px] xl1:dw-pt-[64px] xl1:dw-px-[48px] dw-pb-[30px] lgs:dw-pt-[64px] xlx:dw-px-0 lgx:dw-px-[16px] mdx:dw-px-[32px]\"\n  >\n    {{work_with_list}}\n  </div>\n</div>\n</div>",
        //                 "css_content": "",
        //                 "js_content": "",
        //                 "placeholders": [
        //                     "worked_with_title"
        //                 ],
        //                 "preview_image": "",
        //                 "version": 1,
        //                 "status": "draft",
        //                 "tags": [],
        //                 "thumbnail_url": "",
        //                 "id": "mel727wfcnonno0e1o5",
        //                 "created_at": "2025-08-21T09:23:33.375Z",
        //                 "updated_at": "2025-08-21T11:57:05.540Z",
        //                 "category_name": "Content",
        //                 "category_color": "#10B981",
        //                 "order": 10,
        //                 "repeator": "repeat",
        //                 "cssClass": "",
        //                 "uniqueId": "mel727wfcnonno0e1o5-1755852101033-epnl0jmv2",
        //                 "repeatedComponentId": "melcn35dh75ptnkoclg",
        //                 "repeatedComponentName": [
        //                     "work_with_list"
        //                 ],
        //                 "repeatedComponent": {
        //                     "name": "work_with_organization_card",
        //                     "category_id": "cat2",
        //                     "html_content": "<div data-component=\"work_with_organization_card\"><img\n  src=\"${img-organization_logo}\"\n  alt=\"${organization_name}\"\n  class=\"dw-max-h-[60px] dw-object-contain dw-mx-auto\"\n/>\n</div>",
        //                     "css_content": "",
        //                     "js_content": "",
        //                     "placeholders": [
        //                         "img-organization_logo",
        //                         "organization_name"
        //                     ],
        //                     "preview_image": "",
        //                     "version": 1,
        //                     "status": "draft",
        //                     "tags": [],
        //                     "thumbnail_url": "",
        //                     "id": "melcn35dh75ptnkoclg",
        //                     "created_at": "2025-08-21T11:59:45.073Z",
        //                     "updated_at": "2025-08-22T05:04:51.753Z",
        //                     "category_name": "Content",
        //                     "category_color": "#10B981"
        //                 }
        //             },
        //             {
        //                 "name": "testimonial",
        //                 "category_id": "cat2",
        //                 "html_content": "<div data-component=\"testimonial\"><section\n  class=\"dw-my-16 dw-bg-theme-cardBg dw-bg-[url('${img-background-pattern-bg-image}')] dw-bg-top dw-bg-no-repeat dw-bg-cover xlx:dw-py-24 mdx:dw-py-[64px] dw-py-[48px]\"\n>\n  <div\n    class=\"dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px] dw-px-8\"\n  >\n    <div class=\"dw-flex dw-flex-col dw-items-center\">\n      <h2\n        class=\"dw-font-heading dw-text-center mdx:dw-text-[48px] dw-text-[26px] dw-pb-4 dw-pb-6 dw-font-semibold dw-w-full\"\n      >\n        ${testimonial_title}\n      </h2>\n      <div\n        class=\"dw-grid dw-grid-cols-1 mdx:dw-grid-cols-2 mdl:dw-grid-cols-4 dw-gap-[14px] dw-gap-y-[25px] dw-w-full\"\n      >\n        {{testimonial_cards}}\n      </div>\n    </div>\n  </div>\n</section>\n</div>",
        //                 "css_content": "",
        //                 "js_content": "",
        //                 "placeholders": [
        //                     "img-background-pattern-bg-image",
        //                     "testimonial_title"
        //                 ],
        //                 "preview_image": "",
        //                 "version": 1,
        //                 "status": "draft",
        //                 "tags": [],
        //                 "thumbnail_url": "",
        //                 "id": "mel7pjzof70xdrnynvd",
        //                 "created_at": "2025-08-21T09:41:42.132Z",
        //                 "updated_at": "2025-08-22T05:15:40.170Z",
        //                 "category_name": "Content",
        //                 "category_color": "#10B981",
        //                 "order": 11,
        //                 "repeator": "repeat",
        //                 "cssClass": "",
        //                 "uniqueId": "mel7pjzof70xdrnynvd-1755852120348-58tceb6s8",
        //                 "repeatedComponentId": "melcr47v225ngp43it9",
        //                 "repeatedComponentName": [
        //                     "testimonial_cards"
        //                 ],
        //                 "repeatedComponent": {
        //                     "name": "testimonial_card",
        //                     "category_id": "cat2",
        //                     "html_content": "<div data-component=\"testimonial_card\"><div\n  class=\"dw-bg-body-bg dw-p-[20px_20px_40px] dw-rounded-[16px] dw-h-[315px] dw-shadow-[1px_1px_7px_rgba(0,0,0,0.13),0_0_2px_rgba(0,0,0,0.05)] dw-transition-transform dw-duration-300 dw-ease-out hover:dw--translate-y-1.5\"\n>\n  <div class=\"dw-flex dw-items-center dw-gap-3\">\n    <img\n      src=\"${img-user-image}\"\n      alt=\"${user_name}\"\n      class=\"dw-w-[60px] dw-h-[60px] dw-rounded-full dw-object-cover\"\n    />\n    <p\n      class=\"dw-mb-0 dw-font-bold xl1:dw-text-[18px] lgx:dw-text-[14px] dw-text-[16px]\"\n    >\n      ${user_name}\n    </p>\n  </div>\n  <div class=\"dw-text-yellow-500 dw-text-[25px] dw-leading-none dw-py-[10px]\">\n    ★★★★★\n  </div>\n  <p class=\"xl1:dw-text-[16px] dw-text-[14px]\">${testimonial}</p>\n</div>\n</div>",
        //                     "css_content": "",
        //                     "js_content": "",
        //                     "placeholders": [
        //                         "img-user-image",
        //                         "user_name",
        //                         "testimonial"
        //                     ],
        //                     "preview_image": "",
        //                     "version": 1,
        //                     "status": "draft",
        //                     "tags": [],
        //                     "thumbnail_url": "",
        //                     "id": "melcr47v225ngp43it9",
        //                     "created_at": "2025-08-21T12:02:53.083Z",
        //                     "updated_at": "2025-08-22T05:05:20.861Z",
        //                     "category_name": "Content",
        //                     "category_color": "#10B981"
        //                 }
        //             },
        //             {
        //                 "name": "we_offer_list",
        //                 "category_id": "cat2",
        //                 "html_content": "<div data-component=\"we_offer_list\">\n  <div\n    class=\"dw-my-16 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px] dw-px-[2rem]\"\n  >\n    <h2\n      class=\"dw-font-heading dw-text-center mdx:dw-text-[48px] dw-text-[26px] mdx:dw-pb-[48px] dw-pb-[20px] dw-font-semibold dw-w-full lgl:dw-w-[72%] lgs:dw-w-[80%] dw-mx-auto dw-leading-[30px] mdx:dw-leading-[55px] xl1:dw-leading-[60px]\"\n    >\n      ${we_offer_services_title}\n    </h2>\n\n    <div\n      class=\"dw-grid mdl:dw-grid-cols-2 dw-gap-x-[6px] mdx:dw-gap-x-[40px] lgx:dw-gap-x-[80px] mdx:dw-px-0 dw-px-[6px]\"\n    >\n      {{we_offer_services_list}}\n    </div>\n  </div>\n</div>\n",
        //                 "css_content": "",
        //                 "js_content": "",
        //                 "placeholders": [
        //                     "we_offer_services_title"
        //                 ],
        //                 "preview_image": "",
        //                 "version": 1,
        //                 "status": "draft",
        //                 "tags": [],
        //                 "thumbnail_url": "",
        //                 "id": "meld7qzw23ey3e800oc",
        //                 "created_at": "2025-08-21T12:15:49.100Z",
        //                 "updated_at": "2025-08-22T08:40:00.617Z",
        //                 "category_name": "Content",
        //                 "category_color": "#10B981",
        //                 "order": 12,
        //                 "repeator": "repeat",
        //                 "cssClass": "",
        //                 "uniqueId": "meld7qzw23ey3e800oc-1755852135835-a882ujexy",
        //                 "repeatedComponentId": "melde2eqwtkzw19v4z",
        //                 "repeatedComponentName": [
        //                     "we_offer_services_list"
        //                 ],
        //                 "repeatedComponent": {
        //                     "name": "we_offer_card",
        //                     "category_id": "cat2",
        //                     "html_content": "<div data-component=\"we_offer_card\"><div id=\"we-offer-card\">\n  <h2\n    class=\"lgm:dw-text-[24px] dw-text-[20px] lgx:dw-text-[22px] dw-font-semibold dw-pt-[16px] mdl:dw-pb-[12px] dw-pb-[20px] dw-font-heading\"\n  >\n    <a href=\"${offer_page_redirect_url}\">${service_name} </a>\n  </h2>\n  <img\n    src=\"${img-offer-service-image}\"\n    alt=\"offer-service-image\"\n    class=\"dw-rounded-[16px] dw-w-full dw-mb-3\"\n  />\n  <p class=\"dw-text-[16px] xl1:dw-[18px] dw-mb-4\">\n    ${service_description}\n  </p>\n  <a\n    href=\"${offer_page_redirect_url}\"\n    class=\"dw-text-theme-main dw-text-[14px] dw-my-2 dw-font-medium dw-flex dw-items-center dw-gap-1\"\n  >\n    Learn More\n    <svg\n      class=\"dw-w-[14px] dw-h-[14px]\"\n      viewBox=\"0 0 448 512\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <path\n        class=\"dw-fill-theme-main\"\n        d=\"M190.5 66.9l22.2-22.2c9.4-9.4 24.6-9.4 33.9 0L441 239c9.4 9.4 9.4 24.6 0 33.9L246.6 467.3c-9.4 9.4-24.6 9.4-33.9 0l-22.2-22.2c-9.5-9.5-9.3-25 .4-34.3L311.4 296H24c-13.3 0-24-10.7-24-24v-32c0-13.3 10.7-24 24-24h287.4L190.9 101.2c-9.8-9.3-10-24.8-.4-34.3z\"\n      />\n    </svg>\n  </a>\n</div></div>",
        //                     "css_content": "",
        //                     "js_content": "",
        //                     "placeholders": [
        //                         "offer_page_redirect_url",
        //                         "service_name",
        //                         "img-offer-service-image",
        //                         "service_description"
        //                     ],
        //                     "preview_image": "",
        //                     "version": 1,
        //                     "status": "draft",
        //                     "tags": [],
        //                     "thumbnail_url": "",
        //                     "id": "melde2eqwtkzw19v4z",
        //                     "created_at": "2025-08-21T12:20:43.826Z",
        //                     "updated_at": "2025-08-22T06:49:44.492Z",
        //                     "category_name": "Content",
        //                     "category_color": "#10B981"
        //                 }
        //             }
        //         ],
        //         "version": "v1",
        //         "pagePlaceHolder": [
        //             {
        //                 "categoryName": "hero_section_home_page",
        //                 "CategoryId": "mejxa6hnolket8gfqqg",
        //                 "placeholders": [
        //                     "hero_section_title",
        //                     "hero_section_description",
        //                     "contact_number",
        //                     "hero_section_extra_text",
        //                     "contact_number_button_label",
        //                     "quote_url",
        //                     "get_quote_button_text",
        //                     "img-hero-section-image",
        //                     "img-advantage-image",
        //                     "hero_section_card1_content",
        //                     "hero_section_card2_content",
        //                     "hero_section_card3_content"
        //                 ]
        //             },
        //             {
        //                 "categoryName": "why_section_home_page",
        //                 "CategoryId": "mejxkuo2q6g7dim380s",
        //                 "placeholders": [
        //                     "img-background-pattern-bg-image",
        //                     "why_section_heading",
        //                     "why_section_description",
        //                     "img-why-section-image",
        //                     "img-advantage-image",
        //                     "why_section_card1_content",
        //                     "why_section_card2_content",
        //                     "why_section_card3_content"
        //                 ]
        //             },
        //             {
        //                 "categoryName": "bus_grid_list",
        //                 "CategoryId": "mejz65z1z7hhj4rxbr",
        //                 "placeholders": [
        //                     "buses_list_heading"
        //                 ],
        //                 "repeatedComponentName": [
        //                     "busesList"
        //                 ],
        //                 "repeatedComponentId": "melbj7f65tmd8ocep4a",
        //                 "repeatedComponent": {
        //                     "name": "bud_grid_card",
        //                     "category_id": "cat2",
        //                     "html_content": "<div data-component=\"bud_grid_card\"><div\n  class=\"dw-border dw-border-theme-cardBorder dw-rounded-[32px] dw-bg-theme-cardBg xl1:dw-p-[3rem_2rem_1.5rem] dw-p-[2.5rem_1rem_1rem] dw-min-h-full dw-relative\"\n>\n  <span\n    class=\"dw-absolute dw-z-10 dw-bg-body-bg dw-text-theme-lowOpacityTextColor dw-font-normal dw-border dw-shadow-sm dw-text-[14px] dw-px-[15px] dw-py-[8px] dw-ml-[10px] dw--translate-y-[20px] dw-rounded-full\"\n  >\n    ${bus_price_range}\n  </span>\n\n  <img\n    src=\"${img-bus-image}\"\n    class=\"dw-rounded-[16px] dw-w-full dw-h-auto\"\n    alt=\"img-busImage\"\n  />\n  <img\n    src=\"${img-bus-interior}\"\n    class=\"dw-rounded-[16px] dw-w-full dw-h-auto dw-mt-4\"\n    alt=\"img-busInterior\"\n  />\n\n  <h3\n    class=\"dw-font-semibold lgm:dw-text-[24px] lgx:dw-text-[22px] dw-text-[20px] dw-leading-[32px] dw-uppercase dw-tracking-[-0.176px] dw-text-center dw-pt-[32px] dw-pb-[25px] dw-font-heading dw-mb-2\"\n  >\n    <a href=\"${view_Bus_Url}\"> ${bus_title} </a>\n  </h3>\n\n  <div class=\"dw-flex dw-justify-center dw-gap-4 dw-pb-[5px]\">\n    <a\n      href=\"${view_Bus_Url}\"\n      class=\"dw-bg-body-text dw-text-white dw-border-2 dw-border-body-text dw-rounded-full dw-px-[16px] dw-py-[13px] dw-text-[18px] dw-leading-[24px] dw-font-medium dw-tracking-[-0.24px] hover:dw-border-theme-main hover:dw-bg-theme-main\"\n    >\n      ${bus_view_button_text}\n    </a>\n    <a\n      href=\"${quote_url}\"\n      class=\"dw-bg-theme-main dw-text-white dw-border-2 dw-border-theme-main dw-rounded-full dw-px-[16px] dw-py-[13px] dw-inline-flex dw-items-center dw-justify-center dw-text-[18px] dw-leading-[24px] dw-font-medium dw-tracking-[-0.24px]\"\n    >\n      ${get_quote_button_text}\n    </a>\n  </div>\n</div>\n</div>",
        //                     "css_content": "",
        //                     "js_content": "",
        //                     "placeholders": [
        //                         "bus_price_range",
        //                         "img-bus-image",
        //                         "img-bus-interior",
        //                         "view_Bus_Url",
        //                         "bus_title",
        //                         "bus_view_button_text",
        //                         "quote_url",
        //                         "get_quote_button_text"
        //                     ],
        //                     "preview_image": "",
        //                     "version": 1,
        //                     "status": "draft",
        //                     "tags": [],
        //                     "thumbnail_url": "",
        //                     "id": "melbj7f65tmd8ocep4a",
        //                     "created_at": "2025-08-21T11:28:44.370Z",
        //                     "updated_at": "2025-08-22T05:03:22.688Z",
        //                     "category_name": "Content",
        //                     "category_color": "#10B981"
        //                 }
        //             },
        //             {
        //                 "categoryName": "bus_amenities",
        //                 "CategoryId": "mel05d6jebm3z3va507",
        //                 "placeholders": [
        //                     "amenities_title"
        //                 ],
        //                 "repeatedComponentName": [
        //                     "amenities_cards"
        //                 ],
        //                 "repeatedComponentId": "melbmuizp786kngqmks",
        //                 "repeatedComponent": {
        //                     "name": "amenities_card",
        //                     "category_id": "cat2",
        //                     "html_content": "<div data-component=\"amenities_card\"><div\n  class=\"dw-flex dw-flex-col dw-justify-center dw-items-center dw-text-center dw-border dw-border-gray-200 dw-rounded-[16px] dw-min-h-[180px] dw-shadow-sm dw-px-4 dw-py-6\"\n>\n  <img src=\"${img-amenities_image}\" alt=\"amenities Image\" class=\"dw-mx-auto\" />\n  <p class=\"dw-pt-5 dw-font-medium dw-text-center xl1:dw-text-lg dw-text-base\">\n    ${amenities_text}\n  </p>\n</div>\n</div>",
        //                     "css_content": "",
        //                     "js_content": "",
        //                     "placeholders": [
        //                         "img-amenities_image",
        //                         "amenities_text"
        //                     ],
        //                     "preview_image": "",
        //                     "version": 1,
        //                     "status": "draft",
        //                     "tags": [],
        //                     "thumbnail_url": "",
        //                     "id": "melbmuizp786kngqmks",
        //                     "created_at": "2025-08-21T11:31:34.283Z",
        //                     "updated_at": "2025-08-21T11:31:34.283Z",
        //                     "category_name": "Content",
        //                     "category_color": "#10B981"
        //                 }
        //             },
        //             {
        //                 "categoryName": "note_sure",
        //                 "CategoryId": "mel0t7zfjeg1zxrr0m",
        //                 "placeholders": [
        //                     "note_sure_title",
        //                     "note_sure_description",
        //                     "contact_number",
        //                     "contact_number_button_label",
        //                     "quote_url",
        //                     "get_quote_button_text"
        //                 ]
        //             },
        //             {
        //                 "categoryName": "book_in_minutes",
        //                 "CategoryId": "mel11vphae1ej7eq137",
        //                 "placeholders": [
        //                     "img-book_in_minutes_cta_bg",
        //                     "book_in_minutes_title",
        //                     "contact_number",
        //                     "contact_number_button_label",
        //                     "quote_url",
        //                     "get_quote_button_text"
        //                 ]
        //             },
        //             {
        //                 "categoryName": "common_cta_section",
        //                 "CategoryId": "mel2ma1dkrz5ju2ar8j",
        //                 "placeholders": [
        //                     "cta_title",
        //                     "contact_number",
        //                     "contact_number_button_label",
        //                     "quote_url",
        //                     "get_quote_button_text",
        //                     "img-cta-review-photo",
        //                     "available_24_7_text",
        //                     "img-cta-bus-rental-image"
        //                 ]
        //             },
        //             {
        //                 "categoryName": "faqs",
        //                 "CategoryId": "mel3dogap4t0oax8x8g",
        //                 "placeholders": [
        //                     "faqs_title"
        //                 ],
        //                 "repeatedComponentName": [
        //                     "faqs_questions"
        //                 ],
        //                 "repeatedComponentId": "meldzsff2p93tgx5017",
        //                 "repeatedComponent": {
        //                     "name": "faq_question",
        //                     "category_id": "cat2",
        //                     "html_content": "<div data-component=\"faq_question\"><div class=\"dw-border-t dw-border-[#ddd] dw-py-[15px]\">\n  <button\n    type=\"button\"\n    class=\"accordion-btn dw-w-full dw-text-left dw-flex dw-justify-between dw-items-center dw-font-heading dw-font-semibold dw-text-lg dw-mb-2 dw-leading-tight\"\n  >\n    ${faq_question}\n  </button>\n  <div class=\"dw-mt-2 accordion-body dw-hidden\">\n    <p class=\"dw-text-base\">${faq_answer}</p>\n  </div>\n</div>\n</div>",
        //                     "css_content": "",
        //                     "js_content": "",
        //                     "placeholders": [
        //                         "faq_question",
        //                         "faq_answer"
        //                     ],
        //                     "preview_image": "",
        //                     "version": 1,
        //                     "status": "draft",
        //                     "tags": [],
        //                     "thumbnail_url": "",
        //                     "id": "meldzsff2p93tgx5017",
        //                     "created_at": "2025-08-21T12:37:37.323Z",
        //                     "updated_at": "2025-08-21T12:37:37.323Z",
        //                     "category_name": "Content",
        //                     "category_color": "#10B981"
        //                 }
        //             },
        //             {
        //                 "categoryName": "divider_60",
        //                 "CategoryId": "mel64wfe2bdmijws9vo",
        //                 "placeholders": []
        //             },
        //             {
        //                 "categoryName": "divider_100",
        //                 "CategoryId": "mel65imuhz888tp56ts",
        //                 "placeholders": []
        //             },
        //             {
        //                 "categoryName": "worked_with",
        //                 "CategoryId": "mel727wfcnonno0e1o5",
        //                 "placeholders": [
        //                     "worked_with_title"
        //                 ],
        //                 "repeatedComponentName": [
        //                     "work_with_list"
        //                 ],
        //                 "repeatedComponentId": "melcn35dh75ptnkoclg",
        //                 "repeatedComponent": {
        //                     "name": "work_with_organization_card",
        //                     "category_id": "cat2",
        //                     "html_content": "<div data-component=\"work_with_organization_card\"><img\n  src=\"${img-organization_logo}\"\n  alt=\"${organization_name}\"\n  class=\"dw-max-h-[60px] dw-object-contain dw-mx-auto\"\n/>\n</div>",
        //                     "css_content": "",
        //                     "js_content": "",
        //                     "placeholders": [
        //                         "img-organization_logo",
        //                         "organization_name"
        //                     ],
        //                     "preview_image": "",
        //                     "version": 1,
        //                     "status": "draft",
        //                     "tags": [],
        //                     "thumbnail_url": "",
        //                     "id": "melcn35dh75ptnkoclg",
        //                     "created_at": "2025-08-21T11:59:45.073Z",
        //                     "updated_at": "2025-08-22T05:04:51.753Z",
        //                     "category_name": "Content",
        //                     "category_color": "#10B981"
        //                 }
        //             },
        //             {
        //                 "categoryName": "testimonial",
        //                 "CategoryId": "mel7pjzof70xdrnynvd",
        //                 "placeholders": [
        //                     "img-background-pattern-bg-image",
        //                     "testimonial_title"
        //                 ],
        //                 "repeatedComponentName": [
        //                     "testimonial_cards"
        //                 ],
        //                 "repeatedComponentId": "melcr47v225ngp43it9",
        //                 "repeatedComponent": {
        //                     "name": "testimonial_card",
        //                     "category_id": "cat2",
        //                     "html_content": "<div data-component=\"testimonial_card\"><div\n  class=\"dw-bg-body-bg dw-p-[20px_20px_40px] dw-rounded-[16px] dw-h-[315px] dw-shadow-[1px_1px_7px_rgba(0,0,0,0.13),0_0_2px_rgba(0,0,0,0.05)] dw-transition-transform dw-duration-300 dw-ease-out hover:dw--translate-y-1.5\"\n>\n  <div class=\"dw-flex dw-items-center dw-gap-3\">\n    <img\n      src=\"${img-user-image}\"\n      alt=\"${user_name}\"\n      class=\"dw-w-[60px] dw-h-[60px] dw-rounded-full dw-object-cover\"\n    />\n    <p\n      class=\"dw-mb-0 dw-font-bold xl1:dw-text-[18px] lgx:dw-text-[14px] dw-text-[16px]\"\n    >\n      ${user_name}\n    </p>\n  </div>\n  <div class=\"dw-text-yellow-500 dw-text-[25px] dw-leading-none dw-py-[10px]\">\n    ★★★★★\n  </div>\n  <p class=\"xl1:dw-text-[16px] dw-text-[14px]\">${testimonial}</p>\n</div>\n</div>",
        //                     "css_content": "",
        //                     "js_content": "",
        //                     "placeholders": [
        //                         "img-user-image",
        //                         "user_name",
        //                         "testimonial"
        //                     ],
        //                     "preview_image": "",
        //                     "version": 1,
        //                     "status": "draft",
        //                     "tags": [],
        //                     "thumbnail_url": "",
        //                     "id": "melcr47v225ngp43it9",
        //                     "created_at": "2025-08-21T12:02:53.083Z",
        //                     "updated_at": "2025-08-22T05:05:20.861Z",
        //                     "category_name": "Content",
        //                     "category_color": "#10B981"
        //                 }
        //             },
        //             {
        //                 "categoryName": "we_offer_list",
        //                 "CategoryId": "meld7qzw23ey3e800oc",
        //                 "placeholders": [
        //                     "we_offer_services_title"
        //                 ],
        //                 "repeatedComponentName": [
        //                     "we_offer_services_list"
        //                 ],
        //                 "repeatedComponentId": "melde2eqwtkzw19v4z",
        //                 "repeatedComponent": {
        //                     "name": "we_offer_card",
        //                     "category_id": "cat2",
        //                     "html_content": "<div data-component=\"we_offer_card\"><div id=\"we-offer-card\">\n  <h2\n    class=\"lgm:dw-text-[24px] dw-text-[20px] lgx:dw-text-[22px] dw-font-semibold dw-pt-[16px] mdl:dw-pb-[12px] dw-pb-[20px] dw-font-heading\"\n  >\n    <a href=\"${offer_page_redirect_url}\">${service_name} </a>\n  </h2>\n  <img\n    src=\"${img-offer-service-image}\"\n    alt=\"offer-service-image\"\n    class=\"dw-rounded-[16px] dw-w-full dw-mb-3\"\n  />\n  <p class=\"dw-text-[16px] xl1:dw-[18px] dw-mb-4\">\n    ${service_description}\n  </p>\n  <a\n    href=\"${offer_page_redirect_url}\"\n    class=\"dw-text-theme-main dw-text-[14px] dw-my-2 dw-font-medium dw-flex dw-items-center dw-gap-1\"\n  >\n    Learn More\n    <svg\n      class=\"dw-w-[14px] dw-h-[14px]\"\n      viewBox=\"0 0 448 512\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <path\n        class=\"dw-fill-theme-main\"\n        d=\"M190.5 66.9l22.2-22.2c9.4-9.4 24.6-9.4 33.9 0L441 239c9.4 9.4 9.4 24.6 0 33.9L246.6 467.3c-9.4 9.4-24.6 9.4-33.9 0l-22.2-22.2c-9.5-9.5-9.3-25 .4-34.3L311.4 296H24c-13.3 0-24-10.7-24-24v-32c0-13.3 10.7-24 24-24h287.4L190.9 101.2c-9.8-9.3-10-24.8-.4-34.3z\"\n      />\n    </svg>\n  </a>\n</div></div>",
        //                     "css_content": "",
        //                     "js_content": "",
        //                     "placeholders": [
        //                         "offer_page_redirect_url",
        //                         "service_name",
        //                         "img-offer-service-image",
        //                         "service_description"
        //                     ],
        //                     "preview_image": "",
        //                     "version": 1,
        //                     "status": "draft",
        //                     "tags": [],
        //                     "thumbnail_url": "",
        //                     "id": "melde2eqwtkzw19v4z",
        //                     "created_at": "2025-08-21T12:20:43.826Z",
        //                     "updated_at": "2025-08-22T06:49:44.492Z",
        //                     "category_name": "Content",
        //                     "category_color": "#10B981"
        //                 }
        //             }
        //         ],
        //         "id": "meml1gk01ps1ylfq51x",
        //         "created_at": "2025-08-22T08:42:38.736Z",
        //         "updated_at": "2025-08-22T08:42:38.736Z",
        //         "url": "test",
        //         "type": "static",
        //         "showNavbar": true,
        //         "navPosition": 0,
        //         "order": 0,
        //         "repeator": "single",
        //         "position": 0,
        //         "placeHolder": [
        //             {
        //                 "categoryName": "hero_section_home_page",
        //                 "CategoryId": "mejxa6hnolket8gfqqg",
        //                 "placeholders": [
        //                     "hero_section_title",
        //                     "hero_section_description",
        //                     "contact_number",
        //                     "hero_section_extra_text",
        //                     "contact_number_button_label",
        //                     "quote_url",
        //                     "get_quote_button_text",
        //                     "img-hero-section-image",
        //                     "img-advantage-image",
        //                     "hero_section_card1_content",
        //                     "hero_section_card2_content",
        //                     "hero_section_card3_content"
        //                 ]
        //             },
        //             {
        //                 "categoryName": "why_section_home_page",
        //                 "CategoryId": "mejxkuo2q6g7dim380s",
        //                 "placeholders": [
        //                     "img-background-pattern-bg-image",
        //                     "why_section_heading",
        //                     "why_section_description",
        //                     "img-why-section-image",
        //                     "img-advantage-image",
        //                     "why_section_card1_content",
        //                     "why_section_card2_content",
        //                     "why_section_card3_content"
        //                 ]
        //             },
        //             {
        //                 "categoryName": "bus_grid_list",
        //                 "CategoryId": "mejz65z1z7hhj4rxbr",
        //                 "placeholders": [
        //                     "buses_list_heading"
        //                 ],
        //                 "repeatedComponentName": [
        //                     "busesList"
        //                 ],
        //                 "repeatedComponentId": "melbj7f65tmd8ocep4a",
        //                 "repeatedComponent": {
        //                     "name": "bud_grid_card",
        //                     "category_id": "cat2",
        //                     "html_content": "<div data-component=\"bud_grid_card\"><div\n  class=\"dw-border dw-border-theme-cardBorder dw-rounded-[32px] dw-bg-theme-cardBg xl1:dw-p-[3rem_2rem_1.5rem] dw-p-[2.5rem_1rem_1rem] dw-min-h-full dw-relative\"\n>\n  <span\n    class=\"dw-absolute dw-z-10 dw-bg-body-bg dw-text-theme-lowOpacityTextColor dw-font-normal dw-border dw-shadow-sm dw-text-[14px] dw-px-[15px] dw-py-[8px] dw-ml-[10px] dw--translate-y-[20px] dw-rounded-full\"\n  >\n    ${bus_price_range}\n  </span>\n\n  <img\n    src=\"${img-bus-image}\"\n    class=\"dw-rounded-[16px] dw-w-full dw-h-auto\"\n    alt=\"img-busImage\"\n  />\n  <img\n    src=\"${img-bus-interior}\"\n    class=\"dw-rounded-[16px] dw-w-full dw-h-auto dw-mt-4\"\n    alt=\"img-busInterior\"\n  />\n\n  <h3\n    class=\"dw-font-semibold lgm:dw-text-[24px] lgx:dw-text-[22px] dw-text-[20px] dw-leading-[32px] dw-uppercase dw-tracking-[-0.176px] dw-text-center dw-pt-[32px] dw-pb-[25px] dw-font-heading dw-mb-2\"\n  >\n    <a href=\"${view_Bus_Url}\"> ${bus_title} </a>\n  </h3>\n\n  <div class=\"dw-flex dw-justify-center dw-gap-4 dw-pb-[5px]\">\n    <a\n      href=\"${view_Bus_Url}\"\n      class=\"dw-bg-body-text dw-text-white dw-border-2 dw-border-body-text dw-rounded-full dw-px-[16px] dw-py-[13px] dw-text-[18px] dw-leading-[24px] dw-font-medium dw-tracking-[-0.24px] hover:dw-border-theme-main hover:dw-bg-theme-main\"\n    >\n      ${bus_view_button_text}\n    </a>\n    <a\n      href=\"${quote_url}\"\n      class=\"dw-bg-theme-main dw-text-white dw-border-2 dw-border-theme-main dw-rounded-full dw-px-[16px] dw-py-[13px] dw-inline-flex dw-items-center dw-justify-center dw-text-[18px] dw-leading-[24px] dw-font-medium dw-tracking-[-0.24px]\"\n    >\n      ${get_quote_button_text}\n    </a>\n  </div>\n</div>\n</div>",
        //                     "css_content": "",
        //                     "js_content": "",
        //                     "placeholders": [
        //                         "bus_price_range",
        //                         "img-bus-image",
        //                         "img-bus-interior",
        //                         "view_Bus_Url",
        //                         "bus_title",
        //                         "bus_view_button_text",
        //                         "quote_url",
        //                         "get_quote_button_text"
        //                     ],
        //                     "preview_image": "",
        //                     "version": 1,
        //                     "status": "draft",
        //                     "tags": [],
        //                     "thumbnail_url": "",
        //                     "id": "melbj7f65tmd8ocep4a",
        //                     "created_at": "2025-08-21T11:28:44.370Z",
        //                     "updated_at": "2025-08-22T05:03:22.688Z",
        //                     "category_name": "Content",
        //                     "category_color": "#10B981"
        //                 }
        //             },
        //             {
        //                 "categoryName": "bus_amenities",
        //                 "CategoryId": "mel05d6jebm3z3va507",
        //                 "placeholders": [
        //                     "amenities_title"
        //                 ],
        //                 "repeatedComponentName": [
        //                     "amenities_cards"
        //                 ],
        //                 "repeatedComponentId": "melbmuizp786kngqmks",
        //                 "repeatedComponent": {
        //                     "name": "amenities_card",
        //                     "category_id": "cat2",
        //                     "html_content": "<div data-component=\"amenities_card\"><div\n  class=\"dw-flex dw-flex-col dw-justify-center dw-items-center dw-text-center dw-border dw-border-gray-200 dw-rounded-[16px] dw-min-h-[180px] dw-shadow-sm dw-px-4 dw-py-6\"\n>\n  <img src=\"${img-amenities_image}\" alt=\"amenities Image\" class=\"dw-mx-auto\" />\n  <p class=\"dw-pt-5 dw-font-medium dw-text-center xl1:dw-text-lg dw-text-base\">\n    ${amenities_text}\n  </p>\n</div>\n</div>",
        //                     "css_content": "",
        //                     "js_content": "",
        //                     "placeholders": [
        //                         "img-amenities_image",
        //                         "amenities_text"
        //                     ],
        //                     "preview_image": "",
        //                     "version": 1,
        //                     "status": "draft",
        //                     "tags": [],
        //                     "thumbnail_url": "",
        //                     "id": "melbmuizp786kngqmks",
        //                     "created_at": "2025-08-21T11:31:34.283Z",
        //                     "updated_at": "2025-08-21T11:31:34.283Z",
        //                     "category_name": "Content",
        //                     "category_color": "#10B981"
        //                 }
        //             },
        //             {
        //                 "categoryName": "note_sure",
        //                 "CategoryId": "mel0t7zfjeg1zxrr0m",
        //                 "placeholders": [
        //                     "note_sure_title",
        //                     "note_sure_description",
        //                     "contact_number",
        //                     "contact_number_button_label",
        //                     "quote_url",
        //                     "get_quote_button_text"
        //                 ]
        //             },
        //             {
        //                 "categoryName": "book_in_minutes",
        //                 "CategoryId": "mel11vphae1ej7eq137",
        //                 "placeholders": [
        //                     "img-book_in_minutes_cta_bg",
        //                     "book_in_minutes_title",
        //                     "contact_number",
        //                     "contact_number_button_label",
        //                     "quote_url",
        //                     "get_quote_button_text"
        //                 ]
        //             },
        //             {
        //                 "categoryName": "common_cta_section",
        //                 "CategoryId": "mel2ma1dkrz5ju2ar8j",
        //                 "placeholders": [
        //                     "cta_title",
        //                     "contact_number",
        //                     "contact_number_button_label",
        //                     "quote_url",
        //                     "get_quote_button_text",
        //                     "img-cta-review-photo",
        //                     "available_24_7_text",
        //                     "img-cta-bus-rental-image"
        //                 ]
        //             },
        //             {
        //                 "categoryName": "faqs",
        //                 "CategoryId": "mel3dogap4t0oax8x8g",
        //                 "placeholders": [
        //                     "faqs_title"
        //                 ],
        //                 "repeatedComponentName": [
        //                     "faqs_questions"
        //                 ],
        //                 "repeatedComponentId": "meldzsff2p93tgx5017",
        //                 "repeatedComponent": {
        //                     "name": "faq_question",
        //                     "category_id": "cat2",
        //                     "html_content": "<div data-component=\"faq_question\"><div class=\"dw-border-t dw-border-[#ddd] dw-py-[15px]\">\n  <button\n    type=\"button\"\n    class=\"accordion-btn dw-w-full dw-text-left dw-flex dw-justify-between dw-items-center dw-font-heading dw-font-semibold dw-text-lg dw-mb-2 dw-leading-tight\"\n  >\n    ${faq_question}\n  </button>\n  <div class=\"dw-mt-2 accordion-body dw-hidden\">\n    <p class=\"dw-text-base\">${faq_answer}</p>\n  </div>\n</div>\n</div>",
        //                     "css_content": "",
        //                     "js_content": "",
        //                     "placeholders": [
        //                         "faq_question",
        //                         "faq_answer"
        //                     ],
        //                     "preview_image": "",
        //                     "version": 1,
        //                     "status": "draft",
        //                     "tags": [],
        //                     "thumbnail_url": "",
        //                     "id": "meldzsff2p93tgx5017",
        //                     "created_at": "2025-08-21T12:37:37.323Z",
        //                     "updated_at": "2025-08-21T12:37:37.323Z",
        //                     "category_name": "Content",
        //                     "category_color": "#10B981"
        //                 }
        //             },
        //             {
        //                 "categoryName": "divider_60",
        //                 "CategoryId": "mel64wfe2bdmijws9vo",
        //                 "placeholders": []
        //             },
        //             {
        //                 "categoryName": "divider_100",
        //                 "CategoryId": "mel65imuhz888tp56ts",
        //                 "placeholders": []
        //             },
        //             {
        //                 "categoryName": "worked_with",
        //                 "CategoryId": "mel727wfcnonno0e1o5",
        //                 "placeholders": [
        //                     "worked_with_title"
        //                 ],
        //                 "repeatedComponentName": [
        //                     "work_with_list"
        //                 ],
        //                 "repeatedComponentId": "melcn35dh75ptnkoclg",
        //                 "repeatedComponent": {
        //                     "name": "work_with_organization_card",
        //                     "category_id": "cat2",
        //                     "html_content": "<div data-component=\"work_with_organization_card\"><img\n  src=\"${img-organization_logo}\"\n  alt=\"${organization_name}\"\n  class=\"dw-max-h-[60px] dw-object-contain dw-mx-auto\"\n/>\n</div>",
        //                     "css_content": "",
        //                     "js_content": "",
        //                     "placeholders": [
        //                         "img-organization_logo",
        //                         "organization_name"
        //                     ],
        //                     "preview_image": "",
        //                     "version": 1,
        //                     "status": "draft",
        //                     "tags": [],
        //                     "thumbnail_url": "",
        //                     "id": "melcn35dh75ptnkoclg",
        //                     "created_at": "2025-08-21T11:59:45.073Z",
        //                     "updated_at": "2025-08-22T05:04:51.753Z",
        //                     "category_name": "Content",
        //                     "category_color": "#10B981"
        //                 }
        //             },
        //             {
        //                 "categoryName": "testimonial",
        //                 "CategoryId": "mel7pjzof70xdrnynvd",
        //                 "placeholders": [
        //                     "img-background-pattern-bg-image",
        //                     "testimonial_title"
        //                 ],
        //                 "repeatedComponentName": [
        //                     "testimonial_cards"
        //                 ],
        //                 "repeatedComponentId": "melcr47v225ngp43it9",
        //                 "repeatedComponent": {
        //                     "name": "testimonial_card",
        //                     "category_id": "cat2",
        //                     "html_content": "<div data-component=\"testimonial_card\"><div\n  class=\"dw-bg-body-bg dw-p-[20px_20px_40px] dw-rounded-[16px] dw-h-[315px] dw-shadow-[1px_1px_7px_rgba(0,0,0,0.13),0_0_2px_rgba(0,0,0,0.05)] dw-transition-transform dw-duration-300 dw-ease-out hover:dw--translate-y-1.5\"\n>\n  <div class=\"dw-flex dw-items-center dw-gap-3\">\n    <img\n      src=\"${img-user-image}\"\n      alt=\"${user_name}\"\n      class=\"dw-w-[60px] dw-h-[60px] dw-rounded-full dw-object-cover\"\n    />\n    <p\n      class=\"dw-mb-0 dw-font-bold xl1:dw-text-[18px] lgx:dw-text-[14px] dw-text-[16px]\"\n    >\n      ${user_name}\n    </p>\n  </div>\n  <div class=\"dw-text-yellow-500 dw-text-[25px] dw-leading-none dw-py-[10px]\">\n    ★★★★★\n  </div>\n  <p class=\"xl1:dw-text-[16px] dw-text-[14px]\">${testimonial}</p>\n</div>\n</div>",
        //                     "css_content": "",
        //                     "js_content": "",
        //                     "placeholders": [
        //                         "img-user-image",
        //                         "user_name",
        //                         "testimonial"
        //                     ],
        //                     "preview_image": "",
        //                     "version": 1,
        //                     "status": "draft",
        //                     "tags": [],
        //                     "thumbnail_url": "",
        //                     "id": "melcr47v225ngp43it9",
        //                     "created_at": "2025-08-21T12:02:53.083Z",
        //                     "updated_at": "2025-08-22T05:05:20.861Z",
        //                     "category_name": "Content",
        //                     "category_color": "#10B981"
        //                 }
        //             },
        //             {
        //                 "categoryName": "we_offer_list",
        //                 "CategoryId": "meld7qzw23ey3e800oc",
        //                 "placeholders": [
        //                     "we_offer_services_title"
        //                 ],
        //                 "repeatedComponentName": [
        //                     "we_offer_services_list"
        //                 ],
        //                 "repeatedComponentId": "melde2eqwtkzw19v4z",
        //                 "repeatedComponent": {
        //                     "name": "we_offer_card",
        //                     "category_id": "cat2",
        //                     "html_content": "<div data-component=\"we_offer_card\"><div id=\"we-offer-card\">\n  <h2\n    class=\"lgm:dw-text-[24px] dw-text-[20px] lgx:dw-text-[22px] dw-font-semibold dw-pt-[16px] mdl:dw-pb-[12px] dw-pb-[20px] dw-font-heading\"\n  >\n    <a href=\"${offer_page_redirect_url}\">${service_name} </a>\n  </h2>\n  <img\n    src=\"${img-offer-service-image}\"\n    alt=\"offer-service-image\"\n    class=\"dw-rounded-[16px] dw-w-full dw-mb-3\"\n  />\n  <p class=\"dw-text-[16px] xl1:dw-[18px] dw-mb-4\">\n    ${service_description}\n  </p>\n  <a\n    href=\"${offer_page_redirect_url}\"\n    class=\"dw-text-theme-main dw-text-[14px] dw-my-2 dw-font-medium dw-flex dw-items-center dw-gap-1\"\n  >\n    Learn More\n    <svg\n      class=\"dw-w-[14px] dw-h-[14px]\"\n      viewBox=\"0 0 448 512\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <path\n        class=\"dw-fill-theme-main\"\n        d=\"M190.5 66.9l22.2-22.2c9.4-9.4 24.6-9.4 33.9 0L441 239c9.4 9.4 9.4 24.6 0 33.9L246.6 467.3c-9.4 9.4-24.6 9.4-33.9 0l-22.2-22.2c-9.5-9.5-9.3-25 .4-34.3L311.4 296H24c-13.3 0-24-10.7-24-24v-32c0-13.3 10.7-24 24-24h287.4L190.9 101.2c-9.8-9.3-10-24.8-.4-34.3z\"\n      />\n    </svg>\n  </a>\n</div></div>",
        //                     "css_content": "",
        //                     "js_content": "",
        //                     "placeholders": [
        //                         "offer_page_redirect_url",
        //                         "service_name",
        //                         "img-offer-service-image",
        //                         "service_description"
        //                     ],
        //                     "preview_image": "",
        //                     "version": 1,
        //                     "status": "draft",
        //                     "tags": [],
        //                     "thumbnail_url": "",
        //                     "id": "melde2eqwtkzw19v4z",
        //                     "created_at": "2025-08-21T12:20:43.826Z",
        //                     "updated_at": "2025-08-22T06:49:44.492Z",
        //                     "category_name": "Content",
        //                     "category_color": "#10B981"
        //                 }
        //             }
        //         ]
        //     }
        // ],
        "pages": [
            {
                "name": "test",
                "slug": "test",
                "meta_title": "",
                "meta_description": "",
                "custom_css": "",
                "custom_js": "",
                "full_page_content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>test</title>\n  <!-- Tailwind CSS - Generated locally to avoid CDN warnings -->\n  <style>\n      @font-face {\n        font-family: Inter;\n        font-style: normal;\n        font-weight: 400;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n\n      @font-face {\n        font-family: Inter;\n        font-style: normal;\n        font-weight: 500;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n\n      @font-face {\n        font-family: Inter;\n        font-style: normal;\n        font-weight: 700;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n\n      @font-face {\n        font-family: Phudu;\n        font-style: normal;\n        font-weight: 600;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n\n      @font-face {\n        font-family: Phudu;\n        font-style: normal;\n        font-weight: 700;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n    </style>\n\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script src=\"//unpkg.com/alpinejs\" defer></script>\n\n    <script>\n      tailwind.config = {\n        content: [\"./*.html\"],\n        prefix: \"dw-\",\n        theme: {\n          extend: {\n            colors: {\n              body: {\n                bg: \"#ffffff\", // --body-bg-color\n                text: \"#333333\", // --body-text-color\n              },\n              theme: {\n                main: \"#d5232b\", // --theme-main-color\n                mainColorShade: \"#C41421\",\n                hoverBox: \"rgba(82, 164, 206, 0.43)\", // --theme-box-hover-color\n                cardBorder: \"#e0e0e0\",\n                cardBg: \"#fafafa\",\n                divider: \"#eee\",\n                lowOpacityTextColor: \"#111827\",\n              },\n            },\n            fontFamily: {\n              heading: [\"Phudu\", \"sans-serif\"], // --theme-heading-font\n              text: [\"Inter\", \"sans-serif\"], // --theme-text-font\n            },\n            screens: {\n              xs: \"200px\",\n              smx: \"376px\",\n              smm: \"567px\",\n              mdx: \"768px\",\n              mdl: \"993px\",\n              lgx: \"1171px\",\n              lgs: \"1221px\",\n              lgm: \"1281px\",\n              lgl: \"1361px\",\n              xlx: \"1400px\",\n              xl1: \"1441px\",\n              xl2: \"1600px\",\n              xxl: \"1900px\",\n            },\n          },\n        },\n        plugins: [],\n      };\n    </script>\n  <style>\n    /* Base styles */\n    * { box-sizing: border-box; }\n    html {\n      height: 100%;\n    }\n    body {\n      margin: 0;\n      padding: 20px;\n      font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif;\n      line-height: 1.6;\n      background: white;\n      color: #374151;\n    }\n\n    /* Tailwind CSS classes with tw- prefix */\n    .tw-bg-blue-500 { background-color: rgb(59 130 246) !important; }\n    .tw-bg-blue-600 { background-color: rgb(37 99 235) !important; }\n    .tw-bg-purple-600 { background-color: rgb(147 51 234) !important; }\n    .tw-bg-white { background-color: rgb(255 255 255) !important; }\n    .tw-bg-gray-50 { background-color: rgb(249 250 251) !important; }\n    .tw-bg-gray-100 { background-color: rgb(243 244 246) !important; }\n    .tw-bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)) !important; }\n    .tw-from-blue-600 { --tw-gradient-from: rgb(37 99 235); --tw-gradient-to: rgb(37 99 235 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important; }\n    .tw-to-purple-600 { --tw-gradient-to: rgb(147 51 234) !important; }\n\n    .tw-text-white { color: rgb(255 255 255) !important; }\n    .tw-text-blue-600 { color: rgb(37 99 235) !important; }\n    .tw-text-gray-600 { color: rgb(75 85 99) !important; }\n    .tw-text-gray-800 { color: rgb(31 41 55) !important; }\n    .tw-text-gray-900 { color: rgb(17 24 39) !important; }\n\n    .tw-p-4 { padding: 1rem !important; }\n    .tw-p-6 { padding: 1.5rem !important; }\n    .tw-p-8 { padding: 2rem !important; }\n    .tw-px-4 { padding-left: 1rem; padding-right: 1rem !important; }\n    .tw-px-6 { padding-left: 1.5rem; padding-right: 1.5rem !important; }\n    .tw-px-8 { padding-left: 2rem; padding-right: 2rem !important; }\n    .tw-py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem !important; }\n    .tw-py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem !important; }\n    .tw-py-4 { padding-top: 1rem; padding-bottom: 1rem !important; }\n    .tw-py-16 { padding-top: 4rem; padding-bottom: 4rem !important; }\n    .tw-py-20 { padding-top: 5rem; padding-bottom: 5rem !important; }\n\n    .tw-m-4 { margin: 1rem !important; }\n    .tw-mx-auto { margin-left: auto; margin-right: auto !important; }\n    .tw-mt-2 { margin-top: 0.5rem !important; }\n    .tw-mt-4 { margin-top: 1rem !important; }\n    .tw-mb-4 { margin-bottom: 1rem !important; }\n    .tw-mb-6 { margin-bottom: 1.5rem !important; }\n    .tw-mb-8 { margin-bottom: 2rem !important; }\n\n    .tw-rounded { border-radius: 0.25rem !important; }\n    .tw-rounded-lg { border-radius: 0.5rem !important; }\n    .tw-rounded-xl { border-radius: 0.75rem !important; }\n\n    .tw-text-sm { font-size: 0.875rem; line-height: 1.25rem !important; }\n    .tw-text-base { font-size: 1rem; line-height: 1.5rem !important; }\n    .tw-text-lg { font-size: 1.125rem; line-height: 1.75rem !important; }\n    .tw-text-xl { font-size: 1.25rem; line-height: 1.75rem !important; }\n    .tw-text-2xl { font-size: 1.5rem; line-height: 2rem !important; }\n    .tw-text-3xl { font-size: 1.875rem; line-height: 2.25rem !important; }\n    .tw-text-4xl { font-size: 2.25rem; line-height: 2.5rem !important; }\n    .tw-text-5xl { font-size: 3rem; line-height: 1 !important; }\n\n    .tw-font-medium { font-weight: 500 !important; }\n    .tw-font-semibold { font-weight: 600 !important; }\n    .tw-font-bold { font-weight: 700 !important; }\n\n    .tw-text-center { text-align: center !important; }\n    .tw-text-left { text-align: left !important; }\n\n    .tw-flex { display: flex !important; }\n    .tw-grid { display: grid !important; }\n    .tw-block { display: block !important; }\n    .tw-inline-block { display: inline-block !important; }\n    .tw-hidden { display: none !important; }\n\n    .tw-items-center { align-items: center !important; }\n    .tw-justify-center { justify-content: center !important; }\n    .tw-justify-between { justify-content: space-between !important; }\n\n    .tw-w-full { width: 100% !important; }\n    .tw-w-auto { width: auto !important; }\n    .tw-h-8 { height: 2rem !important; }\n    .tw-h-auto { height: auto !important; }\n\n    .tw-container { width: 100%; margin-left: auto; margin-right: auto !important; }\n    @media (min-width: 640px) { .tw-container { max-width: 640px !important; } }\n    @media (min-width: 768px) { .tw-container { max-width: 768px !important; } }\n    @media (min-width: 1024px) { .tw-container { max-width: 1024px !important; } }\n    @media (min-width: 1280px) { .tw-container { max-width: 1280px !important; } }\n\n    .tw-shadow-sm { box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05) !important; }\n    .tw-shadow { box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1) !important; }\n    .tw-shadow-md { box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important; }\n    .tw-shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important; }\n\n    .tw-border { border-width: 1px !important; }\n    .tw-border-gray-200 { border-color: rgb(229 231 235) !important; }\n    .tw-border-gray-300 { border-color: rgb(209 213 219) !important; }\n\n    .tw-transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms !important; }\n    .tw-transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms !important; }\n\n    .tw-hover\\:tw-bg-gray-100:hover { background-color: rgb(243 244 246) !important; }\n    .tw-hover\\:tw-text-blue-600:hover { color: rgb(37 99 235) !important; }\n\n    /* Grid classes */\n    .tw-grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }\n    .tw-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }\n    .tw-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }\n    .tw-gap-4 { gap: 1rem !important; }\n    .tw-gap-6 { gap: 1.5rem !important; }\n    .tw-gap-8 { gap: 2rem !important; }\n\n    @media (min-width: 768px) {\n      .tw-md\\:tw-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }\n      .tw-md\\:tw-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }\n      .tw-md\\:tw-block { display: block !important; }\n    }\n\n    /* Custom component styles */\n    \n  </style>\n</head>\n<body>\n  <div><div data-component=\"hero_section_home_page\"><div\n  class=\"dw-relative dw-z-0 dw-overflow-hidden dw-bg-white dw-bg-[top_right] dw-bg-no-repeat dw-bg-[length:50%_auto] dw-px-[5rem] dw-pt-2 dw-pb-[9rem] xlx:dw-px-[3rem] xlx:dw-pb-[7.3rem] xlx:dw-bg-[length:46%_auto] xlx:dw-bg-none lgl:dw-px-[3rem] lgl:dw-pb-[8.3rem] lgl:dw-bg-[length:46%_auto] lgl:dw-bg-none lgm:dw-px-[2.5rem] lgm:dw-pb-[9.3rem] lgm:dw-bg-[length:46%_auto] lgm:dw-bg-none lgs:dw-px-[3rem] lgs:dw-pb-[9.3rem] lgs:dw-bg-[length:46%_auto] lgs:dw-bg-none lgx:dw-px-[3rem] lgx:dw-pb-[9.3rem] lgx:dw-bg-[length:46%_auto] lgx:dw-bg-none mdl:dw-pl-[2.5rem] mdl:dw-pr-0 mdl:dw-pb-[9rem] mdl:dw-bg-[length:46%_auto] mdl:dw-bg-none mdx:dw-px-[2.5rem] mdx:dw-pb-[9rem] mdx:dw-bg-[length:46%_auto] smm:dw-px-[1rem] smm:dw-pb-[6rem] smm:dw-bg-none smx:dw-px-0 smx:dw-pb-[6rem] smx:dw-bg-none xs:dw-px-0 xs:dw-pb-[6rem] xs:dw-bg-none\"\n>\n  <svg\n    class=\"dw-absolute dw-top-0 dw-left-1/2 dw-hidden mdx:dw-block\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0.03 0 645.94 800\"\n  >\n    <path\n      d=\"M409.731 554.556L418.669 537.316C421.506 531.775 424.147 526.333 426.631 520.781C431.502 509.725 435.484 498.498 437.992 487.406C443.094 465.014 440.439 444.475 426.827 429.266C413.214 413.753 390.738 403.342 367.505 396.086C318.045 381.852 267.467 366.011 218.531 347.431C206.247 342.767 194.022 337.922 181.845 332.759C169.852 327.719 156.848 321.827 144.952 313.85C133.191 305.9 120.856 295.306 114.489 279.064C111.334 271.077 110.506 262.042 111.567 253.834C112.637 245.605 115.317 238.116 118.642 231.442C125.558 217.769 134.044 207.906 141.691 197.325L165.495 166.277L213.603 104.836L295.511 1.79063L233.07 0C233.07 0 90.8422 130.795 27.3563 213.336C-27.8375 285.077 2.30937 348.466 102.153 391.105C186.423 427.109 265.3 454.98 265.3 454.98C278.889 462.017 288.556 474.352 291.77 488.744C294.984 503.148 291.43 518.138 282.017 529.838L64.7484 800H281.519L373.716 623.644L409.731 554.556Z\"\n      class=\"dw-fill-theme-main\"\n      fill-opacity=\"0.239216\"\n    ></path>\n    <path\n      d=\"M618.27 407.848C588.012 357.233 536.216 321.533 476.212 309.928L263.631 265.109C254.767 263.405 247.316 257.78 243.553 249.963C239.791 242.158 240.192 233.109 244.612 225.622L385.928 4.37188L317.097 2.4L230.075 117.512L183.587 179.95L160.733 211.328C153.378 221.884 145.269 232.32 140.8 242.084C136.041 252.178 134.847 261.798 138.281 269.784C141.592 277.967 149.689 285.577 159.455 291.691C169.342 297.948 180.216 302.659 192.27 307.384C204.13 312.072 216.159 316.517 228.275 320.778C276.955 337.884 325.987 351.959 376.712 365.245C402.72 372.989 430.25 383.362 452.18 406.498C462.87 418.077 470.845 433.284 473.877 449.028C476.994 464.784 475.972 480.297 473.244 494.639C470.37 508.97 465.878 522.437 460.605 535.27C457.939 541.65 455.064 547.97 452.119 554.058L443.547 571.628L409.198 641.628L331.256 800H550.984L634.097 573.991C654.298 519.028 648.527 458.463 618.27 407.848Z\"\n      class=\"dw-fill-theme-main\"\n      fill-opacity=\"0.611765\"\n    ></path>\n  </svg>\n\n  <div\n    class=\"dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdx:dw-grid-cols-2\">\n      <div class=\"sub-buses-1 dw-px-1\">\n        <h1\n          class=\"dw-text-[32px] mdx:dw-text-[48px] dw-font-bold dw-mb-4 dw-font-heading\"\n        >\n          ${hero_section_title}\n        </h1>\n        <p class=\"dw-text-base xl1:dw-text-lg dw-mb-6\">\n          ${hero_section_description}\n          <strong>${contact_number}</strong>\n          ${hero_section_extra_text}\n        </p>\n\n        <div\n          class=\"dw-flex lgm:dw-flex-row dw-flex-col dw-gap-5 dw-items-start\"\n        >\n          <a\n            href=\"tel:${contact_number}\"\n            class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center dw-gap-[10px] dw-pe-[10px] lgs:dw-text-[18px] dw-text-[16px] dw-ps-[3px] dw-py-[3px] dw-text-white dw-bg-body-text dw-rounded-full hover:dw-bg-theme-main dw-border-[2px] dw-border-body-text hover:dw-border-theme-main dw-transition\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"42\"\n              height=\"42\"\n              viewBox=\"0 0 36 36\"\n              fill=\"none\"\n            >\n              <rect width=\"36\" height=\"36\" rx=\"18\" class=\"dw-fill-theme-main\" />\n              <path\n                d=\"M27.7388 22.4138C27.5716 23.6841 26.9477 24.8502 25.9837 25.6942C25.0196 26.5382 23.7813 27.0023 22.5 27.0001C15.0563 27.0001 9.00001 20.9438 9.00001 13.5001C8.99771 12.2188 9.4619 10.9805 10.3059 10.0164C11.1499 9.05234 12.3159 8.42847 13.5863 8.2613C13.9075 8.22208 14.2328 8.2878 14.5136 8.44865C14.7944 8.60951 15.0157 8.85687 15.1444 9.1538L17.1244 13.5741V13.5854C17.2229 13.8127 17.2636 14.0608 17.2428 14.3077C17.222 14.5545 17.1404 14.7924 17.0053 15.0001C16.9884 15.0254 16.9706 15.0488 16.9519 15.0722L15 17.386C15.7022 18.8129 17.1947 20.2922 18.6403 20.9963L20.9222 19.0547C20.9446 19.0359 20.9681 19.0184 20.9925 19.0022C21.2 18.8639 21.4387 18.7794 21.687 18.7565C21.9353 18.7336 22.1854 18.7729 22.4147 18.871L22.4269 18.8766L26.8434 20.8557C27.1409 20.9839 27.3889 21.205 27.5503 21.4858C27.7116 21.7667 27.7778 22.0922 27.7388 22.4138Z\"\n                fill=\"white\"\n              ></path>\n            </svg>\n            ${contact_number_button_label}\n          </a>\n\n          <a\n            href=\"${quote_url}\"\n            class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center dw-gap-[6px] lgs:dw-text-[18px] dw-text-[16px] dw-pe-[10px] dw-ps-3 dw-py-[9.5px] dw-text-white dw-border-[2px] dw-border-theme-main dw-rounded-full dw-bg-theme-main\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              id=\"Layer_1\"\n              data-name=\"Layer 1\"\n              viewBox=\"0 0 35.05 60.44\"\n              width=\"30\"\n              height=\"30\"\n              fill=\"white\"\n            >\n              <path\n                d=\"M21.45 7.46c2.09-.11 4.18.1 6.23.44l.13.22c-.51 1.39-2.17.52-3.38.48-9.62-.33-17.23 4.17-21.45 12.76-.11.23-.47 1.43-.53 1.49-.23.26-1.08.01-1.14-.18-.08-.25 1.18-2.92 1.4-3.33C6.35 12.59 13.75 7.86 21.44 7.45Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M22.15 9.65c.52-.02 4.24.09 4.25.48-.74 1.21-.54.76-1.62.75-7.73-.07-14.38 2.32-18.55 9.17-.33.55-1.51 3.39-1.67 3.51-.13.1-1.1-.11-1.14-.26-.04-.13.91-2.17 1.05-2.46C7.73 14.38 14.91 9.88 22.15 9.66Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M.13 29.21c.06-.03.97 0 1.01.04.09.13-.04 2.64 0 3.07.56 6.25 4.19 12.15 9.25 15.75.45.32 2.88 1.55 2.94 1.71.02.05-.36.77-.39.92C5.95 47.64.79 40.17.09 32.59c-.04-.45-.2-3.25.04-3.38\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"M21.71 11.93c1.05-.06 2.11.02 3.16.09.26.1-.47 1.03-.53 1.05-.37.13-1.97-.06-2.54 0-5.7.56-10.71 3.36-13.73 8.29-.29.48-1.09 2.47-1.23 2.63-.21.23-.9.01-1.14-.18 2.33-6.64 8.99-11.48 16.01-11.89Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M2.42 29.21c.07-.03.96 0 1.01.04-.56 6.56 2.81 12.97 8.03 16.8.39.29 2.65 1.47 2.68 1.62 0 .05-.37.76-.39.92-5.39-2.46-9.76-8.03-11.01-13.82-.15-.7-.78-5.36-.31-5.57Z\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"m4.61 29.21 1.01.04c-.47 6.78 3.33 13.04 9.21 16.23l-.31 1.01C8.17 43.38 3.99 36.33 4.61 29.21M2.94 24.56s.54.12.66.13c-.19.44-.08 3.28-.22 3.38-.03.02-.62.02-.66-.04-.02-.46-.01-3.28.22-3.46ZM5.31 25.09l.66.13c-.14.37-.1 2.77-.22 2.85-.03.02-.62.02-.66-.04 0-.98-.06-1.98.22-2.94\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"M35.05 0 25.01 27.68l8.95.26-23.39 32.5c3.39-9.18 7.02-18.39 9.82-27.72l-8.73-.13z\"\n                style=\"fill: #fdfeff\"\n              />\n              <path\n                d=\"M29.52 8.95c.26 0 0 .31-.04.39-4.07 7.61-10.02 14.97-14.3 22.54l-1.71.13z\"\n                style=\"fill: #fefefe\"\n              />\n            </svg>\n            ${get_quote_button_text}\n          </a>\n        </div>\n      </div>\n\n      <div\n        class=\"dw-z-10 dw-pt-10 dw-pb-10 mdl:dw-pt-16 mdl:dw-ps-10 lgm:dw-ps-[60px]\"\n      >\n        <img\n          src=\"${img-hero-section-image}\"\n          class=\"dw-rounded-[16px] dw-w-full dw-h-auto\"\n          alt=\"img-hero-section-image\"\n        />\n      </div>\n    </div>\n  </div>\n</div>\n\n<div class=\"dw-relative dw--mt-[85px] dw-z-[1]\">\n  <div\n    class=\"dw-px-8 lgm:dw-px-0 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdl:dw-grid-cols-3 dw-gap-3 dw-gap-y-12\">\n      \n      <!-- Box 1 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=`${img-advantage-image}`\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${hero_section_card1_content}\n          </p>\n        </div>\n      </div>\n\n      <!-- Box 2 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${hero_section_card2_content}\n          </p>\n        </div>\n      </div>\n\n      <!-- Box 3 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${hero_section_card3_content}\n          </p>\n        </div>\n      </div>\n    </div>\n  </div>\n</div></div></div>\n<div><div data-component=\"why_section_home_page\"><div\n  class=\"dw-bg-theme-cardBg dw-bg-[url('${img-background-pattern-bg-image}')] dw-bg-top dw-bg-no-repeat dw-bg-cover mdl:dw-pt-24 mdl:dw-pb-[15rem] dw-pt-[5rem] dw-pb-[7rem]\"\n>\n  <div\n    class=\"dw-px-8 lgm:dw-px-0 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdl:dw-grid-cols-2 dw-items-center\">\n      <!-- Left Content -->\n      <div class=\"sub-buses-1 dw-px-1\">\n        <h2\n          class=\"dw-font-heading dw-leading-tight dw-text-[26px] mdx:dw-text-[46px] lgx:text-[48px] dw-font-semibold dw-mb-4\"\n        >\n          ${why_section_heading}\n        </h2>\n        <p class=\"xl1:dw-text-lg dw-text-base dw-leading-relaxed\">\n          ${why_section_description}\n        </p>\n      </div>\n\n      <!-- Right Image -->\n      <div\n        class=\"why-rent-image sub-buses-2 dw-pt-[40px] mdl:dw-ps-[40px] lgm:dw-ps-[60px] lgs:dw-pt-0\"\n      >\n        <img\n          src=\"${img-why-section-image}\"\n          alt=\"img-why-section-image\"\n          class=\"dw-w-full dw-h-auto dw-rounded-[16px]\"\n        />\n      </div>\n    </div>\n  </div>\n</div>\n<div class=\"dw-relative dw--mt-[85px] dw-z-[1]\">\n  <div\n    class=\"dw-px-8 lgm:dw-px-0 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdl:dw-grid-cols-3 dw-gap-3 dw-gap-y-12\">\n      \n      <!-- Box 1 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${why_section_card1_content}\n          </p>\n        </div>\n      </div>\n\n      <!-- Box 2 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${why_section_card2_content}\n          </p>\n        </div>\n      </div>\n\n      <!-- Box 3 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${why_section_card3_content}\n          </p>\n        </div>\n      </div>\n    </div>\n  </div>\n</div></div></div>\n<div><div data-component=\"bus_grid_list\"><div\n  class=\"dw-my-16 dw-mx-auto dw-w-full dw-px-6 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgl:dw-px-6 lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdl:dw-px-6 mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smm:dw-px-6 smx:dw-max-w-[700px] smx:dw-px-6\"\n>\n  <div class=\"dw-flex dw-justify-center\">\n    <h2\n      class=\"xl1:dw-mx-[395px] dw-font-semibold lgm:dw-text-[48px] lgx:dw-text-[50px] mdx:dw-text-[46px] dw-text-[26px] xl1:dw-leading-[60px] lgx:dw-leading-[46px] dw-uppercase dw-text-center dw-pb-[25px] dw-font-heading\"\n    >\n      ${buses_list_heading}\n    </h2>\n  </div>\n\n  <div\n    class=\"dw-grid dw-grid-cols-1 mdx:dw-grid-cols-2 lgs:dw-grid-cols-3 mdx:dw-gap-8 dw-gap-4 xl1:dw-gap-[50px] dw-mb-4\"\n  >\n    {{busesList}}\n  </div>\n</div></div></div>\n<div><div data-component=\"bus_amenities\"><div class=\"dw-my-16\">\n  <div\n    class=\"dw-mx-auto dw-w-full dw-px-8 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgl:dw-px-8 lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] lgs:dw-px-8 mdl:dw-max-w-[1080px] mdl:dw-px-8 mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smm:dw-px-8 smx:dw-max-w-[700px] smx:dw-px-8 mdx:dw-px-[132px]\"\n  >\n    <div class=\"dw-text-center dw-pb-[20px]\">\n      <h2\n        class=\"dw-font-heading xlx:dw-text-[24px] lgx:dw-text-[22px] dw-text-[20px] dw-leading-[32px] dw-font-semibold dw-uppercase dw-tracking-[-0.011em] dw-pt-8 dw-pb-5\"\n      >\n        ${amenities_title}\n      </h2>\n    </div>\n    <div\n      class=\"dw-grid dw-grid-cols-1 mdx:dw-grid-cols-2 mdl:dw-grid-cols-3 lgs:dw-grid-cols-4 dw-gap-x-6 dw-gap-y-6\"\n    >\n      {{amenities_cards}}\n    </div>\n  </div>\n</div></div></div>\n<div><div data-component=\"note_sure\"><div\n  class=\"dw-my-16 dw-mx-auto dw-w-full dw-px-6 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgl:dw-px-6 lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdl:dw-px-6 mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smm:dw-px-6 smx:dw-max-w-[700px] smx:dw-px-6\"\n>\n  <div class=\"dw-mt-[80px] dw-mb-[40px]\">\n    <div class=\"dw-w-full\">\n      <!-- Heading -->\n      <p\n        class=\"dw-font-semibold dw-text-[20px] lgx:dw-text-[22px] xl1:dw-text-[24px] dw-uppercase dw-leading-8 dw-tracking-tight dw-pb-5 dw-text-center dw-m-0 dw-font-heading\"\n      >\n        ${note_sure_title}\n      </p>\n\n      <!-- Description -->\n      <p\n        class=\"dw-text-center dw-font-medium dw-text-[16px] lgx:dw-text-[18px] dw-pb-2\"\n      >\n        ${note_sure_description}\n      </p>\n\n      <!-- Buttons -->\n      <div class=\"dw-text-center dw-mt-10\">\n        <div\n          class=\"dw-flex dw-flex-col mdx:dw-flex-row dw-justify-center dw-items-center dw-gap-4\"\n        >\n          <!-- Call Us Button -->\n          <a\n            href=\"tel:${contact_number}\"\n            class=\"dw-inline-flex dw-font-medium dw-items-center mdx:dw-gap-[10px] dw-gap-[35px] dw-pe-[10px] lgs:dw-text-[18px] dw-text-[16px] dw-ps-[3px] dw-py-[3px] dw-text-white dw-bg-body-text dw-rounded-full hover:dw-bg-theme-main dw-border-[2px] dw-border-body-text hover:dw-border-theme-main dw-transition\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"42\"\n              height=\"42\"\n              viewBox=\"0 0 36 36\"\n              fill=\"none\"\n            >\n              <rect width=\"36\" height=\"36\" rx=\"18\" class=\"dw-fill-theme-main\" />\n              <path\n                d=\"M27.7388 22.4138C27.5716 23.6841 26.9477 24.8502 25.9837 25.6942C25.0196 26.5382 23.7813 27.0023 22.5 27.0001C15.0563 27.0001 9.00001 20.9438 9.00001 13.5001C8.99771 12.2188 9.4619 10.9805 10.3059 10.0164C11.1499 9.05234 12.3159 8.42847 13.5863 8.2613C13.9075 8.22208 14.2328 8.2878 14.5136 8.44865C14.7944 8.60951 15.0157 8.85687 15.1444 9.1538L17.1244 13.5741V13.5854C17.2229 13.8127 17.2636 14.0608 17.2428 14.3077C17.222 14.5545 17.1404 14.7924 17.0053 15.0001C16.9884 15.0254 16.9706 15.0488 16.9519 15.0722L15 17.386C15.7022 18.8129 17.1947 20.2922 18.6403 20.9963L20.9222 19.0547C20.9446 19.0359 20.9681 19.0184 20.9925 19.0022C21.2 18.8639 21.4387 18.7794 21.687 18.7565C21.9353 18.7336 22.1854 18.7729 22.4147 18.871L22.4269 18.8766L26.8434 20.8557C27.1409 20.9839 27.3889 21.205 27.5503 21.4858C27.7116 21.7667 27.7778 22.0922 27.7388 22.4138Z\"\n                fill=\"white\"\n              ></path>\n            </svg>\n            ${contact_number_button_label}\n          </a>\n\n          <!-- Get Quote Button -->\n          <a\n            href=\"${quote_url}\"\n            class=\"dw-inline-flex dw-font-medium dw-items-center dw-gap-[6px] lgs:dw-text-[18px] dw-text-[16px] dw-pe-[10px] dw-ps-3 dw-py-[9.5px] dw-text-white dw-border-[2px] dw-border-theme-main dw-rounded-full dw-bg-theme-main\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              id=\"Layer_1\"\n              data-name=\"Layer 1\"\n              viewBox=\"0 0 35.05 60.44\"\n              width=\"30\"\n              height=\"30\"\n              fill=\"white\"\n            >\n              <path\n                d=\"M21.45 7.46c2.09-.11 4.18.1 6.23.44l.13.22c-.51 1.39-2.17.52-3.38.48-9.62-.33-17.23 4.17-21.45 12.76-.11.23-.47 1.43-.53 1.49-.23.26-1.08.01-1.14-.18-.08-.25 1.18-2.92 1.4-3.33C6.35 12.59 13.75 7.86 21.44 7.45Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M22.15 9.65c.52-.02 4.24.09 4.25.48-.74 1.21-.54.76-1.62.75-7.73-.07-14.38 2.32-18.55 9.17-.33.55-1.51 3.39-1.67 3.51-.13.1-1.1-.11-1.14-.26-.04-.13.91-2.17 1.05-2.46C7.73 14.38 14.91 9.88 22.15 9.66Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M.13 29.21c.06-.03.97 0 1.01.04.09.13-.04 2.64 0 3.07.56 6.25 4.19 12.15 9.25 15.75.45.32 2.88 1.55 2.94 1.71.02.05-.36.77-.39.92C5.95 47.64.79 40.17.09 32.59c-.04-.45-.2-3.25.04-3.38\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"M21.71 11.93c1.05-.06 2.11.02 3.16.09.26.1-.47 1.03-.53 1.05-.37.13-1.97-.06-2.54 0-5.7.56-10.71 3.36-13.73 8.29-.29.48-1.09 2.47-1.23 2.63-.21.23-.9.01-1.14-.18 2.33-6.64 8.99-11.48 16.01-11.89Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M2.42 29.21c.07-.03.96 0 1.01.04-.56 6.56 2.81 12.97 8.03 16.8.39.29 2.65 1.47 2.68 1.62 0 .05-.37.76-.39.92-5.39-2.46-9.76-8.03-11.01-13.82-.15-.7-.78-5.36-.31-5.57Z\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"m4.61 29.21 1.01.04c-.47 6.78 3.33 13.04 9.21 16.23l-.31 1.01C8.17 43.38 3.99 36.33 4.61 29.21M2.94 24.56s.54.12.66.13c-.19.44-.08 3.28-.22 3.38-.03.02-.62.02-.66-.04-.02-.46-.01-3.28.22-3.46ZM5.31 25.09l.66.13c-.14.37-.1 2.77-.22 2.85-.03.02-.62.02-.66-.04 0-.98-.06-1.98.22-2.94\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"M35.05 0 25.01 27.68l8.95.26-23.39 32.5c3.39-9.18 7.02-18.39 9.82-27.72l-8.73-.13z\"\n                style=\"fill: #fdfeff\"\n              />\n              <path\n                d=\"M29.52 8.95c.26 0 0 .31-.04.39-4.07 7.61-10.02 14.97-14.3 22.54l-1.71.13z\"\n                style=\"fill: #fefefe\"\n              />\n            </svg>\n            ${get_quote_button_text}\n          </a>\n        </div>\n      </div>\n    </div>\n  </div>\n</div></div></div>\n<div><div data-component=\"book_in_minutes\"><div\n  class=\"dw-my-16 homepage-cta dw-bg-theme-mainColorShade dw-bg-[url('${img-book_in_minutes_cta_bg}')] dw-bg-no-repeat dw-bg-cover dw-py-[64px] mdx:dw-pb-[96px] mdl:dw-px-[160px] lgm:dw-px-[240px] xl1:dw-px-[160px] xl1:dw-py-[80px]\"\n>\n  <div\n    class=\"dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"row\">\n      <div class=\"dw-w-full\">\n        <h2\n          class=\"dw-mx-1 dw-text-white dw-font-heading dw-text-center dw-mb-[30px] xl1:dw-w-1/2 dw-w-full mdl:dw-mx-auto lgm:dw-text-[48px] lgx:dw-text-[50px] mdx:dw-text-[46px] dw-text-[26px] dw-font-semibold xl1:dw-leading-[60px] lgx:dw-leading-[46px] dw-pb-4\"\n        >\n          ${book_in_minutes_title}\n        </h2>\n        <div\n          class=\"dw-flex dw-flex-col mdx:dw-flex-row dw-items-center dw-justify-center dw-gap-4 dw-mt-6\"\n        >\n          <!-- Call Us Button -->\n          <a\n            href=\"tel:${contact_number}\"\n            class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center mdx:dw-gap-[10px] dw-pe-[40px] dw-gap-[30px] mdx:dw-pe-[10px] lgs:dw-text-[18px] dw-text-[16px] dw-ps-[3px] dw-py-[3px] dw-bg-white dw-rounded-full hover:dw-bg-theme-main hover:dw-text-white dw-border-[2px] dw-border-white hover:dw-border-theme-main dw-transition\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"42\"\n              height=\"42\"\n              viewBox=\"0 0 36 36\"\n              fill=\"none\"\n            >\n              <rect width=\"36\" height=\"36\" rx=\"18\" class=\"dw-fill-theme-main\" />\n              <path\n                d=\"M27.7388 22.4138C27.5716 23.6841 26.9477 24.8502 25.9837 25.6942C25.0196 26.5382 23.7813 27.0023 22.5 27.0001C15.0563 27.0001 9.00001 20.9438 9.00001 13.5001C8.99771 12.2188 9.4619 10.9805 10.3059 10.0164C11.1499 9.05234 12.3159 8.42847 13.5863 8.2613C13.9075 8.22208 14.2328 8.2878 14.5136 8.44865C14.7944 8.60951 15.0157 8.85687 15.1444 9.1538L17.1244 13.5741V13.5854C17.2229 13.8127 17.2636 14.0608 17.2428 14.3077C17.222 14.5545 17.1404 14.7924 17.0053 15.0001C16.9884 15.0254 16.9706 15.0488 16.9519 15.0722L15 17.386C15.7022 18.8129 17.1947 20.2922 18.6403 20.9963L20.9222 19.0547C20.9446 19.0359 20.9681 19.0184 20.9925 19.0022C21.2 18.8639 21.4387 18.7794 21.687 18.7565C21.9353 18.7336 22.1854 18.7729 22.4147 18.871L22.4269 18.8766L26.8434 20.8557C27.1409 20.9839 27.3889 21.205 27.5503 21.4858C27.7116 21.7667 27.7778 22.0922 27.7388 22.4138Z\"\n                fill=\"white\"\n              ></path>\n            </svg>\n            ${contact_number_button_label}\n          </a>\n\n          <!-- Get Quote Button -->\n          <a\n            href=\"${quote_url}\"\n            class=\"dw-flex dw-font-medium dw-w-fit dw-items-center dw-gap-[6px] lgs:dw-text-[18px] dw-text-[16px] dw-pe-[10px] dw-border-[2px] hover:dw-bg-theme-main hover:dw-text-white dw-rounded-full dw-bg-white dw-border-white hover:dw-border-theme-main dw-transition\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              id=\"Layer_1\"\n              data-name=\"Layer 1\"\n              viewBox=\"0 0 72 84\"\n              width=\"48\"\n              height=\"48\"\n              class=\"dw-fill-white\"\n            >\n              <circle\n                cx=\"36\"\n                cy=\"42\"\n                r=\"36\"\n                class=\"dw-fill-theme-main\"\n              ></circle>\n              <g transform=\"translate(18, 18) scale(0.8)\">\n                <path\n                  d=\"M21.45 7.46c2.09-.11 4.18.1 6.23.44l.13.22c-.51 1.39-2.17.52-3.38.48-9.62-.33-17.23 4.17-21.45 12.76-.11.23-.47 1.43-.53 1.49-.23.26-1.08.01-1.14-.18-.08-.25 1.18-2.92 1.4-3.33C6.35 12.59 13.75 7.86 21.44 7.45Z\"\n                  class=\"cls-3\"\n                ></path>\n                <path\n                  d=\"M22.15 9.65c.52-.02 4.24.09 4.25.48-.74 1.21-.54.76-1.62.75-7.73-.07-14.38 2.32-18.55 9.17-.33.55-1.51 3.39-1.673.51-.13.1-1.1-.11-1.14-.26-.04-.13.91-2.17 1.05-2.46C7.73 14.38 14.91 9.88 22.15 9.66Z\"\n                  class=\"cls-3\"\n                ></path>\n                <path\n                  d=\"M.13 29.21c.06-.03.97 0 1.01.04.09.13-.04 2.64 0 3.07.56 6.25 4.19 12.15 9.25 15.75.45.32 2.88 1.55 2.94 1.71.02.05-.36.77-.39.92C5.95 47.64.79 40.17.09 32.59c-.04-.45-.2-3.25.04-3.38\"\n                  class=\"cls-1\"\n                ></path>\n                <path\n                  d=\"M21.71 11.93c1.05-.06 2.11.02 3.16.09.26.1-.47 1.03-.53 1.05-.37.13-1.97-.06-2.54 0-5.7.56-10.71 3.36-13.73 8.29-.29.48-1.09 2.47-1.23 2.63-.21.23-.9.01-1.14-.18 2.33-6.64 8.99-11.48 16.01-11.89Z\"\n                  class=\"cls-3\"\n                ></path>\n                <path\n                  d=\"M2.42 29.21c.07-.03.96 0 1.01.04-.56 6.56 2.81 12.97 8.03 16.8.39.29 2.65 1.47 2.68 1.62 0 .05-.37.76-.39.92-5.39-2.46-9.76-8.03-11.01-13.82-.15-.7-.78-5.36-.31-5.57Z\"\n                  class=\"cls-1\"\n                ></path>\n                <path\n                  d=\"m4.61 29.21 1.01.04c-.47 6.78 3.33 13.04 9.21 16.23l-.31 1.01C8.17 43.38 3.99 36.33 4.61 29.21M2.94 24.56s.54.12.66.13c-.19.44-.08 3.28-.22 3.38-.03.02-.62.02-.66-.04-.02-.46-.01-3.28.22-3.46ZM5.31 25.09l.66.13c-.14.37-.1 2.77-.22 2.85-.03.02-.62.02-.66-.04 0-.98-.06-1.98.22-2.94\"\n                  class=\"cls-1\"\n                ></path>\n                <path\n                  d=\"M35.05 0 25.01 27.68l8.95.26-23.39 32.5c3.39-9.18 7.02-18.39 9.82-27.72l-8.73-.13z\"\n                  style=\"fill: #fdfeff\"\n                ></path>\n                <path\n                  d=\"M29.52 8.95c.26 0 0 .31-.04.39-4.07 7.61-10.02 14.97-14.3 22.54l-1.71.13z\"\n                  style=\"fill: #fefefe\"\n                ></path>\n              </g>\n            </svg>\n            ${get_quote_button_text}\n          </a>\n        </div>\n      </div>\n    </div>\n  </div>\n</div></div></div>\n<div><div data-component=\"common_cta_section\"><div\n  class=\"dw-my-16 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px] dw-px-8 dw-hidden mdx:dw-block\"\n>\n  <div class=\"dw-flex dw-w-full dw-rounded-[50px] dw-overflow-hidden\">\n    <!-- Left Column -->\n    <div\n      class=\"dw-w-[70%] dw-bg-theme-mainColorShade lgm:dw-py-[88px] lgm:dw-px-[80px] dw-py-[56px] dw-px-[48px] mdl:dw-py-[72px] mdl:dw-px-[48px]\"\n    >\n      <p\n        class=\"mdl:dw-text-[30px] dw-text-2xl dw-font-heading dw-font-semibold dw-text-white\"\n      >\n        ${cta_title}\n      </p>\n\n      <div class=\"dw-flex dw-flex-col mdl:dw-flex-row dw-gap-4 dw-mt-6\">\n        <!-- Call Us Button -->\n        <a\n          href=\"tel:${contact_number}\"\n          class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center mdx:dw-gap-[10px] dw-gap-[35px] dw-pe-[10px] lgs:dw-text-[18px] dw-text-[16px] dw-ps-[3px] dw-py-[3px] dw-bg-white dw-rounded-full hover:dw-bg-theme-main hover:dw-text-white dw-border-[2px] dw-border-white hover:dw-border-theme-main dw-transition\"\n        >\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            width=\"42\"\n            height=\"42\"\n            viewBox=\"0 0 36 36\"\n            fill=\"none\"\n          >\n            <rect width=\"36\" height=\"36\" rx=\"18\" class=\"dw-fill-theme-main\" />\n            <path\n              d=\"M27.7388 22.4138C27.5716 23.6841 26.9477 24.8502 25.9837 25.6942C25.0196 26.5382 23.7813 27.0023 22.5 27.0001C15.0563 27.0001 9.00001 20.9438 9.00001 13.5001C8.99771 12.2188 9.4619 10.9805 10.3059 10.0164C11.1499 9.05234 12.3159 8.42847 13.5863 8.2613C13.9075 8.22208 14.2328 8.2878 14.5136 8.44865C14.7944 8.60951 15.0157 8.85687 15.1444 9.1538L17.1244 13.5741V13.5854C17.2229 13.8127 17.2636 14.0608 17.2428 14.3077C17.222 14.5545 17.1404 14.7924 17.0053 15.0001C16.9884 15.0254 16.9706 15.0488 16.9519 15.0722L15 17.386C15.7022 18.8129 17.1947 20.2922 18.6403 20.9963L20.9222 19.0547C20.9446 19.0359 20.9681 19.0184 20.9925 19.0022C21.2 18.8639 21.4387 18.7794 21.687 18.7565C21.9353 18.7336 22.1854 18.7729 22.4147 18.871L22.4269 18.8766L26.8434 20.8557C27.1409 20.9839 27.3889 21.205 27.5503 21.4858C27.7116 21.7667 27.7778 22.0922 27.7388 22.4138Z\"\n              fill=\"white\"\n            ></path>\n          </svg>\n          ${contact_number_button_label}\n        </a>\n\n        <!-- Get Quote Button -->\n        <a\n          href=\"${quote_url}\"\n          class=\"dw-flex dw-font-medium dw-w-fit dw-items-center dw-gap-[6px] lgs:dw-text-[18px] dw-text-[16px] dw-pe-[10px] dw-border-[2px] hover:dw-bg-theme-main hover:dw-text-white dw-rounded-full dw-bg-white dw-border-white hover:dw-border-theme-main dw-transition\"\n        >\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            id=\"Layer_1\"\n            data-name=\"Layer 1\"\n            viewBox=\"0 0 72 84\"\n            width=\"48\"\n            height=\"48\"\n            class=\"dw-fill-white\"\n          >\n            <circle cx=\"36\" cy=\"42\" r=\"36\" class=\"dw-fill-theme-main\"></circle>\n            <g transform=\"translate(18, 18) scale(0.8)\">\n              <path\n                d=\"M21.45 7.46c2.09-.11 4.18.1 6.23.44l.13.22c-.51 1.39-2.17.52-3.38.48-9.62-.33-17.23 4.17-21.45 12.76-.11.23-.47 1.43-.53 1.49-.23.26-1.08.01-1.14-.18-.08-.25 1.18-2.92 1.4-3.33C6.35 12.59 13.75 7.86 21.44 7.45Z\"\n                class=\"cls-3\"\n              ></path>\n              <path\n                d=\"M22.15 9.65c.52-.02 4.24.09 4.25.48-.74 1.21-.54.76-1.62.75-7.73-.07-14.38 2.32-18.55 9.17-.33.55-1.51 3.39-1.673.51-.13.1-1.1-.11-1.14-.26-.04-.13.91-2.17 1.05-2.46C7.73 14.38 14.91 9.88 22.15 9.66Z\"\n                class=\"cls-3\"\n              ></path>\n              <path\n                d=\"M.13 29.21c.06-.03.97 0 1.01.04.09.13-.04 2.64 0 3.07.56 6.25 4.19 12.15 9.25 15.75.45.32 2.88 1.55 2.94 1.71.02.05-.36.77-.39.92C5.95 47.64.79 40.17.09 32.59c-.04-.45-.2-3.25.04-3.38\"\n                class=\"cls-1\"\n              ></path>\n              <path\n                d=\"M21.71 11.93c1.05-.06 2.11.02 3.16.09.26.1-.47 1.03-.53 1.05-.37.13-1.97-.06-2.54 0-5.7.56-10.71 3.36-13.73 8.29-.29.48-1.09 2.47-1.23 2.63-.21.23-.9.01-1.14-.18 2.33-6.64 8.99-11.48 16.01-11.89Z\"\n                class=\"cls-3\"\n              ></path>\n              <path\n                d=\"M2.42 29.21c.07-.03.96 0 1.01.04-.56 6.56 2.81 12.97 8.03 16.8.39.29 2.65 1.47 2.68 1.62 0 .05-.37.76-.39.92-5.39-2.46-9.76-8.03-11.01-13.82-.15-.7-.78-5.36-.31-5.57Z\"\n                class=\"cls-1\"\n              ></path>\n              <path\n                d=\"m4.61 29.21 1.01.04c-.47 6.78 3.33 13.04 9.21 16.23l-.31 1.01C8.17 43.38 3.99 36.33 4.61 29.21M2.94 24.56s.54.12.66.13c-.19.44-.08 3.28-.22 3.38-.03.02-.62.02-.66-.04-.02-.46-.01-3.28.22-3.46ZM5.31 25.09l.66.13c-.14.37-.1 2.77-.22 2.85-.03.02-.62.02-.66-.04 0-.98-.06-1.98.22-2.94\"\n                class=\"cls-1\"\n              ></path>\n              <path\n                d=\"M35.05 0 25.01 27.68l8.95.26-23.39 32.5c3.39-9.18 7.02-18.39 9.82-27.72l-8.73-.13z\"\n                style=\"fill: #fdfeff\"\n              ></path>\n              <path\n                d=\"M29.52 8.95c.26 0 0 .31-.04.39-4.07 7.61-10.02 14.97-14.3 22.54l-1.71.13z\"\n                style=\"fill: #fefefe\"\n              ></path>\n            </g>\n          </svg>\n          ${get_quote_button_text}\n        </a>\n      </div>\n\n      <div class=\"dw-mt-6 dw-flex dw-items-center dw-space-x-4\">\n        <img\n          src=\"${img-cta-review-photo}\"\n          alt=\"cta_review_photo\"\n          class=\"dw-w-[20%] dw-h-auto dw-object-contain\"\n        />\n        <p class=\"dw-text-white mdl:dw-text-[16px] dw-text-sm dw-font-medium\">\n          ${available_24_7_text}\n        </p>\n      </div>\n    </div>\n\n    <!-- Right Column -->\n    <div\n      class=\"mdl:dw-w-[30%] dw-w-[35%] dw-bg-theme-main dw-flex dw-items-center\"\n    >\n      <img\n        src=\"${img-cta-bus-rental-image}\"\n        alt=\"cta_bus_rental_image\"\n        class=\"dw-w-full dw-h-auto dw-object-contain\"\n      />\n    </div>\n  </div>\n</div></div></div>\n<div><div data-component=\"faqs\"><div\n      class=\"lgx:dw-pb-[80px] mdx:dw-pb-[64px] dw-pb-[32px] dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n    >\n      <h1\n        class=\"dw-pb-[48px] dw-mb-2 dw-capitalize dw-mx-auto dw-text-[26px] mdx:dw-text-[48px] dw-font-semibold dw-text-heading dw-text-center dw-pb-5 dw-font-heading dw-leading-tight lgx:dw-px-0 dw-px-6\"\n      >\n        ${faqs_title}\n      </h1>\n\n      <div\n        id=\"accordion\"\n        class=\"dw-w-full mdl:dw-w-[60%] mdl:dw-px-0 dw-px-8 dw-mx-auto\"\n      >\n        <div class=\"accordion\">{{faqs_questions}}</div>\n      </div>\n    </div></div></div>\n<div><div\n  class=\"dw-mx-auto dw-w-full dw-px-6 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n>\n  <hr\n    class=\"dw-border-t dw-border-theme-divider dw-w-[60%] dw-mx-auto dw-opacity-100 dw-my-14\"\n  />\n</div></div>\n<div><div\n  class=\"dw-mx-auto dw-w-full dw-px-8 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n>\n  <hr\n    class=\"dw-border-t dw-border-theme-divider dw-mx-auto dw-opacity-100 dw-my-14\"\n  />\n</div></div>\n<div><div data-component=\"worked_with\"><div\n  class=\"dw-my-16 dw-mx-auto dw-w-full dw-px-4 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgl:dw-px-4 lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdl:dw-px-4 mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smm:dw-px-4 smx:dw-max-w-[700px] smx:dw-px-4\"\n>\n  <p\n    class=\"dw-font-heading dw-text-center xlx:dw-text-[24px] lgx:dw-text-[22px] dw-text-[20px] dw-font-semibold\"\n  >\n    ${worked_with_title}\n  </p>\n\n  <div\n    class=\"dw-grid dw-grid-cols-1 smm:dw-grid-cols-2 mdx:dw-grid-cols-6 mdx:dw-gap-6 dw-gap-y-8 dw-justify-center dw-pt-[64px] dw-px-[48px] xl1:dw-pt-[64px] xl1:dw-px-[48px] dw-pb-[30px] lgs:dw-pt-[64px] xlx:dw-px-0 lgx:dw-px-[16px] mdx:dw-px-[32px]\"\n  >\n    {{work_with_list}}\n  </div>\n</div>\n</div></div>\n<div><div data-component=\"testimonial\"><section\n  class=\"dw-my-16 dw-bg-theme-cardBg dw-bg-[url('${img-background-pattern-bg-image}')] dw-bg-top dw-bg-no-repeat dw-bg-cover xlx:dw-py-24 mdx:dw-py-[64px] dw-py-[48px]\"\n>\n  <div\n    class=\"dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px] dw-px-8\"\n  >\n    <div class=\"dw-flex dw-flex-col dw-items-center\">\n      <h2\n        class=\"dw-font-heading dw-text-center mdx:dw-text-[48px] dw-text-[26px] dw-pb-4 dw-pb-6 dw-font-semibold dw-w-full\"\n      >\n        ${testimonial_title}\n      </h2>\n      <div\n        class=\"dw-grid dw-grid-cols-1 mdx:dw-grid-cols-2 mdl:dw-grid-cols-4 dw-gap-[14px] dw-gap-y-[25px] dw-w-full\"\n      >\n        {{testimonial_cards}}\n      </div>\n    </div>\n  </div>\n</section>\n</div></div>\n<div><div data-component=\"we_offer_list\">\n  <div\n    class=\"dw-my-16 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px] dw-px-[2rem]\"\n  >\n    <h2\n      class=\"dw-font-heading dw-text-center mdx:dw-text-[48px] dw-text-[26px] mdx:dw-pb-[48px] dw-pb-[20px] dw-font-semibold dw-w-full lgl:dw-w-[72%] lgs:dw-w-[80%] dw-mx-auto dw-leading-[30px] mdx:dw-leading-[55px] xl1:dw-leading-[60px]\"\n    >\n      ${we_offer_services_title}\n    </h2>\n\n    <div\n      class=\"dw-grid mdl:dw-grid-cols-2 dw-gap-x-[6px] mdx:dw-gap-x-[40px] lgx:dw-gap-x-[80px] mdx:dw-px-0 dw-px-[6px]\"\n    >\n      {{we_offer_services_list}}\n    </div>\n  </div>\n</div>\n</div>\n\n  <script>\n    try {\n       document.addEventListener(\"DOMContentLoaded\", () => {\n        const accordions = document.querySelectorAll(\".accordion-btn\");\n\n        accordions.forEach((btn) => {\n          btn.addEventListener(\"click\", () => {\n            const body = btn.nextElementSibling;\n\n            body.classList.toggle(\"dw-hidden\");\n\n            accordions.forEach((otherBtn) => {\n              if (otherBtn !== btn) {\n                otherBtn.nextElementSibling.classList.add(\"dw-hidden\");\n              }\n            });\n          });\n        });\n      });\n    } catch(e) {\n      console.error('JavaScript error:', e);\n    }\n  </script>\n</body>\n</html>",

                "components": [
                    {
                        "name": "hero_section_home_page",
                        "category_id": "cat2",
                        "html_content": "<div data-component=\"hero_section_home_page\"><div\n  class=\"dw-relative dw-z-0 dw-overflow-hidden dw-bg-white dw-bg-[top_right] dw-bg-no-repeat dw-bg-[length:50%_auto] dw-px-[5rem] dw-pt-2 dw-pb-[9rem] xlx:dw-px-[3rem] xlx:dw-pb-[7.3rem] xlx:dw-bg-[length:46%_auto] xlx:dw-bg-none lgl:dw-px-[3rem] lgl:dw-pb-[8.3rem] lgl:dw-bg-[length:46%_auto] lgl:dw-bg-none lgm:dw-px-[2.5rem] lgm:dw-pb-[9.3rem] lgm:dw-bg-[length:46%_auto] lgm:dw-bg-none lgs:dw-px-[3rem] lgs:dw-pb-[9.3rem] lgs:dw-bg-[length:46%_auto] lgs:dw-bg-none lgx:dw-px-[3rem] lgx:dw-pb-[9.3rem] lgx:dw-bg-[length:46%_auto] lgx:dw-bg-none mdl:dw-pl-[2.5rem] mdl:dw-pr-0 mdl:dw-pb-[9rem] mdl:dw-bg-[length:46%_auto] mdl:dw-bg-none mdx:dw-px-[2.5rem] mdx:dw-pb-[9rem] mdx:dw-bg-[length:46%_auto] smm:dw-px-[1rem] smm:dw-pb-[6rem] smm:dw-bg-none smx:dw-px-0 smx:dw-pb-[6rem] smx:dw-bg-none xs:dw-px-0 xs:dw-pb-[6rem] xs:dw-bg-none\"\n>\n  <svg\n    class=\"dw-absolute dw-top-0 dw-left-1/2 dw-hidden mdx:dw-block\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0.03 0 645.94 800\"\n  >\n    <path\n      d=\"M409.731 554.556L418.669 537.316C421.506 531.775 424.147 526.333 426.631 520.781C431.502 509.725 435.484 498.498 437.992 487.406C443.094 465.014 440.439 444.475 426.827 429.266C413.214 413.753 390.738 403.342 367.505 396.086C318.045 381.852 267.467 366.011 218.531 347.431C206.247 342.767 194.022 337.922 181.845 332.759C169.852 327.719 156.848 321.827 144.952 313.85C133.191 305.9 120.856 295.306 114.489 279.064C111.334 271.077 110.506 262.042 111.567 253.834C112.637 245.605 115.317 238.116 118.642 231.442C125.558 217.769 134.044 207.906 141.691 197.325L165.495 166.277L213.603 104.836L295.511 1.79063L233.07 0C233.07 0 90.8422 130.795 27.3563 213.336C-27.8375 285.077 2.30937 348.466 102.153 391.105C186.423 427.109 265.3 454.98 265.3 454.98C278.889 462.017 288.556 474.352 291.77 488.744C294.984 503.148 291.43 518.138 282.017 529.838L64.7484 800H281.519L373.716 623.644L409.731 554.556Z\"\n      class=\"dw-fill-theme-main\"\n      fill-opacity=\"0.239216\"\n    ></path>\n    <path\n      d=\"M618.27 407.848C588.012 357.233 536.216 321.533 476.212 309.928L263.631 265.109C254.767 263.405 247.316 257.78 243.553 249.963C239.791 242.158 240.192 233.109 244.612 225.622L385.928 4.37188L317.097 2.4L230.075 117.512L183.587 179.95L160.733 211.328C153.378 221.884 145.269 232.32 140.8 242.084C136.041 252.178 134.847 261.798 138.281 269.784C141.592 277.967 149.689 285.577 159.455 291.691C169.342 297.948 180.216 302.659 192.27 307.384C204.13 312.072 216.159 316.517 228.275 320.778C276.955 337.884 325.987 351.959 376.712 365.245C402.72 372.989 430.25 383.362 452.18 406.498C462.87 418.077 470.845 433.284 473.877 449.028C476.994 464.784 475.972 480.297 473.244 494.639C470.37 508.97 465.878 522.437 460.605 535.27C457.939 541.65 455.064 547.97 452.119 554.058L443.547 571.628L409.198 641.628L331.256 800H550.984L634.097 573.991C654.298 519.028 648.527 458.463 618.27 407.848Z\"\n      class=\"dw-fill-theme-main\"\n      fill-opacity=\"0.611765\"\n    ></path>\n  </svg>\n\n  <div\n    class=\"dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdx:dw-grid-cols-2\">\n      <div class=\"sub-buses-1 dw-px-1\">\n        <h1\n          class=\"dw-text-[32px] mdx:dw-text-[48px] dw-font-bold dw-mb-4 dw-font-heading\"\n        >\n          ${hero_section_title}\n        </h1>\n        <p class=\"dw-text-base xl1:dw-text-lg dw-mb-6\">\n          ${hero_section_description}\n          <strong>${contact_number}</strong>\n          ${hero_section_extra_text}\n        </p>\n\n        <div\n          class=\"dw-flex lgm:dw-flex-row dw-flex-col dw-gap-5 dw-items-start\"\n        >\n          <a\n            href=\"tel:${contact_number}\"\n            class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center dw-gap-[10px] dw-pe-[10px] lgs:dw-text-[18px] dw-text-[16px] dw-ps-[3px] dw-py-[3px] dw-text-white dw-bg-body-text dw-rounded-full hover:dw-bg-theme-main dw-border-[2px] dw-border-body-text hover:dw-border-theme-main dw-transition\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"42\"\n              height=\"42\"\n              viewBox=\"0 0 36 36\"\n              fill=\"none\"\n            >\n              <rect width=\"36\" height=\"36\" rx=\"18\" class=\"dw-fill-theme-main\" />\n              <path\n                d=\"M27.7388 22.4138C27.5716 23.6841 26.9477 24.8502 25.9837 25.6942C25.0196 26.5382 23.7813 27.0023 22.5 27.0001C15.0563 27.0001 9.00001 20.9438 9.00001 13.5001C8.99771 12.2188 9.4619 10.9805 10.3059 10.0164C11.1499 9.05234 12.3159 8.42847 13.5863 8.2613C13.9075 8.22208 14.2328 8.2878 14.5136 8.44865C14.7944 8.60951 15.0157 8.85687 15.1444 9.1538L17.1244 13.5741V13.5854C17.2229 13.8127 17.2636 14.0608 17.2428 14.3077C17.222 14.5545 17.1404 14.7924 17.0053 15.0001C16.9884 15.0254 16.9706 15.0488 16.9519 15.0722L15 17.386C15.7022 18.8129 17.1947 20.2922 18.6403 20.9963L20.9222 19.0547C20.9446 19.0359 20.9681 19.0184 20.9925 19.0022C21.2 18.8639 21.4387 18.7794 21.687 18.7565C21.9353 18.7336 22.1854 18.7729 22.4147 18.871L22.4269 18.8766L26.8434 20.8557C27.1409 20.9839 27.3889 21.205 27.5503 21.4858C27.7116 21.7667 27.7778 22.0922 27.7388 22.4138Z\"\n                fill=\"white\"\n              ></path>\n            </svg>\n            ${contact_number_button_label}\n          </a>\n\n          <a\n            href=\"${quote_url}\"\n            class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center dw-gap-[6px] lgs:dw-text-[18px] dw-text-[16px] dw-pe-[10px] dw-ps-3 dw-py-[9.5px] dw-text-white dw-border-[2px] dw-border-theme-main dw-rounded-full dw-bg-theme-main\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              id=\"Layer_1\"\n              data-name=\"Layer 1\"\n              viewBox=\"0 0 35.05 60.44\"\n              width=\"30\"\n              height=\"30\"\n              fill=\"white\"\n            >\n              <path\n                d=\"M21.45 7.46c2.09-.11 4.18.1 6.23.44l.13.22c-.51 1.39-2.17.52-3.38.48-9.62-.33-17.23 4.17-21.45 12.76-.11.23-.47 1.43-.53 1.49-.23.26-1.08.01-1.14-.18-.08-.25 1.18-2.92 1.4-3.33C6.35 12.59 13.75 7.86 21.44 7.45Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M22.15 9.65c.52-.02 4.24.09 4.25.48-.74 1.21-.54.76-1.62.75-7.73-.07-14.38 2.32-18.55 9.17-.33.55-1.51 3.39-1.67 3.51-.13.1-1.1-.11-1.14-.26-.04-.13.91-2.17 1.05-2.46C7.73 14.38 14.91 9.88 22.15 9.66Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M.13 29.21c.06-.03.97 0 1.01.04.09.13-.04 2.64 0 3.07.56 6.25 4.19 12.15 9.25 15.75.45.32 2.88 1.55 2.94 1.71.02.05-.36.77-.39.92C5.95 47.64.79 40.17.09 32.59c-.04-.45-.2-3.25.04-3.38\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"M21.71 11.93c1.05-.06 2.11.02 3.16.09.26.1-.47 1.03-.53 1.05-.37.13-1.97-.06-2.54 0-5.7.56-10.71 3.36-13.73 8.29-.29.48-1.09 2.47-1.23 2.63-.21.23-.9.01-1.14-.18 2.33-6.64 8.99-11.48 16.01-11.89Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M2.42 29.21c.07-.03.96 0 1.01.04-.56 6.56 2.81 12.97 8.03 16.8.39.29 2.65 1.47 2.68 1.62 0 .05-.37.76-.39.92-5.39-2.46-9.76-8.03-11.01-13.82-.15-.7-.78-5.36-.31-5.57Z\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"m4.61 29.21 1.01.04c-.47 6.78 3.33 13.04 9.21 16.23l-.31 1.01C8.17 43.38 3.99 36.33 4.61 29.21M2.94 24.56s.54.12.66.13c-.19.44-.08 3.28-.22 3.38-.03.02-.62.02-.66-.04-.02-.46-.01-3.28.22-3.46ZM5.31 25.09l.66.13c-.14.37-.1 2.77-.22 2.85-.03.02-.62.02-.66-.04 0-.98-.06-1.98.22-2.94\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"M35.05 0 25.01 27.68l8.95.26-23.39 32.5c3.39-9.18 7.02-18.39 9.82-27.72l-8.73-.13z\"\n                style=\"fill: #fdfeff\"\n              />\n              <path\n                d=\"M29.52 8.95c.26 0 0 .31-.04.39-4.07 7.61-10.02 14.97-14.3 22.54l-1.71.13z\"\n                style=\"fill: #fefefe\"\n              />\n            </svg>\n            ${get_quote_button_text}\n          </a>\n        </div>\n      </div>\n\n      <div\n        class=\"dw-z-10 dw-pt-10 dw-pb-10 mdl:dw-pt-16 mdl:dw-ps-10 lgm:dw-ps-[60px]\"\n      >\n        <img\n          src=\"${img-hero-section-image}\"\n          class=\"dw-rounded-[16px] dw-w-full dw-h-auto\"\n          alt=\"img-hero-section-image\"\n        />\n      </div>\n    </div>\n  </div>\n</div>\n\n<div class=\"dw-relative dw--mt-[85px] dw-z-[1]\">\n  <div\n    class=\"dw-px-8 lgm:dw-px-0 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdl:dw-grid-cols-3 dw-gap-3 dw-gap-y-12\">\n      \n      <!-- Box 1 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=`${img-advantage-image}`\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${hero_section_card1_content}\n          </p>\n        </div>\n      </div>\n\n      <!-- Box 2 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${hero_section_card2_content}\n          </p>\n        </div>\n      </div>\n\n      <!-- Box 3 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${hero_section_card3_content}\n          </p>\n        </div>\n      </div>\n    </div>\n  </div>\n</div></div>",
                        "css_content": "",
                        "js_content": "",
                        "placeholders": [
                            "hero_section_title",
                            "hero_section_description",
                            "contact_number",
                            "hero_section_extra_text",
                            "contact_number_button_label",
                            "quote_url",
                            "get_quote_button_text",
                            "img-hero-section-image",
                            "img-advantage-image",
                            "hero_section_card1_content",
                            "hero_section_card2_content",
                            "hero_section_card3_content"
                        ],
                        "preview_image": "",
                        "version": 1,
                        "status": "draft",
                        "tags": [],
                        "thumbnail_url": "",
                        "id": "mejxa6hnolket8gfqqg",
                        "created_at": "2025-08-20T12:02:02.459Z",
                        "updated_at": "2025-08-21T11:19:53.780Z",
                        "category_name": "Content",
                        "category_color": "#10B981",
                        "order": 0,
                        "repeator": "single",
                        "cssClass": "",
                        "uniqueId": "mejxa6hnolket8gfqqg-1755852044433-n3nr3fxos"
                    },
                    {
                        "name": "why_section_home_page",
                        "category_id": "cat2",
                        "html_content": "<div data-component=\"why_section_home_page\"><div\n  class=\"dw-bg-theme-cardBg dw-bg-[url('${img-background-pattern-bg-image}')] dw-bg-top dw-bg-no-repeat dw-bg-cover mdl:dw-pt-24 mdl:dw-pb-[15rem] dw-pt-[5rem] dw-pb-[7rem]\"\n>\n  <div\n    class=\"dw-px-8 lgm:dw-px-0 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdl:dw-grid-cols-2 dw-items-center\">\n      <!-- Left Content -->\n      <div class=\"sub-buses-1 dw-px-1\">\n        <h2\n          class=\"dw-font-heading dw-leading-tight dw-text-[26px] mdx:dw-text-[46px] lgx:text-[48px] dw-font-semibold dw-mb-4\"\n        >\n          ${why_section_heading}\n        </h2>\n        <p class=\"xl1:dw-text-lg dw-text-base dw-leading-relaxed\">\n          ${why_section_description}\n        </p>\n      </div>\n\n      <!-- Right Image -->\n      <div\n        class=\"why-rent-image sub-buses-2 dw-pt-[40px] mdl:dw-ps-[40px] lgm:dw-ps-[60px] lgs:dw-pt-0\"\n      >\n        <img\n          src=\"${img-why-section-image}\"\n          alt=\"img-why-section-image\"\n          class=\"dw-w-full dw-h-auto dw-rounded-[16px]\"\n        />\n      </div>\n    </div>\n  </div>\n</div>\n<div class=\"dw-relative dw--mt-[85px] dw-z-[1]\">\n  <div\n    class=\"dw-px-8 lgm:dw-px-0 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdl:dw-grid-cols-3 dw-gap-3 dw-gap-y-12\">\n      \n      <!-- Box 1 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${why_section_card1_content}\n          </p>\n        </div>\n      </div>\n\n      <!-- Box 2 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${why_section_card2_content}\n          </p>\n        </div>\n      </div>\n\n      <!-- Box 3 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${why_section_card3_content}\n          </p>\n        </div>\n      </div>\n    </div>\n  </div>\n</div></div>",
                        "css_content": "",
                        "js_content": "",
                        "placeholders": [
                            "img-background-pattern-bg-image",
                            "why_section_heading",
                            "why_section_description",
                            "img-why-section-image",
                            "img-advantage-image",
                            "why_section_card1_content",
                            "why_section_card2_content",
                            "why_section_card3_content"
                        ],
                        "preview_image": "",
                        "version": 1,
                        "status": "draft",
                        "tags": [],
                        "thumbnail_url": "",
                        "id": "mejxkuo2q6g7dim380s",
                        "created_at": "2025-08-20T12:10:20.354Z",
                        "updated_at": "2025-08-21T11:21:58.734Z",
                        "category_name": "Content",
                        "category_color": "#10B981",
                        "order": 1,
                        "repeator": "single",
                        "cssClass": "",
                        "uniqueId": "mejxkuo2q6g7dim380s-1755852046724-wf8wnll0e"
                    },
                    {
                        "name": "bus_grid_list",
                        "category_id": "cat2",
                        "html_content": "<div data-component=\"bus_grid_list\"><div\n  class=\"dw-my-16 dw-mx-auto dw-w-full dw-px-6 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgl:dw-px-6 lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdl:dw-px-6 mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smm:dw-px-6 smx:dw-max-w-[700px] smx:dw-px-6\"\n>\n  <div class=\"dw-flex dw-justify-center\">\n    <h2\n      class=\"xl1:dw-mx-[395px] dw-font-semibold lgm:dw-text-[48px] lgx:dw-text-[50px] mdx:dw-text-[46px] dw-text-[26px] xl1:dw-leading-[60px] lgx:dw-leading-[46px] dw-uppercase dw-text-center dw-pb-[25px] dw-font-heading\"\n    >\n      ${buses_list_heading}\n    </h2>\n  </div>\n\n  <div\n    class=\"dw-grid dw-grid-cols-1 mdx:dw-grid-cols-2 lgs:dw-grid-cols-3 mdx:dw-gap-8 dw-gap-4 xl1:dw-gap-[50px] dw-mb-4\"\n  >\n    {{busesList}}\n  </div>\n</div></div>",
                        "css_content": "",
                        "js_content": "",
                        "placeholders": [
                            "buses_list_heading"
                        ],
                        "preview_image": "",
                        "version": 1,
                        "status": "draft",
                        "tags": [],
                        "thumbnail_url": "",
                        "id": "mejz65z1z7hhj4rxbr",
                        "created_at": "2025-08-20T12:54:54.397Z",
                        "updated_at": "2025-08-21T11:23:11.906Z",
                        "category_name": "Content",
                        "category_color": "#10B981",
                        "order": 2,
                        "repeator": "repeat",
                        "cssClass": "",
                        "uniqueId": "mejz65z1z7hhj4rxbr-1755852048083-gnecsryuy",
                        "repeatedComponentId": "melbj7f65tmd8ocep4a",
                        "repeatedComponentName": [
                            "busesList"
                        ],
                        "repeatedComponent": {
                            "name": "bud_grid_card",
                            "category_id": "cat2",
                            "html_content": "<div data-component=\"bud_grid_card\"><div\n  class=\"dw-border dw-border-theme-cardBorder dw-rounded-[32px] dw-bg-theme-cardBg xl1:dw-p-[3rem_2rem_1.5rem] dw-p-[2.5rem_1rem_1rem] dw-min-h-full dw-relative\"\n>\n  <span\n    class=\"dw-absolute dw-z-10 dw-bg-body-bg dw-text-theme-lowOpacityTextColor dw-font-normal dw-border dw-shadow-sm dw-text-[14px] dw-px-[15px] dw-py-[8px] dw-ml-[10px] dw--translate-y-[20px] dw-rounded-full\"\n  >\n    ${bus_price_range}\n  </span>\n\n  <img\n    src=\"${img-bus-image}\"\n    class=\"dw-rounded-[16px] dw-w-full dw-h-auto\"\n    alt=\"img-busImage\"\n  />\n  <img\n    src=\"${img-bus-interior}\"\n    class=\"dw-rounded-[16px] dw-w-full dw-h-auto dw-mt-4\"\n    alt=\"img-busInterior\"\n  />\n\n  <h3\n    class=\"dw-font-semibold lgm:dw-text-[24px] lgx:dw-text-[22px] dw-text-[20px] dw-leading-[32px] dw-uppercase dw-tracking-[-0.176px] dw-text-center dw-pt-[32px] dw-pb-[25px] dw-font-heading dw-mb-2\"\n  >\n    <a href=\"${view_Bus_Url}\"> ${bus_title} </a>\n  </h3>\n\n  <div class=\"dw-flex dw-justify-center dw-gap-4 dw-pb-[5px]\">\n    <a\n      href=\"${view_Bus_Url}\"\n      class=\"dw-bg-body-text dw-text-white dw-border-2 dw-border-body-text dw-rounded-full dw-px-[16px] dw-py-[13px] dw-text-[18px] dw-leading-[24px] dw-font-medium dw-tracking-[-0.24px] hover:dw-border-theme-main hover:dw-bg-theme-main\"\n    >\n      ${bus_view_button_text}\n    </a>\n    <a\n      href=\"${quote_url}\"\n      class=\"dw-bg-theme-main dw-text-white dw-border-2 dw-border-theme-main dw-rounded-full dw-px-[16px] dw-py-[13px] dw-inline-flex dw-items-center dw-justify-center dw-text-[18px] dw-leading-[24px] dw-font-medium dw-tracking-[-0.24px]\"\n    >\n      ${get_quote_button_text}\n    </a>\n  </div>\n</div>\n</div>",
                            "css_content": "",
                            "js_content": "",
                            "placeholders": [
                                "bus_price_range",
                                "img-bus-image",
                                "img-bus-interior",
                                "view_Bus_Url",
                                "bus_title",
                                "bus_view_button_text",
                                "quote_url",
                                "get_quote_button_text"
                            ],
                            "preview_image": "",
                            "version": 1,
                            "status": "draft",
                            "tags": [],
                            "thumbnail_url": "",
                            "id": "melbj7f65tmd8ocep4a",
                            "created_at": "2025-08-21T11:28:44.370Z",
                            "updated_at": "2025-08-22T05:03:22.688Z",
                            "category_name": "Content",
                            "category_color": "#10B981"
                        }
                    },
                    {
                        "name": "bus_amenities",
                        "category_id": "cat2",
                        "html_content": "<div data-component=\"bus_amenities\"><div class=\"dw-my-16\">\n  <div\n    class=\"dw-mx-auto dw-w-full dw-px-8 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgl:dw-px-8 lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] lgs:dw-px-8 mdl:dw-max-w-[1080px] mdl:dw-px-8 mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smm:dw-px-8 smx:dw-max-w-[700px] smx:dw-px-8 mdx:dw-px-[132px]\"\n  >\n    <div class=\"dw-text-center dw-pb-[20px]\">\n      <h2\n        class=\"dw-font-heading xlx:dw-text-[24px] lgx:dw-text-[22px] dw-text-[20px] dw-leading-[32px] dw-font-semibold dw-uppercase dw-tracking-[-0.011em] dw-pt-8 dw-pb-5\"\n      >\n        ${amenities_title}\n      </h2>\n    </div>\n    <div\n      class=\"dw-grid dw-grid-cols-1 mdx:dw-grid-cols-2 mdl:dw-grid-cols-3 lgs:dw-grid-cols-4 dw-gap-x-6 dw-gap-y-6\"\n    >\n      {{amenities_cards}}\n    </div>\n  </div>\n</div></div>",
                        "css_content": "",
                        "js_content": "",
                        "placeholders": [
                            "amenities_title"
                        ],
                        "preview_image": "",
                        "version": 1,
                        "status": "draft",
                        "tags": [],
                        "thumbnail_url": "",
                        "id": "mel05d6jebm3z3va507",
                        "created_at": "2025-08-21T06:10:02.875Z",
                        "updated_at": "2025-08-21T11:29:41.045Z",
                        "category_name": "Content",
                        "category_color": "#10B981",
                        "order": 3,
                        "repeator": "repeat",
                        "cssClass": "",
                        "uniqueId": "mel05d6jebm3z3va507-1755852061728-3pnoa23ny",
                        "repeatedComponentId": "melbmuizp786kngqmks",
                        "repeatedComponentName": [
                            "amenities_cards"
                        ],
                        "repeatedComponent": {
                            "name": "amenities_card",
                            "category_id": "cat2",
                            "html_content": "<div data-component=\"amenities_card\"><div\n  class=\"dw-flex dw-flex-col dw-justify-center dw-items-center dw-text-center dw-border dw-border-gray-200 dw-rounded-[16px] dw-min-h-[180px] dw-shadow-sm dw-px-4 dw-py-6\"\n>\n  <img src=\"${img-amenities_image}\" alt=\"amenities Image\" class=\"dw-mx-auto\" />\n  <p class=\"dw-pt-5 dw-font-medium dw-text-center xl1:dw-text-lg dw-text-base\">\n    ${amenities_text}\n  </p>\n</div>\n</div>",
                            "css_content": "",
                            "js_content": "",
                            "placeholders": [
                                "img-amenities_image",
                                "amenities_text"
                            ],
                            "preview_image": "",
                            "version": 1,
                            "status": "draft",
                            "tags": [],
                            "thumbnail_url": "",
                            "id": "melbmuizp786kngqmks",
                            "created_at": "2025-08-21T11:31:34.283Z",
                            "updated_at": "2025-08-21T11:31:34.283Z",
                            "category_name": "Content",
                            "category_color": "#10B981"
                        }
                    },
                    {
                        "name": "note_sure",
                        "category_id": "cat2",
                        "html_content": "<div data-component=\"note_sure\"><div\n  class=\"dw-my-16 dw-mx-auto dw-w-full dw-px-6 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgl:dw-px-6 lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdl:dw-px-6 mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smm:dw-px-6 smx:dw-max-w-[700px] smx:dw-px-6\"\n>\n  <div class=\"dw-mt-[80px] dw-mb-[40px]\">\n    <div class=\"dw-w-full\">\n      <!-- Heading -->\n      <p\n        class=\"dw-font-semibold dw-text-[20px] lgx:dw-text-[22px] xl1:dw-text-[24px] dw-uppercase dw-leading-8 dw-tracking-tight dw-pb-5 dw-text-center dw-m-0 dw-font-heading\"\n      >\n        ${note_sure_title}\n      </p>\n\n      <!-- Description -->\n      <p\n        class=\"dw-text-center dw-font-medium dw-text-[16px] lgx:dw-text-[18px] dw-pb-2\"\n      >\n        ${note_sure_description}\n      </p>\n\n      <!-- Buttons -->\n      <div class=\"dw-text-center dw-mt-10\">\n        <div\n          class=\"dw-flex dw-flex-col mdx:dw-flex-row dw-justify-center dw-items-center dw-gap-4\"\n        >\n          <!-- Call Us Button -->\n          <a\n            href=\"tel:${contact_number}\"\n            class=\"dw-inline-flex dw-font-medium dw-items-center mdx:dw-gap-[10px] dw-gap-[35px] dw-pe-[10px] lgs:dw-text-[18px] dw-text-[16px] dw-ps-[3px] dw-py-[3px] dw-text-white dw-bg-body-text dw-rounded-full hover:dw-bg-theme-main dw-border-[2px] dw-border-body-text hover:dw-border-theme-main dw-transition\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"42\"\n              height=\"42\"\n              viewBox=\"0 0 36 36\"\n              fill=\"none\"\n            >\n              <rect width=\"36\" height=\"36\" rx=\"18\" class=\"dw-fill-theme-main\" />\n              <path\n                d=\"M27.7388 22.4138C27.5716 23.6841 26.9477 24.8502 25.9837 25.6942C25.0196 26.5382 23.7813 27.0023 22.5 27.0001C15.0563 27.0001 9.00001 20.9438 9.00001 13.5001C8.99771 12.2188 9.4619 10.9805 10.3059 10.0164C11.1499 9.05234 12.3159 8.42847 13.5863 8.2613C13.9075 8.22208 14.2328 8.2878 14.5136 8.44865C14.7944 8.60951 15.0157 8.85687 15.1444 9.1538L17.1244 13.5741V13.5854C17.2229 13.8127 17.2636 14.0608 17.2428 14.3077C17.222 14.5545 17.1404 14.7924 17.0053 15.0001C16.9884 15.0254 16.9706 15.0488 16.9519 15.0722L15 17.386C15.7022 18.8129 17.1947 20.2922 18.6403 20.9963L20.9222 19.0547C20.9446 19.0359 20.9681 19.0184 20.9925 19.0022C21.2 18.8639 21.4387 18.7794 21.687 18.7565C21.9353 18.7336 22.1854 18.7729 22.4147 18.871L22.4269 18.8766L26.8434 20.8557C27.1409 20.9839 27.3889 21.205 27.5503 21.4858C27.7116 21.7667 27.7778 22.0922 27.7388 22.4138Z\"\n                fill=\"white\"\n              ></path>\n            </svg>\n            ${contact_number_button_label}\n          </a>\n\n          <!-- Get Quote Button -->\n          <a\n            href=\"${quote_url}\"\n            class=\"dw-inline-flex dw-font-medium dw-items-center dw-gap-[6px] lgs:dw-text-[18px] dw-text-[16px] dw-pe-[10px] dw-ps-3 dw-py-[9.5px] dw-text-white dw-border-[2px] dw-border-theme-main dw-rounded-full dw-bg-theme-main\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              id=\"Layer_1\"\n              data-name=\"Layer 1\"\n              viewBox=\"0 0 35.05 60.44\"\n              width=\"30\"\n              height=\"30\"\n              fill=\"white\"\n            >\n              <path\n                d=\"M21.45 7.46c2.09-.11 4.18.1 6.23.44l.13.22c-.51 1.39-2.17.52-3.38.48-9.62-.33-17.23 4.17-21.45 12.76-.11.23-.47 1.43-.53 1.49-.23.26-1.08.01-1.14-.18-.08-.25 1.18-2.92 1.4-3.33C6.35 12.59 13.75 7.86 21.44 7.45Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M22.15 9.65c.52-.02 4.24.09 4.25.48-.74 1.21-.54.76-1.62.75-7.73-.07-14.38 2.32-18.55 9.17-.33.55-1.51 3.39-1.67 3.51-.13.1-1.1-.11-1.14-.26-.04-.13.91-2.17 1.05-2.46C7.73 14.38 14.91 9.88 22.15 9.66Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M.13 29.21c.06-.03.97 0 1.01.04.09.13-.04 2.64 0 3.07.56 6.25 4.19 12.15 9.25 15.75.45.32 2.88 1.55 2.94 1.71.02.05-.36.77-.39.92C5.95 47.64.79 40.17.09 32.59c-.04-.45-.2-3.25.04-3.38\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"M21.71 11.93c1.05-.06 2.11.02 3.16.09.26.1-.47 1.03-.53 1.05-.37.13-1.97-.06-2.54 0-5.7.56-10.71 3.36-13.73 8.29-.29.48-1.09 2.47-1.23 2.63-.21.23-.9.01-1.14-.18 2.33-6.64 8.99-11.48 16.01-11.89Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M2.42 29.21c.07-.03.96 0 1.01.04-.56 6.56 2.81 12.97 8.03 16.8.39.29 2.65 1.47 2.68 1.62 0 .05-.37.76-.39.92-5.39-2.46-9.76-8.03-11.01-13.82-.15-.7-.78-5.36-.31-5.57Z\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"m4.61 29.21 1.01.04c-.47 6.78 3.33 13.04 9.21 16.23l-.31 1.01C8.17 43.38 3.99 36.33 4.61 29.21M2.94 24.56s.54.12.66.13c-.19.44-.08 3.28-.22 3.38-.03.02-.62.02-.66-.04-.02-.46-.01-3.28.22-3.46ZM5.31 25.09l.66.13c-.14.37-.1 2.77-.22 2.85-.03.02-.62.02-.66-.04 0-.98-.06-1.98.22-2.94\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"M35.05 0 25.01 27.68l8.95.26-23.39 32.5c3.39-9.18 7.02-18.39 9.82-27.72l-8.73-.13z\"\n                style=\"fill: #fdfeff\"\n              />\n              <path\n                d=\"M29.52 8.95c.26 0 0 .31-.04.39-4.07 7.61-10.02 14.97-14.3 22.54l-1.71.13z\"\n                style=\"fill: #fefefe\"\n              />\n            </svg>\n            ${get_quote_button_text}\n          </a>\n        </div>\n      </div>\n    </div>\n  </div>\n</div></div>",
                        "css_content": "",
                        "js_content": "",
                        "placeholders": [
                            "note_sure_title",
                            "note_sure_description",
                            "contact_number",
                            "contact_number_button_label",
                            "quote_url",
                            "get_quote_button_text"
                        ],
                        "preview_image": "",
                        "version": 1,
                        "status": "draft",
                        "tags": [],
                        "thumbnail_url": "",
                        "id": "mel0t7zfjeg1zxrr0m",
                        "created_at": "2025-08-21T06:28:35.883Z",
                        "updated_at": "2025-08-22T05:04:09.666Z",
                        "category_name": "Content",
                        "category_color": "#10B981",
                        "order": 4,
                        "repeator": "single",
                        "cssClass": "",
                        "uniqueId": "mel0t7zfjeg1zxrr0m-1755852073254-6fdl28lr0"
                    },
                    {
                        "name": "book_in_minutes",
                        "category_id": "cat2",
                        "html_content": "<div data-component=\"book_in_minutes\"><div\n  class=\"dw-my-16 homepage-cta dw-bg-theme-mainColorShade dw-bg-[url('${img-book_in_minutes_cta_bg}')] dw-bg-no-repeat dw-bg-cover dw-py-[64px] mdx:dw-pb-[96px] mdl:dw-px-[160px] lgm:dw-px-[240px] xl1:dw-px-[160px] xl1:dw-py-[80px]\"\n>\n  <div\n    class=\"dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"row\">\n      <div class=\"dw-w-full\">\n        <h2\n          class=\"dw-mx-1 dw-text-white dw-font-heading dw-text-center dw-mb-[30px] xl1:dw-w-1/2 dw-w-full mdl:dw-mx-auto lgm:dw-text-[48px] lgx:dw-text-[50px] mdx:dw-text-[46px] dw-text-[26px] dw-font-semibold xl1:dw-leading-[60px] lgx:dw-leading-[46px] dw-pb-4\"\n        >\n          ${book_in_minutes_title}\n        </h2>\n        <div\n          class=\"dw-flex dw-flex-col mdx:dw-flex-row dw-items-center dw-justify-center dw-gap-4 dw-mt-6\"\n        >\n          <!-- Call Us Button -->\n          <a\n            href=\"tel:${contact_number}\"\n            class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center mdx:dw-gap-[10px] dw-pe-[40px] dw-gap-[30px] mdx:dw-pe-[10px] lgs:dw-text-[18px] dw-text-[16px] dw-ps-[3px] dw-py-[3px] dw-bg-white dw-rounded-full hover:dw-bg-theme-main hover:dw-text-white dw-border-[2px] dw-border-white hover:dw-border-theme-main dw-transition\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"42\"\n              height=\"42\"\n              viewBox=\"0 0 36 36\"\n              fill=\"none\"\n            >\n              <rect width=\"36\" height=\"36\" rx=\"18\" class=\"dw-fill-theme-main\" />\n              <path\n                d=\"M27.7388 22.4138C27.5716 23.6841 26.9477 24.8502 25.9837 25.6942C25.0196 26.5382 23.7813 27.0023 22.5 27.0001C15.0563 27.0001 9.00001 20.9438 9.00001 13.5001C8.99771 12.2188 9.4619 10.9805 10.3059 10.0164C11.1499 9.05234 12.3159 8.42847 13.5863 8.2613C13.9075 8.22208 14.2328 8.2878 14.5136 8.44865C14.7944 8.60951 15.0157 8.85687 15.1444 9.1538L17.1244 13.5741V13.5854C17.2229 13.8127 17.2636 14.0608 17.2428 14.3077C17.222 14.5545 17.1404 14.7924 17.0053 15.0001C16.9884 15.0254 16.9706 15.0488 16.9519 15.0722L15 17.386C15.7022 18.8129 17.1947 20.2922 18.6403 20.9963L20.9222 19.0547C20.9446 19.0359 20.9681 19.0184 20.9925 19.0022C21.2 18.8639 21.4387 18.7794 21.687 18.7565C21.9353 18.7336 22.1854 18.7729 22.4147 18.871L22.4269 18.8766L26.8434 20.8557C27.1409 20.9839 27.3889 21.205 27.5503 21.4858C27.7116 21.7667 27.7778 22.0922 27.7388 22.4138Z\"\n                fill=\"white\"\n              ></path>\n            </svg>\n            ${contact_number_button_label}\n          </a>\n\n          <!-- Get Quote Button -->\n          <a\n            href=\"${quote_url}\"\n            class=\"dw-flex dw-font-medium dw-w-fit dw-items-center dw-gap-[6px] lgs:dw-text-[18px] dw-text-[16px] dw-pe-[10px] dw-border-[2px] hover:dw-bg-theme-main hover:dw-text-white dw-rounded-full dw-bg-white dw-border-white hover:dw-border-theme-main dw-transition\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              id=\"Layer_1\"\n              data-name=\"Layer 1\"\n              viewBox=\"0 0 72 84\"\n              width=\"48\"\n              height=\"48\"\n              class=\"dw-fill-white\"\n            >\n              <circle\n                cx=\"36\"\n                cy=\"42\"\n                r=\"36\"\n                class=\"dw-fill-theme-main\"\n              ></circle>\n              <g transform=\"translate(18, 18) scale(0.8)\">\n                <path\n                  d=\"M21.45 7.46c2.09-.11 4.18.1 6.23.44l.13.22c-.51 1.39-2.17.52-3.38.48-9.62-.33-17.23 4.17-21.45 12.76-.11.23-.47 1.43-.53 1.49-.23.26-1.08.01-1.14-.18-.08-.25 1.18-2.92 1.4-3.33C6.35 12.59 13.75 7.86 21.44 7.45Z\"\n                  class=\"cls-3\"\n                ></path>\n                <path\n                  d=\"M22.15 9.65c.52-.02 4.24.09 4.25.48-.74 1.21-.54.76-1.62.75-7.73-.07-14.38 2.32-18.55 9.17-.33.55-1.51 3.39-1.673.51-.13.1-1.1-.11-1.14-.26-.04-.13.91-2.17 1.05-2.46C7.73 14.38 14.91 9.88 22.15 9.66Z\"\n                  class=\"cls-3\"\n                ></path>\n                <path\n                  d=\"M.13 29.21c.06-.03.97 0 1.01.04.09.13-.04 2.64 0 3.07.56 6.25 4.19 12.15 9.25 15.75.45.32 2.88 1.55 2.94 1.71.02.05-.36.77-.39.92C5.95 47.64.79 40.17.09 32.59c-.04-.45-.2-3.25.04-3.38\"\n                  class=\"cls-1\"\n                ></path>\n                <path\n                  d=\"M21.71 11.93c1.05-.06 2.11.02 3.16.09.26.1-.47 1.03-.53 1.05-.37.13-1.97-.06-2.54 0-5.7.56-10.71 3.36-13.73 8.29-.29.48-1.09 2.47-1.23 2.63-.21.23-.9.01-1.14-.18 2.33-6.64 8.99-11.48 16.01-11.89Z\"\n                  class=\"cls-3\"\n                ></path>\n                <path\n                  d=\"M2.42 29.21c.07-.03.96 0 1.01.04-.56 6.56 2.81 12.97 8.03 16.8.39.29 2.65 1.47 2.68 1.62 0 .05-.37.76-.39.92-5.39-2.46-9.76-8.03-11.01-13.82-.15-.7-.78-5.36-.31-5.57Z\"\n                  class=\"cls-1\"\n                ></path>\n                <path\n                  d=\"m4.61 29.21 1.01.04c-.47 6.78 3.33 13.04 9.21 16.23l-.31 1.01C8.17 43.38 3.99 36.33 4.61 29.21M2.94 24.56s.54.12.66.13c-.19.44-.08 3.28-.22 3.38-.03.02-.62.02-.66-.04-.02-.46-.01-3.28.22-3.46ZM5.31 25.09l.66.13c-.14.37-.1 2.77-.22 2.85-.03.02-.62.02-.66-.04 0-.98-.06-1.98.22-2.94\"\n                  class=\"cls-1\"\n                ></path>\n                <path\n                  d=\"M35.05 0 25.01 27.68l8.95.26-23.39 32.5c3.39-9.18 7.02-18.39 9.82-27.72l-8.73-.13z\"\n                  style=\"fill: #fdfeff\"\n                ></path>\n                <path\n                  d=\"M29.52 8.95c.26 0 0 .31-.04.39-4.07 7.61-10.02 14.97-14.3 22.54l-1.71.13z\"\n                  style=\"fill: #fefefe\"\n                ></path>\n              </g>\n            </svg>\n            ${get_quote_button_text}\n          </a>\n        </div>\n      </div>\n    </div>\n  </div>\n</div></div>",
                        "css_content": "",
                        "js_content": "",
                        "placeholders": [
                            "img-book_in_minutes_cta_bg",
                            "book_in_minutes_title",
                            "contact_number",
                            "contact_number_button_label",
                            "quote_url",
                            "get_quote_button_text"
                        ],
                        "preview_image": "",
                        "version": 1,
                        "status": "draft",
                        "tags": [],
                        "thumbnail_url": "",
                        "id": "mel11vphae1ej7eq137",
                        "created_at": "2025-08-21T06:35:19.877Z",
                        "updated_at": "2025-08-21T12:08:09.560Z",
                        "category_name": "Content",
                        "category_color": "#10B981",
                        "order": 5,
                        "repeator": "single",
                        "cssClass": "",
                        "uniqueId": "mel11vphae1ej7eq137-1755852075658-xxen4bk19"
                    },
                    {
                        "name": "common_cta_section",
                        "category_id": "cat2",
                        "html_content": "<div data-component=\"common_cta_section\"><div\n  class=\"dw-my-16 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px] dw-px-8 dw-hidden mdx:dw-block\"\n>\n  <div class=\"dw-flex dw-w-full dw-rounded-[50px] dw-overflow-hidden\">\n    <!-- Left Column -->\n    <div\n      class=\"dw-w-[70%] dw-bg-theme-mainColorShade lgm:dw-py-[88px] lgm:dw-px-[80px] dw-py-[56px] dw-px-[48px] mdl:dw-py-[72px] mdl:dw-px-[48px]\"\n    >\n      <p\n        class=\"mdl:dw-text-[30px] dw-text-2xl dw-font-heading dw-font-semibold dw-text-white\"\n      >\n        ${cta_title}\n      </p>\n\n      <div class=\"dw-flex dw-flex-col mdl:dw-flex-row dw-gap-4 dw-mt-6\">\n        <!-- Call Us Button -->\n        <a\n          href=\"tel:${contact_number}\"\n          class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center mdx:dw-gap-[10px] dw-gap-[35px] dw-pe-[10px] lgs:dw-text-[18px] dw-text-[16px] dw-ps-[3px] dw-py-[3px] dw-bg-white dw-rounded-full hover:dw-bg-theme-main hover:dw-text-white dw-border-[2px] dw-border-white hover:dw-border-theme-main dw-transition\"\n        >\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            width=\"42\"\n            height=\"42\"\n            viewBox=\"0 0 36 36\"\n            fill=\"none\"\n          >\n            <rect width=\"36\" height=\"36\" rx=\"18\" class=\"dw-fill-theme-main\" />\n            <path\n              d=\"M27.7388 22.4138C27.5716 23.6841 26.9477 24.8502 25.9837 25.6942C25.0196 26.5382 23.7813 27.0023 22.5 27.0001C15.0563 27.0001 9.00001 20.9438 9.00001 13.5001C8.99771 12.2188 9.4619 10.9805 10.3059 10.0164C11.1499 9.05234 12.3159 8.42847 13.5863 8.2613C13.9075 8.22208 14.2328 8.2878 14.5136 8.44865C14.7944 8.60951 15.0157 8.85687 15.1444 9.1538L17.1244 13.5741V13.5854C17.2229 13.8127 17.2636 14.0608 17.2428 14.3077C17.222 14.5545 17.1404 14.7924 17.0053 15.0001C16.9884 15.0254 16.9706 15.0488 16.9519 15.0722L15 17.386C15.7022 18.8129 17.1947 20.2922 18.6403 20.9963L20.9222 19.0547C20.9446 19.0359 20.9681 19.0184 20.9925 19.0022C21.2 18.8639 21.4387 18.7794 21.687 18.7565C21.9353 18.7336 22.1854 18.7729 22.4147 18.871L22.4269 18.8766L26.8434 20.8557C27.1409 20.9839 27.3889 21.205 27.5503 21.4858C27.7116 21.7667 27.7778 22.0922 27.7388 22.4138Z\"\n              fill=\"white\"\n            ></path>\n          </svg>\n          ${contact_number_button_label}\n        </a>\n\n        <!-- Get Quote Button -->\n        <a\n          href=\"${quote_url}\"\n          class=\"dw-flex dw-font-medium dw-w-fit dw-items-center dw-gap-[6px] lgs:dw-text-[18px] dw-text-[16px] dw-pe-[10px] dw-border-[2px] hover:dw-bg-theme-main hover:dw-text-white dw-rounded-full dw-bg-white dw-border-white hover:dw-border-theme-main dw-transition\"\n        >\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            id=\"Layer_1\"\n            data-name=\"Layer 1\"\n            viewBox=\"0 0 72 84\"\n            width=\"48\"\n            height=\"48\"\n            class=\"dw-fill-white\"\n          >\n            <circle cx=\"36\" cy=\"42\" r=\"36\" class=\"dw-fill-theme-main\"></circle>\n            <g transform=\"translate(18, 18) scale(0.8)\">\n              <path\n                d=\"M21.45 7.46c2.09-.11 4.18.1 6.23.44l.13.22c-.51 1.39-2.17.52-3.38.48-9.62-.33-17.23 4.17-21.45 12.76-.11.23-.47 1.43-.53 1.49-.23.26-1.08.01-1.14-.18-.08-.25 1.18-2.92 1.4-3.33C6.35 12.59 13.75 7.86 21.44 7.45Z\"\n                class=\"cls-3\"\n              ></path>\n              <path\n                d=\"M22.15 9.65c.52-.02 4.24.09 4.25.48-.74 1.21-.54.76-1.62.75-7.73-.07-14.38 2.32-18.55 9.17-.33.55-1.51 3.39-1.673.51-.13.1-1.1-.11-1.14-.26-.04-.13.91-2.17 1.05-2.46C7.73 14.38 14.91 9.88 22.15 9.66Z\"\n                class=\"cls-3\"\n              ></path>\n              <path\n                d=\"M.13 29.21c.06-.03.97 0 1.01.04.09.13-.04 2.64 0 3.07.56 6.25 4.19 12.15 9.25 15.75.45.32 2.88 1.55 2.94 1.71.02.05-.36.77-.39.92C5.95 47.64.79 40.17.09 32.59c-.04-.45-.2-3.25.04-3.38\"\n                class=\"cls-1\"\n              ></path>\n              <path\n                d=\"M21.71 11.93c1.05-.06 2.11.02 3.16.09.26.1-.47 1.03-.53 1.05-.37.13-1.97-.06-2.54 0-5.7.56-10.71 3.36-13.73 8.29-.29.48-1.09 2.47-1.23 2.63-.21.23-.9.01-1.14-.18 2.33-6.64 8.99-11.48 16.01-11.89Z\"\n                class=\"cls-3\"\n              ></path>\n              <path\n                d=\"M2.42 29.21c.07-.03.96 0 1.01.04-.56 6.56 2.81 12.97 8.03 16.8.39.29 2.65 1.47 2.68 1.62 0 .05-.37.76-.39.92-5.39-2.46-9.76-8.03-11.01-13.82-.15-.7-.78-5.36-.31-5.57Z\"\n                class=\"cls-1\"\n              ></path>\n              <path\n                d=\"m4.61 29.21 1.01.04c-.47 6.78 3.33 13.04 9.21 16.23l-.31 1.01C8.17 43.38 3.99 36.33 4.61 29.21M2.94 24.56s.54.12.66.13c-.19.44-.08 3.28-.22 3.38-.03.02-.62.02-.66-.04-.02-.46-.01-3.28.22-3.46ZM5.31 25.09l.66.13c-.14.37-.1 2.77-.22 2.85-.03.02-.62.02-.66-.04 0-.98-.06-1.98.22-2.94\"\n                class=\"cls-1\"\n              ></path>\n              <path\n                d=\"M35.05 0 25.01 27.68l8.95.26-23.39 32.5c3.39-9.18 7.02-18.39 9.82-27.72l-8.73-.13z\"\n                style=\"fill: #fdfeff\"\n              ></path>\n              <path\n                d=\"M29.52 8.95c.26 0 0 .31-.04.39-4.07 7.61-10.02 14.97-14.3 22.54l-1.71.13z\"\n                style=\"fill: #fefefe\"\n              ></path>\n            </g>\n          </svg>\n          ${get_quote_button_text}\n        </a>\n      </div>\n\n      <div class=\"dw-mt-6 dw-flex dw-items-center dw-space-x-4\">\n        <img\n          src=\"${img-cta-review-photo}\"\n          alt=\"cta_review_photo\"\n          class=\"dw-w-[20%] dw-h-auto dw-object-contain\"\n        />\n        <p class=\"dw-text-white mdl:dw-text-[16px] dw-text-sm dw-font-medium\">\n          ${available_24_7_text}\n        </p>\n      </div>\n    </div>\n\n    <!-- Right Column -->\n    <div\n      class=\"mdl:dw-w-[30%] dw-w-[35%] dw-bg-theme-main dw-flex dw-items-center\"\n    >\n      <img\n        src=\"${img-cta-bus-rental-image}\"\n        alt=\"cta_bus_rental_image\"\n        class=\"dw-w-full dw-h-auto dw-object-contain\"\n      />\n    </div>\n  </div>\n</div></div>",
                        "css_content": "",
                        "js_content": "",
                        "placeholders": [
                            "cta_title",
                            "contact_number",
                            "contact_number_button_label",
                            "quote_url",
                            "get_quote_button_text",
                            "img-cta-review-photo",
                            "available_24_7_text",
                            "img-cta-bus-rental-image"
                        ],
                        "preview_image": "",
                        "version": 1,
                        "status": "draft",
                        "tags": [],
                        "thumbnail_url": "",
                        "id": "mel2ma1dkrz5ju2ar8j",
                        "created_at": "2025-08-21T07:19:11.185Z",
                        "updated_at": "2025-08-22T04:17:02.112Z",
                        "category_name": "Content",
                        "category_color": "#10B981",
                        "order": 6,
                        "repeator": "single",
                        "cssClass": "",
                        "uniqueId": "mel2ma1dkrz5ju2ar8j-1755852080376-vbqugjqf6"
                    },
                    {
                        "name": "faqs",
                        "category_id": "cat2",
                        "html_content": "<div data-component=\"faqs\"><div\n      class=\"lgx:dw-pb-[80px] mdx:dw-pb-[64px] dw-pb-[32px] dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n    >\n      <h1\n        class=\"dw-pb-[48px] dw-mb-2 dw-capitalize dw-mx-auto dw-text-[26px] mdx:dw-text-[48px] dw-font-semibold dw-text-heading dw-text-center dw-pb-5 dw-font-heading dw-leading-tight lgx:dw-px-0 dw-px-6\"\n      >\n        ${faqs_title}\n      </h1>\n\n      <div\n        id=\"accordion\"\n        class=\"dw-w-full mdl:dw-w-[60%] mdl:dw-px-0 dw-px-8 dw-mx-auto\"\n      >\n        <div class=\"accordion\">{{faqs_questions}}</div>\n      </div>\n    </div></div>",
                        "css_content": "",
                        "js_content": " document.addEventListener(\"DOMContentLoaded\", () => {\n        const accordions = document.querySelectorAll(\".accordion-btn\");\n\n        accordions.forEach((btn) => {\n          btn.addEventListener(\"click\", () => {\n            const body = btn.nextElementSibling;\n\n            body.classList.toggle(\"dw-hidden\");\n\n            accordions.forEach((otherBtn) => {\n              if (otherBtn !== btn) {\n                otherBtn.nextElementSibling.classList.add(\"dw-hidden\");\n              }\n            });\n          });\n        });\n      });",
                        "placeholders": [
                            "faqs_title"
                        ],
                        "preview_image": "",
                        "version": 1,
                        "status": "draft",
                        "tags": [],
                        "thumbnail_url": "",
                        "id": "mel3dogap4t0oax8x8g",
                        "created_at": "2025-08-21T07:40:29.578Z",
                        "updated_at": "2025-08-22T04:20:06.258Z",
                        "category_name": "Content",
                        "category_color": "#10B981",
                        "order": 7,
                        "repeator": "repeat",
                        "cssClass": "",
                        "uniqueId": "mel3dogap4t0oax8x8g-1755852086182-bcsbfmz1n",
                        "repeatedComponentId": "meldzsff2p93tgx5017",
                        "repeatedComponentName": [
                            "faqs_questions"
                        ],
                        "repeatedComponent": {
                            "name": "faq_question",
                            "category_id": "cat2",
                            "html_content": "<div data-component=\"faq_question\"><div class=\"dw-border-t dw-border-[#ddd] dw-py-[15px]\">\n  <button\n    type=\"button\"\n    class=\"accordion-btn dw-w-full dw-text-left dw-flex dw-justify-between dw-items-center dw-font-heading dw-font-semibold dw-text-lg dw-mb-2 dw-leading-tight\"\n  >\n    ${faq_question}\n  </button>\n  <div class=\"dw-mt-2 accordion-body dw-hidden\">\n    <p class=\"dw-text-base\">${faq_answer}</p>\n  </div>\n</div>\n</div>",
                            "css_content": "",
                            "js_content": "",
                            "placeholders": [
                                "faq_question",
                                "faq_answer"
                            ],
                            "preview_image": "",
                            "version": 1,
                            "status": "draft",
                            "tags": [],
                            "thumbnail_url": "",
                            "id": "meldzsff2p93tgx5017",
                            "created_at": "2025-08-21T12:37:37.323Z",
                            "updated_at": "2025-08-21T12:37:37.323Z",
                            "category_name": "Content",
                            "category_color": "#10B981"
                        }
                    },
                    {
                        "name": "divider_60",
                        "category_id": "cat2",
                        "html_content": "<div\n  class=\"dw-mx-auto dw-w-full dw-px-6 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n>\n  <hr\n    class=\"dw-border-t dw-border-theme-divider dw-w-[60%] dw-mx-auto dw-opacity-100 dw-my-14\"\n  />\n</div>",
                        "css_content": "",
                        "js_content": "",
                        "placeholders": [],
                        "preview_image": "",
                        "version": 1,
                        "status": "draft",
                        "tags": [],
                        "thumbnail_url": "",
                        "id": "mel64wfe2bdmijws9vo",
                        "created_at": "2025-08-21T08:57:38.858Z",
                        "updated_at": "2025-08-21T08:57:38.858Z",
                        "category_name": "Content",
                        "category_color": "#10B981",
                        "order": 8,
                        "repeator": "single",
                        "cssClass": "",
                        "uniqueId": "mel64wfe2bdmijws9vo-1755852098284-4i7wuvfki"
                    },
                    {
                        "name": "divider_100",
                        "category_id": "cat2",
                        "html_content": "<div\n  class=\"dw-mx-auto dw-w-full dw-px-8 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n>\n  <hr\n    class=\"dw-border-t dw-border-theme-divider dw-mx-auto dw-opacity-100 dw-my-14\"\n  />\n</div>",
                        "css_content": "",
                        "js_content": "",
                        "placeholders": [],
                        "preview_image": "",
                        "version": 1,
                        "status": "draft",
                        "tags": [],
                        "thumbnail_url": "",
                        "id": "mel65imuhz888tp56ts",
                        "created_at": "2025-08-21T08:58:07.638Z",
                        "updated_at": "2025-08-21T08:58:07.638Z",
                        "category_name": "Content",
                        "category_color": "#10B981",
                        "order": 9,
                        "repeator": "single",
                        "cssClass": "",
                        "uniqueId": "mel65imuhz888tp56ts-1755852099574-asb41r557"
                    },
                    {
                        "name": "worked_with",
                        "category_id": "cat2",
                        "html_content": "<div data-component=\"worked_with\"><div\n  class=\"dw-my-16 dw-mx-auto dw-w-full dw-px-4 xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgl:dw-px-4 lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdl:dw-px-4 mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smm:dw-px-4 smx:dw-max-w-[700px] smx:dw-px-4\"\n>\n  <p\n    class=\"dw-font-heading dw-text-center xlx:dw-text-[24px] lgx:dw-text-[22px] dw-text-[20px] dw-font-semibold\"\n  >\n    ${worked_with_title}\n  </p>\n\n  <div\n    class=\"dw-grid dw-grid-cols-1 smm:dw-grid-cols-2 mdx:dw-grid-cols-6 mdx:dw-gap-6 dw-gap-y-8 dw-justify-center dw-pt-[64px] dw-px-[48px] xl1:dw-pt-[64px] xl1:dw-px-[48px] dw-pb-[30px] lgs:dw-pt-[64px] xlx:dw-px-0 lgx:dw-px-[16px] mdx:dw-px-[32px]\"\n  >\n    {{work_with_list}}\n  </div>\n</div>\n</div>",
                        "css_content": "",
                        "js_content": "",
                        "placeholders": [
                            "worked_with_title"
                        ],
                        "preview_image": "",
                        "version": 1,
                        "status": "draft",
                        "tags": [],
                        "thumbnail_url": "",
                        "id": "mel727wfcnonno0e1o5",
                        "created_at": "2025-08-21T09:23:33.375Z",
                        "updated_at": "2025-08-21T11:57:05.540Z",
                        "category_name": "Content",
                        "category_color": "#10B981",
                        "order": 10,
                        "repeator": "repeat",
                        "cssClass": "",
                        "uniqueId": "mel727wfcnonno0e1o5-1755852101033-epnl0jmv2",
                        "repeatedComponentId": "melcn35dh75ptnkoclg",
                        "repeatedComponentName": [
                            "work_with_list"
                        ],
                        "repeatedComponent": {
                            "name": "work_with_organization_card",
                            "category_id": "cat2",
                            "html_content": "<div data-component=\"work_with_organization_card\"><img\n  src=\"${img-organization_logo}\"\n  alt=\"${organization_name}\"\n  class=\"dw-max-h-[60px] dw-object-contain dw-mx-auto\"\n/>\n</div>",
                            "css_content": "",
                            "js_content": "",
                            "placeholders": [
                                "img-organization_logo",
                                "organization_name"
                            ],
                            "preview_image": "",
                            "version": 1,
                            "status": "draft",
                            "tags": [],
                            "thumbnail_url": "",
                            "id": "melcn35dh75ptnkoclg",
                            "created_at": "2025-08-21T11:59:45.073Z",
                            "updated_at": "2025-08-22T05:04:51.753Z",
                            "category_name": "Content",
                            "category_color": "#10B981"
                        }
                    },
                    {
                        "name": "testimonial",
                        "category_id": "cat2",
                        "html_content": "<div data-component=\"testimonial\"><section\n  class=\"dw-my-16 dw-bg-theme-cardBg dw-bg-[url('${img-background-pattern-bg-image}')] dw-bg-top dw-bg-no-repeat dw-bg-cover xlx:dw-py-24 mdx:dw-py-[64px] dw-py-[48px]\"\n>\n  <div\n    class=\"dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px] dw-px-8\"\n  >\n    <div class=\"dw-flex dw-flex-col dw-items-center\">\n      <h2\n        class=\"dw-font-heading dw-text-center mdx:dw-text-[48px] dw-text-[26px] dw-pb-4 dw-pb-6 dw-font-semibold dw-w-full\"\n      >\n        ${testimonial_title}\n      </h2>\n      <div\n        class=\"dw-grid dw-grid-cols-1 mdx:dw-grid-cols-2 mdl:dw-grid-cols-4 dw-gap-[14px] dw-gap-y-[25px] dw-w-full\"\n      >\n        {{testimonial_cards}}\n      </div>\n    </div>\n  </div>\n</section>\n</div>",
                        "css_content": "",
                        "js_content": "",
                        "placeholders": [
                            "img-background-pattern-bg-image",
                            "testimonial_title"
                        ],
                        "preview_image": "",
                        "version": 1,
                        "status": "draft",
                        "tags": [],
                        "thumbnail_url": "",
                        "id": "mel7pjzof70xdrnynvd",
                        "created_at": "2025-08-21T09:41:42.132Z",
                        "updated_at": "2025-08-22T05:15:40.170Z",
                        "category_name": "Content",
                        "category_color": "#10B981",
                        "order": 11,
                        "repeator": "repeat",
                        "cssClass": "",
                        "uniqueId": "mel7pjzof70xdrnynvd-1755852120348-58tceb6s8",
                        "repeatedComponentId": "melcr47v225ngp43it9",
                        "repeatedComponentName": [
                            "testimonial_cards"
                        ],
                        "repeatedComponent": {
                            "name": "testimonial_card",
                            "category_id": "cat2",
                            "html_content": "<div data-component=\"testimonial_card\"><div\n  class=\"dw-bg-body-bg dw-p-[20px_20px_40px] dw-rounded-[16px] dw-h-[315px] dw-shadow-[1px_1px_7px_rgba(0,0,0,0.13),0_0_2px_rgba(0,0,0,0.05)] dw-transition-transform dw-duration-300 dw-ease-out hover:dw--translate-y-1.5\"\n>\n  <div class=\"dw-flex dw-items-center dw-gap-3\">\n    <img\n      src=\"${img-user-image}\"\n      alt=\"${user_name}\"\n      class=\"dw-w-[60px] dw-h-[60px] dw-rounded-full dw-object-cover\"\n    />\n    <p\n      class=\"dw-mb-0 dw-font-bold xl1:dw-text-[18px] lgx:dw-text-[14px] dw-text-[16px]\"\n    >\n      ${user_name}\n    </p>\n  </div>\n  <div class=\"dw-text-yellow-500 dw-text-[25px] dw-leading-none dw-py-[10px]\">\n    ★★★★★\n  </div>\n  <p class=\"xl1:dw-text-[16px] dw-text-[14px]\">${testimonial}</p>\n</div>\n</div>",
                            "css_content": "",
                            "js_content": "",
                            "placeholders": [
                                "img-user-image",
                                "user_name",
                                "testimonial"
                            ],
                            "preview_image": "",
                            "version": 1,
                            "status": "draft",
                            "tags": [],
                            "thumbnail_url": "",
                            "id": "melcr47v225ngp43it9",
                            "created_at": "2025-08-21T12:02:53.083Z",
                            "updated_at": "2025-08-22T05:05:20.861Z",
                            "category_name": "Content",
                            "category_color": "#10B981"
                        }
                    },
                    {
                        "name": "we_offer_list",
                        "category_id": "cat2",
                        "html_content": "<div data-component=\"we_offer_list\">\n  <div\n    class=\"dw-my-16 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px] dw-px-[2rem]\"\n  >\n    <h2\n      class=\"dw-font-heading dw-text-center mdx:dw-text-[48px] dw-text-[26px] mdx:dw-pb-[48px] dw-pb-[20px] dw-font-semibold dw-w-full lgl:dw-w-[72%] lgs:dw-w-[80%] dw-mx-auto dw-leading-[30px] mdx:dw-leading-[55px] xl1:dw-leading-[60px]\"\n    >\n      ${we_offer_services_title}\n    </h2>\n\n    <div\n      class=\"dw-grid mdl:dw-grid-cols-2 dw-gap-x-[6px] mdx:dw-gap-x-[40px] lgx:dw-gap-x-[80px] mdx:dw-px-0 dw-px-[6px]\"\n    >\n      {{we_offer_services_list}}\n    </div>\n  </div>\n</div>\n",
                        "css_content": "",
                        "js_content": "",
                        "placeholders": [
                            "we_offer_services_title"
                        ],
                        "preview_image": "",
                        "version": 1,
                        "status": "draft",
                        "tags": [],
                        "thumbnail_url": "",
                        "id": "meld7qzw23ey3e800oc",
                        "created_at": "2025-08-21T12:15:49.100Z",
                        "updated_at": "2025-08-22T08:40:00.617Z",
                        "category_name": "Content",
                        "category_color": "#10B981",
                        "order": 12,
                        "repeator": "repeat",
                        "cssClass": "",
                        "uniqueId": "meld7qzw23ey3e800oc-1755852135835-a882ujexy",
                        "repeatedComponentId": "melde2eqwtkzw19v4z",
                        "repeatedComponentName": [
                            "we_offer_services_list"
                        ],
                        "repeatedComponent": {
                            "name": "we_offer_card",
                            "category_id": "cat2",
                            "html_content": "<div data-component=\"we_offer_card\"><div id=\"we-offer-card\">\n  <h2\n    class=\"lgm:dw-text-[24px] dw-text-[20px] lgx:dw-text-[22px] dw-font-semibold dw-pt-[16px] mdl:dw-pb-[12px] dw-pb-[20px] dw-font-heading\"\n  >\n    <a href=\"${offer_page_redirect_url}\">${service_name} </a>\n  </h2>\n  <img\n    src=\"${img-offer-service-image}\"\n    alt=\"offer-service-image\"\n    class=\"dw-rounded-[16px] dw-w-full dw-mb-3\"\n  />\n  <p class=\"dw-text-[16px] xl1:dw-[18px] dw-mb-4\">\n    ${service_description}\n  </p>\n  <a\n    href=\"${offer_page_redirect_url}\"\n    class=\"dw-text-theme-main dw-text-[14px] dw-my-2 dw-font-medium dw-flex dw-items-center dw-gap-1\"\n  >\n    Learn More\n    <svg\n      class=\"dw-w-[14px] dw-h-[14px]\"\n      viewBox=\"0 0 448 512\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <path\n        class=\"dw-fill-theme-main\"\n        d=\"M190.5 66.9l22.2-22.2c9.4-9.4 24.6-9.4 33.9 0L441 239c9.4 9.4 9.4 24.6 0 33.9L246.6 467.3c-9.4 9.4-24.6 9.4-33.9 0l-22.2-22.2c-9.5-9.5-9.3-25 .4-34.3L311.4 296H24c-13.3 0-24-10.7-24-24v-32c0-13.3 10.7-24 24-24h287.4L190.9 101.2c-9.8-9.3-10-24.8-.4-34.3z\"\n      />\n    </svg>\n  </a>\n</div></div>",
                            "css_content": "",
                            "js_content": "",
                            "placeholders": [
                                "offer_page_redirect_url",
                                "service_name",
                                "img-offer-service-image",
                                "service_description"
                            ],
                            "preview_image": "",
                            "version": 1,
                            "status": "draft",
                            "tags": [],
                            "thumbnail_url": "",
                            "id": "melde2eqwtkzw19v4z",
                            "created_at": "2025-08-21T12:20:43.826Z",
                            "updated_at": "2025-08-22T06:49:44.492Z",
                            "category_name": "Content",
                            "category_color": "#10B981"
                        }
                    }
                ],
                "version": "v1",
                "pagePlaceHolder": [
                    {
                        "categoryName": "hero_section_home_page",
                        "CategoryId": "mejxa6hnolket8gfqqg",
                        "placeholders": [
                            "hero_section_title",
                            "hero_section_description",
                            "contact_number",
                            "hero_section_extra_text",
                            "contact_number_button_label",
                            "quote_url",
                            "get_quote_button_text",
                            "img-hero-section-image",
                            "img-advantage-image",
                            "hero_section_card1_content",
                            "hero_section_card2_content",
                            "hero_section_card3_content"
                        ]
                    },
                    {
                        "categoryName": "why_section_home_page",
                        "CategoryId": "mejxkuo2q6g7dim380s",
                        "placeholders": [
                            "img-background-pattern-bg-image",
                            "why_section_heading",
                            "why_section_description",
                            "img-why-section-image",
                            "img-advantage-image",
                            "why_section_card1_content",
                            "why_section_card2_content",
                            "why_section_card3_content"
                        ]
                    },
                    {
                        "categoryName": "bus_grid_list",
                        "CategoryId": "mejz65z1z7hhj4rxbr",
                        "placeholders": [
                            "buses_list_heading"
                        ],
                        "repeatedComponentName": [
                            "busesList"
                        ],
                        "repeatedComponentId": "melbj7f65tmd8ocep4a",
                        "repeatedComponent": {
                            "name": "bud_grid_card",
                            "category_id": "cat2",
                            "html_content": "<div data-component=\"bud_grid_card\"><div\n  class=\"dw-border dw-border-theme-cardBorder dw-rounded-[32px] dw-bg-theme-cardBg xl1:dw-p-[3rem_2rem_1.5rem] dw-p-[2.5rem_1rem_1rem] dw-min-h-full dw-relative\"\n>\n  <span\n    class=\"dw-absolute dw-z-10 dw-bg-body-bg dw-text-theme-lowOpacityTextColor dw-font-normal dw-border dw-shadow-sm dw-text-[14px] dw-px-[15px] dw-py-[8px] dw-ml-[10px] dw--translate-y-[20px] dw-rounded-full\"\n  >\n    ${bus_price_range}\n  </span>\n\n  <img\n    src=\"${img-bus-image}\"\n    class=\"dw-rounded-[16px] dw-w-full dw-h-auto\"\n    alt=\"img-busImage\"\n  />\n  <img\n    src=\"${img-bus-interior}\"\n    class=\"dw-rounded-[16px] dw-w-full dw-h-auto dw-mt-4\"\n    alt=\"img-busInterior\"\n  />\n\n  <h3\n    class=\"dw-font-semibold lgm:dw-text-[24px] lgx:dw-text-[22px] dw-text-[20px] dw-leading-[32px] dw-uppercase dw-tracking-[-0.176px] dw-text-center dw-pt-[32px] dw-pb-[25px] dw-font-heading dw-mb-2\"\n  >\n    <a href=\"${view_Bus_Url}\"> ${bus_title} </a>\n  </h3>\n\n  <div class=\"dw-flex dw-justify-center dw-gap-4 dw-pb-[5px]\">\n    <a\n      href=\"${view_Bus_Url}\"\n      class=\"dw-bg-body-text dw-text-white dw-border-2 dw-border-body-text dw-rounded-full dw-px-[16px] dw-py-[13px] dw-text-[18px] dw-leading-[24px] dw-font-medium dw-tracking-[-0.24px] hover:dw-border-theme-main hover:dw-bg-theme-main\"\n    >\n      ${bus_view_button_text}\n    </a>\n    <a\n      href=\"${quote_url}\"\n      class=\"dw-bg-theme-main dw-text-white dw-border-2 dw-border-theme-main dw-rounded-full dw-px-[16px] dw-py-[13px] dw-inline-flex dw-items-center dw-justify-center dw-text-[18px] dw-leading-[24px] dw-font-medium dw-tracking-[-0.24px]\"\n    >\n      ${get_quote_button_text}\n    </a>\n  </div>\n</div>\n</div>",
                            "css_content": "",
                            "js_content": "",
                            "placeholders": [
                                "bus_price_range",
                                "img-bus-image",
                                "img-bus-interior",
                                "view_Bus_Url",
                                "bus_title",
                                "bus_view_button_text",
                                "quote_url",
                                "get_quote_button_text"
                            ],
                            "preview_image": "",
                            "version": 1,
                            "status": "draft",
                            "tags": [],
                            "thumbnail_url": "",
                            "id": "melbj7f65tmd8ocep4a",
                            "created_at": "2025-08-21T11:28:44.370Z",
                            "updated_at": "2025-08-22T05:03:22.688Z",
                            "category_name": "Content",
                            "category_color": "#10B981"
                        }
                    },
                    {
                        "categoryName": "bus_amenities",
                        "CategoryId": "mel05d6jebm3z3va507",
                        "placeholders": [
                            "amenities_title"
                        ],
                        "repeatedComponentName": [
                            "amenities_cards"
                        ],
                        "repeatedComponentId": "melbmuizp786kngqmks",
                        "repeatedComponent": {
                            "name": "amenities_card",
                            "category_id": "cat2",
                            "html_content": "<div data-component=\"amenities_card\"><div\n  class=\"dw-flex dw-flex-col dw-justify-center dw-items-center dw-text-center dw-border dw-border-gray-200 dw-rounded-[16px] dw-min-h-[180px] dw-shadow-sm dw-px-4 dw-py-6\"\n>\n  <img src=\"${img-amenities_image}\" alt=\"amenities Image\" class=\"dw-mx-auto\" />\n  <p class=\"dw-pt-5 dw-font-medium dw-text-center xl1:dw-text-lg dw-text-base\">\n    ${amenities_text}\n  </p>\n</div>\n</div>",
                            "css_content": "",
                            "js_content": "",
                            "placeholders": [
                                "img-amenities_image",
                                "amenities_text"
                            ],
                            "preview_image": "",
                            "version": 1,
                            "status": "draft",
                            "tags": [],
                            "thumbnail_url": "",
                            "id": "melbmuizp786kngqmks",
                            "created_at": "2025-08-21T11:31:34.283Z",
                            "updated_at": "2025-08-21T11:31:34.283Z",
                            "category_name": "Content",
                            "category_color": "#10B981"
                        }
                    },
                    {
                        "categoryName": "note_sure",
                        "CategoryId": "mel0t7zfjeg1zxrr0m",
                        "placeholders": [
                            "note_sure_title",
                            "note_sure_description",
                            "contact_number",
                            "contact_number_button_label",
                            "quote_url",
                            "get_quote_button_text"
                        ]
                    },
                    {
                        "categoryName": "book_in_minutes",
                        "CategoryId": "mel11vphae1ej7eq137",
                        "placeholders": [
                            "img-book_in_minutes_cta_bg",
                            "book_in_minutes_title",
                            "contact_number",
                            "contact_number_button_label",
                            "quote_url",
                            "get_quote_button_text"
                        ]
                    },
                    {
                        "categoryName": "common_cta_section",
                        "CategoryId": "mel2ma1dkrz5ju2ar8j",
                        "placeholders": [
                            "cta_title",
                            "contact_number",
                            "contact_number_button_label",
                            "quote_url",
                            "get_quote_button_text",
                            "img-cta-review-photo",
                            "available_24_7_text",
                            "img-cta-bus-rental-image"
                        ]
                    },
                    {
                        "categoryName": "faqs",
                        "CategoryId": "mel3dogap4t0oax8x8g",
                        "placeholders": [
                            "faqs_title"
                        ],
                        "repeatedComponentName": [
                            "faqs_questions"
                        ],
                        "repeatedComponentId": "meldzsff2p93tgx5017",
                        "repeatedComponent": {
                            "name": "faq_question",
                            "category_id": "cat2",
                            "html_content": "<div data-component=\"faq_question\"><div class=\"dw-border-t dw-border-[#ddd] dw-py-[15px]\">\n  <button\n    type=\"button\"\n    class=\"accordion-btn dw-w-full dw-text-left dw-flex dw-justify-between dw-items-center dw-font-heading dw-font-semibold dw-text-lg dw-mb-2 dw-leading-tight\"\n  >\n    ${faq_question}\n  </button>\n  <div class=\"dw-mt-2 accordion-body dw-hidden\">\n    <p class=\"dw-text-base\">${faq_answer}</p>\n  </div>\n</div>\n</div>",
                            "css_content": "",
                            "js_content": "",
                            "placeholders": [
                                "faq_question",
                                "faq_answer"
                            ],
                            "preview_image": "",
                            "version": 1,
                            "status": "draft",
                            "tags": [],
                            "thumbnail_url": "",
                            "id": "meldzsff2p93tgx5017",
                            "created_at": "2025-08-21T12:37:37.323Z",
                            "updated_at": "2025-08-21T12:37:37.323Z",
                            "category_name": "Content",
                            "category_color": "#10B981"
                        }
                    },
                    {
                        "categoryName": "divider_60",
                        "CategoryId": "mel64wfe2bdmijws9vo",
                        "placeholders": []
                    },
                    {
                        "categoryName": "divider_100",
                        "CategoryId": "mel65imuhz888tp56ts",
                        "placeholders": []
                    },
                    {
                        "categoryName": "worked_with",
                        "CategoryId": "mel727wfcnonno0e1o5",
                        "placeholders": [
                            "worked_with_title"
                        ],
                        "repeatedComponentName": [
                            "work_with_list"
                        ],
                        "repeatedComponentId": "melcn35dh75ptnkoclg",
                        "repeatedComponent": {
                            "name": "work_with_organization_card",
                            "category_id": "cat2",
                            "html_content": "<div data-component=\"work_with_organization_card\"><img\n  src=\"${img-organization_logo}\"\n  alt=\"${organization_name}\"\n  class=\"dw-max-h-[60px] dw-object-contain dw-mx-auto\"\n/>\n</div>",
                            "css_content": "",
                            "js_content": "",
                            "placeholders": [
                                "img-organization_logo",
                                "organization_name"
                            ],
                            "preview_image": "",
                            "version": 1,
                            "status": "draft",
                            "tags": [],
                            "thumbnail_url": "",
                            "id": "melcn35dh75ptnkoclg",
                            "created_at": "2025-08-21T11:59:45.073Z",
                            "updated_at": "2025-08-22T05:04:51.753Z",
                            "category_name": "Content",
                            "category_color": "#10B981"
                        }
                    },
                    {
                        "categoryName": "testimonial",
                        "CategoryId": "mel7pjzof70xdrnynvd",
                        "placeholders": [
                            "img-background-pattern-bg-image",
                            "testimonial_title"
                        ],
                        "repeatedComponentName": [
                            "testimonial_cards"
                        ],
                        "repeatedComponentId": "melcr47v225ngp43it9",
                        "repeatedComponent": {
                            "name": "testimonial_card",
                            "category_id": "cat2",
                            "html_content": "<div data-component=\"testimonial_card\"><div\n  class=\"dw-bg-body-bg dw-p-[20px_20px_40px] dw-rounded-[16px] dw-h-[315px] dw-shadow-[1px_1px_7px_rgba(0,0,0,0.13),0_0_2px_rgba(0,0,0,0.05)] dw-transition-transform dw-duration-300 dw-ease-out hover:dw--translate-y-1.5\"\n>\n  <div class=\"dw-flex dw-items-center dw-gap-3\">\n    <img\n      src=\"${img-user-image}\"\n      alt=\"${user_name}\"\n      class=\"dw-w-[60px] dw-h-[60px] dw-rounded-full dw-object-cover\"\n    />\n    <p\n      class=\"dw-mb-0 dw-font-bold xl1:dw-text-[18px] lgx:dw-text-[14px] dw-text-[16px]\"\n    >\n      ${user_name}\n    </p>\n  </div>\n  <div class=\"dw-text-yellow-500 dw-text-[25px] dw-leading-none dw-py-[10px]\">\n    ★★★★★\n  </div>\n  <p class=\"xl1:dw-text-[16px] dw-text-[14px]\">${testimonial}</p>\n</div>\n</div>",
                            "css_content": "",
                            "js_content": "",
                            "placeholders": [
                                "img-user-image",
                                "user_name",
                                "testimonial"
                            ],
                            "preview_image": "",
                            "version": 1,
                            "status": "draft",
                            "tags": [],
                            "thumbnail_url": "",
                            "id": "melcr47v225ngp43it9",
                            "created_at": "2025-08-21T12:02:53.083Z",
                            "updated_at": "2025-08-22T05:05:20.861Z",
                            "category_name": "Content",
                            "category_color": "#10B981"
                        }
                    },
                    {
                        "categoryName": "we_offer_list",
                        "CategoryId": "meld7qzw23ey3e800oc",
                        "placeholders": [
                            "we_offer_services_title"
                        ],
                        "repeatedComponentName": [
                            "we_offer_services_list"
                        ],
                        "repeatedComponentId": "melde2eqwtkzw19v4z",
                        "repeatedComponent": {
                            "name": "we_offer_card",
                            "category_id": "cat2",
                            "html_content": "<div data-component=\"we_offer_card\"><div id=\"we-offer-card\">\n  <h2\n    class=\"lgm:dw-text-[24px] dw-text-[20px] lgx:dw-text-[22px] dw-font-semibold dw-pt-[16px] mdl:dw-pb-[12px] dw-pb-[20px] dw-font-heading\"\n  >\n    <a href=\"${offer_page_redirect_url}\">${service_name} </a>\n  </h2>\n  <img\n    src=\"${img-offer-service-image}\"\n    alt=\"offer-service-image\"\n    class=\"dw-rounded-[16px] dw-w-full dw-mb-3\"\n  />\n  <p class=\"dw-text-[16px] xl1:dw-[18px] dw-mb-4\">\n    ${service_description}\n  </p>\n  <a\n    href=\"${offer_page_redirect_url}\"\n    class=\"dw-text-theme-main dw-text-[14px] dw-my-2 dw-font-medium dw-flex dw-items-center dw-gap-1\"\n  >\n    Learn More\n    <svg\n      class=\"dw-w-[14px] dw-h-[14px]\"\n      viewBox=\"0 0 448 512\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <path\n        class=\"dw-fill-theme-main\"\n        d=\"M190.5 66.9l22.2-22.2c9.4-9.4 24.6-9.4 33.9 0L441 239c9.4 9.4 9.4 24.6 0 33.9L246.6 467.3c-9.4 9.4-24.6 9.4-33.9 0l-22.2-22.2c-9.5-9.5-9.3-25 .4-34.3L311.4 296H24c-13.3 0-24-10.7-24-24v-32c0-13.3 10.7-24 24-24h287.4L190.9 101.2c-9.8-9.3-10-24.8-.4-34.3z\"\n      />\n    </svg>\n  </a>\n</div></div>",
                            "css_content": "",
                            "js_content": "",
                            "placeholders": [
                                "offer_page_redirect_url",
                                "service_name",
                                "img-offer-service-image",
                                "service_description"
                            ],
                            "preview_image": "",
                            "version": 1,
                            "status": "draft",
                            "tags": [],
                            "thumbnail_url": "",
                            "id": "melde2eqwtkzw19v4z",
                            "created_at": "2025-08-21T12:20:43.826Z",
                            "updated_at": "2025-08-22T06:49:44.492Z",
                            "category_name": "Content",
                            "category_color": "#10B981"
                        }
                    }
                ],
                "id": "meml1gk01ps1ylfq51x",
                "created_at": "2025-08-22T08:42:38.736Z",
                "updated_at": "2025-08-22T08:42:38.736Z",
                "url": "/test",
                "type": "static",
                "showNavbar": true,
                "navPosition": 0,
                "order": 0
            }
        ],
        "content": [],
        "version": 1,
        "status": "draft",
        "id": "meml24fvojrv35qnmnp",
        "created_at": "2025-08-22T08:43:09.691Z",
        "updated_at": "2025-08-22T08:58:54.136Z",
        "description": "",
        "contentJSON": {
            "testT": {
                "test": {
                    "hero_section_home_page": {
                        "hero_section_title": "Clifton Bus Rentals",
                        "hero_section_description": "It’s easy to book group transportation when you partner with Bus Rental Company Clifton! Our reservation specialists are available 24/7 at ",
                        "hero_section_extra_text": "to pair you with a bus that’s perfect for any occasion. Whether it’s a wedding, sporting event, corporate gathering, school field trip, or anything else — we offer an amazing selection of vehicles and tons of transportation partners, so you’ll definitely find your ideal ride for any trip from coast to coast. And, we’re proud to be Clifton’s best bus company!",
                        "get_quote_button_text": "Get 30-Second Online Quote",
                        "quote_url": "/get-quote/",
                        "contact_number": "2014640115",
                        "contact_number_button_label": "Call Us: ************",
                        "hero_section_card1_content": "Get online pricing & availability in 30-seconds",
                        "hero_section_card2_content": "Customer Support available everyday 24/7/365",
                        "hero_section_card3_content": "Search All Different Types of Buses to Find the perfect fit for Your Trip",
                        "img-hero-section-image": "clifton-charter-bus",
                        "img-advantage-image": "advantage"
                    },
                    "why_section_home_page": {
                        "why_section_heading": "Why Rent a Bus With Bus Rental Company Clifton?",
                        "why_section_description": "When you book with Bus Rental Company Clifton, you can trust that you’re working with one of Clifton’s best bus companies. We know what it takes to give you and your party an elevated level of service unlike any other company in the state! Our dedicated, professional team makes every phase of booking your limo or bus rental as easy as possible by partnering with local operators, so you’ll have even more options to choose from than with one single company AND you’ll have a dedicated reservation team standing by to support you with every part of your trip. Give our expert reservation specialists a call when you’re ready and we’ll find a way to make your travels perfect, every time.",
                        "why_section_card1_content": "Request the amenities you want — from Wifi to restrooms & more",
                        "why_section_card2_content": "Compare Pictures & prices side-by-side in seconds",
                        "why_section_card3_content": "Hundreds of buses available statewide",
                        "img-background-pattern-bg-image": "background-pattern",
                        "img-why-section-image": "clifton-charter-bus",
                        "img-advantage-image": "advantage"
                    },
                    "bus_grid_list": {
                        "buses_list_heading": "Our Clifton Bus Rental Options",
                        "busesList": [
                            {
                                "bus_price_range": "$180 – $500+ per hour",
                                "img-bus-image": "clifton-charter-bus",
                                "img-bus-interior": "passenger-charter-bus-interior-clifton",
                                "bus_title": "50 Passenger Charter Bus",
                                "bus_view_button_text": "View This Bus",
                                "quote_url": "/get-quote/",
                                "get_quote_button_text": "Get a Quote",
                                "view_Bus_Url": "/50-passenger-charter-bus-rental"
                            },
                            {
                                "bus_price_range": "$180 – $500+ per hour",
                                "img-bus-image": "clifton-charter-bus",
                                "img-bus-interior": "passenger-charter-bus-interior-clifton",
                                "bus_title": "54 Passenger Charter Bus",
                                "bus_view_button_text": "View This Bus",
                                "quote_url": "/get-quote/",
                                "get_quote_button_text": "Get a Quote",
                                "view_Bus_Url": "/54-passenger-charter-bus-rental"
                            },
                            {
                                "bus_price_range": "$180 – $500+ per hour",
                                "img-bus-image": "clifton-charter-bus",
                                "img-bus-interior": "passenger-charter-bus-interior-clifton",
                                "bus_title": "55 Passenger Charter Bus",
                                "bus_view_button_text": "View This Bus",
                                "quote_url": "/get-quote/",
                                "get_quote_button_text": "Get a Quote",
                                "view_Bus_Url": "/55-passenger-charter-bus-rental"
                            },
                            {
                                "bus_price_range": "$180 – $500+ per hour",
                                "img-bus-image": "clifton-charter-bus",
                                "img-bus-interior": "passenger-charter-bus-interior-clifton",
                                "bus_title": "56 Passenger Charter Bus",
                                "bus_view_button_text": "View This Bus",
                                "quote_url": "/get-quote/",
                                "get_quote_button_text": "Get a Quote",
                                "view_Bus_Url": "/56-passenger-charter-bus-rental"
                            },
                            {
                                "bus_price_range": "$150 – $430+ per hour",
                                "img-bus-image": "clifton-charter-bus",
                                "img-bus-interior": "passenger-charter-bus-interior-clifton",
                                "bus_title": "15 Passenger Minibus",
                                "bus_view_button_text": "View This Bus",
                                "quote_url": "/get-quote/",
                                "get_quote_button_text": "Get a Quote",
                                "view_Bus_Url": "/15-passenger-minibus-rental"
                            },
                            {
                                "bus_price_range": "$150 – $430+ per hour",
                                "img-bus-image": "clifton-charter-bus",
                                "img-bus-interior": "passenger-charter-bus-interior-clifton",
                                "bus_title": "18 Passenger Minibus",
                                "bus_view_button_text": "View This Bus",
                                "quote_url": "/get-quote/",
                                "get_quote_button_text": "Get a Quote",
                                "view_Bus_Url": "/18-passenger-minibus-rental"
                            },
                            {
                                "bus_price_range": "$150 – $430+ per hour",
                                "img-bus-image": "clifton-charter-bus",
                                "img-bus-interior": "passenger-charter-bus-interior-clifton",
                                "bus_title": "20 Passenger Mini bus",
                                "bus_view_button_text": "View This Bus",
                                "quote_url": "/get-quote/",
                                "get_quote_button_text": "Get a Quote",
                                "view_Bus_Url": "/20-passenger-minibus-rental"
                            },
                            {
                                "bus_price_range": "$150 – $450+ per hour",
                                "img-bus-image": "clifton-charter-bus",
                                "img-bus-interior": "passenger-charter-bus-interior-clifton",
                                "bus_title": "25 Passenger Mini bus",
                                "bus_view_button_text": "View This Bus",
                                "quote_url": "/get-quote/",
                                "get_quote_button_text": "Get a Quote",
                                "view_Bus_Url": "/25-passenger-minibus-rental"
                            },
                            {
                                "bus_price_range": "$150 – $450+ per hour",
                                "img-bus-image": "clifton-charter-bus",
                                "img-bus-interior": "passenger-charter-bus-interior-clifton",
                                "bus_title": "28 Passenger Mini bus",
                                "bus_view_button_text": "View This Bus",
                                "quote_url": "/get-quote/",
                                "get_quote_button_text": "Get a Quote",
                                "view_Bus_Url": "/28-passenger-minibus-rental"
                            },
                            {
                                "bus_price_range": "$150 – $450+ per hour",
                                "img-bus-image": "clifton-charter-bus",
                                "img-bus-interior": "passenger-charter-bus-interior-clifton",
                                "bus_title": "30 Passenger Minibus",
                                "bus_view_button_text": "View This Bus",
                                "quote_url": "/get-quote/",
                                "get_quote_button_text": "Get a Quote",
                                "view_Bus_Url": "/30-passenger-minibus-rental"
                            },
                            {
                                "bus_price_range": "$150 – $450+ per hour",
                                "img-bus-image": "clifton-charter-bus",
                                "img-bus-interior": "passenger-charter-bus-interior-clifton",
                                "bus_title": "35 Passenger Minibus",
                                "bus_view_button_text": "View This Bus",
                                "quote_url": "/get-quote/",
                                "get_quote_button_text": "Get a Quote",
                                "view_Bus_Url": "/35-passenger-minibus-rental"
                            },
                            {
                                "bus_price_range": "$155 – $450+ per hour",
                                "img-bus-image": "clifton-charter-bus",
                                "img-bus-interior": "passenger-charter-bus-interior-clifton",
                                "bus_title": "Sprinter Van Rental With Driver",
                                "bus_view_button_text": "View This Bus",
                                "quote_url": "/get-quote/",
                                "get_quote_button_text": "Get a Quote",
                                "view_Bus_Url": "/sprinter-van-rental-with-driver"
                            },
                            {
                                "bus_price_range": "$145 – $450+ per hour",
                                "img-bus-image": "clifton-charter-bus",
                                "img-bus-interior": "passenger-charter-bus-interior-clifton",
                                "bus_title": "School Bus",
                                "bus_view_button_text": "View This Bus",
                                "quote_url": "/get-quote/",
                                "get_quote_button_text": "Get a Quote",
                                "view_Bus_Url": "/school-bus-rental"
                            },
                            {
                                "bus_price_range": "$180 - $450+ per hour",
                                "img-bus-image": "clifton-charter-bus",
                                "img-bus-interior": "passenger-charter-bus-interior-clifton",
                                "bus_title": "Party Bus",
                                "bus_view_button_text": "View This Bus",
                                "quote_url": "/get-quote/",
                                "get_quote_button_text": "Get a Quote",
                                "view_Bus_Url": "/party-bus-rental"
                            },
                            {
                                "bus_price_range": "$180 - $450+ per hour",
                                "img-bus-image": "clifton-charter-bus",
                                "img-bus-interior": "passenger-charter-bus-interior-clifton",
                                "bus_title": "Sprinter Limo",
                                "bus_view_button_text": "View This Bus",
                                "quote_url": "/get-quote/",
                                "get_quote_button_text": "Get a Quote",
                                "view_Bus_Url": "/sprinter-limo-rental"
                            }
                        ]
                    },
                    "bus_amenities": {
                        "amenities_title": "Our Clifton Buses Offer Amenities Like",
                        "amenities_cards": [
                            {
                                "img-amenities_image": "wifi",
                                "amenities_text": "Reclining Seats with Armrests and Footrests"
                            },
                            {
                                "img-amenities_image": "wifi",
                                "amenities_text": "Onboard Restroom"
                            },
                            {
                                "img-amenities_image": "wifi",
                                "amenities_text": "Complimentary Wi-Fi"
                            },
                            {
                                "img-amenities_image": "wifi",
                                "amenities_text": "Power Outlets & USB Charging Ports at Every Seat"
                            },
                            {
                                "img-amenities_image": "wifi",
                                "amenities_text": "Generous Legroom"
                            },
                            {
                                "img-amenities_image": "wifi",
                                "amenities_text": "Climate Control with Personal Air Vents"
                            },
                            {
                                "img-amenities_image": "wifi",
                                "amenities_text": "ADA Accessibility"
                            },
                            {
                                "img-amenities_image": "wifi",
                                "amenities_text": "Ample Luggage Space"
                            },
                            {
                                "img-amenities_image": "wifi",
                                "amenities_text": "Individual Reading Lights"
                            },
                            {
                                "img-amenities_image": "wifi",
                                "amenities_text": "Onboard Entertainment System (TVs, DVD/Blu-ray, Streaming)"
                            },
                            {
                                "img-amenities_image": "wifi",
                                "amenities_text": "PA System / Microphones"
                            },
                            {
                                "img-amenities_image": "wifi",
                                "amenities_text": "Panoramic Windows"
                            },
                            {
                                "img-amenities_image": "wifi",
                                "amenities_text": "Interior and Ambient Lighting"
                            },
                            {
                                "img-amenities_image": "wifi",
                                "amenities_text": "Perimeter Lighting for Night Visibility"
                            },
                            {
                                "img-amenities_image": "wifi",
                                "amenities_text": "Entry Stairway for Smooth Boarding"
                            },
                            {
                                "img-amenities_image": "wifi",
                                "amenities_text": "Standard, Premium, Luxury Seating"
                            }
                        ]
                    },
                    "note_sure": {
                        "note_sure_title": "Not sure which bus is best for you?",
                        "note_sure_description": "If you don’t see what you need, call us! We offer Clifton's largest selection of vehicles with many more options not shown online including party buses, shuttle buses, sprinter vans/limos, stretch limousines, charter buses and more!",
                        "contact_number_button_label": "Call Us: ************",
                        "get_quote_button_text": "Get 30-Second Online Quote",
                        "contact_number": "2014640115",
                        "quote_url": "/get-quote/"
                    },
                    "book_in_minutes": {
                        "book_in_minutes_title": "Book a Clifton Bus Rental in Minutes!",
                        "contact_number_button_label": "Call Us: ************",
                        "get_quote_button_text": "Get 30-Second Online Quote",
                        "img-book_in_minutes_cta_bg": "book-in-minutes-cta-bg",
                        "contact_number": "2014640115",
                        "quote_url": "/get-quote/"
                    },
                    "common_cta_section": {
                        "cta_title": "Get a Quote in 30 Seconds",
                        "available_24_7_text": "We’re available everyday 24/7/365!",
                        "contact_number_button_label": "Call Us: ************",
                        "get_quote_button_text": "Get 30-Second Online Quote",
                        "contact_number": "2014640115",
                        "quote_url": "/get-quote/",
                        "img-cta-review-photo": "review-photo",
                        "img-cta-bus-rental-image": "clifton-bus-rental"
                    },
                    "faqs": {
                        "faqs_title": "Learn More About Bus Rental Company Clifton",
                        "faqs_questions": [
                            {
                                "faq_question": "How is Bus Rental Company Clifton different from charter bus providers in Clifton?",
                                "faq_answer": "With Bus Rental Company Clifton you can get quotes on our entire network of buses. That's hundreds of local options in one place and 30-seconds to view pricing & availability. We make booking simple, with personalized 1 on 1 attention, flexible policies for all types of travel and groups, and 24/7/365 customer support."
                            },
                            {
                                "faq_question": "What types of groups typically use Bus Rental Company Clifton?",
                                "faq_answer": "Bus Rental Company Clifton specializes in transportation large and small! We've moved small groups of up to 10-36, as well as large groups of more than 1,000 at a time. We've even worked with companies, event planners, and the government, so you can be sure your group is in right hands."
                            },
                            {
                                "faq_question": "What cities does Bus Rental Company Clifton serve?",
                                "faq_answer": "No matter where you need to go in New Jersey, we have vehicles readily available, including buses, party and mini buses, and limos. We can also help with travel out of New Jersey, including to New York, Pennsylvania, Connecticut and more. If your destination city isn't on our website, give us a call—we also charter a bus rental in Teaneck, a shuttle bus rental in Paterson, or a Bloomfield shuttle bus rental. Feel free to bring up any location, from Hoboken to Hackensack to Edison and beyond. We're familiar with most cities and all of NJ's counties."
                            },
                            {
                                "faq_question": "What airports does Bus Rental Company Clifton serve?",
                                "faq_answer": "Traveling by plane with a group can present some logistical nightmares, whether it's due to the costs of baggage or having people arrive at the terminal at different times. With us, that's no problem at all; our private charter bus shuttles to Newark Liberty International, Teterboro, John F. Kennedy International and LaGuardia — and all New Jersey airports — will have enough space for everyone and their stuff. We'll also transport you comfortably and promptly from your gate to your hotel!"
                            },
                            {
                                "faq_question": "What types of vehicles does Bus Rental Company Clifton offer in Clifton?",
                                "faq_answer": "At Bus Rental Company Clifton, we offer a wide range of vehicles for rent, from smaller, 10 to 15 seat sprinter vans and 6 to 20 seat limousines to medium and large 15-56 passenger charter buses, minibuses, and even party buses. Our goal is to find you the perfect ride for your next trip."
                            },
                            {
                                "faq_question": "What amenities does Bus Rental Company Clifton?",
                                "faq_answer": "Bus Rental Company Clifton charter buses come with features designed to keep you entertained and comfortable. Your charter or mini bus, or even your limo can include a DVD or VHS player with flat screen TVS, AM/FM radio with AUX and Bluetooth connectivity so you can play your music, plush seating and wraparound style seats, free WiFi, individual power outlets and even more, all available upon request."
                            },
                            {
                                "faq_question": "Does Bus Rental Company Clifton offer buses with restrooms?",
                                "faq_answer": "Yes. If you're planning to be on the road for more than a few hours and want to avoid extra potty breaks, ask our team about booking a charter bus rental with a restroom. These are usually only available on the full-size motorcoaches and party buses."
                            },
                            {
                                "faq_question": "Does Bus Rental Company Clifton offer ADA accessible buses?",
                                "faq_answer": "Yes. At Bus Rental Company Clifton, we believe that everyone deserves comfortable transportation. The buses and minibuses in our network can come equipped with ADA-compliant features such as a wheelchair lift, widened aisles, and seating space for mobility devices, along with accessible restrooms with handrails."
                            },
                            {
                                "faq_question": "Does Bus Rental Company Clifton offer buses with WiFi?",
                                "faq_answer": "Yes. A WiFi connection is a must have on long road trips, so book a charter bus with WiFi for streaming music and movies."
                            },
                            {
                                "faq_question": "Does Bus Rental Company Clifton offer buses with outlets?",
                                "faq_answer": "Yes. In addition to onboard WiFi, our network's minibuses and charter buses may also include USB ports and power outlets where everyone can recharge their devices. That means that your electronics, such as smartphones and laptops, will be fully powered up when you arrive at your destination."
                            },
                            {
                                "faq_question": "Does Bus Rental Company Clifton offer buses with tvs?",
                                "faq_answer": "Yes. Many charter buses come with flat screen TVs with a DVD player that you can use to play movies and shows while traveling. We also offer multiple packages, depending on your needs and what we can provide—one-way, daily, multi-day, hourly, weekend and even much longer."
                            },
                            {
                                "faq_question": "How many seats are usually on a charter bus in Clifton?",
                                "faq_answer": "From 15-56. It all depends on your needs! If you're getting a bunch of friends together to celebrate a birthday with a mini bus or party bus rental in New Jersey, we can provide a vehicle from our network that perfectly accommodates your group. We can also provide a custom fleet for large-scale events and occasions, like for our Kearny bus rentals and East Orange shuttle bus rentals."
                            },
                            {
                                "faq_question": "How much luggage can we bring?",
                                "faq_answer": "It varies by bus. Some of our charter buses can hold up to one large suitcase and one carry-on per passenger. Minibuses, on the other hand, usually only have enough space above the seat for carry-ons. When you call to reserve your bus, just let us know how much luggage you're bringing and we'll be more than happy to find the right bus."
                            },
                            {
                                "faq_question": "What if we need multiple pickup locations?",
                                "faq_answer": "Unlimited stops. Just let us know where everyone is, when they need to be picked up, and where they're going, and we'll provide a driver who will be there on time. It is recommended you schedule a pick-up time earlier than usual and plan a trip with rest stops along the way if you're traveling with a huge group or a group of minors."
                            },
                            {
                                "faq_question": "Can the bus stay with us overnight?",
                                "faq_answer": "Yes. Planning a field trip to Boston, Manhattan, Philadelphia, or D.C.? No problem. You and the members of your group can enjoy relaxing in reclining seats or catching some Zs on the bus's sleeper berths. And no worries about how that will work. When you reserve a vehicle, we ensure your driver has hotel accommodations and plenty of time for rest each night; rest assured that your team will be in good hands."
                            },
                            {
                                "faq_question": "Can we wrap or brand the bus?",
                                "faq_answer": "Yes. It's possible! You may already be getting your group together in one place, for example at a college campus or in a nearby suburb. But you'll have a hard time figuring out what your bus rental is on the road if there are more than a few dozen vehicles in the parking lot at any given time—especially when everyone is being dropped off at different points throughout the pickup and dropoff process. That's where a branded charter bus comes into play. Not only can you identify your group's vehicle easily, the custom branding will enhance your trip or event even before it starts! When you are ready, your reservation specialists can take care of all the details of your branding or bus wrapping needs. Just allow at least two or three weeks' advanced notice."
                            },
                            {
                                "faq_question": "How much is a bus rental in Clifton?",
                                "faq_answer": "Pricing Varies, but getting a quote is easy! Just tell us your trip details and our online quote tool will get you pricing and pictures in just seconds! Alternatively, you could always just give us a call at ************ and we'll get you a free, custom quote over the phone — it's that easy!"
                            },
                            {
                                "faq_question": "What details do you need to give me an exact quote?",
                                "faq_answer": "We can get you quotes in seconds! Give us a call! Or use our online quote tool! With your trip or event dates, the cities and states for all your pick-up/drop-off locations, and a headcount of your travel group, we are sure to find you a custom package of ground transportation, no matter where around the state you're headed."
                            },
                            {
                                "faq_question": "How far in advance should I reserve my Clifton bus rental?",
                                "faq_answer": "3-6 months. During peak travel times, such as around major events like concerts, sports games and even festivals, it can be hard to find a charter or minibus due to high demand. Certain months like April through June and September through early November are also busy times for travel. Some dates sell out if you wait too long. Bus Rental Company Clifton, we recommend inquiring about pricing and booking your chariot as soon as possible."
                            },
                            {
                                "faq_question": "What's the Clifton traffic like?",
                                "faq_answer": "Always include buffer time in a rental. Even if your itinerary looks airtight on paper, real life has a way of throwing in unexpected twists along the way. Even the most reliable driver might run a few minutes behind due to weather, road congestion, or something else (like a detour!) To maximize your chances of arriving on schedule, include wiggle room in the planned beginning and end times for each leg of your trip."
                            },
                            {
                                "faq_question": "Where can we load and unload in downtown Clifton?",
                                "faq_answer": "There are multiple pickup and dropoff points across Clifton. You may choose anywhere on a public street that isn't a bus lane, an area with a 'no stopping anytime' designation or next to a crosswalk (e.g., Allwood Road, Route 3 or Valley Road). A rental expert can also help you out if you need advice."
                            },
                            {
                                "faq_question": "Where can I park a bus in Clifton?",
                                "faq_answer": "Parking options aren't as readily available in Clifton, which is something to consider when booking a hotel for your overnight stay. Ask your hotel whether they offer lot parking for charter buses. If not, alternate overnight parking arrangements include the Clifton lot near the Howard Johnson or street parking along Clifton Avenue."
                            },
                            {
                                "faq_question": "What are some popular local destinations or routes for bus trips around Clifton?",
                                "faq_answer": "Clifton offers plenty of attractions for a day trip, like a ride to the Oakeside Bloomfield Cultural Center with educational tours, private events, and more. Or go brewery-hopping through Clifton, Paterson, East Orange, Kearny, Bloomfield, and Wayne. No need to choose a DD when your driver will get you there at every stop. We can also organize a shuttle bus rental in Wayne, Bloomfield, Paterson, and any nearby city for a trip from Clifton."
                            }
                        ]
                    },
                    "worked_with": {
                        "worked_with_title": "A few of the companies we’ve worked with",
                        "work_with_list": [
                            {
                                "img-organization_logo": "Disney-logo",
                                "organization_name": "disney"
                            },
                            {
                                "img-organization_logo": "Disney-logo",
                                "organization_name": "cocacola"
                            },
                            {
                                "img-organization_logo": "Disney-logo",
                                "organization_name": "walmart"
                            },
                            {
                                "img-organization_logo": "Disney-logo",
                                "organization_name": "fedEx"
                            },
                            {
                                "img-organization_logo": "Disney-logo",
                                "organization_name": "pepsi"
                            },
                            {
                                "img-organization_logo": "Disney-logo",
                                "organization_name": "aramark"
                            }
                        ]
                    },
                    "testimonial": {
                        "img-background-pattern-bg-image": "background-pattern",
                        "testimonial_title": "What Our Customers Say",
                        "testimonial_cards": [
                            {
                                "img-user-image": "joo-p",
                                "user_name": "Natalia G.",
                                "testimonial": "We used the service for a company off-site. Everything went according to plan, with no issues at all."
                            },
                            {
                                "img-user-image": "joo-p",
                                "user_name": "João P.",
                                "testimonial": "Booked for a private celebration with a large group. The entire experience was smooth and timely."
                            },
                            {
                                "img-user-image": "joo-p",
                                "user_name": "Emma K.",
                                "testimonial": "Used for a wedding guest shuttle, and the process was seamless from start to finish. Highly recommend."
                            },
                            {
                                "img-user-image": "joo-p",
                                "user_name": "Oskar D.",
                                "testimonial": "Hired for a sports event day trip. Communication was clear, and everything ran exactly on time."
                            }
                        ]
                    },
                    "we_offer_list": {
                        "we_offer_services_title": "Group Transportation Services We Offer at Bus Rental Company Clifton",
                        "we_offer_services_list": [
                            {
                                "service_name": "Corporate Transportation & Employee Shuttles",
                                "service_description": "Bus Rental Company Clifton specializes in providing charter bus rentals to the Clifton area for corporate outings and events of all types and sizes, from simple airport one-way transfers for a small group of VIP clients to shuttles for conventions and conferences with thousands of attendees. We even offer sprinter and party buses for rent too! You can book a ride with all the right features to ensure your trip goes exactly according to plan, including comfortable amenities like plush seating, personal climate controls, WiFi, power outlets and more to keep your passengers productive and connected between stops on the road! Our team is here to support you! We also offer long-term corporate contracts for recurring shuttles to business parks and other events, as well as reliable, ongoing transportation around your hospital or university campus in Clifton.",
                                "offer_page_redirect_url": "/services/charter-bus-rentals",
                                "img-offer-service-image": "clifton-corporate-bus-rental"
                            },
                            {
                                "service_name": "Corporate Transportation & Employee Shuttles",
                                "service_description": "Bus Rental Company Clifton specializes in providing charter bus rentals to the Clifton area for corporate outings and events of all types and sizes, from simple airport one-way transfers for a small group of VIP clients to shuttles for conventions and conferences with thousands of attendees. We even offer sprinter and party buses for rent too! You can book a ride with all the right features to ensure your trip goes exactly according to plan, including comfortable amenities like plush seating, personal climate controls, WiFi, power outlets and more to keep your passengers productive and connected between stops on the road! Our team is here to support you! We also offer long-term corporate contracts for recurring shuttles to business parks and other events, as well as reliable, ongoing transportation around your hospital or university campus in Clifton.",
                                "offer_page_redirect_url": "/services/charter-bus-rentals",
                                "img-offer-service-image": "clifton-corporate-bus-rental"
                            },
                            {
                                "service_name": "Corporate Transportation & Employee Shuttles",
                                "service_description": "Bus Rental Company Clifton specializes in providing charter bus rentals to the Clifton area for corporate outings and events of all types and sizes, from simple airport one-way transfers for a small group of VIP clients to shuttles for conventions and conferences with thousands of attendees. We even offer sprinter and party buses for rent too! You can book a ride with all the right features to ensure your trip goes exactly according to plan, including comfortable amenities like plush seating, personal climate controls, WiFi, power outlets and more to keep your passengers productive and connected between stops on the road! Our team is here to support you! We also offer long-term corporate contracts for recurring shuttles to business parks and other events, as well as reliable, ongoing transportation around your hospital or university campus in Clifton.",
                                "offer_page_redirect_url": "/services/charter-bus-rentals",
                                "img-offer-service-image": "clifton-corporate-bus-rental"
                            },
                            {
                                "service_name": "Corporate Transportation & Employee Shuttles",
                                "service_description": "Bus Rental Company Clifton specializes in providing charter bus rentals to the Clifton area for corporate outings and events of all types and sizes, from simple airport one-way transfers for a small group of VIP clients to shuttles for conventions and conferences with thousands of attendees. We even offer sprinter and party buses for rent too! You can book a ride with all the right features to ensure your trip goes exactly according to plan, including comfortable amenities like plush seating, personal climate controls, WiFi, power outlets and more to keep your passengers productive and connected between stops on the road! Our team is here to support you! We also offer long-term corporate contracts for recurring shuttles to business parks and other events, as well as reliable, ongoing transportation around your hospital or university campus in Clifton.",
                                "offer_page_redirect_url": "/services/charter-bus-rentals",
                                "img-offer-service-image": "clifton-corporate-bus-rental"
                            }
                        ]
                    }
                }
            }
        },
        "FileList": []
    }
]

export const templateFilterOptions = [
    { value: "recently_modified", label: "Recently Modified" },
    { value: "earliest_modified", label: "Earliest Modified" },
    { value: "last_created", label: "Last Created" },
    { value: "earliest_created", label: "Earliest Created" },
];

export const getSortParams = (filter) => {
    switch (filter) {
        case "recently_modified":
            return {
                sort: "updatedAt",
                sortBy: "desc",
            };
        // return "sort=updatedAt&sortBy=desc";
        case "earliest_modified":
            return {
                sort: "updatedAt",
                sortBy: "asc",
            };
        // return "sort=updatedAt&sortBy=asc";
        case "last_created":
            return {
                sort: "createdAt",
                sortBy: "desc",
            };
        // return "sort=createdAt&sortBy=desc";
        case "earliest_created":
            return {
                sort: "createdAt",
                sortBy: "asc",
            };
        // return "sort=createdAt&sortBy=asc";
        default:
            return {
                sort: "updatedAt",
                sortBy: "desc",
            };
        // return "sort=updatedAt&sortBy=desc";
    }
};