import { <PERSON><PERSON>, Card, Col, Form, Input, message, Row, Tooltip } from "antd";
import Title from "antd/es/typography/Title";
import { MinusCircle, Plus } from "lucide-react";
import React, { useCallback, useEffect, useRef, useState } from "react";
import useStorage from "../../../hooks/use-storage";

const GlobalVariiableTab = ({ templatePage, setTemplatePage }) => {
  const [importing, setImporting] = useState(false);
  const fileInputRef = useRef(null);
  const formInstancesRef = useRef({});
  const [internalForm] = Form.useForm();

  // Import JSON handler
  const handleImportJSON = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  // File change handler
  const onFileChange = useCallback(async (e) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setImporting(true);
    try {
      const text = await file.text();
      const parsed = JSON.parse(text);
      let mergedPages = {};
      // if (parsed) {
      //   console.log(parsed, "parce....");
      // }

      message.success(
        `JSON imported successfully! ${
          Object.keys(mergedPages).length
        } page(s) added.`
      );
    } catch (err) {
      console.error("Import JSON error:", err);
      if (err instanceof SyntaxError) {
        message.error("Invalid JSON format. Please check your file syntax.");
      } else {
        message.error(
          "Failed to import JSON. Please check the file format and structure."
        );
      }
    } finally {
      setImporting(false);
      if (fileInputRef.current) fileInputRef.current.value = "";
    }
  }, []);

  // Cleanup effect
  React.useEffect(() => {
    return () => {
      formInstancesRef.current = {};
    };
  }, []);

  return (
    <>
      <Card>
        <div className="tw-space-y-4">
          <div className="tw-flex tw-justify-between tw-items-center tw-mb-4">
            <div>
              <Title level={4} className="!tw-mb-0 !tw-text-gray-900">
                Global Variable Details
              </Title>
            </div>
            <div className="tw-space-x-2">
              <Tooltip title="Import JSON file to auto-generate form fields and populate dynamic page data">
                <Button
                  type="primary"
                  // icon={<Upload className="tw-w-4 tw-h-4" />}
                  onClick={handleImportJSON}
                  loading={importing}
                  disabled={Object.keys(templatePage?.GVariable).length == 0}
                  className="tw-text-white tw-px-6 tw-h-10 tw-flex tw-items-center tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
                >
                  {importing ? "Importing..." : "Import JSON"}
                </Button>
              </Tooltip>
              <input
                type="file"
                ref={fileInputRef}
                onChange={onFileChange}
                accept=".json"
                style={{ display: "none" }}
              />
            </div>
          </div>

          {Object.keys(templatePage?.GVariable).length != 0 ? (
            <Form
              form={internalForm}
              name="dynamic_form_GVariable"
              layout="vertical"
              size="large"
              onValuesChange={(e, allValues) => {
                const obj = {};
                // console.log(e, "e", allValues);
                allValues?.GVariable?.sectionItems?.map((el) => {
                  obj[el.key] = el.value;
                });
                setTemplatePage((prev) => ({
                  ...prev,
                  GVariable: obj,
                }));
              }}
              initialValues={{
                GVariable: {
                  sectionItems: Object.keys(templatePage?.GVariable).map(
                    (key) => ({
                      key,
                      value: templatePage?.GVariable[key],
                    })
                  ),
                },
              }}
            >
              <Form.List name={["GVariable", "sectionItems"]}>
                {(fields, { add, remove }) => (
                  <>
                    {fields.map((subField) => (
                      <Row key={subField.key} gutter={[16, 0]}>
                        <Col span={10}>
                          <Form.Item
                            {...subField}
                            name={[subField.name, "key"]}
                            rules={[
                              { required: true, message: "Key is required" },
                            ]}
                          >
                            <Input placeholder="Enter Key" />
                          </Form.Item>
                        </Col>
                        <Col span={10}>
                          <Form.Item
                            {...subField}
                            name={[subField.name, "value"]}
                            rules={[
                              { required: true, message: "Value is required" },
                            ]}
                          >
                            <Input placeholder="Enter Value" />
                          </Form.Item>
                        </Col>
                        <Col span={4} className="tw-mt-2">
                          <MinusCircle
                            className="tw-text-red-500 tw-cursor-pointer"
                            onClick={() => remove(subField.name)}
                          />
                        </Col>
                      </Row>
                    ))}
                    <Button
                      type="dashed"
                      onClick={() => add({ key: "", value: "" })}
                      block
                      icon={<Plus className="tw-w-4 tw-h-4" />}
                    >
                      Add Variable
                    </Button>
                  </>
                )}
              </Form.List>
            </Form>
          ) : (
            <div className="tw-text-center tw-py-8 tw-text-gray-500">
              No Global Variable Found
            </div>
          )}
        </div>
      </Card>
    </>
  );
};

export default GlobalVariiableTab;
