import {
  BrowserRouter as Router,
  Navigate,
  RouterProvider,
  createBrowserRouter,
} from "react-router-dom";
import { AuthProvider, useAuth } from "./contexts/AuthContext";
import { SidebarProvider } from "./contexts/SidebarContext";
import { ErrorProvider } from "./contexts/ErrorContext";
import { InternetProvider } from "./contexts/InternetContext";
import ErrorBoundary from "./components/ErrorPages/ErrorBoundary";
import RouterWithMonitor from "./components/ErrorPages/RouterWithMonitor";
import ALL_ROUTES from "./util/Route";

function App() {
  const router = createBrowserRouter(ALL_ROUTES({}));

  // if (Object.values(ROLES).findIndex((el) => el === user?.role) === -1) {
  //   localStorage.clear();
  //   document.cookie = "SAID=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
  // }
  return (
    <ErrorBoundary>
      <ErrorProvider>
        <InternetProvider>
          <AuthProvider>
            <SidebarProvider>
              <RouterProvider router={router} />
            </SidebarProvider>
          </AuthProvider>
        </InternetProvider>
      </ErrorProvider>
    </ErrorBoundary>
  );
}

function ProtectedRoute({ children }) {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="tw-min-h-screen tw-flex tw-items-center tw-justify-center">
        <div className="tw-animate-spin tw-rounded-full tw-h-32 tw-w-32 tw-border-b-2 tw-border-blue-600"></div>
      </div>
    );
  }

  return user ? children : <Navigate to="/login" />;
}

function AdminRoute({ children }) {
  const { user } = useAuth();
  return user && user.role === "admin" ? children : <Navigate to="/" />;
}

export default App;
