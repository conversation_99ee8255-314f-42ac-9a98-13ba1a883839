import React, { useEffect, useMemo, useState } from "react";

import { DndProvider } from "react-dnd";
import MediaBar from "../../Templates/Component/MediaBar";
import ContentCollapseBar from "../../Templates/Component/ContentCollapseBar";
import { ChevronDown, Image, ListCollapse } from "lucide-react";
import { HTML5Backend } from "react-dnd-html5-backend";
import PageListPreview from "./PageListPreview";
import { Card, Collapse, message } from "antd";
import {
  downloadWebsiteZip,
  convertPageListToExportFormat,
} from "../../../utils/simpleWebsiteExporter";
// import {
//   downloadWorkingWebsiteZip,
//   convertPageListToExportFormat as convertPageListToWorkingExportFormat,
// } from "../../utils/workingWebsiteExporter";
// import {
//   downloadWorkingWebsiteZip,
//   convertPageListToExportFormat as convertPageListToWorkingFormat,
// } from "../../utils/workingWebsiteExporter";
import {
  filterEmptyFields,
  replaceTemplateContent,
} from "../../../util/functions";
import WarningModal from "./WarningModal";
import BrandingDetailsCard from "./BrandingDetailsCard";
import { predefinedColors } from "../../../util/content";
import TabList from "../../common/TabList";

const pageGenerator = ({
  templateObj,
  dynamicContentForm,
  allVariables,
  isExport = false,
  bradingDetails,
}) => {
  const pages = [];
  const dynamicsitemapobj = {};
  const staticsitemapobj = [];
  if (!templateObj?.pages?.length) return [];
  // console.log(allVariables, "allVariables");
  templateObj?.pages?.map((templatePage) => {
    const pageObj = JSON.parse(JSON.stringify(templatePage));
    if (pageObj?.type == "dynamic") {
      if (Object.keys(dynamicContentForm)?.length) {
        const page = dynamicContentForm?.[pageObj?.name];
        const sections = page?.sections;
        if (sections?.length) {
          sections?.map((el) => {
            const slugArr = el?.sectionItems;
            slugArr?.map((slugObj) => {
              let dynamicPageObj = JSON.parse(JSON.stringify(pageObj));
              if (slugObj?.isCustom) {
                let customPage = templateObj?.pages?.find(
                  (page) => page.name === slugObj?.label
                );
                if (
                  customPage &&
                  customPage != null &&
                  customPage != undefined
                ) {
                  // remove old page if same name already exists
                  const existingIndex = pages?.findIndex(
                    (p) => p?.name === customPage?.name
                  );
                  if (existingIndex !== -1) {
                    pages?.splice(existingIndex, 1);
                  }

                  dynamicPageObj = JSON.parse(JSON.stringify(customPage));
                }
              }
              const pageName = slugObj?.label;
              pages?.push({
                ...replaceTemplateContent({
                  page: dynamicPageObj,
                  tempContent: templateObj?.templateContentJSON,
                  content: templateObj?.contentJSON,
                  slug: slugObj,
                  mediaFiles: templateObj?.FileList,
                  allVariables,
                  templateObj,
                  isExport,
                  dynamicContentForm,
                  bradingDetails,
                }),
                url: `${dynamicPageObj.url}`,
                name: slugObj?.label,
              });
              dynamicsitemapobj[page?.sitemapLabel]
                ? dynamicsitemapobj[page?.sitemapLabel].push({
                    url: `${dynamicPageObj.url}.html`,
                    label: slugObj?.extra?.sitemapLabel,
                  })
                : (dynamicsitemapobj[page?.sitemapLabel] = [
                    {
                      url: `${dynamicPageObj.url}.html`,
                      label: slugObj?.extra?.sitemapLabel,
                    },
                  ]);
              allVariables[pageName] = {
                ...dynamicPageObj.variable,
              };
            });
          });
        }
      } else {
        pages?.push({
          ...replaceTemplateContent({
            page: pageObj,
            tempContent: templateObj?.templateContentJSON,
            content: templateObj?.contentJSON,
            mediaFiles: templateObj?.FileList,
            allVariables,
            templateObj,
            isExport,
            dynamicContentForm,
            bradingDetails,
          }),
          url: pageObj.url,
          name: pageObj.name,
        });
        allVariables[pageObj.name] = { ...pageObj.variable };
      }
    } else {
      if (pageObj?.name === "sitemap") {
        return;
      }
      if (!["privacy-policy", "Home", "home"].includes(pageObj?.name))
        staticsitemapobj.push({
          url: `${pageObj.url}.html`,
          label: pageObj?.sitemapLabel || pageObj?.name,
        });

      replaceTemplateContent({
        page: pageObj,
        tempContent: templateObj?.templateContentJSON,
        content: templateObj?.contentJSON,
        mediaFiles: templateObj?.FileList,
        allVariables,
        isExport,
        templateObj,
        dynamicContentForm,
        bradingDetails,
      });
      allVariables[pageObj.name] = { ...pageObj.variable };
      pages.push(pageObj);
    }
  });
  const sitemapList = {
    sitemap_warp1: {
      sitemap_list: staticsitemapobj,
      title: "Sitemap",
    },
    ...Object.entries(dynamicsitemapobj).reduce((acc, [key, value], index) => {
      acc[`sitemap_warp${index + 2}`] = {
        sitemap_list: value,
        title: key,
      };
      return acc;
    }, {}),
  };
  const sitemapPageObj = templateObj?.pages?.find(
    (page) => page.name === "sitemap"
  );
  replaceTemplateContent({
    page: sitemapPageObj,
    tempContent: templateObj?.templateContentJSON,
    content: templateObj?.contentJSON,
    mediaFiles: templateObj?.FileList,
    allVariables: {
      ...allVariables,
      sitemap: { ...allVariables?.sitemap, ...sitemapList },
    },
    templateObj,
    isExport,
    dynamicContentForm,
    bradingDetails,
  });
  allVariables[sitemapPageObj.name] = { ...sitemapPageObj.variable };
  pages.push(sitemapPageObj);

  return pages;
};

const BradingTab = ({
  templateObj,
  setTemplateObj,
  // formData,
  // setFormData,
  onCancel,
  handleSubmit,
  saving,
  pages,
  components = [],
  dynamicContentForm = {},
  bradingDetails,
  setBradingDetails,
}) => {
  // const [contentJSON, setContentJSON] = useState(templateObj?.contentJSON ?? {});
  // const [filterContentJson, setFilterContetnJson] = useState({});
  const [fileList, setFileList] = useState({});

  const [previewMode, setPreviewMode] = useState("contents");
  const [reset, setReset] = useState(false);
  const [webContentJson, setWebContetnJson] = useState({});
  const [pageList, setPageList] = useState([]);
  const [open, setOpen] = useState(false);
  useEffect(() => {
    const fileObj = {};
    Object.keys(fileList)?.map((key) => {
      fileObj[`_img-${key}`] = {
        url: fileList[key]?.blobUrl,
        name: fileList[key]?.originalName,
        path: "/assets/" + fileList[key]?.originalName,
      };
    });
    setTemplateObj((prev) => ({
      ...prev,
      FileList: { ...prev?.FileList, ...(fileObj || {}) },
    }));
    setReset((prev) => !prev);
  }, [fileList]);
  // console.log(dynamicContentForm, "dynamicContentForm", templateObj);
  const handleBrandingChange = (patch) => {
    setBradingDetails((prev) => ({ ...prev, ...patch }));
    // setReset((prev) => !prev);
    // if (patch?.faviconFile?.length) {
    //   const imgDetail = patch?.faviconFile[0];
    //   const blobUrl = URL.createObjectURL(imgDetail?.originFileObj);
    //   const imageKey = `${imgDetail?.name?.replace(/\.[^/.]+$/, "")}`;
    //   setFileList((prev) => ({
    //     ...prev,
    //     [imageKey]: {
    //       blobUrl,
    //       originalName: imgDetail?.name,
    //     },
    //   }));
    // }
  };

  useEffect(() => {
    setWebContetnJson((pr) => {
      const allVariables = JSON.parse(JSON.stringify(pr));

      setPageList((pr) =>
        pageGenerator({
          templateObj,
          dynamicContentForm,
          allVariables,
          bradingDetails,
          // isExport: true,
        })
      );
      return allVariables;
    });
  }, [reset, dynamicContentForm]);

  const tabContents = {
    contents: {
      key: "contents",
      label: "Content",
      icon: <ListCollapse className="tw-w-4 tw-h-4 tw-mr-2" />,
      content: (
        <ContentCollapseBar
          contentJSON={webContentJson}
          setContentJSON={setWebContetnJson}
          saving={saving}
          setReset={setReset}
          templateObj={templateObj}
          dynamicContentForm={dynamicContentForm}
        />
      ),
    },
    media: {
      key: "media",
      label: "Media",
      icon: <Image className="tw-w-4 tw-h-4 tw-mr-2" />,
      content: (
        <MediaBar
          templateObj={templateObj}
          setTemplateObj={setTemplateObj}
          fileList={fileList}
          setFileList={setFileList}
        />
      ),
    },
  };

  // useEffect(() => {
  //   setContentJSON((prev) => ({
  //     ...prev,
  //     ...templateObj?.contentJSON,
  //   }));
  // }, [templateObj?.contentJSON]);

  // Debounced contentJSON for preview updates (500ms delay)
  // const debouncedContentJSON = useDebounce(contentJSON, 500);

  const handleSaveContent = async () => {
    try {
      // Update templateObj with generated content
      const updatedFormData = {
        ...templateObj,
        // full_template_content: fullTemplateContent,
        // templateComponentList: templateComponentList,
        // contentJSON: contentJSON || [],
        FileList: fileList || [],
      };
      handleSubmit(updatedFormData);
    } catch (error) {
      console.error("Error generating template structure:", error);
      message.error("Failed to generate template structure. Please try again.");
    }
  };

  const empties = useMemo(
    () => filterEmptyFields(webContentJson) || null,
    [webContentJson]
  );

  const handleExport = async ({
    templateObj,
    dynamicContentForm,
    webContentJson,
  }) => {
    console.log("Starting simple website export...");
    try {
      // Convert pageList to the required format
      if (empties) {
        setOpen(true);
        return;
      }
      const pagesForExport = pageGenerator({
        templateObj,
        dynamicContentForm,
        allVariables: webContentJson,
        isExport: true,
        bradingDetails,
      });
      const pages = convertPageListToExportFormat(pagesForExport);

      // Get base URL from branding details or use default
      const baseUrl = bradingDetails?.website_url || "https://example.com/";

      // Use working export function for better SEO and performance with sitemap generation
      // const result = await downloadWorkingWebsiteZip(
      //   pages,
      //   templateObj?.FileList,
      //   `website-working-${new Date().toISOString().split("T")[0]}`,
      //   dynamicContentForm,
      //   baseUrl: templateObj?.GVariable?.website_url
      // );

      // Use working export function for better SEO and performance
      // const result = await downloadWorkingWebsiteZip(
      //   pages,
      //   templateObj?.FileList,
      //   `website-working-${new Date().toISOString().split("T")[0]}`
      // );
      const result = await downloadWebsiteZip({
        pageList: pages,
        zipName: `website-${new Date().toISOString().split("T")[0]}.zip`,
        fileList: templateObj?.FileList,
        dynamicContent: dynamicContentForm,
        baseUrl: templateObj?.GVariable?.website_url,
      });

      message.success(
        `Website exported successfully! ${pageList.length} pages generated.`
      );
      // console.log("Website exported successfully:", result);
    } catch (error) {
      console.error("Export failed:", error);
      message.error(`Export failed: ${error.message}`);
    }
  };
  return (
    <DndProvider backend={HTML5Backend}>
      <div className="tw-py-4">
        <Collapse
          size="middle"
          className="tw-bg-white"
          expandIcon={({ isActive }) => (
            <ChevronDown
              className={`tw-w-3 tw-h-3 tw-text-gray-400 tw-transition-transform tw-duration-200 ${
                isActive ? "tw-rotate-180" : ""
              }`}
            />
          )}
          expandIconPosition="end"
          items={[
            {
              key: "branding_details",
              label: (
                <span className="tw-text-lg tw-font-semibold  tw-text-gray-600">
                  Branding Details
                </span>
              ),
              children: (
                <BrandingDetailsCard
                  values={bradingDetails}
                  onChange={handleBrandingChange}
                  predefinedColors={predefinedColors}
                />
              ),
            },
          ]}
        />
      </div>
      <Card className="tw-mt-4" styles={{ body: { padding: "16px" } }}>
        <div className="tw-h-screen tw-flex tw-overflow-hidden tw-space-x-2">
          <PageListPreview
            // contentJSON={contentJSON}
            // fileList={fileList}
            // components={components}
            pageList={pageList}
            extraDetail={{
              templateObj,
              dynamicContentForm,
              webContentJson,
              GVariable: templateObj?.GVariable,
            }}
            exporthandler={handleExport}
            bradingDetails={bradingDetails}
          />

          {/* Right Side - Content Editor */}
          <div className="tw-h-full tw-w-full tw-flex tw-flex-col tw-bg-white tw-flex-1">
            <TabList
              tabContents={tabContents}
              setPreviewMode={setPreviewMode}
              previewMode={previewMode}
            />
            <div className="tw-flex-1 tw-flex tw-flex-col tw-overflow-hidden">
              {tabContents[previewMode]?.content ? (
                tabContents[previewMode]?.content
              ) : (
                <></>
              )}
            </div>
          </div>
        </div>
      </Card>

      <WarningModal open={open} setOpen={setOpen} empties={empties} />
    </DndProvider>
  );
};

export default BradingTab;
