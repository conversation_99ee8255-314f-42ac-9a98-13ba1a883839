import React, { useState, useEffect, useCallback } from "react";
import { Form, Input, Button, Typography, Row, Col, message, Spin } from "antd";
import { Plus, Minus, Search, MinusCircle, Diamond } from "lucide-react";
import useHttp from "../../../hooks/use-http";
import { CONSTANTS } from "../../../util/constant/CONSTANTS";
import { apiGenerator } from "../../../util/functions";
import SearchBar from "../../common/SearchBar";
import useDebounce from "../../../hooks/useDebounce";

const { Title } = Typography;

const GlobalVariablesTab = () => {
  const api = useHttp();
  const [form] = Form.useForm();
  const [variables, setVariables] = useState([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredVariables, setFilteredVariables] = useState([]);
  const [originalVariables, setOriginalVariables] = useState([]); // Store original state for comparison
  const [hasChanges, setHasChanges] = useState(false); // Track if there are changes

  // Debounced search term
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Load global variables on component mount
  useEffect(() => {
    loadGlobalVariables();
  }, []);

  // Filter variables based on search term
  useEffect(() => {
    const formValues = form.getFieldsValue();
    const currentVariables = formValues.variables || [];

    if (debouncedSearchTerm) {
      const filtered = currentVariables.filter((variable) =>
        variable.name?.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
      );
      setFilteredVariables(filtered);
    } else {
      setFilteredVariables(currentVariables);
    }
  }, [debouncedSearchTerm, form]);

  // Track form changes
  const checkForChanges = useCallback(() => {
    const formValues = form.getFieldsValue();
    const currentVariables = formValues.variables || [];

    // Compare with original variables to detect changes
    const hasFormChanges =
      JSON.stringify(currentVariables) !== JSON.stringify(originalVariables);
    setHasChanges(hasFormChanges);
  }, [form, originalVariables]);

  // Listen to form changes
  useEffect(() => {
    const subscription = form.getFieldsValue();
    checkForChanges();
  }, [form, checkForChanges]);

  // Load global variables from API
  const loadGlobalVariables = useCallback(async () => {
    setLoading(true);
    try {
      await api.sendRequest(
        CONSTANTS.API.globalVarible.get,
        (response) => {
          // Handle the API response format - it returns variables as comma-separated string
          const variablesString = response?.data?.variables || "";
          const variablesList = variablesString
            ? variablesString.split(",").map((variable, index) => ({
                id: `var_${index}`,
                name: variable.trim(),
                value: "",
              }))
            : [];

          setVariables(variablesList);
          setFilteredVariables(variablesList);
          setOriginalVariables(variablesList); // Store original state

          // Initialize form with existing variables
          const formData = {
            variables: variablesList,
          };
          form.setFieldsValue(formData);
        },
        null,
        null,
        (error) => {
          console.error("Error loading global variables:", error);
          message.error("Failed to load global variables");
        }
      );
    } catch (error) {
      console.error("Error in loadGlobalVariables:", error);
      message.error("Failed to load global variables");
    } finally {
      setLoading(false);
    }
  }, [api, form]);

  // Handle search
  const handleSearch = useCallback((searchValue) => {
    setSearchTerm(searchValue);
  }, []);

  // Save all variables (bulk update)
  const saveAllVariables = useCallback(async () => {
    setSaving(true);
    try {
      const formValues = form.getFieldsValue();
      const variablesList = formValues.variables || [];

      // Filter out empty variables and create comma-separated string
      const validVariables = variablesList
        .filter((variable) => variable.name && variable.name.trim())
        .map((variable) => variable.name.trim());

      const payload = {
        variables: validVariables.join(","),
      };

      await api.sendRequest(
        CONSTANTS.API.globalVarible.create,
        (response) => {
          message.success("Variables saved successfully");
          setHasChanges(false); // Reset changes flag
          loadGlobalVariables(); // Reload the list
        },
        payload,
        null,
        // "Variables saved successfully",
        (error) => {
          console.error("Error saving variables:", error);
          message.error("Failed to save variables");
        }
      );
    } catch (error) {
      console.error("Error in saveAllVariables:", error);
      message.error("Failed to save variables");
    } finally {
      setSaving(false);
    }
  }, [api, form, loadGlobalVariables]);

  // Handle onBlur for auto-save
  const handleBlur = useCallback(() => {
    // Only save if there are actual changes
    if (hasChanges) {
      saveAllVariables();
    }
  }, [hasChanges, saveAllVariables]);

  // Add new variable
  const addVariable = useCallback(() => {
    const currentValues = form.getFieldsValue();
    const newVariable = {
      id: null,
      name: "",
      value: "",
    };

    const updatedVariables = [...(currentValues.variables || []), newVariable];
    form.setFieldsValue({ variables: updatedVariables });
  }, [form]);

  // Remove variable
  const removeVariable = useCallback(
    (index) => {
      const currentValues = form.getFieldsValue();
      const updatedVariables = currentValues.variables.filter(
        (_, i) => i !== index
      );
      form.setFieldsValue({ variables: updatedVariables });
      // Auto-save after removal (removal is always a change)
      setTimeout(() => saveAllVariables(), 100);
    },
    [form, saveAllVariables]
  );

  // if (loading) {
  //   return (
  //     <div className="tw-flex tw-justify-center tw-items-center tw-py-8">
  //       <Spin size="large" />
  //     </div>
  //   );
  // }
  const formItemLayoutWithOutLabel = {
    wrapperCol: {
      xs: { span: 24, offset: 0 },
      sm: { span: 20, offset: 4 },
    },
  };

  return (
    <div className="tw-bg-transparent tw-rounded-lg  tw-p-6">
      {/* Header */}
      <div className="tw-flex tw-items-center tw-justify-between tw-mb-6">
        <div>
          <Title level={3} className="!tw-mb-0 !tw-text-gray-900">
            Global Variables ({filteredVariables?.length})
          </Title>
        </div>
        <Button
          type="primary"
          size="large"
          icon={<Plus className="tw-w-4 tw-h-4" />}
          onClick={addVariable}
          className="tw-px-6 tw-h-10 tw-flex tw-items-center tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
        >
          Add Variable
        </Button>
      </div>

      {/* Search Bar */}
      <div className="tw-mb-6">
        <SearchBar
          type="page"
          placeholder="Search variables"
          handleSearch={handleSearch}
        />
      </div>

      {/* Variables Form */}
      {loading ? (
        <div className="tw-flex tw-justify-center tw-items-center tw-py-8">
          <Spin size="default" />
        </div>
      ) : (
        <Form
          form={form}
          name="globalVariables"
          layout="vertical"
          initialValues={{ variables: [] }}
          size="large"
          onValuesChange={checkForChanges}
        >
          <Form.List name="variables">
            {(fields, { add, remove }) => {
              // Use filtered variables for display if search is active
              const displayFields = debouncedSearchTerm
                ? fields.filter((field, index) => {
                    const formValues = form.getFieldsValue();
                    const variable = formValues.variables?.[index];
                    return variable?.name
                      ?.toLowerCase()
                      .includes(debouncedSearchTerm.toLowerCase());
                  })
                : fields;

              return (
                <div className="tw-space-y-4">
                  <Row gutter={[16, 16]} align="middle">
                    {displayFields.map((field, index) => (
                      <Col span={12}>
                        <Form.Item
                          required={false}
                          className="tw-mb-0"
                          style={{ marginBottom: 0 }}
                        >
                          <div className="tw-flex tw-items-center tw-gap-2">
                            <Form.Item
                              {...field}
                              name={[field?.name, "name"]}
                              rules={[
                                {
                                  required: true,
                                  message: "Variable name is required",
                                },
                                {
                                  pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/,
                                  message:
                                    "Variable name must contain only letters, numbers, and underscores",
                                },
                              ]}
                              noStyle
                            >
                              <Input
                                placeholder="Enter variable"
                                onBlur={handleBlur}
                                prefix="$"
                                className="tw-rounded-lg"
                              />
                            </Form.Item>

                            {fields.length > 1 ? (
                              <MinusCircle
                                className="tw-text-red-500 tw-cursor-pointer"
                                onClick={() => removeVariable(field.name)}
                              />
                            ) : null}
                          </div>
                        </Form.Item>
                      </Col>
                    ))}
                  </Row>

                  {displayFields.length === 0 && (
                    <div className="tw-text-center tw-py-8 tw-text-gray-500">
                      <Diamond className="tw-w-12 tw-h-12 tw-mx-auto tw-mb-4 tw-text-gray-300" />
                      <p className="tw-text-lg tw-font-medium tw-mb-2">
                        No global variables
                      </p>
                      <p className="tw-text-sm">
                        {searchTerm
                          ? "Try adjusting your search"
                          : "Create your first global variable"}
                      </p>
                    </div>
                  )}
                </div>
              );
            }}
          </Form.List>
        </Form>
      )}

      {/* Loading overlay for saving */}
      {/* {saving && (
        <div className="tw-fixed tw-inset-0 tw-bg-black tw-bg-opacity-25 tw-flex tw-items-center tw-justify-center tw-z-50">
          <div className="tw-bg-white tw-p-6 tw-rounded-lg tw-shadow-lg">
            <Spin size="large" />
            <p className="tw-mt-4 tw-text-center">Saving...</p>
          </div>
        </div>
      )} */}
    </div>
  );
};

export default GlobalVariablesTab;
