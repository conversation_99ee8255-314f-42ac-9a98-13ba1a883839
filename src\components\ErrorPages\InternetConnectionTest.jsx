import React from "react";
import { <PERSON><PERSON>, <PERSON>, Typo<PERSON>, <PERSON> } from "antd";
import { WifiOutlined, WifiOffOutlined } from "@ant-design/icons";
import { useInternet } from "../../contexts/InternetContext";
import useInternetConnection from "../../hooks/useInternetConnection";

const { Title, Text } = Typography;

const InternetConnectionTest = () => {
  const { isOnline, connectionQuality, getConnectionStats } = useInternet();
  const { manualRetry, getConnectionStatus, canRetry } = useInternetConnection({
    redirectOnOffline: false, // Don't redirect from this test component
    autoRetry: false,
  });

  const stats = getConnectionStats();
  const status = getConnectionStatus();

  const handleTestConnection = async () => {
    const success = await manualRetry();
    console.log("Connection test result:", success);
  };

  const handleSimulateOffline = () => {
    // This is for testing purposes - in real scenarios, you'd disconnect from network
    console.log("Simulating offline state...");
    // You can use browser dev tools to simulate offline
  };

  return (
    <Card title="Internet Connection Test" className="tw-m-4">
      <Space direction="vertical" size="large" className="tw-w-full">
        <div className="tw-flex tw-items-center tw-gap-2">
          {isOnline ? (
            <WifiOutlined className="tw-text-green-500 tw-text-xl" />
          ) : (
            <WifiOffOutlined className="tw-text-red-500 tw-text-xl" />
          )}
          <Title level={4} className="tw-mb-0">
            Status: {isOnline ? "Online" : "Offline"}
          </Title>
        </div>

        <div className="tw-grid tw-grid-cols-2 tw-gap-4">
          <div>
            <Text strong>Connection Quality:</Text>
            <br />
            <Text>{connectionQuality}</Text>
          </div>
          <div>
            <Text strong>Uptime:</Text>
            <br />
            <Text>{stats.uptime}%</Text>
          </div>
          <div>
            <Text strong>Retry Count:</Text>
            <br />
            <Text>{status.retryCount}</Text>
          </div>
          <div>
            <Text strong>Total Connections:</Text>
            <br />
            <Text>{stats.totalConnections}</Text>
          </div>
        </div>

        <div className="tw-flex tw-gap-2">
          <Button
            type="primary"
            icon={<WifiOutlined />}
            onClick={handleTestConnection}
            disabled={!canRetry}
          >
            Test Connection
          </Button>
          <Button icon={<WifiOffOutlined />} onClick={handleSimulateOffline}>
            Simulate Offline (Dev Tools)
          </Button>
        </div>

        <div className="tw-bg-gray-50 tw-p-4 tw-rounded">
          <Text type="secondary" className="tw-text-sm">
            <strong>Testing Instructions:</strong>
            <br />
            1. Use browser dev tools (F12) → Network tab → Check "Offline"
            <br />
            2. You should see the internet loss page automatically
            <br />
            3. Uncheck "Offline" to restore connection
            <br />
            4. You should be redirected back to the previous page
          </Text>
        </div>
      </Space>
    </Card>
  );
};

export default InternetConnectionTest;
