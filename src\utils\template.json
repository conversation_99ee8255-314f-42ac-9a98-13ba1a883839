{"name": "dynamic Template 6pm", "description": "dynamic Template 6pm", "full_template_content": "\n      <div class=\"template-container\">\n        <div class=\"template-pages\">\n          \n        <div class=\"template-page\" data-page-id=\"memu2w5jds6feyll53\" data-position=\"0\">\n          <div class=\"page-content\">\n            <!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>dynamic-city-6pm</title>\n  <!-- Tail<PERSON> CSS - Generated locally to avoid CDN warnings -->\n<style>\n      @font-face {\n        font-family: Inter;\n        font-style: normal;\n        font-weight: 400;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n      @font-face {\n        font-family: Inter;\n        font-style: normal;\n        font-weight: 500;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n      @font-face {\n        font-family: Inter;\n        font-style: normal;\n        font-weight: 700;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n      @font-face {\n        font-family: Phudu;\n        font-style: normal;\n        font-weight: 600;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n      @font-face {\n        font-family: Phudu;\n        font-style: normal;\n        font-weight: 700;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n    </style>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script src=\"//unpkg.com/alpinejs\" defer></script>\n    <script>\n      tailwind.config = {\n        content: [\"./*.html\"],\n        prefix: \"dw-\",\n        theme: {\n          extend: {\n            colors: {\n              body: {\n                bg: \"#ffffff\", // --body-bg-color\n                text: \"#333333\", // --body-text-color\n              },\n              theme: {\n                main: \"#d5232b\", // --theme-main-color\n                mainColorShade: \"#C41421\",\n                hoverBox: \"rgba(82, 164, 206, 0.43)\", // --theme-box-hover-color\n                cardBorder: \"#e0e0e0\",\n                cardBg: \"#fafafa\",\n                divider: \"#eee\",\n                lowOpacityTextColor: \"#111827\",\n              },\n            },\n            fontFamily: {\n              heading: [\"Phudu\", \"sans-serif\"], // --theme-heading-font\n              text: [\"Inter\", \"sans-serif\"], // --theme-text-font\n            },\n            screens: {\n              xs: \"200px\",\n              smx: \"376px\",\n              smm: \"567px\",\n              mdx: \"768px\",\n              mdl: \"993px\",\n              lgx: \"1171px\",\n              lgs: \"1221px\",\n              lgm: \"1281px\",\n              lgl: \"1361px\",\n              xlx: \"1400px\",\n              xl1: \"1441px\",\n              xl2: \"1600px\",\n              xxl: \"1900px\",\n            },\n          },\n        },\n        plugins: [],\n      };\n    </script>\n  \n\n  <style>\n    /* Base styles */\n    * { box-sizing: border-box; }\n    html {\n      height: 100%;\n    }\n    body {\n      margin: 0;\n      padding: 20px;\n      font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif;\n      line-height: 1.6;\n      background: white;\n      color: #374151;\n    }\n\n    /* Tailwind CSS classes with tw- prefix */\n    .tw-bg-blue-500 { background-color: rgb(59 130 246) !important; }\n    .tw-bg-blue-600 { background-color: rgb(37 99 235) !important; }\n    .tw-bg-purple-600 { background-color: rgb(147 51 234) !important; }\n    .tw-bg-white { background-color: rgb(255 255 255) !important; }\n    .tw-bg-gray-50 { background-color: rgb(249 250 251) !important; }\n    .tw-bg-gray-100 { background-color: rgb(243 244 246) !important; }\n    .tw-bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)) !important; }\n    .tw-from-blue-600 { --tw-gradient-from: rgb(37 99 235); --tw-gradient-to: rgb(37 99 235 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important; }\n    .tw-to-purple-600 { --tw-gradient-to: rgb(147 51 234) !important; }\n\n    .tw-text-white { color: rgb(255 255 255) !important; }\n    .tw-text-blue-600 { color: rgb(37 99 235) !important; }\n    .tw-text-gray-600 { color: rgb(75 85 99) !important; }\n    .tw-text-gray-800 { color: rgb(31 41 55) !important; }\n    .tw-text-gray-900 { color: rgb(17 24 39) !important; }\n\n    .tw-p-4 { padding: 1rem !important; }\n    .tw-p-6 { padding: 1.5rem !important; }\n    .tw-p-8 { padding: 2rem !important; }\n    .tw-px-4 { padding-left: 1rem; padding-right: 1rem !important; }\n    .tw-px-6 { padding-left: 1.5rem; padding-right: 1.5rem !important; }\n    .tw-px-8 { padding-left: 2rem; padding-right: 2rem !important; }\n    .tw-py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem !important; }\n    .tw-py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem !important; }\n    .tw-py-4 { padding-top: 1rem; padding-bottom: 1rem !important; }\n    .tw-py-16 { padding-top: 4rem; padding-bottom: 4rem !important; }\n    .tw-py-20 { padding-top: 5rem; padding-bottom: 5rem !important; }\n\n    .tw-m-4 { margin: 1rem !important; }\n    .tw-mx-auto { margin-left: auto; margin-right: auto !important; }\n    .tw-mt-2 { margin-top: 0.5rem !important; }\n    .tw-mt-4 { margin-top: 1rem !important; }\n    .tw-mb-4 { margin-bottom: 1rem !important; }\n    .tw-mb-6 { margin-bottom: 1.5rem !important; }\n    .tw-mb-8 { margin-bottom: 2rem !important; }\n\n    .tw-rounded { border-radius: 0.25rem !important; }\n    .tw-rounded-lg { border-radius: 0.5rem !important; }\n    .tw-rounded-xl { border-radius: 0.75rem !important; }\n\n    .tw-text-sm { font-size: 0.875rem; line-height: 1.25rem !important; }\n    .tw-text-base { font-size: 1rem; line-height: 1.5rem !important; }\n    .tw-text-lg { font-size: 1.125rem; line-height: 1.75rem !important; }\n    .tw-text-xl { font-size: 1.25rem; line-height: 1.75rem !important; }\n    .tw-text-2xl { font-size: 1.5rem; line-height: 2rem !important; }\n    .tw-text-3xl { font-size: 1.875rem; line-height: 2.25rem !important; }\n    .tw-text-4xl { font-size: 2.25rem; line-height: 2.5rem !important; }\n    .tw-text-5xl { font-size: 3rem; line-height: 1 !important; }\n\n    .tw-font-medium { font-weight: 500 !important; }\n    .tw-font-semibold { font-weight: 600 !important; }\n    .tw-font-bold { font-weight: 700 !important; }\n\n    .tw-text-center { text-align: center !important; }\n    .tw-text-left { text-align: left !important; }\n\n    .tw-flex { display: flex !important; }\n    .tw-grid { display: grid !important; }\n    .tw-block { display: block !important; }\n    .tw-inline-block { display: inline-block !important; }\n    .tw-hidden { display: none !important; }\n\n    .tw-items-center { align-items: center !important; }\n    .tw-justify-center { justify-content: center !important; }\n    .tw-justify-between { justify-content: space-between !important; }\n\n    .tw-w-full { width: 100% !important; }\n    .tw-w-auto { width: auto !important; }\n    .tw-h-8 { height: 2rem !important; }\n    .tw-h-auto { height: auto !important; }\n\n    .tw-container { width: 100%; margin-left: auto; margin-right: auto !important; }\n    @media (min-width: 640px) { .tw-container { max-width: 640px !important; } }\n    @media (min-width: 768px) { .tw-container { max-width: 768px !important; } }\n    @media (min-width: 1024px) { .tw-container { max-width: 1024px !important; } }\n    @media (min-width: 1280px) { .tw-container { max-width: 1280px !important; } }\n\n    .tw-shadow-sm { box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05) !important; }\n    .tw-shadow { box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1) !important; }\n    .tw-shadow-md { box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important; }\n    .tw-shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important; }\n\n    .tw-border { border-width: 1px !important; }\n    .tw-border-gray-200 { border-color: rgb(229 231 235) !important; }\n    .tw-border-gray-300 { border-color: rgb(209 213 219) !important; }\n\n    .tw-transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms !important; }\n    .tw-transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms !important; }\n\n    .tw-hover\\:tw-bg-gray-100:hover { background-color: rgb(243 244 246) !important; }\n    .tw-hover\\:tw-text-blue-600:hover { color: rgb(37 99 235) !important; }\n\n    /* Grid classes */\n    .tw-grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }\n    .tw-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }\n    .tw-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }\n    .tw-gap-4 { gap: 1rem !important; }\n    .tw-gap-6 { gap: 1.5rem !important; }\n    .tw-gap-8 { gap: 2rem !important; }\n\n    @media (min-width: 768px) {\n      .tw-md\\:tw-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }\n      .tw-md\\:tw-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }\n      .tw-md\\:tw-block { display: block !important; }\n    }\n\n    /* Custom component styles */\n    \n  </style>\n</head>\n<body>\n  <div><div data-component=\"dynamic hero 6 pm\"><div\n  class=\"dw-relative dw-z-0 dw-overflow-hidden dw-bg-white dw-bg-[top_right] dw-bg-no-repeat dw-bg-[length:50%_auto] dw-px-[5rem] dw-pt-2 dw-pb-[9rem] xlx:dw-px-[3rem] xlx:dw-pb-[7.3rem] xlx:dw-bg-[length:46%_auto] xlx:dw-bg-none lgl:dw-px-[3rem] lgl:dw-pb-[8.3rem] lgl:dw-bg-[length:46%_auto] lgl:dw-bg-none lgm:dw-px-[2.5rem] lgm:dw-pb-[9.3rem] lgm:dw-bg-[length:46%_auto] lgm:dw-bg-none lgs:dw-px-[3rem] lgs:dw-pb-[9.3rem] lgs:dw-bg-[length:46%_auto] lgs:dw-bg-none lgx:dw-px-[3rem] lgx:dw-pb-[9.3rem] lgx:dw-bg-[length:46%_auto] lgx:dw-bg-none mdl:dw-pl-[2.5rem] mdl:dw-pr-0 mdl:dw-pb-[9rem] mdl:dw-bg-[length:46%_auto] mdl:dw-bg-none mdx:dw-px-[2.5rem] mdx:dw-pb-[9rem] mdx:dw-bg-[length:46%_auto] smm:dw-px-[1rem] smm:dw-pb-[6rem] smm:dw-bg-none smx:dw-px-0 smx:dw-pb-[6rem] smx:dw-bg-none xs:dw-px-0 xs:dw-pb-[6rem] xs:dw-bg-none\"\n>\n  <svg\n    class=\"dw-absolute dw-top-0 dw-left-1/2 dw-hidden mdx:dw-block\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0.03 0 645.94 800\"\n  >\n    <path\n      d=\"M409.731 554.556L418.669 537.316C421.506 531.775 424.147 526.333 426.631 520.781C431.502 509.725 435.484 498.498 437.992 487.406C443.094 465.014 440.439 444.475 426.827 429.266C413.214 413.753 390.738 403.342 367.505 396.086C318.045 381.852 267.467 366.011 218.531 347.431C206.247 342.767 194.022 337.922 181.845 332.759C169.852 327.719 156.848 321.827 144.952 313.85C133.191 305.9 120.856 295.306 114.489 279.064C111.334 271.077 110.506 262.042 111.567 253.834C112.637 245.605 115.317 238.116 118.642 231.442C125.558 217.769 134.044 207.906 141.691 197.325L165.495 166.277L213.603 104.836L295.511 1.79063L233.07 0C233.07 0 90.8422 130.795 27.3563 213.336C-27.8375 285.077 2.30937 348.466 102.153 391.105C186.423 427.109 265.3 454.98 265.3 454.98C278.889 462.017 288.556 474.352 291.77 488.744C294.984 503.148 291.43 518.138 282.017 529.838L64.7484 800H281.519L373.716 623.644L409.731 554.556Z\"\n      class=\"dw-fill-theme-main\"\n      fill-opacity=\"0.239216\"\n    ></path>\n    <path\n      d=\"M618.27 407.848C588.012 357.233 536.216 321.533 476.212 309.928L263.631 265.109C254.767 263.405 247.316 257.78 243.553 249.963C239.791 242.158 240.192 233.109 244.612 225.622L385.928 4.37188L317.097 2.4L230.075 117.512L183.587 179.95L160.733 211.328C153.378 221.884 145.269 232.32 140.8 242.084C136.041 252.178 134.847 261.798 138.281 269.784C141.592 277.967 149.689 285.577 159.455 291.691C169.342 297.948 180.216 302.659 192.27 307.384C204.13 312.072 216.159 316.517 228.275 320.778C276.955 337.884 325.987 351.959 376.712 365.245C402.72 372.989 430.25 383.362 452.18 406.498C462.87 418.077 470.845 433.284 473.877 449.028C476.994 464.784 475.972 480.297 473.244 494.639C470.37 508.97 465.878 522.437 460.605 535.27C457.939 541.65 455.064 547.97 452.119 554.058L443.547 571.628L409.198 641.628L331.256 800H550.984L634.097 573.991C654.298 519.028 648.527 458.463 618.27 407.848Z\"\n      class=\"dw-fill-theme-main\"\n      fill-opacity=\"0.611765\"\n    ></path>\n  </svg>\n  <div\n    class=\"dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdx:dw-grid-cols-2\">\n      <div class=\"sub-buses-1 dw-px-1\">\n        <h1\n          class=\"dw-text-[32px] mdx:dw-text-[48px] dw-font-bold dw-mb-4 dw-font-heading\"\n        >\n          ${hero_section_title}\n        </h1>\n        <p class=\"dw-text-base xl1:dw-text-lg dw-mb-6\">\n          ${hero_section_description}\n        </p>\n        <div\n          class=\"dw-flex lgm:dw-flex-row dw-flex-col dw-gap-5 dw-items-start\"\n        >\n          <a\n            href=\"tel:${contact_number}\"\n            class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center dw-gap-[10px] dw-pe-[10px] lgs:dw-text-[18px] dw-text-[16px] dw-ps-[3px] dw-py-[3px] dw-text-white dw-bg-body-text dw-rounded-full hover:dw-bg-theme-main dw-border-[2px] dw-border-body-text hover:dw-border-theme-main dw-transition\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"42\"\n              height=\"42\"\n              viewBox=\"0 0 36 36\"\n              fill=\"none\"\n            >\n              <rect width=\"36\" height=\"36\" rx=\"18\" class=\"dw-fill-theme-main\" />\n              <path\n                d=\"M27.7388 22.4138C27.5716 23.6841 26.9477 24.8502 25.9837 25.6942C25.0196 26.5382 23.7813 27.0023 22.5 27.0001C15.0563 27.0001 9.00001 20.9438 9.00001 13.5001C8.99771 12.2188 9.4619 10.9805 10.3059 10.0164C11.1499 9.05234 12.3159 8.42847 13.5863 8.2613C13.9075 8.22208 14.2328 8.2878 14.5136 8.44865C14.7944 8.60951 15.0157 8.85687 15.1444 9.1538L17.1244 13.5741V13.5854C17.2229 13.8127 17.2636 14.0608 17.2428 14.3077C17.222 14.5545 17.1404 14.7924 17.0053 15.0001C16.9884 15.0254 16.9706 15.0488 16.9519 15.0722L15 17.386C15.7022 18.8129 17.1947 20.2922 18.6403 20.9963L20.9222 19.0547C20.9446 19.0359 20.9681 19.0184 20.9925 19.0022C21.2 18.8639 21.4387 18.7794 21.687 18.7565C21.9353 18.7336 22.1854 18.7729 22.4147 18.871L22.4269 18.8766L26.8434 20.8557C27.1409 20.9839 27.3889 21.205 27.5503 21.4858C27.7116 21.7667 27.7778 22.0922 27.7388 22.4138Z\"\n                fill=\"white\"\n              ></path>\n            </svg>\n            ${contact_number_button_label}\n          </a>\n          <a\n            href=\"${quote_url}\"\n            class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center dw-gap-[6px] lgs:dw-text-[18px] dw-text-[16px] dw-pe-[10px] dw-ps-3 dw-py-[9.5px] dw-text-white dw-border-[2px] dw-border-theme-main dw-rounded-full dw-bg-theme-main\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              id=\"Layer_1\"\n              data-name=\"Layer 1\"\n              viewBox=\"0 0 35.05 60.44\"\n              width=\"30\"\n              height=\"30\"\n              fill=\"white\"\n            >\n              <path\n                d=\"M21.45 7.46c2.09-.11 4.18.1 6.23.44l.13.22c-.51 1.39-2.17.52-3.38.48-9.62-.33-17.23 4.17-21.45 12.76-.11.23-.47 1.43-.53 1.49-.23.26-1.08.01-1.14-.18-.08-.25 1.18-2.92 1.4-3.33C6.35 12.59 13.75 7.86 21.44 7.45Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M22.15 9.65c.52-.02 4.24.09 4.25.48-.74 1.21-.54.76-1.62.75-7.73-.07-14.38 2.32-18.55 9.17-.33.55-1.51 3.39-1.67 3.51-.13.1-1.1-.11-1.14-.26-.04-.13.91-2.17 1.05-2.46C7.73 14.38 14.91 9.88 22.15 9.66Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M.13 29.21c.06-.03.97 0 1.01.04.09.13-.04 2.64 0 3.07.56 6.25 4.19 12.15 9.25 15.75.45.32 2.88 1.55 2.94 1.71.02.05-.36.77-.39.92C5.95 47.64.79 40.17.09 32.59c-.04-.45-.2-3.25.04-3.38\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"M21.71 11.93c1.05-.06 2.11.02 3.16.09.26.1-.47 1.03-.53 1.05-.37.13-1.97-.06-2.54 0-5.7.56-10.71 3.36-13.73 8.29-.29.48-1.09 2.47-1.23 2.63-.21.23-.9.01-1.14-.18 2.33-6.64 8.99-11.48 16.01-11.89Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M2.42 29.21c.07-.03.96 0 1.01.04-.56 6.56 2.81 12.97 8.03 16.8.39.29 2.65 1.47 2.68 1.62 0 .05-.37.76-.39.92-5.39-2.46-9.76-8.03-11.01-13.82-.15-.7-.78-5.36-.31-5.57Z\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"m4.61 29.21 1.01.04c-.47 6.78 3.33 13.04 9.21 16.23l-.31 1.01C8.17 43.38 3.99 36.33 4.61 29.21M2.94 24.56s.54.12.66.13c-.19.44-.08 3.28-.22 3.38-.03.02-.62.02-.66-.04-.02-.46-.01-3.28.22-3.46ZM5.31 25.09l.66.13c-.14.37-.1 2.77-.22 2.85-.03.02-.62.02-.66-.04 0-.98-.06-1.98.22-2.94\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"M35.05 0 25.01 27.68l8.95.26-23.39 32.5c3.39-9.18 7.02-18.39 9.82-27.72l-8.73-.13z\"\n                style=\"fill: #fdfeff\"\n              />\n              <path\n                d=\"M29.52 8.95c.26 0 0 .31-.04.39-4.07 7.61-10.02 14.97-14.3 22.54l-1.71.13z\"\n                style=\"fill: #fefefe\"\n              />\n            </svg>\n            ${get_quote_button_text}\n          </a>\n        </div>\n      </div>\n      <div\n        class=\"dw-z-10 dw-pt-10 dw-pb-10 mdl:dw-pt-16 mdl:dw-ps-10 lgm:dw-ps-[60px]\"\n      >\n        <img\n          src=\"${img-hero-section-image}\"\n          class=\"dw-rounded-[16px] dw-w-full dw-h-auto\"\n          alt=\"img-hero-section-image\"\n        />\n      </div>\n    </div>\n  </div>\n</div>\n<div class=\"dw-relative dw--mt-[85px] dw-z-[1]\">\n  <div\n    class=\"dw-px-8 lgm:dw-px-0 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdl:dw-grid-cols-3 dw-gap-3 dw-gap-y-12\">\n      \n      <!-- Box 1 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${hero_section_card1_content}\n          </p>\n        </div>\n      </div>\n      <!-- Box 2 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${hero_section_card2_content}\n          </p>\n        </div>\n      </div>\n      <!-- Box 3 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${hero_section_card3_content}\n          </p>\n        </div>\n      </div>\n    </div>\n  </div>\n</div></div></div>\n<div><div data-component=\"dynamic why section 6pm\"><div\n  class=\"dw-bg-theme-cardBg dw-bg-[url('${img-background-pattern-bg-image}')] dw-bg-top dw-bg-no-repeat dw-bg-cover mdl:dw-pt-24 mdl:dw-pb-[15rem] dw-pt-[5rem] dw-pb-[7rem]\"\n>\n  <div\n    class=\"dw-px-8 lgm:dw-px-0 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdl:dw-grid-cols-2 dw-items-center\">\n      <!-- Left Content -->\n      <div class=\"sub-buses-1 dw-px-1\">\n        <h2\n          class=\"dw-font-heading dw-leading-tight dw-text-[26px] mdx:dw-text-[46px] lgx:text-[48px] dw-font-semibold dw-mb-4\"\n        >\n          ${why_section_heading}\n        </h2>\n        <p class=\"xl1:dw-text-lg dw-text-base dw-leading-relaxed\">\n          ${why_section_description}\n        </p>\n      </div>\n      <!-- Right Image -->\n      <div\n        class=\"why-rent-image sub-buses-2 dw-pt-[40px] mdl:dw-ps-[40px] lgm:dw-ps-[60px] lgs:dw-pt-0\"\n      >\n        <img\n          src=\"${img-why-section-image}\"\n          alt=\"img-why-section-image\"\n          class=\"dw-w-full dw-h-auto dw-rounded-[16px]\"\n        />\n      </div>\n    </div>\n  </div>\n  </div></div></div>\n\n  <script>\n    try {\n      \n    } catch(e) {\n      console.error('JavaScript error:', e);\n    }\n  </script>\n</body>\n</html>\n          </div>\n        </div>\n      \n\n        <div class=\"template-page\" data-page-id=\"mek6d46ni42euwjtdjk\" data-position=\"1\">\n          <div class=\"page-content\">\n            <!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>Bus Rental Company Clifton | Bus Rentals in Clifton</title>\n  <!-- Tailwind CSS - Generated locally to avoid CDN warnings -->\n  <style>\n    \n/* Tailwind CSS Reset and Base Styles */\n*,\n::before,\n::after {\n  box-sizing: border-box;\n  border-width: 0;\n  border-style: solid;\n  border-color: #e5e7eb;\n}\n\n::before,\n::after {\n  --tw-content: '';\n}\n\nhtml {\n  line-height: 1.5;\n  -webkit-text-size-adjust: 100%;\n  -moz-tab-size: 4;\n  tab-size: 4;\n  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  font-feature-settings: normal;\n  font-variation-settings: normal;\n}\n\nbody {\n  margin: 0;\n  line-height: inherit;\n}\n\n/* Tailwind CSS Utilities with tw- prefix */\n\n/* Display */\n.tw-block { display: block; }\n.tw-inline-block { display: inline-block; }\n.tw-inline { display: inline; }\n.tw-flex { display: flex; }\n.tw-inline-flex { display: inline-flex; }\n.tw-grid { display: grid; }\n.tw-hidden { display: none; }\n\n/* Position */\n.tw-static { position: static; }\n.tw-fixed { position: fixed; }\n.tw-absolute { position: absolute; }\n.tw-relative { position: relative; }\n.tw-sticky { position: sticky; }\n\n/* Top, Right, Bottom, Left */\n.tw-top-0 { top: 0px; }\n.tw-right-0 { right: 0px; }\n.tw-bottom-0 { bottom: 0px; }\n.tw-left-0 { left: 0px; }\n.tw-top-1\\/2 { top: 50%; }\n.tw-left-1\\/2 { left: 50%; }\n\n/* Z-Index */\n.tw-z-10 { z-index: 10; }\n.tw-z-20 { z-index: 20; }\n.tw-z-30 { z-index: 30; }\n.tw-z-40 { z-index: 40; }\n.tw-z-50 { z-index: 50; }\n\n/* Flex Direction */\n.tw-flex-row { flex-direction: row; }\n.tw-flex-row-reverse { flex-direction: row-reverse; }\n.tw-flex-col { flex-direction: column; }\n.tw-flex-col-reverse { flex-direction: column-reverse; }\n\n/* Flex Wrap */\n.tw-flex-wrap { flex-wrap: wrap; }\n.tw-flex-wrap-reverse { flex-wrap: wrap-reverse; }\n.tw-flex-nowrap { flex-wrap: nowrap; }\n\n/* Align Items */\n.tw-items-start { align-items: flex-start; }\n.tw-items-end { align-items: flex-end; }\n.tw-items-center { align-items: center; }\n.tw-items-baseline { align-items: baseline; }\n.tw-items-stretch { align-items: stretch; }\n\n/* Justify Content */\n.tw-justify-start { justify-content: flex-start; }\n.tw-justify-end { justify-content: flex-end; }\n.tw-justify-center { justify-content: center; }\n.tw-justify-between { justify-content: space-between; }\n.tw-justify-around { justify-content: space-around; }\n.tw-justify-evenly { justify-content: space-evenly; }\n\n/* Gap */\n.tw-gap-1 { gap: 0.25rem; }\n.tw-gap-2 { gap: 0.5rem; }\n.tw-gap-3 { gap: 0.75rem; }\n.tw-gap-4 { gap: 1rem; }\n.tw-gap-5 { gap: 1.25rem; }\n.tw-gap-6 { gap: 1.5rem; }\n.tw-gap-8 { gap: 2rem; }\n\n/* Padding */\n.tw-p-0 { padding: 0px; }\n.tw-p-1 { padding: 0.25rem; }\n.tw-p-2 { padding: 0.5rem; }\n.tw-p-3 { padding: 0.75rem; }\n.tw-p-4 { padding: 1rem; }\n.tw-p-5 { padding: 1.25rem; }\n.tw-p-6 { padding: 1.5rem; }\n.tw-p-8 { padding: 2rem; }\n.tw-p-10 { padding: 2.5rem; }\n.tw-p-12 { padding: 3rem; }\n.tw-p-16 { padding: 4rem; }\n\n.tw-px-1 { padding-left: 0.25rem; padding-right: 0.25rem; }\n.tw-px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }\n.tw-px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }\n.tw-px-4 { padding-left: 1rem; padding-right: 1rem; }\n.tw-px-5 { padding-left: 1.25rem; padding-right: 1.25rem; }\n.tw-px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }\n.tw-px-8 { padding-left: 2rem; padding-right: 2rem; }\n\n.tw-py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }\n.tw-py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }\n.tw-py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }\n.tw-py-4 { padding-top: 1rem; padding-bottom: 1rem; }\n.tw-py-5 { padding-top: 1.25rem; padding-bottom: 1.25rem; }\n.tw-py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }\n.tw-py-8 { padding-top: 2rem; padding-bottom: 2rem; }\n\n.tw-pt-1 { padding-top: 0.25rem; }\n.tw-pt-2 { padding-top: 0.5rem; }\n.tw-pt-3 { padding-top: 0.75rem; }\n.tw-pt-4 { padding-top: 1rem; }\n.tw-pt-6 { padding-top: 1.5rem; }\n.tw-pt-8 { padding-top: 2rem; }\n\n.tw-pr-1 { padding-right: 0.25rem; }\n.tw-pr-2 { padding-right: 0.5rem; }\n.tw-pr-3 { padding-right: 0.75rem; }\n.tw-pr-4 { padding-right: 1rem; }\n.tw-pr-6 { padding-right: 1.5rem; }\n.tw-pr-8 { padding-right: 2rem; }\n\n.tw-pb-1 { padding-bottom: 0.25rem; }\n.tw-pb-2 { padding-bottom: 0.5rem; }\n.tw-pb-3 { padding-bottom: 0.75rem; }\n.tw-pb-4 { padding-bottom: 1rem; }\n.tw-pb-6 { padding-bottom: 1.5rem; }\n.tw-pb-8 { padding-bottom: 2rem; }\n\n.tw-pl-1 { padding-left: 0.25rem; }\n.tw-pl-2 { padding-left: 0.5rem; }\n.tw-pl-3 { padding-left: 0.75rem; }\n.tw-pl-4 { padding-left: 1rem; }\n.tw-pl-6 { padding-left: 1.5rem; }\n.tw-pl-8 { padding-left: 2rem; }\n\n/* Margin */\n.tw-m-0 { margin: 0px; }\n.tw-m-1 { margin: 0.25rem; }\n.tw-m-2 { margin: 0.5rem; }\n.tw-m-3 { margin: 0.75rem; }\n.tw-m-4 { margin: 1rem; }\n.tw-m-5 { margin: 1.25rem; }\n.tw-m-6 { margin: 1.5rem; }\n.tw-m-8 { margin: 2rem; }\n.tw-m-auto { margin: auto; }\n\n.tw-mx-1 { margin-left: 0.25rem; margin-right: 0.25rem; }\n.tw-mx-2 { margin-left: 0.5rem; margin-right: 0.5rem; }\n.tw-mx-3 { margin-left: 0.75rem; margin-right: 0.75rem; }\n.tw-mx-4 { margin-left: 1rem; margin-right: 1rem; }\n.tw-mx-6 { margin-left: 1.5rem; margin-right: 1.5rem; }\n.tw-mx-8 { margin-left: 2rem; margin-right: 2rem; }\n.tw-mx-auto { margin-left: auto; margin-right: auto; }\n\n.tw-my-1 { margin-top: 0.25rem; margin-bottom: 0.25rem; }\n.tw-my-2 { margin-top: 0.5rem; margin-bottom: 0.5rem; }\n.tw-my-3 { margin-top: 0.75rem; margin-bottom: 0.75rem; }\n.tw-my-4 { margin-top: 1rem; margin-bottom: 1rem; }\n.tw-my-6 { margin-top: 1.5rem; margin-bottom: 1.5rem; }\n.tw-my-8 { margin-top: 2rem; margin-bottom: 2rem; }\n\n.tw-mt-1 { margin-top: 0.25rem; }\n.tw-mt-2 { margin-top: 0.5rem; }\n.tw-mt-3 { margin-top: 0.75rem; }\n.tw-mt-4 { margin-top: 1rem; }\n.tw-mt-6 { margin-top: 1.5rem; }\n.tw-mt-8 { margin-top: 2rem; }\n\n.tw-mr-1 { margin-right: 0.25rem; }\n.tw-mr-2 { margin-right: 0.5rem; }\n.tw-mr-3 { margin-right: 0.75rem; }\n.tw-mr-4 { margin-right: 1rem; }\n.tw-mr-6 { margin-right: 1.5rem; }\n.tw-mr-8 { margin-right: 2rem; }\n\n.tw-mb-1 { margin-bottom: 0.25rem; }\n.tw-mb-2 { margin-bottom: 0.5rem; }\n.tw-mb-3 { margin-bottom: 0.75rem; }\n.tw-mb-4 { margin-bottom: 1rem; }\n.tw-mb-6 { margin-bottom: 1.5rem; }\n.tw-mb-8 { margin-bottom: 2rem; }\n\n.tw-ml-1 { margin-left: 0.25rem; }\n.tw-ml-2 { margin-left: 0.5rem; }\n.tw-ml-3 { margin-left: 0.75rem; }\n.tw-ml-4 { margin-left: 1rem; }\n.tw-ml-6 { margin-left: 1.5rem; }\n.tw-ml-8 { margin-left: 2rem; }\n\n/* Width */\n.tw-w-auto { width: auto; }\n.tw-w-full { width: 100%; }\n.tw-w-1\\/2 { width: 50%; }\n.tw-w-1\\/3 { width: 33.333333%; }\n.tw-w-2\\/3 { width: 66.666667%; }\n.tw-w-1\\/4 { width: 25%; }\n.tw-w-3\\/4 { width: 75%; }\n.tw-w-1\\/5 { width: 20%; }\n.tw-w-2\\/5 { width: 40%; }\n.tw-w-3\\/5 { width: 60%; }\n.tw-w-4\\/5 { width: 80%; }\n\n.tw-w-4 { width: 1rem; }\n.tw-w-5 { width: 1.25rem; }\n.tw-w-6 { width: 1.5rem; }\n.tw-w-8 { width: 2rem; }\n.tw-w-10 { width: 2.5rem; }\n.tw-w-12 { width: 3rem; }\n.tw-w-16 { width: 4rem; }\n.tw-w-20 { width: 5rem; }\n.tw-w-24 { width: 6rem; }\n.tw-w-32 { width: 8rem; }\n.tw-w-40 { width: 10rem; }\n.tw-w-48 { width: 12rem; }\n.tw-w-56 { width: 14rem; }\n.tw-w-64 { width: 16rem; }\n\n/* Height */\n.tw-h-auto { height: auto; }\n.tw-h-full { height: 100%; }\n.tw-h-screen { height: 100vh; }\n\n.tw-h-4 { height: 1rem; }\n.tw-h-5 { height: 1.25rem; }\n.tw-h-6 { height: 1.5rem; }\n.tw-h-8 { height: 2rem; }\n.tw-h-10 { height: 2.5rem; }\n.tw-h-12 { height: 3rem; }\n.tw-h-16 { height: 4rem; }\n.tw-h-20 { height: 5rem; }\n.tw-h-24 { height: 6rem; }\n.tw-h-32 { height: 8rem; }\n.tw-h-40 { height: 10rem; }\n.tw-h-48 { height: 12rem; }\n.tw-h-56 { height: 14rem; }\n.tw-h-64 { height: 16rem; }\n\n/* Min/Max Width */\n.tw-min-w-0 { min-width: 0px; }\n.tw-min-w-full { min-width: 100%; }\n.tw-max-w-none { max-width: none; }\n.tw-max-w-xs { max-width: 20rem; }\n.tw-max-w-sm { max-width: 24rem; }\n.tw-max-w-md { max-width: 28rem; }\n.tw-max-w-lg { max-width: 32rem; }\n.tw-max-w-xl { max-width: 36rem; }\n.tw-max-w-2xl { max-width: 42rem; }\n.tw-max-w-3xl { max-width: 48rem; }\n.tw-max-w-4xl { max-width: 56rem; }\n.tw-max-w-5xl { max-width: 64rem; }\n.tw-max-w-6xl { max-width: 72rem; }\n.tw-max-w-7xl { max-width: 80rem; }\n.tw-max-w-full { max-width: 100%; }\n\n/* Min/Max Height */\n.tw-min-h-0 { min-height: 0px; }\n.tw-min-h-full { min-height: 100%; }\n.tw-min-h-screen { min-height: 100vh; }\n.tw-max-h-full { max-height: 100%; }\n.tw-max-h-screen { max-height: 100vh; }\n\n/* Font Family */\n.tw-font-sans { font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; }\n.tw-font-serif { font-family: ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif; }\n.tw-font-mono { font-family: ui-monospace, SFMono-Regular, \"SF Mono\", Consolas, \"Liberation Mono\", Menlo, monospace; }\n\n/* Font Size */\n.tw-text-xs { font-size: 0.75rem; line-height: 1rem; }\n.tw-text-sm { font-size: 0.875rem; line-height: 1.25rem; }\n.tw-text-base { font-size: 1rem; line-height: 1.5rem; }\n.tw-text-lg { font-size: 1.125rem; line-height: 1.75rem; }\n.tw-text-xl { font-size: 1.25rem; line-height: 1.75rem; }\n.tw-text-2xl { font-size: 1.5rem; line-height: 2rem; }\n.tw-text-3xl { font-size: 1.875rem; line-height: 2.25rem; }\n.tw-text-4xl { font-size: 2.25rem; line-height: 2.5rem; }\n.tw-text-5xl { font-size: 3rem; line-height: 1; }\n.tw-text-6xl { font-size: 3.75rem; line-height: 1; }\n\n/* Font Weight */\n.tw-font-thin { font-weight: 100; }\n.tw-font-extralight { font-weight: 200; }\n.tw-font-light { font-weight: 300; }\n.tw-font-normal { font-weight: 400; }\n.tw-font-medium { font-weight: 500; }\n.tw-font-semibold { font-weight: 600; }\n.tw-font-bold { font-weight: 700; }\n.tw-font-extrabold { font-weight: 800; }\n.tw-font-black { font-weight: 900; }\n\n/* Text Align */\n.tw-text-left { text-align: left; }\n.tw-text-center { text-align: center; }\n.tw-text-right { text-align: right; }\n.tw-text-justify { text-align: justify; }\n\n/* Text Color */\n.tw-text-transparent { color: transparent; }\n.tw-text-current { color: currentColor; }\n.tw-text-black { color: rgb(0 0 0); }\n.tw-text-white { color: rgb(255 255 255); }\n.tw-text-gray-50 { color: rgb(249 250 251); }\n.tw-text-gray-100 { color: rgb(243 244 246); }\n.tw-text-gray-200 { color: rgb(229 231 235); }\n.tw-text-gray-300 { color: rgb(209 213 219); }\n.tw-text-gray-400 { color: rgb(156 163 175); }\n.tw-text-gray-500 { color: rgb(107 114 128); }\n.tw-text-gray-600 { color: rgb(75 85 99); }\n.tw-text-font-color { color: rgb(55 65 81); }\n.tw-text-gray-800 { color: rgb(31 41 55); }\n.tw-text-gray-900 { color: rgb(17 24 39); }\n.tw-text-red-500 { color: rgb(239 68 68); }\n.tw-text-red-600 { color: rgb(220 38 38); }\n.tw-text-blue-500 { color: rgb(59 130 246); }\n.tw-text-blue-600 { color: rgb(37 99 235); }\n.tw-text-green-500 { color: rgb(34 197 94); }\n.tw-text-green-600 { color: rgb(22 163 74); }\n.tw-text-yellow-500 { color: rgb(234 179 8); }\n.tw-text-purple-500 { color: rgb(168 85 247); }\n.tw-text-purple-600 { color: rgb(147 51 234); }\n\n/* Background Color */\n.tw-bg-transparent { background-color: transparent; }\n.tw-bg-current { background-color: currentColor; }\n.tw-bg-black { background-color: rgb(0 0 0); }\n.tw-bg-white { background-color: rgb(255 255 255); }\n.tw-bg-gray-50 { background-color: rgb(249 250 251); }\n.tw-bg-gray-100 { background-color: rgb(243 244 246); }\n.tw-bg-gray-200 { background-color: rgb(229 231 235); }\n.tw-bg-gray-300 { background-color: rgb(209 213 219); }\n.tw-bg-gray-400 { background-color: rgb(156 163 175); }\n.tw-bg-gray-500 { background-color: rgb(107 114 128); }\n.tw-bg-gray-600 { background-color: rgb(75 85 99); }\n.tw-bg-gray-700 { background-color: rgb(55 65 81); }\n.tw-bg-gray-800 { background-color: rgb(31 41 55); }\n.tw-bg-gray-900 { background-color: rgb(17 24 39); }\n.tw-bg-red-500 { background-color: rgb(239 68 68); }\n.tw-bg-red-600 { background-color: rgb(220 38 38); }\n.tw-bg-blue-500 { background-color: rgb(59 130 246); }\n.tw-bg-blue-600 { background-color: rgb(37 99 235); }\n.tw-bg-green-500 { background-color: rgb(34 197 94); }\n.tw-bg-green-600 { background-color: rgb(22 163 74); }\n.tw-bg-yellow-500 { background-color: rgb(234 179 8); }\n.tw-bg-purple-500 { background-color: rgb(168 85 247); }\n.tw-bg-purple-600 { background-color: rgb(147 51 234); }\n\n/* Background Gradient */\n.tw-bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }\n.tw-bg-gradient-to-l { background-image: linear-gradient(to left, var(--tw-gradient-stops)); }\n.tw-bg-gradient-to-t { background-image: linear-gradient(to top, var(--tw-gradient-stops)); }\n.tw-bg-gradient-to-b { background-image: linear-gradient(to bottom, var(--tw-gradient-stops)); }\n.tw-bg-gradient-to-tr { background-image: linear-gradient(to top right, var(--tw-gradient-stops)); }\n.tw-bg-gradient-to-tl { background-image: linear-gradient(to top left, var(--tw-gradient-stops)); }\n.tw-bg-gradient-to-br { background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)); }\n.tw-bg-gradient-to-bl { background-image: linear-gradient(to bottom left, var(--tw-gradient-stops)); }\n\n.tw-from-blue-500 { --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position); --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }\n.tw-from-blue-600 { --tw-gradient-from: #2563eb var(--tw-gradient-from-position); --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }\n.tw-to-purple-500 { --tw-gradient-to: #a855f7 var(--tw-gradient-to-position); }\n.tw-to-purple-600 { --tw-gradient-to: #9333ea var(--tw-gradient-to-position); }\n\n/* Border */\n.tw-border-0 { border-width: 0px; }\n.tw-border { border-width: 1px; }\n.tw-border-2 { border-width: 2px; }\n.tw-border-4 { border-width: 4px; }\n.tw-border-8 { border-width: 8px; }\n\n.tw-border-t { border-top-width: 1px; }\n.tw-border-r { border-right-width: 1px; }\n.tw-border-b { border-bottom-width: 1px; }\n.tw-border-l { border-left-width: 1px; }\n\n/* Border Color */\n.tw-border-transparent { border-color: transparent; }\n.tw-border-current { border-color: currentColor; }\n.tw-border-black { border-color: rgb(0 0 0); }\n.tw-border-white { border-color: rgb(255 255 255); }\n.tw-border-gray-200 { border-color: rgb(229 231 235); }\n.tw-border-gray-300 { border-color: rgb(209 213 219); }\n.tw-border-gray-400 { border-color: rgb(156 163 175); }\n.tw-border-gray-500 { border-color: rgb(107 114 128); }\n.tw-border-blue-500 { border-color: rgb(59 130 246); }\n.tw-border-red-500 { border-color: rgb(239 68 68); }\n\n/* Border Radius */\n.tw-rounded-none { border-radius: 0px; }\n.tw-rounded-sm { border-radius: 0.125rem; }\n.tw-rounded { border-radius: 0.25rem; }\n.tw-rounded-md { border-radius: 0.375rem; }\n.tw-rounded-lg { border-radius: 0.5rem; }\n.tw-rounded-xl { border-radius: 0.75rem; }\n.tw-rounded-2xl { border-radius: 1rem; }\n.tw-rounded-3xl { border-radius: 1.5rem; }\n.tw-rounded-full { border-radius: 9999px; }\n\n/* Box Shadow */\n.tw-shadow-sm { box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05); }\n.tw-shadow { box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1); }\n.tw-shadow-md { box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); }\n.tw-shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); }\n.tw-shadow-xl { box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1); }\n.tw-shadow-2xl { box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25); }\n.tw-shadow-none { box-shadow: 0 0 #0000; }\n\n/* Opacity */\n.tw-opacity-0 { opacity: 0; }\n.tw-opacity-5 { opacity: 0.05; }\n.tw-opacity-10 { opacity: 0.1; }\n.tw-opacity-20 { opacity: 0.2; }\n.tw-opacity-25 { opacity: 0.25; }\n.tw-opacity-30 { opacity: 0.3; }\n.tw-opacity-40 { opacity: 0.4; }\n.tw-opacity-50 { opacity: 0.5; }\n.tw-opacity-60 { opacity: 0.6; }\n.tw-opacity-70 { opacity: 0.7; }\n.tw-opacity-75 { opacity: 0.75; }\n.tw-opacity-80 { opacity: 0.8; }\n.tw-opacity-90 { opacity: 0.9; }\n.tw-opacity-95 { opacity: 0.95; }\n.tw-opacity-100 { opacity: 1; }\n\n/* Cursor */\n.tw-cursor-auto { cursor: auto; }\n.tw-cursor-default { cursor: default; }\n.tw-cursor-pointer { cursor: pointer; }\n.tw-cursor-wait { cursor: wait; }\n.tw-cursor-text { cursor: text; }\n.tw-cursor-move { cursor: move; }\n.tw-cursor-help { cursor: help; }\n.tw-cursor-not-allowed { cursor: not-allowed; }\n\n/* Transition */\n.tw-transition-none { transition-property: none; }\n.tw-transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }\n.tw-transition { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }\n.tw-transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }\n.tw-transition-opacity { transition-property: opacity; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }\n.tw-transition-shadow { transition-property: box-shadow; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }\n.tw-transition-transform { transition-property: transform; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }\n\n/* Duration */\n.tw-duration-75 { transition-duration: 75ms; }\n.tw-duration-100 { transition-duration: 100ms; }\n.tw-duration-150 { transition-duration: 150ms; }\n.tw-duration-200 { transition-duration: 200ms; }\n.tw-duration-300 { transition-duration: 300ms; }\n.tw-duration-500 { transition-duration: 500ms; }\n.tw-duration-700 { transition-duration: 700ms; }\n.tw-duration-1000 { transition-duration: 1000ms; }\n\n/* Transform */\n.tw-transform { transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }\n.tw--translate-y-1\\/2 { --tw-translate-y: -50%; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }\n\n/* Hover States */\n.tw-hover\\:tw-bg-gray-50:hover { background-color: rgb(249 250 251); }\n.tw-hover\\:tw-bg-gray-100:hover { background-color: rgb(243 244 246); }\n.tw-hover\\:tw-bg-blue-600:hover { background-color: rgb(37 99 235); }\n.tw-hover\\:tw-text-gray-900:hover { color: rgb(17 24 39); }\n.tw-hover\\:tw-text-blue-600:hover { color: rgb(37 99 235); }\n\n/* Focus States */\n.tw-focus\\:tw-ring-2:focus { --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color); box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000); }\n.tw-focus\\:tw-ring-blue-500:focus { --tw-ring-opacity: 1; --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity)); }\n.tw-focus\\:tw-border-transparent:focus { border-color: transparent; }\n\n/* Container */\n.tw-container { width: 100%; }\n@media (min-width: 640px) { .tw-container { max-width: 640px; } }\n@media (min-width: 768px) { .tw-container { max-width: 768px; } }\n@media (min-width: 1024px) { .tw-container { max-width: 1024px; } }\n@media (min-width: 1280px) { .tw-container { max-width: 1280px; } }\n@media (min-width: 1536px) { .tw-container { max-width: 1536px; } }\n\n/* Responsive Utilities */\n@media (min-width: 640px) {\n  .tw-sm\\:tw-w-64 { width: 16rem; }\n  .tw-sm\\:tw-text-lg { font-size: 1.125rem; line-height: 1.75rem; }\n}\n\n@media (min-width: 768px) {\n  .tw-md\\:tw-flex { display: flex; }\n  .tw-md\\:tw-hidden { display: none; }\n  .tw-md\\:tw-w-1\\/2 { width: 50%; }\n}\n\n@media (min-width: 1024px) {\n  .tw-lg\\:tw-flex { display: flex; }\n  .tw-lg\\:tw-hidden { display: none; }\n  .tw-lg\\:tw-w-1\\/3 { width: 33.333333%; }\n}\n\n  </style>\n  <style>\n    /* Base styles */\n    * { box-sizing: border-box; }\n    html {\n      height: 100%;\n    }\n    body {\n      margin: 0;\n      padding: 20px;\n      font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif;\n      line-height: 1.6;\n      background: white;\n      color: #374151;\n    }\n\n    /* Tailwind CSS classes with tw- prefix */\n    .tw-bg-blue-500 { background-color: rgb(59 130 246) !important; }\n    .tw-bg-blue-600 { background-color: rgb(37 99 235) !important; }\n    .tw-bg-purple-600 { background-color: rgb(147 51 234) !important; }\n    .tw-bg-white { background-color: rgb(255 255 255) !important; }\n    .tw-bg-gray-50 { background-color: rgb(249 250 251) !important; }\n    .tw-bg-gray-100 { background-color: rgb(243 244 246) !important; }\n    .tw-bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)) !important; }\n    .tw-from-blue-600 { --tw-gradient-from: rgb(37 99 235); --tw-gradient-to: rgb(37 99 235 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important; }\n    .tw-to-purple-600 { --tw-gradient-to: rgb(147 51 234) !important; }\n\n    .tw-text-white { color: rgb(255 255 255) !important; }\n    .tw-text-blue-600 { color: rgb(37 99 235) !important; }\n    .tw-text-gray-600 { color: rgb(75 85 99) !important; }\n    .tw-text-gray-800 { color: rgb(31 41 55) !important; }\n    .tw-text-gray-900 { color: rgb(17 24 39) !important; }\n\n    .tw-p-4 { padding: 1rem !important; }\n    .tw-p-6 { padding: 1.5rem !important; }\n    .tw-p-8 { padding: 2rem !important; }\n    .tw-px-4 { padding-left: 1rem; padding-right: 1rem !important; }\n    .tw-px-6 { padding-left: 1.5rem; padding-right: 1.5rem !important; }\n    .tw-px-8 { padding-left: 2rem; padding-right: 2rem !important; }\n    .tw-py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem !important; }\n    .tw-py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem !important; }\n    .tw-py-4 { padding-top: 1rem; padding-bottom: 1rem !important; }\n    .tw-py-16 { padding-top: 4rem; padding-bottom: 4rem !important; }\n    .tw-py-20 { padding-top: 5rem; padding-bottom: 5rem !important; }\n\n    .tw-m-4 { margin: 1rem !important; }\n    .tw-mx-auto { margin-left: auto; margin-right: auto !important; }\n    .tw-mt-2 { margin-top: 0.5rem !important; }\n    .tw-mt-4 { margin-top: 1rem !important; }\n    .tw-mb-4 { margin-bottom: 1rem !important; }\n    .tw-mb-6 { margin-bottom: 1.5rem !important; }\n    .tw-mb-8 { margin-bottom: 2rem !important; }\n\n    .tw-rounded { border-radius: 0.25rem !important; }\n    .tw-rounded-lg { border-radius: 0.5rem !important; }\n    .tw-rounded-xl { border-radius: 0.75rem !important; }\n\n    .tw-text-sm { font-size: 0.875rem; line-height: 1.25rem !important; }\n    .tw-text-base { font-size: 1rem; line-height: 1.5rem !important; }\n    .tw-text-lg { font-size: 1.125rem; line-height: 1.75rem !important; }\n    .tw-text-xl { font-size: 1.25rem; line-height: 1.75rem !important; }\n    .tw-text-2xl { font-size: 1.5rem; line-height: 2rem !important; }\n    .tw-text-3xl { font-size: 1.875rem; line-height: 2.25rem !important; }\n    .tw-text-4xl { font-size: 2.25rem; line-height: 2.5rem !important; }\n    .tw-text-5xl { font-size: 3rem; line-height: 1 !important; }\n\n    .tw-font-medium { font-weight: 500 !important; }\n    .tw-font-semibold { font-weight: 600 !important; }\n    .tw-font-bold { font-weight: 700 !important; }\n\n    .tw-text-center { text-align: center !important; }\n    .tw-text-left { text-align: left !important; }\n\n    .tw-flex { display: flex !important; }\n    .tw-grid { display: grid !important; }\n    .tw-block { display: block !important; }\n    .tw-inline-block { display: inline-block !important; }\n    .tw-hidden { display: none !important; }\n\n    .tw-items-center { align-items: center !important; }\n    .tw-justify-center { justify-content: center !important; }\n    .tw-justify-between { justify-content: space-between !important; }\n\n    .tw-w-full { width: 100% !important; }\n    .tw-w-auto { width: auto !important; }\n    .tw-h-8 { height: 2rem !important; }\n    .tw-h-auto { height: auto !important; }\n\n    .tw-container { width: 100%; margin-left: auto; margin-right: auto !important; }\n    @media (min-width: 640px) { .tw-container { max-width: 640px !important; } }\n    @media (min-width: 768px) { .tw-container { max-width: 768px !important; } }\n    @media (min-width: 1024px) { .tw-container { max-width: 1024px !important; } }\n    @media (min-width: 1280px) { .tw-container { max-width: 1280px !important; } }\n\n    .tw-shadow-sm { box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05) !important; }\n    .tw-shadow { box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1) !important; }\n    .tw-shadow-md { box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important; }\n    .tw-shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important; }\n\n    .tw-border { border-width: 1px !important; }\n    .tw-border-gray-200 { border-color: rgb(229 231 235) !important; }\n    .tw-border-gray-300 { border-color: rgb(209 213 219) !important; }\n\n    .tw-transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms !important; }\n    .tw-transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms !important; }\n\n    .tw-hover\\:tw-bg-gray-100:hover { background-color: rgb(243 244 246) !important; }\n    .tw-hover\\:tw-text-blue-600:hover { color: rgb(37 99 235) !important; }\n\n    /* Grid classes */\n    .tw-grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }\n    .tw-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }\n    .tw-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }\n    .tw-gap-4 { gap: 1rem !important; }\n    .tw-gap-6 { gap: 1.5rem !important; }\n    .tw-gap-8 { gap: 2rem !important; }\n\n    @media (min-width: 768px) {\n      .tw-md\\:tw-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }\n      .tw-md\\:tw-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }\n      .tw-md\\:tw-block { display: block !important; }\n    }\n\n    /* Custom component styles */\n    .main-dev {\nmargin: \"auto\";\n}\n\n  </style>\n</head>\n<body>\n  <div><!DOCTYPE html>\n<html lang=\"en\">\n  <head> \n    <meta charset=\"UTF-8\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n    <title>Document</title>\n    <style>\n      @font-face {\n        font-family: Inter;\n        font-style: normal;\n        font-weight: 400;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n      @font-face {\n        font-family: Inter;\n        font-style: normal;\n        font-weight: 500;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n      @font-face {\n        font-family: Inter;\n        font-style: normal;\n        font-weight: 700;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n      @font-face {\n        font-family: Phudu;\n        font-style: normal;\n        font-weight: 600;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n      @font-face {\n        font-family: Phudu;\n        font-style: normal;\n        font-weight: 700;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n    </style>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script>\n      tailwind.config = {\n        content: [\"./*.html\"],\n        // prefix: \"dw-\",\n        theme: {\n          extend: {\n            colors: {\n              body: {\n                bg: \"#ffffff\", // --body-bg-color\n                text: \"#333333\", // --body-text-color\n              },\n              theme: {\n                main: \"#d5232b\", // --theme-main-color\n                mainColorShade: \"#C41421\",\n                hoverBox: \"rgba(82, 164, 206, 0.43)\", // --theme-box-hover-color\n                cardBorder: \"#e0e0e0\",\n                cardBg: \"#fafafa\",\n                divider: \"#eee\",\n                lowOpacityTextColor: \"#111827\",\n              },\n            },\n            fontFamily: {\n              heading: [\"Phudu\", \"sans-serif\"], // --theme-heading-font\n              text: [\"Inter\", \"sans-serif\"], // --theme-text-font\n            },\n            screens: {\n              xs: \"200px\",\n              smx: \"376px\",\n              smm: \"567px\",\n              mdx: \"768px\",\n              mdl: \"993px\",\n              lgx: \"1171px\",\n              lgs: \"1221px\",\n              lgm: \"1281px\",\n              lgl: \"1361px\",\n              xlx: \"1400px\",\n              xl1: \"1441px\",\n              xl2: \"1600px\",\n              xxl: \"1900px\",\n            },\n          },\n        },\n        plugins: [],\n      };\n    </script>\n  </head>\n  <body class=\"bg-body-bg text-body-text font-text\">\n    <div\n      class=\"my-16 mx-auto w-full px-6 xl1:max-w-[1440px] lgl:max-w-[1300px] lgl:px-6 lgs:max-w-[1200px] lgx:max-w-[1100px] mdl:max-w-[1080px] mdl:px-6 mdx:max-w-[1000px] smm:max-w-[940px] smm:px-6 smx:max-w-[700px] smx:px-6\"\n    >\n      <div class=\"flex justify-center\">\n        <h2\n          class=\"xl1:mx-[395px] font-semibold lgm:text-[48px] lgx:text-[50px] mdx:text-[46px] text-[26px] xl1:leading-[60px] lgx:leading-[46px] uppercase text-center pb-[25px] font-heading\"\n        >\n          ${Title}\n        </h2>\n      </div>\n      <div\n        class=\"grid grid-cols-1 mdx:grid-cols-2 lgs:grid-cols-3 mdx:gap-8 gap-4 xl1:gap-[50px] mb-4\"\n      >\n        <div\n          class=\"border border-theme-cardBorder rounded-[32px] bg-theme-cardBg xl1:p-[3rem_2rem_1.5rem] p-[2.5rem_1rem_1rem] min-h-full relative\"\n        >\n          <span\n            class=\"absolute z-10 bg-body-bg text-theme-lowOpacityTextColor font-normal border shadow-sm text-[14px] px-[15px] py-[8px] ml-[10px] -translate-y-[20px] rounded-full\"\n          >\n            ${Amount}\n          </span>\n          <img\n            src=\"https://busrentalcompanyclifton.com/wp-content/uploads/50-passenger-charter-bus-clifton.jpeg\"\n            class=\"rounded-[16px] w-full h-auto\"\n            alt=\"50 passenger charter bus clifton\"\n          />\n          <img\n            src=\"https://busrentalcompanyclifton.com/wp-content/uploads/50-passenger-charter-bus-interior-clifton.jpeg\"\n            class=\"rounded-[16px] w-full h-auto mt-4\"\n            alt=\"50 passenger charter bus interior clifton\"\n          />\n          <h3\n            class=\"font-semibold lgm:text-[24px] lgx:text-[22px] text-[20px] leading-[32px] uppercase tracking-[-0.176px] text-center pt-[32px] pb-[25px] font-heading mb-2\"\n          >\n            <a\n              href=\"https://busrentalcompanyclifton.com/50-passenger-charter-bus-rental/\"\n            >\n              ${link_span}\n            </a>\n          </h3>\n          <div class=\"flex justify-center gap-4 pb-[5px]\">\n            <a\n              href=\"https://busrentalcompanyclifton.com/50-passenger-charter-bus-rental/\"\n              class=\"bg-body-text text-white border-2 border-body-text rounded-full px-[16px] py-[13px] text-[18px] leading-[24px] font-medium tracking-[-0.24px] hover:border-theme-main hover:bg-theme-main\"\n            >\n              ${view_link}\n            </a>\n            <a\n              href=\"/get-quote/\"\n              class=\"bg-theme-main text-white border-2 border-theme-main rounded-full px-[16px] py-[13px] inline-flex items-center justify-center text-[18px] leading-[24px] font-medium tracking-[-0.24px]\"\n            >\n              ${Get_quote}\n            </a>\n          </div>\n        </div>\n        \n      </div>\n    </div>\n    <!-- <div\n      class=\"mx-auto w-full xl1:max-w-[1440px] lgl:max-w-[1300px] lgs:max-w-[1200px] lgx:max-w-[1100px] mdl:max-w-[1080px] mdx:max-w-[1000px] smm:max-w-[940px] smx:max-w-[700px]\"\n    ></div> -->\n  </body>\n</html></div>\n<div><div data-component=\"section Template \"><section class=\"px-6 py-12 bg-gray-50\">\n  <div class=\"max-w-6xl mx-auto\">\n    <header class=\"text-center mb-10\">\n      <h2 class=\"text-3xl font-extrabold text-gray-800\">${Title}</h2>\n      <p class=\"mt-2 text-gray-600 max-w-2xl mx-auto\">\n        ${description}\n      </p>\n    </header>\n\n    <div class=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8\">\n      {{ITEMS}}\n    </div>\n\n    <footer class=\"mt-12 text-center\">\n      <a href={${ViewButtonLink}} class=\"text-indigo-600 hover:underline font-medium\">${ViewButton}</a>\n    </footer>\n  </div>\n</section></div></div>\n\n  <script>\n    try {\n      // buses.js\n\nconst busOptions = [\n  {\n    title: \"50 Passenger Charter Bus\",\n    price: \"$180 – $500+ per hour\",\n    image1: \"https://busrentalcompanyclifton.com/wp-content/uploads/50-passenger-charter-bus-clifton.jpeg\",\n    image2: \"https://busrentalcompanyclifton.com/wp-content/uploads/50-passenger-charter-bus-interior-clifton.jpeg\",\n    link: \"https://busrentalcompanyclifton.com/50-passenger-charter-bus-rental/\"\n  },\n  {\n    title: \"40 Passenger Party Bus\",\n    price: \"$150 – $450+ per hour\",\n    image1: \"https://busrentalcompanyclifton.com/wp-content/uploads/25-passenger-minibus-clifton.jpeg\",\n    image2: \"https://busrentalcompanyclifton.com/wp-content/uploads/15-passenger-minibus-interior-clifton.jpeg\",\n    link: \"https://busrentalcompanyclifton.com/40-passenger-party-bus-rental/\"\n  },\n  {\n    title: \"Sprinter Van Rental\",\n    price: \"$80 – $150+ per hour\",\n    image1: \"https://busrentalcompanyclifton.com/wp-content/uploads/sprinter-van-with-driver-clifton.jpeg\",\n    image2: \"https://busrentalcompanyclifton.com/wp-content/uploads/sprinter-van-with-driver-interior-clifton.jpeg\",\n    link: \"https://busrentalcompanyclifton.com/sprinter-van-rental/\"\n  }\n];\n\nwindow.addEventListener(\"DOMContentLoaded\", () => {\n  const container = document.querySelector(\".grid\");\n\n  busOptions.forEach(option => {\n    const card = `\n      <div class=\"border border-theme-cardBorder rounded-[32px] bg-theme-cardBg xl1:p-[3rem_2rem_1.5rem] p-[2.5rem_1rem_1rem] min-h-full relative\">\n        <span class=\"absolute z-10 bg-body-bg text-theme-lowOpacityTextColor font-normal border shadow-sm text-[14px] px-[15px] py-[8px] ml-[10px] -translate-y-[20px] rounded-full\">\n          ${option.price}\n        </span>\n        <img src=\"${option.image1}\" class=\"rounded-[16px] w-full h-auto\" alt=\"${option.title.toLowerCase()}\">\n        <img src=\"${option.image2}\" class=\"rounded-[16px] w-full h-auto mt-4\" alt=\"${option.title.toLowerCase()} interior\">\n        <h3 class=\"font-semibold lgm:text-[24px] lgx:text-[22px] text-[20px] leading-[32px] uppercase tracking-[-0.176px] text-center pt-[32px] pb-[25px] font-heading mb-2\">\n          <a href=\"${option.link}\">${option.title}</a>\n        </h3>\n        <div class=\"flex justify-center gap-4 pb-[5px]\">\n          <a href=\"${option.link}\" class=\"bg-body-text text-white border-2 border-body-text rounded-full px-[16px] py-[13px] text-[18px] leading-[24px] font-medium tracking-[-0.24px] hover:border-theme-main hover:bg-theme-main\">\n            View This Bus\n          </a>\n          <a href=\"/get-quote/\" class=\"bg-theme-main text-white border-2 border-theme-main rounded-full px-[16px] py-[13px] inline-flex items-center justify-center text-[18px] leading-[24px] font-medium tracking-[-0.24px]\">\n            Get a Quote\n          </a>\n        </div>\n      </div>\n    `;\n\n    container.insertAdjacentHTML(\"beforeend\", card);\n  });\n});\n    } catch(e) {\n      console.error('JavaScript error:', e);\n    }\n  </script>\n</body>\n</html>\n          </div>\n        </div>\n      \n        </div>\n      </div>\n    ", "pages": [{"name": "dynamic-city-6pm", "slug": "city", "meta_title": "", "meta_description": "", "custom_css": "", "custom_js": "", "full_page_content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>dynamic-city-6pm</title>\n  <!-- Tailwind CSS - Generated locally to avoid CDN warnings -->\n<style>\n      @font-face {\n        font-family: Inter;\n        font-style: normal;\n        font-weight: 400;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n      @font-face {\n        font-family: Inter;\n        font-style: normal;\n        font-weight: 500;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n      @font-face {\n        font-family: Inter;\n        font-style: normal;\n        font-weight: 700;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n      @font-face {\n        font-family: Phudu;\n        font-style: normal;\n        font-weight: 600;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n      @font-face {\n        font-family: Phudu;\n        font-style: normal;\n        font-weight: 700;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n    </style>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script src=\"//unpkg.com/alpinejs\" defer></script>\n    <script>\n      tailwind.config = {\n        content: [\"./*.html\"],\n        prefix: \"dw-\",\n        theme: {\n          extend: {\n            colors: {\n              body: {\n                bg: \"#ffffff\", // --body-bg-color\n                text: \"#333333\", // --body-text-color\n              },\n              theme: {\n                main: \"#d5232b\", // --theme-main-color\n                mainColorShade: \"#C41421\",\n                hoverBox: \"rgba(82, 164, 206, 0.43)\", // --theme-box-hover-color\n                cardBorder: \"#e0e0e0\",\n                cardBg: \"#fafafa\",\n                divider: \"#eee\",\n                lowOpacityTextColor: \"#111827\",\n              },\n            },\n            fontFamily: {\n              heading: [\"Phudu\", \"sans-serif\"], // --theme-heading-font\n              text: [\"Inter\", \"sans-serif\"], // --theme-text-font\n            },\n            screens: {\n              xs: \"200px\",\n              smx: \"376px\",\n              smm: \"567px\",\n              mdx: \"768px\",\n              mdl: \"993px\",\n              lgx: \"1171px\",\n              lgs: \"1221px\",\n              lgm: \"1281px\",\n              lgl: \"1361px\",\n              xlx: \"1400px\",\n              xl1: \"1441px\",\n              xl2: \"1600px\",\n              xxl: \"1900px\",\n            },\n          },\n        },\n        plugins: [],\n      };\n    </script>\n  \n\n  <style>\n    /* Base styles */\n    * { box-sizing: border-box; }\n    html {\n      height: 100%;\n    }\n    body {\n      margin: 0;\n      padding: 20px;\n      font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif;\n      line-height: 1.6;\n      background: white;\n      color: #374151;\n    }\n\n    /* Tailwind CSS classes with tw- prefix */\n    .tw-bg-blue-500 { background-color: rgb(59 130 246) !important; }\n    .tw-bg-blue-600 { background-color: rgb(37 99 235) !important; }\n    .tw-bg-purple-600 { background-color: rgb(147 51 234) !important; }\n    .tw-bg-white { background-color: rgb(255 255 255) !important; }\n    .tw-bg-gray-50 { background-color: rgb(249 250 251) !important; }\n    .tw-bg-gray-100 { background-color: rgb(243 244 246) !important; }\n    .tw-bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)) !important; }\n    .tw-from-blue-600 { --tw-gradient-from: rgb(37 99 235); --tw-gradient-to: rgb(37 99 235 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important; }\n    .tw-to-purple-600 { --tw-gradient-to: rgb(147 51 234) !important; }\n\n    .tw-text-white { color: rgb(255 255 255) !important; }\n    .tw-text-blue-600 { color: rgb(37 99 235) !important; }\n    .tw-text-gray-600 { color: rgb(75 85 99) !important; }\n    .tw-text-gray-800 { color: rgb(31 41 55) !important; }\n    .tw-text-gray-900 { color: rgb(17 24 39) !important; }\n\n    .tw-p-4 { padding: 1rem !important; }\n    .tw-p-6 { padding: 1.5rem !important; }\n    .tw-p-8 { padding: 2rem !important; }\n    .tw-px-4 { padding-left: 1rem; padding-right: 1rem !important; }\n    .tw-px-6 { padding-left: 1.5rem; padding-right: 1.5rem !important; }\n    .tw-px-8 { padding-left: 2rem; padding-right: 2rem !important; }\n    .tw-py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem !important; }\n    .tw-py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem !important; }\n    .tw-py-4 { padding-top: 1rem; padding-bottom: 1rem !important; }\n    .tw-py-16 { padding-top: 4rem; padding-bottom: 4rem !important; }\n    .tw-py-20 { padding-top: 5rem; padding-bottom: 5rem !important; }\n\n    .tw-m-4 { margin: 1rem !important; }\n    .tw-mx-auto { margin-left: auto; margin-right: auto !important; }\n    .tw-mt-2 { margin-top: 0.5rem !important; }\n    .tw-mt-4 { margin-top: 1rem !important; }\n    .tw-mb-4 { margin-bottom: 1rem !important; }\n    .tw-mb-6 { margin-bottom: 1.5rem !important; }\n    .tw-mb-8 { margin-bottom: 2rem !important; }\n\n    .tw-rounded { border-radius: 0.25rem !important; }\n    .tw-rounded-lg { border-radius: 0.5rem !important; }\n    .tw-rounded-xl { border-radius: 0.75rem !important; }\n\n    .tw-text-sm { font-size: 0.875rem; line-height: 1.25rem !important; }\n    .tw-text-base { font-size: 1rem; line-height: 1.5rem !important; }\n    .tw-text-lg { font-size: 1.125rem; line-height: 1.75rem !important; }\n    .tw-text-xl { font-size: 1.25rem; line-height: 1.75rem !important; }\n    .tw-text-2xl { font-size: 1.5rem; line-height: 2rem !important; }\n    .tw-text-3xl { font-size: 1.875rem; line-height: 2.25rem !important; }\n    .tw-text-4xl { font-size: 2.25rem; line-height: 2.5rem !important; }\n    .tw-text-5xl { font-size: 3rem; line-height: 1 !important; }\n\n    .tw-font-medium { font-weight: 500 !important; }\n    .tw-font-semibold { font-weight: 600 !important; }\n    .tw-font-bold { font-weight: 700 !important; }\n\n    .tw-text-center { text-align: center !important; }\n    .tw-text-left { text-align: left !important; }\n\n    .tw-flex { display: flex !important; }\n    .tw-grid { display: grid !important; }\n    .tw-block { display: block !important; }\n    .tw-inline-block { display: inline-block !important; }\n    .tw-hidden { display: none !important; }\n\n    .tw-items-center { align-items: center !important; }\n    .tw-justify-center { justify-content: center !important; }\n    .tw-justify-between { justify-content: space-between !important; }\n\n    .tw-w-full { width: 100% !important; }\n    .tw-w-auto { width: auto !important; }\n    .tw-h-8 { height: 2rem !important; }\n    .tw-h-auto { height: auto !important; }\n\n    .tw-container { width: 100%; margin-left: auto; margin-right: auto !important; }\n    @media (min-width: 640px) { .tw-container { max-width: 640px !important; } }\n    @media (min-width: 768px) { .tw-container { max-width: 768px !important; } }\n    @media (min-width: 1024px) { .tw-container { max-width: 1024px !important; } }\n    @media (min-width: 1280px) { .tw-container { max-width: 1280px !important; } }\n\n    .tw-shadow-sm { box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05) !important; }\n    .tw-shadow { box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1) !important; }\n    .tw-shadow-md { box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important; }\n    .tw-shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important; }\n\n    .tw-border { border-width: 1px !important; }\n    .tw-border-gray-200 { border-color: rgb(229 231 235) !important; }\n    .tw-border-gray-300 { border-color: rgb(209 213 219) !important; }\n\n    .tw-transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms !important; }\n    .tw-transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms !important; }\n\n    .tw-hover\\:tw-bg-gray-100:hover { background-color: rgb(243 244 246) !important; }\n    .tw-hover\\:tw-text-blue-600:hover { color: rgb(37 99 235) !important; }\n\n    /* Grid classes */\n    .tw-grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }\n    .tw-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }\n    .tw-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }\n    .tw-gap-4 { gap: 1rem !important; }\n    .tw-gap-6 { gap: 1.5rem !important; }\n    .tw-gap-8 { gap: 2rem !important; }\n\n    @media (min-width: 768px) {\n      .tw-md\\:tw-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }\n      .tw-md\\:tw-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }\n      .tw-md\\:tw-block { display: block !important; }\n    }\n\n    /* Custom component styles */\n    \n  </style>\n</head>\n<body>\n  <div><div data-component=\"dynamic hero 6 pm\"><div\n  class=\"dw-relative dw-z-0 dw-overflow-hidden dw-bg-white dw-bg-[top_right] dw-bg-no-repeat dw-bg-[length:50%_auto] dw-px-[5rem] dw-pt-2 dw-pb-[9rem] xlx:dw-px-[3rem] xlx:dw-pb-[7.3rem] xlx:dw-bg-[length:46%_auto] xlx:dw-bg-none lgl:dw-px-[3rem] lgl:dw-pb-[8.3rem] lgl:dw-bg-[length:46%_auto] lgl:dw-bg-none lgm:dw-px-[2.5rem] lgm:dw-pb-[9.3rem] lgm:dw-bg-[length:46%_auto] lgm:dw-bg-none lgs:dw-px-[3rem] lgs:dw-pb-[9.3rem] lgs:dw-bg-[length:46%_auto] lgs:dw-bg-none lgx:dw-px-[3rem] lgx:dw-pb-[9.3rem] lgx:dw-bg-[length:46%_auto] lgx:dw-bg-none mdl:dw-pl-[2.5rem] mdl:dw-pr-0 mdl:dw-pb-[9rem] mdl:dw-bg-[length:46%_auto] mdl:dw-bg-none mdx:dw-px-[2.5rem] mdx:dw-pb-[9rem] mdx:dw-bg-[length:46%_auto] smm:dw-px-[1rem] smm:dw-pb-[6rem] smm:dw-bg-none smx:dw-px-0 smx:dw-pb-[6rem] smx:dw-bg-none xs:dw-px-0 xs:dw-pb-[6rem] xs:dw-bg-none\"\n>\n  <svg\n    class=\"dw-absolute dw-top-0 dw-left-1/2 dw-hidden mdx:dw-block\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0.03 0 645.94 800\"\n  >\n    <path\n      d=\"M409.731 554.556L418.669 537.316C421.506 531.775 424.147 526.333 426.631 520.781C431.502 509.725 435.484 498.498 437.992 487.406C443.094 465.014 440.439 444.475 426.827 429.266C413.214 413.753 390.738 403.342 367.505 396.086C318.045 381.852 267.467 366.011 218.531 347.431C206.247 342.767 194.022 337.922 181.845 332.759C169.852 327.719 156.848 321.827 144.952 313.85C133.191 305.9 120.856 295.306 114.489 279.064C111.334 271.077 110.506 262.042 111.567 253.834C112.637 245.605 115.317 238.116 118.642 231.442C125.558 217.769 134.044 207.906 141.691 197.325L165.495 166.277L213.603 104.836L295.511 1.79063L233.07 0C233.07 0 90.8422 130.795 27.3563 213.336C-27.8375 285.077 2.30937 348.466 102.153 391.105C186.423 427.109 265.3 454.98 265.3 454.98C278.889 462.017 288.556 474.352 291.77 488.744C294.984 503.148 291.43 518.138 282.017 529.838L64.7484 800H281.519L373.716 623.644L409.731 554.556Z\"\n      class=\"dw-fill-theme-main\"\n      fill-opacity=\"0.239216\"\n    ></path>\n    <path\n      d=\"M618.27 407.848C588.012 357.233 536.216 321.533 476.212 309.928L263.631 265.109C254.767 263.405 247.316 257.78 243.553 249.963C239.791 242.158 240.192 233.109 244.612 225.622L385.928 4.37188L317.097 2.4L230.075 117.512L183.587 179.95L160.733 211.328C153.378 221.884 145.269 232.32 140.8 242.084C136.041 252.178 134.847 261.798 138.281 269.784C141.592 277.967 149.689 285.577 159.455 291.691C169.342 297.948 180.216 302.659 192.27 307.384C204.13 312.072 216.159 316.517 228.275 320.778C276.955 337.884 325.987 351.959 376.712 365.245C402.72 372.989 430.25 383.362 452.18 406.498C462.87 418.077 470.845 433.284 473.877 449.028C476.994 464.784 475.972 480.297 473.244 494.639C470.37 508.97 465.878 522.437 460.605 535.27C457.939 541.65 455.064 547.97 452.119 554.058L443.547 571.628L409.198 641.628L331.256 800H550.984L634.097 573.991C654.298 519.028 648.527 458.463 618.27 407.848Z\"\n      class=\"dw-fill-theme-main\"\n      fill-opacity=\"0.611765\"\n    ></path>\n  </svg>\n  <div\n    class=\"dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdx:dw-grid-cols-2\">\n      <div class=\"sub-buses-1 dw-px-1\">\n        <h1\n          class=\"dw-text-[32px] mdx:dw-text-[48px] dw-font-bold dw-mb-4 dw-font-heading\"\n        >\n          ${hero_section_title}\n        </h1>\n        <p class=\"dw-text-base xl1:dw-text-lg dw-mb-6\">\n          ${hero_section_description}\n        </p>\n        <div\n          class=\"dw-flex lgm:dw-flex-row dw-flex-col dw-gap-5 dw-items-start\"\n        >\n          <a\n            href=\"tel:${contact_number}\"\n            class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center dw-gap-[10px] dw-pe-[10px] lgs:dw-text-[18px] dw-text-[16px] dw-ps-[3px] dw-py-[3px] dw-text-white dw-bg-body-text dw-rounded-full hover:dw-bg-theme-main dw-border-[2px] dw-border-body-text hover:dw-border-theme-main dw-transition\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"42\"\n              height=\"42\"\n              viewBox=\"0 0 36 36\"\n              fill=\"none\"\n            >\n              <rect width=\"36\" height=\"36\" rx=\"18\" class=\"dw-fill-theme-main\" />\n              <path\n                d=\"M27.7388 22.4138C27.5716 23.6841 26.9477 24.8502 25.9837 25.6942C25.0196 26.5382 23.7813 27.0023 22.5 27.0001C15.0563 27.0001 9.00001 20.9438 9.00001 13.5001C8.99771 12.2188 9.4619 10.9805 10.3059 10.0164C11.1499 9.05234 12.3159 8.42847 13.5863 8.2613C13.9075 8.22208 14.2328 8.2878 14.5136 8.44865C14.7944 8.60951 15.0157 8.85687 15.1444 9.1538L17.1244 13.5741V13.5854C17.2229 13.8127 17.2636 14.0608 17.2428 14.3077C17.222 14.5545 17.1404 14.7924 17.0053 15.0001C16.9884 15.0254 16.9706 15.0488 16.9519 15.0722L15 17.386C15.7022 18.8129 17.1947 20.2922 18.6403 20.9963L20.9222 19.0547C20.9446 19.0359 20.9681 19.0184 20.9925 19.0022C21.2 18.8639 21.4387 18.7794 21.687 18.7565C21.9353 18.7336 22.1854 18.7729 22.4147 18.871L22.4269 18.8766L26.8434 20.8557C27.1409 20.9839 27.3889 21.205 27.5503 21.4858C27.7116 21.7667 27.7778 22.0922 27.7388 22.4138Z\"\n                fill=\"white\"\n              ></path>\n            </svg>\n            ${contact_number_button_label}\n          </a>\n          <a\n            href=\"${quote_url}\"\n            class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center dw-gap-[6px] lgs:dw-text-[18px] dw-text-[16px] dw-pe-[10px] dw-ps-3 dw-py-[9.5px] dw-text-white dw-border-[2px] dw-border-theme-main dw-rounded-full dw-bg-theme-main\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              id=\"Layer_1\"\n              data-name=\"Layer 1\"\n              viewBox=\"0 0 35.05 60.44\"\n              width=\"30\"\n              height=\"30\"\n              fill=\"white\"\n            >\n              <path\n                d=\"M21.45 7.46c2.09-.11 4.18.1 6.23.44l.13.22c-.51 1.39-2.17.52-3.38.48-9.62-.33-17.23 4.17-21.45 12.76-.11.23-.47 1.43-.53 1.49-.23.26-1.08.01-1.14-.18-.08-.25 1.18-2.92 1.4-3.33C6.35 12.59 13.75 7.86 21.44 7.45Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M22.15 9.65c.52-.02 4.24.09 4.25.48-.74 1.21-.54.76-1.62.75-7.73-.07-14.38 2.32-18.55 9.17-.33.55-1.51 3.39-1.67 3.51-.13.1-1.1-.11-1.14-.26-.04-.13.91-2.17 1.05-2.46C7.73 14.38 14.91 9.88 22.15 9.66Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M.13 29.21c.06-.03.97 0 1.01.04.09.13-.04 2.64 0 3.07.56 6.25 4.19 12.15 9.25 15.75.45.32 2.88 1.55 2.94 1.71.02.05-.36.77-.39.92C5.95 47.64.79 40.17.09 32.59c-.04-.45-.2-3.25.04-3.38\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"M21.71 11.93c1.05-.06 2.11.02 3.16.09.26.1-.47 1.03-.53 1.05-.37.13-1.97-.06-2.54 0-5.7.56-10.71 3.36-13.73 8.29-.29.48-1.09 2.47-1.23 2.63-.21.23-.9.01-1.14-.18 2.33-6.64 8.99-11.48 16.01-11.89Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M2.42 29.21c.07-.03.96 0 1.01.04-.56 6.56 2.81 12.97 8.03 16.8.39.29 2.65 1.47 2.68 1.62 0 .05-.37.76-.39.92-5.39-2.46-9.76-8.03-11.01-13.82-.15-.7-.78-5.36-.31-5.57Z\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"m4.61 29.21 1.01.04c-.47 6.78 3.33 13.04 9.21 16.23l-.31 1.01C8.17 43.38 3.99 36.33 4.61 29.21M2.94 24.56s.54.12.66.13c-.19.44-.08 3.28-.22 3.38-.03.02-.62.02-.66-.04-.02-.46-.01-3.28.22-3.46ZM5.31 25.09l.66.13c-.14.37-.1 2.77-.22 2.85-.03.02-.62.02-.66-.04 0-.98-.06-1.98.22-2.94\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"M35.05 0 25.01 27.68l8.95.26-23.39 32.5c3.39-9.18 7.02-18.39 9.82-27.72l-8.73-.13z\"\n                style=\"fill: #fdfeff\"\n              />\n              <path\n                d=\"M29.52 8.95c.26 0 0 .31-.04.39-4.07 7.61-10.02 14.97-14.3 22.54l-1.71.13z\"\n                style=\"fill: #fefefe\"\n              />\n            </svg>\n            ${get_quote_button_text}\n          </a>\n        </div>\n      </div>\n      <div\n        class=\"dw-z-10 dw-pt-10 dw-pb-10 mdl:dw-pt-16 mdl:dw-ps-10 lgm:dw-ps-[60px]\"\n      >\n        <img\n          src=\"${img-hero-section-image}\"\n          class=\"dw-rounded-[16px] dw-w-full dw-h-auto\"\n          alt=\"img-hero-section-image\"\n        />\n      </div>\n    </div>\n  </div>\n</div>\n<div class=\"dw-relative dw--mt-[85px] dw-z-[1]\">\n  <div\n    class=\"dw-px-8 lgm:dw-px-0 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdl:dw-grid-cols-3 dw-gap-3 dw-gap-y-12\">\n      \n      <!-- Box 1 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${hero_section_card1_content}\n          </p>\n        </div>\n      </div>\n      <!-- Box 2 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${hero_section_card2_content}\n          </p>\n        </div>\n      </div>\n      <!-- Box 3 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${hero_section_card3_content}\n          </p>\n        </div>\n      </div>\n    </div>\n  </div>\n</div></div></div>\n<div><div data-component=\"dynamic why section 6pm\"><div\n  class=\"dw-bg-theme-cardBg dw-bg-[url('${img-background-pattern-bg-image}')] dw-bg-top dw-bg-no-repeat dw-bg-cover mdl:dw-pt-24 mdl:dw-pb-[15rem] dw-pt-[5rem] dw-pb-[7rem]\"\n>\n  <div\n    class=\"dw-px-8 lgm:dw-px-0 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdl:dw-grid-cols-2 dw-items-center\">\n      <!-- Left Content -->\n      <div class=\"sub-buses-1 dw-px-1\">\n        <h2\n          class=\"dw-font-heading dw-leading-tight dw-text-[26px] mdx:dw-text-[46px] lgx:text-[48px] dw-font-semibold dw-mb-4\"\n        >\n          ${why_section_heading}\n        </h2>\n        <p class=\"xl1:dw-text-lg dw-text-base dw-leading-relaxed\">\n          ${why_section_description}\n        </p>\n      </div>\n      <!-- Right Image -->\n      <div\n        class=\"why-rent-image sub-buses-2 dw-pt-[40px] mdl:dw-ps-[40px] lgm:dw-ps-[60px] lgs:dw-pt-0\"\n      >\n        <img\n          src=\"${img-why-section-image}\"\n          alt=\"img-why-section-image\"\n          class=\"dw-w-full dw-h-auto dw-rounded-[16px]\"\n        />\n      </div>\n    </div>\n  </div>\n  </div></div></div>\n\n  <script>\n    try {\n      \n    } catch(e) {\n      console.error('JavaScript error:', e);\n    }\n  </script>\n</body>\n</html>", "pageComponentList": [{"name": "dynamic hero 6 pm", "category_id": "mel963cdj36kxptu75", "html_content": "<div data-component=\"dynamic hero 6 pm\"><div\n  class=\"dw-relative dw-z-0 dw-overflow-hidden dw-bg-white dw-bg-[top_right] dw-bg-no-repeat dw-bg-[length:50%_auto] dw-px-[5rem] dw-pt-2 dw-pb-[9rem] xlx:dw-px-[3rem] xlx:dw-pb-[7.3rem] xlx:dw-bg-[length:46%_auto] xlx:dw-bg-none lgl:dw-px-[3rem] lgl:dw-pb-[8.3rem] lgl:dw-bg-[length:46%_auto] lgl:dw-bg-none lgm:dw-px-[2.5rem] lgm:dw-pb-[9.3rem] lgm:dw-bg-[length:46%_auto] lgm:dw-bg-none lgs:dw-px-[3rem] lgs:dw-pb-[9.3rem] lgs:dw-bg-[length:46%_auto] lgs:dw-bg-none lgx:dw-px-[3rem] lgx:dw-pb-[9.3rem] lgx:dw-bg-[length:46%_auto] lgx:dw-bg-none mdl:dw-pl-[2.5rem] mdl:dw-pr-0 mdl:dw-pb-[9rem] mdl:dw-bg-[length:46%_auto] mdl:dw-bg-none mdx:dw-px-[2.5rem] mdx:dw-pb-[9rem] mdx:dw-bg-[length:46%_auto] smm:dw-px-[1rem] smm:dw-pb-[6rem] smm:dw-bg-none smx:dw-px-0 smx:dw-pb-[6rem] smx:dw-bg-none xs:dw-px-0 xs:dw-pb-[6rem] xs:dw-bg-none\"\n>\n  <svg\n    class=\"dw-absolute dw-top-0 dw-left-1/2 dw-hidden mdx:dw-block\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0.03 0 645.94 800\"\n  >\n    <path\n      d=\"M409.731 554.556L418.669 537.316C421.506 531.775 424.147 526.333 426.631 520.781C431.502 509.725 435.484 498.498 437.992 487.406C443.094 465.014 440.439 444.475 426.827 429.266C413.214 413.753 390.738 403.342 367.505 396.086C318.045 381.852 267.467 366.011 218.531 347.431C206.247 342.767 194.022 337.922 181.845 332.759C169.852 327.719 156.848 321.827 144.952 313.85C133.191 305.9 120.856 295.306 114.489 279.064C111.334 271.077 110.506 262.042 111.567 253.834C112.637 245.605 115.317 238.116 118.642 231.442C125.558 217.769 134.044 207.906 141.691 197.325L165.495 166.277L213.603 104.836L295.511 1.79063L233.07 0C233.07 0 90.8422 130.795 27.3563 213.336C-27.8375 285.077 2.30937 348.466 102.153 391.105C186.423 427.109 265.3 454.98 265.3 454.98C278.889 462.017 288.556 474.352 291.77 488.744C294.984 503.148 291.43 518.138 282.017 529.838L64.7484 800H281.519L373.716 623.644L409.731 554.556Z\"\n      class=\"dw-fill-theme-main\"\n      fill-opacity=\"0.239216\"\n    ></path>\n    <path\n      d=\"M618.27 407.848C588.012 357.233 536.216 321.533 476.212 309.928L263.631 265.109C254.767 263.405 247.316 257.78 243.553 249.963C239.791 242.158 240.192 233.109 244.612 225.622L385.928 4.37188L317.097 2.4L230.075 117.512L183.587 179.95L160.733 211.328C153.378 221.884 145.269 232.32 140.8 242.084C136.041 252.178 134.847 261.798 138.281 269.784C141.592 277.967 149.689 285.577 159.455 291.691C169.342 297.948 180.216 302.659 192.27 307.384C204.13 312.072 216.159 316.517 228.275 320.778C276.955 337.884 325.987 351.959 376.712 365.245C402.72 372.989 430.25 383.362 452.18 406.498C462.87 418.077 470.845 433.284 473.877 449.028C476.994 464.784 475.972 480.297 473.244 494.639C470.37 508.97 465.878 522.437 460.605 535.27C457.939 541.65 455.064 547.97 452.119 554.058L443.547 571.628L409.198 641.628L331.256 800H550.984L634.097 573.991C654.298 519.028 648.527 458.463 618.27 407.848Z\"\n      class=\"dw-fill-theme-main\"\n      fill-opacity=\"0.611765\"\n    ></path>\n  </svg>\n  <div\n    class=\"dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdx:dw-grid-cols-2\">\n      <div class=\"sub-buses-1 dw-px-1\">\n        <h1\n          class=\"dw-text-[32px] mdx:dw-text-[48px] dw-font-bold dw-mb-4 dw-font-heading\"\n        >\n          ${hero_section_title}\n        </h1>\n        <p class=\"dw-text-base xl1:dw-text-lg dw-mb-6\">\n          ${hero_section_description}\n        </p>\n        <div\n          class=\"dw-flex lgm:dw-flex-row dw-flex-col dw-gap-5 dw-items-start\"\n        >\n          <a\n            href=\"tel:${contact_number}\"\n            class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center dw-gap-[10px] dw-pe-[10px] lgs:dw-text-[18px] dw-text-[16px] dw-ps-[3px] dw-py-[3px] dw-text-white dw-bg-body-text dw-rounded-full hover:dw-bg-theme-main dw-border-[2px] dw-border-body-text hover:dw-border-theme-main dw-transition\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"42\"\n              height=\"42\"\n              viewBox=\"0 0 36 36\"\n              fill=\"none\"\n            >\n              <rect width=\"36\" height=\"36\" rx=\"18\" class=\"dw-fill-theme-main\" />\n              <path\n                d=\"M27.7388 22.4138C27.5716 23.6841 26.9477 24.8502 25.9837 25.6942C25.0196 26.5382 23.7813 27.0023 22.5 27.0001C15.0563 27.0001 9.00001 20.9438 9.00001 13.5001C8.99771 12.2188 9.4619 10.9805 10.3059 10.0164C11.1499 9.05234 12.3159 8.42847 13.5863 8.2613C13.9075 8.22208 14.2328 8.2878 14.5136 8.44865C14.7944 8.60951 15.0157 8.85687 15.1444 9.1538L17.1244 13.5741V13.5854C17.2229 13.8127 17.2636 14.0608 17.2428 14.3077C17.222 14.5545 17.1404 14.7924 17.0053 15.0001C16.9884 15.0254 16.9706 15.0488 16.9519 15.0722L15 17.386C15.7022 18.8129 17.1947 20.2922 18.6403 20.9963L20.9222 19.0547C20.9446 19.0359 20.9681 19.0184 20.9925 19.0022C21.2 18.8639 21.4387 18.7794 21.687 18.7565C21.9353 18.7336 22.1854 18.7729 22.4147 18.871L22.4269 18.8766L26.8434 20.8557C27.1409 20.9839 27.3889 21.205 27.5503 21.4858C27.7116 21.7667 27.7778 22.0922 27.7388 22.4138Z\"\n                fill=\"white\"\n              ></path>\n            </svg>\n            ${contact_number_button_label}\n          </a>\n          <a\n            href=\"${quote_url}\"\n            class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center dw-gap-[6px] lgs:dw-text-[18px] dw-text-[16px] dw-pe-[10px] dw-ps-3 dw-py-[9.5px] dw-text-white dw-border-[2px] dw-border-theme-main dw-rounded-full dw-bg-theme-main\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              id=\"Layer_1\"\n              data-name=\"Layer 1\"\n              viewBox=\"0 0 35.05 60.44\"\n              width=\"30\"\n              height=\"30\"\n              fill=\"white\"\n            >\n              <path\n                d=\"M21.45 7.46c2.09-.11 4.18.1 6.23.44l.13.22c-.51 1.39-2.17.52-3.38.48-9.62-.33-17.23 4.17-21.45 12.76-.11.23-.47 1.43-.53 1.49-.23.26-1.08.01-1.14-.18-.08-.25 1.18-2.92 1.4-3.33C6.35 12.59 13.75 7.86 21.44 7.45Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M22.15 9.65c.52-.02 4.24.09 4.25.48-.74 1.21-.54.76-1.62.75-7.73-.07-14.38 2.32-18.55 9.17-.33.55-1.51 3.39-1.67 3.51-.13.1-1.1-.11-1.14-.26-.04-.13.91-2.17 1.05-2.46C7.73 14.38 14.91 9.88 22.15 9.66Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M.13 29.21c.06-.03.97 0 1.01.04.09.13-.04 2.64 0 3.07.56 6.25 4.19 12.15 9.25 15.75.45.32 2.88 1.55 2.94 1.71.02.05-.36.77-.39.92C5.95 47.64.79 40.17.09 32.59c-.04-.45-.2-3.25.04-3.38\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"M21.71 11.93c1.05-.06 2.11.02 3.16.09.26.1-.47 1.03-.53 1.05-.37.13-1.97-.06-2.54 0-5.7.56-10.71 3.36-13.73 8.29-.29.48-1.09 2.47-1.23 2.63-.21.23-.9.01-1.14-.18 2.33-6.64 8.99-11.48 16.01-11.89Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M2.42 29.21c.07-.03.96 0 1.01.04-.56 6.56 2.81 12.97 8.03 16.8.39.29 2.65 1.47 2.68 1.62 0 .05-.37.76-.39.92-5.39-2.46-9.76-8.03-11.01-13.82-.15-.7-.78-5.36-.31-5.57Z\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"m4.61 29.21 1.01.04c-.47 6.78 3.33 13.04 9.21 16.23l-.31 1.01C8.17 43.38 3.99 36.33 4.61 29.21M2.94 24.56s.54.12.66.13c-.19.44-.08 3.28-.22 3.38-.03.02-.62.02-.66-.04-.02-.46-.01-3.28.22-3.46ZM5.31 25.09l.66.13c-.14.37-.1 2.77-.22 2.85-.03.02-.62.02-.66-.04 0-.98-.06-1.98.22-2.94\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"M35.05 0 25.01 27.68l8.95.26-23.39 32.5c3.39-9.18 7.02-18.39 9.82-27.72l-8.73-.13z\"\n                style=\"fill: #fdfeff\"\n              />\n              <path\n                d=\"M29.52 8.95c.26 0 0 .31-.04.39-4.07 7.61-10.02 14.97-14.3 22.54l-1.71.13z\"\n                style=\"fill: #fefefe\"\n              />\n            </svg>\n            ${get_quote_button_text}\n          </a>\n        </div>\n      </div>\n      <div\n        class=\"dw-z-10 dw-pt-10 dw-pb-10 mdl:dw-pt-16 mdl:dw-ps-10 lgm:dw-ps-[60px]\"\n      >\n        <img\n          src=\"${img-hero-section-image}\"\n          class=\"dw-rounded-[16px] dw-w-full dw-h-auto\"\n          alt=\"img-hero-section-image\"\n        />\n      </div>\n    </div>\n  </div>\n</div>\n<div class=\"dw-relative dw--mt-[85px] dw-z-[1]\">\n  <div\n    class=\"dw-px-8 lgm:dw-px-0 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdl:dw-grid-cols-3 dw-gap-3 dw-gap-y-12\">\n      \n      <!-- Box 1 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${hero_section_card1_content}\n          </p>\n        </div>\n      </div>\n      <!-- Box 2 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${hero_section_card2_content}\n          </p>\n        </div>\n      </div>\n      <!-- Box 3 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${hero_section_card3_content}\n          </p>\n        </div>\n      </div>\n    </div>\n  </div>\n</div></div>", "css_content": "", "js_content": "", "placeholders": ["hero_section_title", "hero_section_description", "contact_number", "contact_number_button_label", "quote_url", "get_quote_button_text", "img-hero-section-image", "img-advantage-image", "hero_section_card1_content", "hero_section_card2_content", "hero_section_card3_content"], "preview_image": "", "version": 1, "status": "draft", "tags": [], "thumbnail_url": "", "id": "memu023nuog7g3qjai", "created_at": "2025-08-22T12:53:29.891Z", "updated_at": "2025-08-22T12:53:29.891Z", "category_name": "Footer", "category_color": "#783636", "order": 0, "repeator": "single", "cssClass": "", "uniqueId": "memu023nuog7g3qjai-1755867264945-op33d9nd5"}, {"name": "dynamic why section 6pm", "category_id": "mel963cdj36kxptu75", "html_content": "<div data-component=\"dynamic why section 6pm\"><div\n  class=\"dw-bg-theme-cardBg dw-bg-[url('${img-background-pattern-bg-image}')] dw-bg-top dw-bg-no-repeat dw-bg-cover mdl:dw-pt-24 mdl:dw-pb-[15rem] dw-pt-[5rem] dw-pb-[7rem]\"\n>\n  <div\n    class=\"dw-px-8 lgm:dw-px-0 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdl:dw-grid-cols-2 dw-items-center\">\n      <!-- Left Content -->\n      <div class=\"sub-buses-1 dw-px-1\">\n        <h2\n          class=\"dw-font-heading dw-leading-tight dw-text-[26px] mdx:dw-text-[46px] lgx:text-[48px] dw-font-semibold dw-mb-4\"\n        >\n          ${why_section_heading}\n        </h2>\n        <p class=\"xl1:dw-text-lg dw-text-base dw-leading-relaxed\">\n          ${why_section_description}\n        </p>\n      </div>\n      <!-- Right Image -->\n      <div\n        class=\"why-rent-image sub-buses-2 dw-pt-[40px] mdl:dw-ps-[40px] lgm:dw-ps-[60px] lgs:dw-pt-0\"\n      >\n        <img\n          src=\"${img-why-section-image}\"\n          alt=\"img-why-section-image\"\n          class=\"dw-w-full dw-h-auto dw-rounded-[16px]\"\n        />\n      </div>\n    </div>\n  </div>\n  </div></div>", "css_content": "", "js_content": "", "placeholders": ["img-background-pattern-bg-image", "why_section_heading", "why_section_description", "img-why-section-image"], "preview_image": "", "version": 1, "status": "draft", "tags": [], "thumbnail_url": "", "id": "memu0p5sgfbkuc42hp", "created_at": "2025-08-22T12:53:59.776Z", "updated_at": "2025-08-22T12:53:59.776Z", "category_name": "Footer", "category_color": "#783636", "order": 1, "repeator": "single", "cssClass": "", "uniqueId": "memu0p5sgfbkuc42hp-1755867266625-8qzk5eg8h"}], "components": [{"name": "dynamic hero 6 pm", "category_id": "mel963cdj36kxptu75", "html_content": "<div data-component=\"dynamic hero 6 pm\"><div\n  class=\"dw-relative dw-z-0 dw-overflow-hidden dw-bg-white dw-bg-[top_right] dw-bg-no-repeat dw-bg-[length:50%_auto] dw-px-[5rem] dw-pt-2 dw-pb-[9rem] xlx:dw-px-[3rem] xlx:dw-pb-[7.3rem] xlx:dw-bg-[length:46%_auto] xlx:dw-bg-none lgl:dw-px-[3rem] lgl:dw-pb-[8.3rem] lgl:dw-bg-[length:46%_auto] lgl:dw-bg-none lgm:dw-px-[2.5rem] lgm:dw-pb-[9.3rem] lgm:dw-bg-[length:46%_auto] lgm:dw-bg-none lgs:dw-px-[3rem] lgs:dw-pb-[9.3rem] lgs:dw-bg-[length:46%_auto] lgs:dw-bg-none lgx:dw-px-[3rem] lgx:dw-pb-[9.3rem] lgx:dw-bg-[length:46%_auto] lgx:dw-bg-none mdl:dw-pl-[2.5rem] mdl:dw-pr-0 mdl:dw-pb-[9rem] mdl:dw-bg-[length:46%_auto] mdl:dw-bg-none mdx:dw-px-[2.5rem] mdx:dw-pb-[9rem] mdx:dw-bg-[length:46%_auto] smm:dw-px-[1rem] smm:dw-pb-[6rem] smm:dw-bg-none smx:dw-px-0 smx:dw-pb-[6rem] smx:dw-bg-none xs:dw-px-0 xs:dw-pb-[6rem] xs:dw-bg-none\"\n>\n  <svg\n    class=\"dw-absolute dw-top-0 dw-left-1/2 dw-hidden mdx:dw-block\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0.03 0 645.94 800\"\n  >\n    <path\n      d=\"M409.731 554.556L418.669 537.316C421.506 531.775 424.147 526.333 426.631 520.781C431.502 509.725 435.484 498.498 437.992 487.406C443.094 465.014 440.439 444.475 426.827 429.266C413.214 413.753 390.738 403.342 367.505 396.086C318.045 381.852 267.467 366.011 218.531 347.431C206.247 342.767 194.022 337.922 181.845 332.759C169.852 327.719 156.848 321.827 144.952 313.85C133.191 305.9 120.856 295.306 114.489 279.064C111.334 271.077 110.506 262.042 111.567 253.834C112.637 245.605 115.317 238.116 118.642 231.442C125.558 217.769 134.044 207.906 141.691 197.325L165.495 166.277L213.603 104.836L295.511 1.79063L233.07 0C233.07 0 90.8422 130.795 27.3563 213.336C-27.8375 285.077 2.30937 348.466 102.153 391.105C186.423 427.109 265.3 454.98 265.3 454.98C278.889 462.017 288.556 474.352 291.77 488.744C294.984 503.148 291.43 518.138 282.017 529.838L64.7484 800H281.519L373.716 623.644L409.731 554.556Z\"\n      class=\"dw-fill-theme-main\"\n      fill-opacity=\"0.239216\"\n    ></path>\n    <path\n      d=\"M618.27 407.848C588.012 357.233 536.216 321.533 476.212 309.928L263.631 265.109C254.767 263.405 247.316 257.78 243.553 249.963C239.791 242.158 240.192 233.109 244.612 225.622L385.928 4.37188L317.097 2.4L230.075 117.512L183.587 179.95L160.733 211.328C153.378 221.884 145.269 232.32 140.8 242.084C136.041 252.178 134.847 261.798 138.281 269.784C141.592 277.967 149.689 285.577 159.455 291.691C169.342 297.948 180.216 302.659 192.27 307.384C204.13 312.072 216.159 316.517 228.275 320.778C276.955 337.884 325.987 351.959 376.712 365.245C402.72 372.989 430.25 383.362 452.18 406.498C462.87 418.077 470.845 433.284 473.877 449.028C476.994 464.784 475.972 480.297 473.244 494.639C470.37 508.97 465.878 522.437 460.605 535.27C457.939 541.65 455.064 547.97 452.119 554.058L443.547 571.628L409.198 641.628L331.256 800H550.984L634.097 573.991C654.298 519.028 648.527 458.463 618.27 407.848Z\"\n      class=\"dw-fill-theme-main\"\n      fill-opacity=\"0.611765\"\n    ></path>\n  </svg>\n  <div\n    class=\"dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdx:dw-grid-cols-2\">\n      <div class=\"sub-buses-1 dw-px-1\">\n        <h1\n          class=\"dw-text-[32px] mdx:dw-text-[48px] dw-font-bold dw-mb-4 dw-font-heading\"\n        >\n          ${hero_section_title}\n        </h1>\n        <p class=\"dw-text-base xl1:dw-text-lg dw-mb-6\">\n          ${hero_section_description}\n        </p>\n        <div\n          class=\"dw-flex lgm:dw-flex-row dw-flex-col dw-gap-5 dw-items-start\"\n        >\n          <a\n            href=\"tel:${contact_number}\"\n            class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center dw-gap-[10px] dw-pe-[10px] lgs:dw-text-[18px] dw-text-[16px] dw-ps-[3px] dw-py-[3px] dw-text-white dw-bg-body-text dw-rounded-full hover:dw-bg-theme-main dw-border-[2px] dw-border-body-text hover:dw-border-theme-main dw-transition\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"42\"\n              height=\"42\"\n              viewBox=\"0 0 36 36\"\n              fill=\"none\"\n            >\n              <rect width=\"36\" height=\"36\" rx=\"18\" class=\"dw-fill-theme-main\" />\n              <path\n                d=\"M27.7388 22.4138C27.5716 23.6841 26.9477 24.8502 25.9837 25.6942C25.0196 26.5382 23.7813 27.0023 22.5 27.0001C15.0563 27.0001 9.00001 20.9438 9.00001 13.5001C8.99771 12.2188 9.4619 10.9805 10.3059 10.0164C11.1499 9.05234 12.3159 8.42847 13.5863 8.2613C13.9075 8.22208 14.2328 8.2878 14.5136 8.44865C14.7944 8.60951 15.0157 8.85687 15.1444 9.1538L17.1244 13.5741V13.5854C17.2229 13.8127 17.2636 14.0608 17.2428 14.3077C17.222 14.5545 17.1404 14.7924 17.0053 15.0001C16.9884 15.0254 16.9706 15.0488 16.9519 15.0722L15 17.386C15.7022 18.8129 17.1947 20.2922 18.6403 20.9963L20.9222 19.0547C20.9446 19.0359 20.9681 19.0184 20.9925 19.0022C21.2 18.8639 21.4387 18.7794 21.687 18.7565C21.9353 18.7336 22.1854 18.7729 22.4147 18.871L22.4269 18.8766L26.8434 20.8557C27.1409 20.9839 27.3889 21.205 27.5503 21.4858C27.7116 21.7667 27.7778 22.0922 27.7388 22.4138Z\"\n                fill=\"white\"\n              ></path>\n            </svg>\n            ${contact_number_button_label}\n          </a>\n          <a\n            href=\"${quote_url}\"\n            class=\"dw-inline-flex dw-w-fit dw-font-medium dw-items-center dw-gap-[6px] lgs:dw-text-[18px] dw-text-[16px] dw-pe-[10px] dw-ps-3 dw-py-[9.5px] dw-text-white dw-border-[2px] dw-border-theme-main dw-rounded-full dw-bg-theme-main\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              id=\"Layer_1\"\n              data-name=\"Layer 1\"\n              viewBox=\"0 0 35.05 60.44\"\n              width=\"30\"\n              height=\"30\"\n              fill=\"white\"\n            >\n              <path\n                d=\"M21.45 7.46c2.09-.11 4.18.1 6.23.44l.13.22c-.51 1.39-2.17.52-3.38.48-9.62-.33-17.23 4.17-21.45 12.76-.11.23-.47 1.43-.53 1.49-.23.26-1.08.01-1.14-.18-.08-.25 1.18-2.92 1.4-3.33C6.35 12.59 13.75 7.86 21.44 7.45Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M22.15 9.65c.52-.02 4.24.09 4.25.48-.74 1.21-.54.76-1.62.75-7.73-.07-14.38 2.32-18.55 9.17-.33.55-1.51 3.39-1.67 3.51-.13.1-1.1-.11-1.14-.26-.04-.13.91-2.17 1.05-2.46C7.73 14.38 14.91 9.88 22.15 9.66Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M.13 29.21c.06-.03.97 0 1.01.04.09.13-.04 2.64 0 3.07.56 6.25 4.19 12.15 9.25 15.75.45.32 2.88 1.55 2.94 1.71.02.05-.36.77-.39.92C5.95 47.64.79 40.17.09 32.59c-.04-.45-.2-3.25.04-3.38\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"M21.71 11.93c1.05-.06 2.11.02 3.16.09.26.1-.47 1.03-.53 1.05-.37.13-1.97-.06-2.54 0-5.7.56-10.71 3.36-13.73 8.29-.29.48-1.09 2.47-1.23 2.63-.21.23-.9.01-1.14-.18 2.33-6.64 8.99-11.48 16.01-11.89Z\"\n                class=\"cls-3\"\n              />\n              <path\n                d=\"M2.42 29.21c.07-.03.96 0 1.01.04-.56 6.56 2.81 12.97 8.03 16.8.39.29 2.65 1.47 2.68 1.62 0 .05-.37.76-.39.92-5.39-2.46-9.76-8.03-11.01-13.82-.15-.7-.78-5.36-.31-5.57Z\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"m4.61 29.21 1.01.04c-.47 6.78 3.33 13.04 9.21 16.23l-.31 1.01C8.17 43.38 3.99 36.33 4.61 29.21M2.94 24.56s.54.12.66.13c-.19.44-.08 3.28-.22 3.38-.03.02-.62.02-.66-.04-.02-.46-.01-3.28.22-3.46ZM5.31 25.09l.66.13c-.14.37-.1 2.77-.22 2.85-.03.02-.62.02-.66-.04 0-.98-.06-1.98.22-2.94\"\n                class=\"cls-1\"\n              />\n              <path\n                d=\"M35.05 0 25.01 27.68l8.95.26-23.39 32.5c3.39-9.18 7.02-18.39 9.82-27.72l-8.73-.13z\"\n                style=\"fill: #fdfeff\"\n              />\n              <path\n                d=\"M29.52 8.95c.26 0 0 .31-.04.39-4.07 7.61-10.02 14.97-14.3 22.54l-1.71.13z\"\n                style=\"fill: #fefefe\"\n              />\n            </svg>\n            ${get_quote_button_text}\n          </a>\n        </div>\n      </div>\n      <div\n        class=\"dw-z-10 dw-pt-10 dw-pb-10 mdl:dw-pt-16 mdl:dw-ps-10 lgm:dw-ps-[60px]\"\n      >\n        <img\n          src=\"${img-hero-section-image}\"\n          class=\"dw-rounded-[16px] dw-w-full dw-h-auto\"\n          alt=\"img-hero-section-image\"\n        />\n      </div>\n    </div>\n  </div>\n</div>\n<div class=\"dw-relative dw--mt-[85px] dw-z-[1]\">\n  <div\n    class=\"dw-px-8 lgm:dw-px-0 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdl:dw-grid-cols-3 dw-gap-3 dw-gap-y-12\">\n      \n      <!-- Box 1 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${hero_section_card1_content}\n          </p>\n        </div>\n      </div>\n      <!-- Box 2 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${hero_section_card2_content}\n          </p>\n        </div>\n      </div>\n      <!-- Box 3 -->\n      <div class=\"dw-px-[6px] hero-boxes\">\n        <div\n          class=\"dw-flex dw-gap-6 dw-items-start dw-bg-white dw-shadow-[0_4px_10px_-2px_rgba(0,0,0,0.5)] hover:dw-shadow-[0_4px_10px_-2px_rgba(82,164,206,0.43)] dw-rounded-[16px] dw-py-5 dw-px-10 dw-h-[80%] dw-box-content\"\n        >\n          <img\n            decoding=\"async\"\n            width=\"48\"\n            height=\"48\"\n            src=\"${img-advantage-image}\"\n            alt=\"img-advantage-image\"\n          />\n          <p class=\"dw-font-semibold dw-font-heading dw-text-2xl\">\n            ${hero_section_card3_content}\n          </p>\n        </div>\n      </div>\n    </div>\n  </div>\n</div></div>", "css_content": "", "js_content": "", "placeholders": ["hero_section_title", "hero_section_description", "contact_number", "contact_number_button_label", "quote_url", "get_quote_button_text", "img-hero-section-image", "img-advantage-image", "hero_section_card1_content", "hero_section_card2_content", "hero_section_card3_content"], "preview_image": "", "version": 1, "status": "draft", "tags": [], "thumbnail_url": "", "id": "memu023nuog7g3qjai", "created_at": "2025-08-22T12:53:29.891Z", "updated_at": "2025-08-22T12:53:29.891Z", "category_name": "Footer", "category_color": "#783636", "order": 0, "repeator": "single", "cssClass": "", "uniqueId": "memu023nuog7g3qjai-1755867264945-op33d9nd5"}, {"name": "dynamic why section 6pm", "category_id": "mel963cdj36kxptu75", "html_content": "<div data-component=\"dynamic why section 6pm\"><div\n  class=\"dw-bg-theme-cardBg dw-bg-[url('${img-background-pattern-bg-image}')] dw-bg-top dw-bg-no-repeat dw-bg-cover mdl:dw-pt-24 mdl:dw-pb-[15rem] dw-pt-[5rem] dw-pb-[7rem]\"\n>\n  <div\n    class=\"dw-px-8 lgm:dw-px-0 dw-mx-auto dw-w-full xl1:dw-max-w-[1440px] lgl:dw-max-w-[1300px] lgs:dw-max-w-[1200px] lgx:dw-max-w-[1100px] mdl:dw-max-w-[1080px] mdx:dw-max-w-[1000px] smm:dw-max-w-[940px] smx:dw-max-w-[700px]\"\n  >\n    <div class=\"dw-grid dw-grid-cols-1 mdl:dw-grid-cols-2 dw-items-center\">\n      <!-- Left Content -->\n      <div class=\"sub-buses-1 dw-px-1\">\n        <h2\n          class=\"dw-font-heading dw-leading-tight dw-text-[26px] mdx:dw-text-[46px] lgx:text-[48px] dw-font-semibold dw-mb-4\"\n        >\n          ${why_section_heading}\n        </h2>\n        <p class=\"xl1:dw-text-lg dw-text-base dw-leading-relaxed\">\n          ${why_section_description}\n        </p>\n      </div>\n      <!-- Right Image -->\n      <div\n        class=\"why-rent-image sub-buses-2 dw-pt-[40px] mdl:dw-ps-[40px] lgm:dw-ps-[60px] lgs:dw-pt-0\"\n      >\n        <img\n          src=\"${img-why-section-image}\"\n          alt=\"img-why-section-image\"\n          class=\"dw-w-full dw-h-auto dw-rounded-[16px]\"\n        />\n      </div>\n    </div>\n  </div>\n  </div></div>", "css_content": "", "js_content": "", "placeholders": ["img-background-pattern-bg-image", "why_section_heading", "why_section_description", "img-why-section-image"], "preview_image": "", "version": 1, "status": "draft", "tags": [], "thumbnail_url": "", "id": "memu0p5sgfbkuc42hp", "created_at": "2025-08-22T12:53:59.776Z", "updated_at": "2025-08-22T12:53:59.776Z", "category_name": "Footer", "category_color": "#783636", "order": 1, "repeator": "single", "cssClass": "", "uniqueId": "memu0p5sgfbkuc42hp-1755867266625-8qzk5eg8h"}], "version": "v1", "pagePlaceHolder": [{"categoryName": "dynamic hero 6 pm", "CategoryId": "memu023nuog7g3qjai", "placeholders": ["hero_section_title", "hero_section_description", "contact_number", "contact_number_button_label", "quote_url", "get_quote_button_text", "img-hero-section-image", "img-advantage-image", "hero_section_card1_content", "hero_section_card2_content", "hero_section_card3_content"]}, {"categoryName": "dynamic why section 6pm", "CategoryId": "memu0p5sgfbkuc42hp", "placeholders": ["img-background-pattern-bg-image", "why_section_heading", "why_section_description", "img-why-section-image"]}], "id": "memu2w5jds6feyll53", "created_at": "2025-08-22T12:55:42.151Z", "updated_at": "2025-08-22T12:55:42.151Z", "url": "/city", "type": "dynamic", "showNavbar": true, "navPosition": 0, "order": 0}, {"name": "Final Testing Purpose", "slug": "b<PERSON><PERSON><PERSON>", "meta_title": "Bus Rental Company Clifton | Bus Rentals in Clifton", "meta_description": "Bus Rental Company Clifton | Bus Rentals in Clifton", "wrapper_class": ".main-dev", "custom_css": ".main-dev {\nmargin: \"auto\";\n}\n", "custom_js": "", "full_page_content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>Bus Rental Company Clifton | Bus Rentals in Clifton</title>\n  <!-- Tailwind CSS - Generated locally to avoid CDN warnings -->\n  <style>\n    \n/* Tailwind CSS Reset and Base Styles */\n*,\n::before,\n::after {\n  box-sizing: border-box;\n  border-width: 0;\n  border-style: solid;\n  border-color: #e5e7eb;\n}\n\n::before,\n::after {\n  --tw-content: '';\n}\n\nhtml {\n  line-height: 1.5;\n  -webkit-text-size-adjust: 100%;\n  -moz-tab-size: 4;\n  tab-size: 4;\n  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Robot<PERSON>, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  font-feature-settings: normal;\n  font-variation-settings: normal;\n}\n\nbody {\n  margin: 0;\n  line-height: inherit;\n}\n\n/* Tailwind CSS Utilities with tw- prefix */\n\n/* Display */\n.tw-block { display: block; }\n.tw-inline-block { display: inline-block; }\n.tw-inline { display: inline; }\n.tw-flex { display: flex; }\n.tw-inline-flex { display: inline-flex; }\n.tw-grid { display: grid; }\n.tw-hidden { display: none; }\n\n/* Position */\n.tw-static { position: static; }\n.tw-fixed { position: fixed; }\n.tw-absolute { position: absolute; }\n.tw-relative { position: relative; }\n.tw-sticky { position: sticky; }\n\n/* Top, Right, Bottom, Left */\n.tw-top-0 { top: 0px; }\n.tw-right-0 { right: 0px; }\n.tw-bottom-0 { bottom: 0px; }\n.tw-left-0 { left: 0px; }\n.tw-top-1\\/2 { top: 50%; }\n.tw-left-1\\/2 { left: 50%; }\n\n/* Z-Index */\n.tw-z-10 { z-index: 10; }\n.tw-z-20 { z-index: 20; }\n.tw-z-30 { z-index: 30; }\n.tw-z-40 { z-index: 40; }\n.tw-z-50 { z-index: 50; }\n\n/* Flex Direction */\n.tw-flex-row { flex-direction: row; }\n.tw-flex-row-reverse { flex-direction: row-reverse; }\n.tw-flex-col { flex-direction: column; }\n.tw-flex-col-reverse { flex-direction: column-reverse; }\n\n/* Flex Wrap */\n.tw-flex-wrap { flex-wrap: wrap; }\n.tw-flex-wrap-reverse { flex-wrap: wrap-reverse; }\n.tw-flex-nowrap { flex-wrap: nowrap; }\n\n/* Align Items */\n.tw-items-start { align-items: flex-start; }\n.tw-items-end { align-items: flex-end; }\n.tw-items-center { align-items: center; }\n.tw-items-baseline { align-items: baseline; }\n.tw-items-stretch { align-items: stretch; }\n\n/* Justify Content */\n.tw-justify-start { justify-content: flex-start; }\n.tw-justify-end { justify-content: flex-end; }\n.tw-justify-center { justify-content: center; }\n.tw-justify-between { justify-content: space-between; }\n.tw-justify-around { justify-content: space-around; }\n.tw-justify-evenly { justify-content: space-evenly; }\n\n/* Gap */\n.tw-gap-1 { gap: 0.25rem; }\n.tw-gap-2 { gap: 0.5rem; }\n.tw-gap-3 { gap: 0.75rem; }\n.tw-gap-4 { gap: 1rem; }\n.tw-gap-5 { gap: 1.25rem; }\n.tw-gap-6 { gap: 1.5rem; }\n.tw-gap-8 { gap: 2rem; }\n\n/* Padding */\n.tw-p-0 { padding: 0px; }\n.tw-p-1 { padding: 0.25rem; }\n.tw-p-2 { padding: 0.5rem; }\n.tw-p-3 { padding: 0.75rem; }\n.tw-p-4 { padding: 1rem; }\n.tw-p-5 { padding: 1.25rem; }\n.tw-p-6 { padding: 1.5rem; }\n.tw-p-8 { padding: 2rem; }\n.tw-p-10 { padding: 2.5rem; }\n.tw-p-12 { padding: 3rem; }\n.tw-p-16 { padding: 4rem; }\n\n.tw-px-1 { padding-left: 0.25rem; padding-right: 0.25rem; }\n.tw-px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }\n.tw-px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }\n.tw-px-4 { padding-left: 1rem; padding-right: 1rem; }\n.tw-px-5 { padding-left: 1.25rem; padding-right: 1.25rem; }\n.tw-px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }\n.tw-px-8 { padding-left: 2rem; padding-right: 2rem; }\n\n.tw-py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }\n.tw-py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }\n.tw-py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }\n.tw-py-4 { padding-top: 1rem; padding-bottom: 1rem; }\n.tw-py-5 { padding-top: 1.25rem; padding-bottom: 1.25rem; }\n.tw-py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }\n.tw-py-8 { padding-top: 2rem; padding-bottom: 2rem; }\n\n.tw-pt-1 { padding-top: 0.25rem; }\n.tw-pt-2 { padding-top: 0.5rem; }\n.tw-pt-3 { padding-top: 0.75rem; }\n.tw-pt-4 { padding-top: 1rem; }\n.tw-pt-6 { padding-top: 1.5rem; }\n.tw-pt-8 { padding-top: 2rem; }\n\n.tw-pr-1 { padding-right: 0.25rem; }\n.tw-pr-2 { padding-right: 0.5rem; }\n.tw-pr-3 { padding-right: 0.75rem; }\n.tw-pr-4 { padding-right: 1rem; }\n.tw-pr-6 { padding-right: 1.5rem; }\n.tw-pr-8 { padding-right: 2rem; }\n\n.tw-pb-1 { padding-bottom: 0.25rem; }\n.tw-pb-2 { padding-bottom: 0.5rem; }\n.tw-pb-3 { padding-bottom: 0.75rem; }\n.tw-pb-4 { padding-bottom: 1rem; }\n.tw-pb-6 { padding-bottom: 1.5rem; }\n.tw-pb-8 { padding-bottom: 2rem; }\n\n.tw-pl-1 { padding-left: 0.25rem; }\n.tw-pl-2 { padding-left: 0.5rem; }\n.tw-pl-3 { padding-left: 0.75rem; }\n.tw-pl-4 { padding-left: 1rem; }\n.tw-pl-6 { padding-left: 1.5rem; }\n.tw-pl-8 { padding-left: 2rem; }\n\n/* Margin */\n.tw-m-0 { margin: 0px; }\n.tw-m-1 { margin: 0.25rem; }\n.tw-m-2 { margin: 0.5rem; }\n.tw-m-3 { margin: 0.75rem; }\n.tw-m-4 { margin: 1rem; }\n.tw-m-5 { margin: 1.25rem; }\n.tw-m-6 { margin: 1.5rem; }\n.tw-m-8 { margin: 2rem; }\n.tw-m-auto { margin: auto; }\n\n.tw-mx-1 { margin-left: 0.25rem; margin-right: 0.25rem; }\n.tw-mx-2 { margin-left: 0.5rem; margin-right: 0.5rem; }\n.tw-mx-3 { margin-left: 0.75rem; margin-right: 0.75rem; }\n.tw-mx-4 { margin-left: 1rem; margin-right: 1rem; }\n.tw-mx-6 { margin-left: 1.5rem; margin-right: 1.5rem; }\n.tw-mx-8 { margin-left: 2rem; margin-right: 2rem; }\n.tw-mx-auto { margin-left: auto; margin-right: auto; }\n\n.tw-my-1 { margin-top: 0.25rem; margin-bottom: 0.25rem; }\n.tw-my-2 { margin-top: 0.5rem; margin-bottom: 0.5rem; }\n.tw-my-3 { margin-top: 0.75rem; margin-bottom: 0.75rem; }\n.tw-my-4 { margin-top: 1rem; margin-bottom: 1rem; }\n.tw-my-6 { margin-top: 1.5rem; margin-bottom: 1.5rem; }\n.tw-my-8 { margin-top: 2rem; margin-bottom: 2rem; }\n\n.tw-mt-1 { margin-top: 0.25rem; }\n.tw-mt-2 { margin-top: 0.5rem; }\n.tw-mt-3 { margin-top: 0.75rem; }\n.tw-mt-4 { margin-top: 1rem; }\n.tw-mt-6 { margin-top: 1.5rem; }\n.tw-mt-8 { margin-top: 2rem; }\n\n.tw-mr-1 { margin-right: 0.25rem; }\n.tw-mr-2 { margin-right: 0.5rem; }\n.tw-mr-3 { margin-right: 0.75rem; }\n.tw-mr-4 { margin-right: 1rem; }\n.tw-mr-6 { margin-right: 1.5rem; }\n.tw-mr-8 { margin-right: 2rem; }\n\n.tw-mb-1 { margin-bottom: 0.25rem; }\n.tw-mb-2 { margin-bottom: 0.5rem; }\n.tw-mb-3 { margin-bottom: 0.75rem; }\n.tw-mb-4 { margin-bottom: 1rem; }\n.tw-mb-6 { margin-bottom: 1.5rem; }\n.tw-mb-8 { margin-bottom: 2rem; }\n\n.tw-ml-1 { margin-left: 0.25rem; }\n.tw-ml-2 { margin-left: 0.5rem; }\n.tw-ml-3 { margin-left: 0.75rem; }\n.tw-ml-4 { margin-left: 1rem; }\n.tw-ml-6 { margin-left: 1.5rem; }\n.tw-ml-8 { margin-left: 2rem; }\n\n/* Width */\n.tw-w-auto { width: auto; }\n.tw-w-full { width: 100%; }\n.tw-w-1\\/2 { width: 50%; }\n.tw-w-1\\/3 { width: 33.333333%; }\n.tw-w-2\\/3 { width: 66.666667%; }\n.tw-w-1\\/4 { width: 25%; }\n.tw-w-3\\/4 { width: 75%; }\n.tw-w-1\\/5 { width: 20%; }\n.tw-w-2\\/5 { width: 40%; }\n.tw-w-3\\/5 { width: 60%; }\n.tw-w-4\\/5 { width: 80%; }\n\n.tw-w-4 { width: 1rem; }\n.tw-w-5 { width: 1.25rem; }\n.tw-w-6 { width: 1.5rem; }\n.tw-w-8 { width: 2rem; }\n.tw-w-10 { width: 2.5rem; }\n.tw-w-12 { width: 3rem; }\n.tw-w-16 { width: 4rem; }\n.tw-w-20 { width: 5rem; }\n.tw-w-24 { width: 6rem; }\n.tw-w-32 { width: 8rem; }\n.tw-w-40 { width: 10rem; }\n.tw-w-48 { width: 12rem; }\n.tw-w-56 { width: 14rem; }\n.tw-w-64 { width: 16rem; }\n\n/* Height */\n.tw-h-auto { height: auto; }\n.tw-h-full { height: 100%; }\n.tw-h-screen { height: 100vh; }\n\n.tw-h-4 { height: 1rem; }\n.tw-h-5 { height: 1.25rem; }\n.tw-h-6 { height: 1.5rem; }\n.tw-h-8 { height: 2rem; }\n.tw-h-10 { height: 2.5rem; }\n.tw-h-12 { height: 3rem; }\n.tw-h-16 { height: 4rem; }\n.tw-h-20 { height: 5rem; }\n.tw-h-24 { height: 6rem; }\n.tw-h-32 { height: 8rem; }\n.tw-h-40 { height: 10rem; }\n.tw-h-48 { height: 12rem; }\n.tw-h-56 { height: 14rem; }\n.tw-h-64 { height: 16rem; }\n\n/* Min/Max Width */\n.tw-min-w-0 { min-width: 0px; }\n.tw-min-w-full { min-width: 100%; }\n.tw-max-w-none { max-width: none; }\n.tw-max-w-xs { max-width: 20rem; }\n.tw-max-w-sm { max-width: 24rem; }\n.tw-max-w-md { max-width: 28rem; }\n.tw-max-w-lg { max-width: 32rem; }\n.tw-max-w-xl { max-width: 36rem; }\n.tw-max-w-2xl { max-width: 42rem; }\n.tw-max-w-3xl { max-width: 48rem; }\n.tw-max-w-4xl { max-width: 56rem; }\n.tw-max-w-5xl { max-width: 64rem; }\n.tw-max-w-6xl { max-width: 72rem; }\n.tw-max-w-7xl { max-width: 80rem; }\n.tw-max-w-full { max-width: 100%; }\n\n/* Min/Max Height */\n.tw-min-h-0 { min-height: 0px; }\n.tw-min-h-full { min-height: 100%; }\n.tw-min-h-screen { min-height: 100vh; }\n.tw-max-h-full { max-height: 100%; }\n.tw-max-h-screen { max-height: 100vh; }\n\n/* Font Family */\n.tw-font-sans { font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; }\n.tw-font-serif { font-family: ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif; }\n.tw-font-mono { font-family: ui-monospace, SFMono-Regular, \"SF Mono\", Consolas, \"Liberation Mono\", Menlo, monospace; }\n\n/* Font Size */\n.tw-text-xs { font-size: 0.75rem; line-height: 1rem; }\n.tw-text-sm { font-size: 0.875rem; line-height: 1.25rem; }\n.tw-text-base { font-size: 1rem; line-height: 1.5rem; }\n.tw-text-lg { font-size: 1.125rem; line-height: 1.75rem; }\n.tw-text-xl { font-size: 1.25rem; line-height: 1.75rem; }\n.tw-text-2xl { font-size: 1.5rem; line-height: 2rem; }\n.tw-text-3xl { font-size: 1.875rem; line-height: 2.25rem; }\n.tw-text-4xl { font-size: 2.25rem; line-height: 2.5rem; }\n.tw-text-5xl { font-size: 3rem; line-height: 1; }\n.tw-text-6xl { font-size: 3.75rem; line-height: 1; }\n\n/* Font Weight */\n.tw-font-thin { font-weight: 100; }\n.tw-font-extralight { font-weight: 200; }\n.tw-font-light { font-weight: 300; }\n.tw-font-normal { font-weight: 400; }\n.tw-font-medium { font-weight: 500; }\n.tw-font-semibold { font-weight: 600; }\n.tw-font-bold { font-weight: 700; }\n.tw-font-extrabold { font-weight: 800; }\n.tw-font-black { font-weight: 900; }\n\n/* Text Align */\n.tw-text-left { text-align: left; }\n.tw-text-center { text-align: center; }\n.tw-text-right { text-align: right; }\n.tw-text-justify { text-align: justify; }\n\n/* Text Color */\n.tw-text-transparent { color: transparent; }\n.tw-text-current { color: currentColor; }\n.tw-text-black { color: rgb(0 0 0); }\n.tw-text-white { color: rgb(255 255 255); }\n.tw-text-gray-50 { color: rgb(249 250 251); }\n.tw-text-gray-100 { color: rgb(243 244 246); }\n.tw-text-gray-200 { color: rgb(229 231 235); }\n.tw-text-gray-300 { color: rgb(209 213 219); }\n.tw-text-gray-400 { color: rgb(156 163 175); }\n.tw-text-gray-500 { color: rgb(107 114 128); }\n.tw-text-gray-600 { color: rgb(75 85 99); }\n.tw-text-font-color { color: rgb(55 65 81); }\n.tw-text-gray-800 { color: rgb(31 41 55); }\n.tw-text-gray-900 { color: rgb(17 24 39); }\n.tw-text-red-500 { color: rgb(239 68 68); }\n.tw-text-red-600 { color: rgb(220 38 38); }\n.tw-text-blue-500 { color: rgb(59 130 246); }\n.tw-text-blue-600 { color: rgb(37 99 235); }\n.tw-text-green-500 { color: rgb(34 197 94); }\n.tw-text-green-600 { color: rgb(22 163 74); }\n.tw-text-yellow-500 { color: rgb(234 179 8); }\n.tw-text-purple-500 { color: rgb(168 85 247); }\n.tw-text-purple-600 { color: rgb(147 51 234); }\n\n/* Background Color */\n.tw-bg-transparent { background-color: transparent; }\n.tw-bg-current { background-color: currentColor; }\n.tw-bg-black { background-color: rgb(0 0 0); }\n.tw-bg-white { background-color: rgb(255 255 255); }\n.tw-bg-gray-50 { background-color: rgb(249 250 251); }\n.tw-bg-gray-100 { background-color: rgb(243 244 246); }\n.tw-bg-gray-200 { background-color: rgb(229 231 235); }\n.tw-bg-gray-300 { background-color: rgb(209 213 219); }\n.tw-bg-gray-400 { background-color: rgb(156 163 175); }\n.tw-bg-gray-500 { background-color: rgb(107 114 128); }\n.tw-bg-gray-600 { background-color: rgb(75 85 99); }\n.tw-bg-gray-700 { background-color: rgb(55 65 81); }\n.tw-bg-gray-800 { background-color: rgb(31 41 55); }\n.tw-bg-gray-900 { background-color: rgb(17 24 39); }\n.tw-bg-red-500 { background-color: rgb(239 68 68); }\n.tw-bg-red-600 { background-color: rgb(220 38 38); }\n.tw-bg-blue-500 { background-color: rgb(59 130 246); }\n.tw-bg-blue-600 { background-color: rgb(37 99 235); }\n.tw-bg-green-500 { background-color: rgb(34 197 94); }\n.tw-bg-green-600 { background-color: rgb(22 163 74); }\n.tw-bg-yellow-500 { background-color: rgb(234 179 8); }\n.tw-bg-purple-500 { background-color: rgb(168 85 247); }\n.tw-bg-purple-600 { background-color: rgb(147 51 234); }\n\n/* Background Gradient */\n.tw-bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }\n.tw-bg-gradient-to-l { background-image: linear-gradient(to left, var(--tw-gradient-stops)); }\n.tw-bg-gradient-to-t { background-image: linear-gradient(to top, var(--tw-gradient-stops)); }\n.tw-bg-gradient-to-b { background-image: linear-gradient(to bottom, var(--tw-gradient-stops)); }\n.tw-bg-gradient-to-tr { background-image: linear-gradient(to top right, var(--tw-gradient-stops)); }\n.tw-bg-gradient-to-tl { background-image: linear-gradient(to top left, var(--tw-gradient-stops)); }\n.tw-bg-gradient-to-br { background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)); }\n.tw-bg-gradient-to-bl { background-image: linear-gradient(to bottom left, var(--tw-gradient-stops)); }\n\n.tw-from-blue-500 { --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position); --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }\n.tw-from-blue-600 { --tw-gradient-from: #2563eb var(--tw-gradient-from-position); --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }\n.tw-to-purple-500 { --tw-gradient-to: #a855f7 var(--tw-gradient-to-position); }\n.tw-to-purple-600 { --tw-gradient-to: #9333ea var(--tw-gradient-to-position); }\n\n/* Border */\n.tw-border-0 { border-width: 0px; }\n.tw-border { border-width: 1px; }\n.tw-border-2 { border-width: 2px; }\n.tw-border-4 { border-width: 4px; }\n.tw-border-8 { border-width: 8px; }\n\n.tw-border-t { border-top-width: 1px; }\n.tw-border-r { border-right-width: 1px; }\n.tw-border-b { border-bottom-width: 1px; }\n.tw-border-l { border-left-width: 1px; }\n\n/* Border Color */\n.tw-border-transparent { border-color: transparent; }\n.tw-border-current { border-color: currentColor; }\n.tw-border-black { border-color: rgb(0 0 0); }\n.tw-border-white { border-color: rgb(255 255 255); }\n.tw-border-gray-200 { border-color: rgb(229 231 235); }\n.tw-border-gray-300 { border-color: rgb(209 213 219); }\n.tw-border-gray-400 { border-color: rgb(156 163 175); }\n.tw-border-gray-500 { border-color: rgb(107 114 128); }\n.tw-border-blue-500 { border-color: rgb(59 130 246); }\n.tw-border-red-500 { border-color: rgb(239 68 68); }\n\n/* Border Radius */\n.tw-rounded-none { border-radius: 0px; }\n.tw-rounded-sm { border-radius: 0.125rem; }\n.tw-rounded { border-radius: 0.25rem; }\n.tw-rounded-md { border-radius: 0.375rem; }\n.tw-rounded-lg { border-radius: 0.5rem; }\n.tw-rounded-xl { border-radius: 0.75rem; }\n.tw-rounded-2xl { border-radius: 1rem; }\n.tw-rounded-3xl { border-radius: 1.5rem; }\n.tw-rounded-full { border-radius: 9999px; }\n\n/* Box Shadow */\n.tw-shadow-sm { box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05); }\n.tw-shadow { box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1); }\n.tw-shadow-md { box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); }\n.tw-shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); }\n.tw-shadow-xl { box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1); }\n.tw-shadow-2xl { box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25); }\n.tw-shadow-none { box-shadow: 0 0 #0000; }\n\n/* Opacity */\n.tw-opacity-0 { opacity: 0; }\n.tw-opacity-5 { opacity: 0.05; }\n.tw-opacity-10 { opacity: 0.1; }\n.tw-opacity-20 { opacity: 0.2; }\n.tw-opacity-25 { opacity: 0.25; }\n.tw-opacity-30 { opacity: 0.3; }\n.tw-opacity-40 { opacity: 0.4; }\n.tw-opacity-50 { opacity: 0.5; }\n.tw-opacity-60 { opacity: 0.6; }\n.tw-opacity-70 { opacity: 0.7; }\n.tw-opacity-75 { opacity: 0.75; }\n.tw-opacity-80 { opacity: 0.8; }\n.tw-opacity-90 { opacity: 0.9; }\n.tw-opacity-95 { opacity: 0.95; }\n.tw-opacity-100 { opacity: 1; }\n\n/* Cursor */\n.tw-cursor-auto { cursor: auto; }\n.tw-cursor-default { cursor: default; }\n.tw-cursor-pointer { cursor: pointer; }\n.tw-cursor-wait { cursor: wait; }\n.tw-cursor-text { cursor: text; }\n.tw-cursor-move { cursor: move; }\n.tw-cursor-help { cursor: help; }\n.tw-cursor-not-allowed { cursor: not-allowed; }\n\n/* Transition */\n.tw-transition-none { transition-property: none; }\n.tw-transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }\n.tw-transition { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }\n.tw-transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }\n.tw-transition-opacity { transition-property: opacity; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }\n.tw-transition-shadow { transition-property: box-shadow; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }\n.tw-transition-transform { transition-property: transform; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }\n\n/* Duration */\n.tw-duration-75 { transition-duration: 75ms; }\n.tw-duration-100 { transition-duration: 100ms; }\n.tw-duration-150 { transition-duration: 150ms; }\n.tw-duration-200 { transition-duration: 200ms; }\n.tw-duration-300 { transition-duration: 300ms; }\n.tw-duration-500 { transition-duration: 500ms; }\n.tw-duration-700 { transition-duration: 700ms; }\n.tw-duration-1000 { transition-duration: 1000ms; }\n\n/* Transform */\n.tw-transform { transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }\n.tw--translate-y-1\\/2 { --tw-translate-y: -50%; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }\n\n/* Hover States */\n.tw-hover\\:tw-bg-gray-50:hover { background-color: rgb(249 250 251); }\n.tw-hover\\:tw-bg-gray-100:hover { background-color: rgb(243 244 246); }\n.tw-hover\\:tw-bg-blue-600:hover { background-color: rgb(37 99 235); }\n.tw-hover\\:tw-text-gray-900:hover { color: rgb(17 24 39); }\n.tw-hover\\:tw-text-blue-600:hover { color: rgb(37 99 235); }\n\n/* Focus States */\n.tw-focus\\:tw-ring-2:focus { --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color); box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000); }\n.tw-focus\\:tw-ring-blue-500:focus { --tw-ring-opacity: 1; --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity)); }\n.tw-focus\\:tw-border-transparent:focus { border-color: transparent; }\n\n/* Container */\n.tw-container { width: 100%; }\n@media (min-width: 640px) { .tw-container { max-width: 640px; } }\n@media (min-width: 768px) { .tw-container { max-width: 768px; } }\n@media (min-width: 1024px) { .tw-container { max-width: 1024px; } }\n@media (min-width: 1280px) { .tw-container { max-width: 1280px; } }\n@media (min-width: 1536px) { .tw-container { max-width: 1536px; } }\n\n/* Responsive Utilities */\n@media (min-width: 640px) {\n  .tw-sm\\:tw-w-64 { width: 16rem; }\n  .tw-sm\\:tw-text-lg { font-size: 1.125rem; line-height: 1.75rem; }\n}\n\n@media (min-width: 768px) {\n  .tw-md\\:tw-flex { display: flex; }\n  .tw-md\\:tw-hidden { display: none; }\n  .tw-md\\:tw-w-1\\/2 { width: 50%; }\n}\n\n@media (min-width: 1024px) {\n  .tw-lg\\:tw-flex { display: flex; }\n  .tw-lg\\:tw-hidden { display: none; }\n  .tw-lg\\:tw-w-1\\/3 { width: 33.333333%; }\n}\n\n  </style>\n  <style>\n    /* Base styles */\n    * { box-sizing: border-box; }\n    html {\n      height: 100%;\n    }\n    body {\n      margin: 0;\n      padding: 20px;\n      font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif;\n      line-height: 1.6;\n      background: white;\n      color: #374151;\n    }\n\n    /* Tailwind CSS classes with tw- prefix */\n    .tw-bg-blue-500 { background-color: rgb(59 130 246) !important; }\n    .tw-bg-blue-600 { background-color: rgb(37 99 235) !important; }\n    .tw-bg-purple-600 { background-color: rgb(147 51 234) !important; }\n    .tw-bg-white { background-color: rgb(255 255 255) !important; }\n    .tw-bg-gray-50 { background-color: rgb(249 250 251) !important; }\n    .tw-bg-gray-100 { background-color: rgb(243 244 246) !important; }\n    .tw-bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)) !important; }\n    .tw-from-blue-600 { --tw-gradient-from: rgb(37 99 235); --tw-gradient-to: rgb(37 99 235 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important; }\n    .tw-to-purple-600 { --tw-gradient-to: rgb(147 51 234) !important; }\n\n    .tw-text-white { color: rgb(255 255 255) !important; }\n    .tw-text-blue-600 { color: rgb(37 99 235) !important; }\n    .tw-text-gray-600 { color: rgb(75 85 99) !important; }\n    .tw-text-gray-800 { color: rgb(31 41 55) !important; }\n    .tw-text-gray-900 { color: rgb(17 24 39) !important; }\n\n    .tw-p-4 { padding: 1rem !important; }\n    .tw-p-6 { padding: 1.5rem !important; }\n    .tw-p-8 { padding: 2rem !important; }\n    .tw-px-4 { padding-left: 1rem; padding-right: 1rem !important; }\n    .tw-px-6 { padding-left: 1.5rem; padding-right: 1.5rem !important; }\n    .tw-px-8 { padding-left: 2rem; padding-right: 2rem !important; }\n    .tw-py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem !important; }\n    .tw-py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem !important; }\n    .tw-py-4 { padding-top: 1rem; padding-bottom: 1rem !important; }\n    .tw-py-16 { padding-top: 4rem; padding-bottom: 4rem !important; }\n    .tw-py-20 { padding-top: 5rem; padding-bottom: 5rem !important; }\n\n    .tw-m-4 { margin: 1rem !important; }\n    .tw-mx-auto { margin-left: auto; margin-right: auto !important; }\n    .tw-mt-2 { margin-top: 0.5rem !important; }\n    .tw-mt-4 { margin-top: 1rem !important; }\n    .tw-mb-4 { margin-bottom: 1rem !important; }\n    .tw-mb-6 { margin-bottom: 1.5rem !important; }\n    .tw-mb-8 { margin-bottom: 2rem !important; }\n\n    .tw-rounded { border-radius: 0.25rem !important; }\n    .tw-rounded-lg { border-radius: 0.5rem !important; }\n    .tw-rounded-xl { border-radius: 0.75rem !important; }\n\n    .tw-text-sm { font-size: 0.875rem; line-height: 1.25rem !important; }\n    .tw-text-base { font-size: 1rem; line-height: 1.5rem !important; }\n    .tw-text-lg { font-size: 1.125rem; line-height: 1.75rem !important; }\n    .tw-text-xl { font-size: 1.25rem; line-height: 1.75rem !important; }\n    .tw-text-2xl { font-size: 1.5rem; line-height: 2rem !important; }\n    .tw-text-3xl { font-size: 1.875rem; line-height: 2.25rem !important; }\n    .tw-text-4xl { font-size: 2.25rem; line-height: 2.5rem !important; }\n    .tw-text-5xl { font-size: 3rem; line-height: 1 !important; }\n\n    .tw-font-medium { font-weight: 500 !important; }\n    .tw-font-semibold { font-weight: 600 !important; }\n    .tw-font-bold { font-weight: 700 !important; }\n\n    .tw-text-center { text-align: center !important; }\n    .tw-text-left { text-align: left !important; }\n\n    .tw-flex { display: flex !important; }\n    .tw-grid { display: grid !important; }\n    .tw-block { display: block !important; }\n    .tw-inline-block { display: inline-block !important; }\n    .tw-hidden { display: none !important; }\n\n    .tw-items-center { align-items: center !important; }\n    .tw-justify-center { justify-content: center !important; }\n    .tw-justify-between { justify-content: space-between !important; }\n\n    .tw-w-full { width: 100% !important; }\n    .tw-w-auto { width: auto !important; }\n    .tw-h-8 { height: 2rem !important; }\n    .tw-h-auto { height: auto !important; }\n\n    .tw-container { width: 100%; margin-left: auto; margin-right: auto !important; }\n    @media (min-width: 640px) { .tw-container { max-width: 640px !important; } }\n    @media (min-width: 768px) { .tw-container { max-width: 768px !important; } }\n    @media (min-width: 1024px) { .tw-container { max-width: 1024px !important; } }\n    @media (min-width: 1280px) { .tw-container { max-width: 1280px !important; } }\n\n    .tw-shadow-sm { box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05) !important; }\n    .tw-shadow { box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1) !important; }\n    .tw-shadow-md { box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important; }\n    .tw-shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important; }\n\n    .tw-border { border-width: 1px !important; }\n    .tw-border-gray-200 { border-color: rgb(229 231 235) !important; }\n    .tw-border-gray-300 { border-color: rgb(209 213 219) !important; }\n\n    .tw-transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms !important; }\n    .tw-transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms !important; }\n\n    .tw-hover\\:tw-bg-gray-100:hover { background-color: rgb(243 244 246) !important; }\n    .tw-hover\\:tw-text-blue-600:hover { color: rgb(37 99 235) !important; }\n\n    /* Grid classes */\n    .tw-grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }\n    .tw-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }\n    .tw-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }\n    .tw-gap-4 { gap: 1rem !important; }\n    .tw-gap-6 { gap: 1.5rem !important; }\n    .tw-gap-8 { gap: 2rem !important; }\n\n    @media (min-width: 768px) {\n      .tw-md\\:tw-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }\n      .tw-md\\:tw-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }\n      .tw-md\\:tw-block { display: block !important; }\n    }\n\n    /* Custom component styles */\n    .main-dev {\nmargin: \"auto\";\n}\n\n  </style>\n</head>\n<body>\n  <div><!DOCTYPE html>\n<html lang=\"en\">\n  <head> \n    <meta charset=\"UTF-8\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n    <title>Document</title>\n    <style>\n      @font-face {\n        font-family: Inter;\n        font-style: normal;\n        font-weight: 400;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n      @font-face {\n        font-family: Inter;\n        font-style: normal;\n        font-weight: 500;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n      @font-face {\n        font-family: Inter;\n        font-style: normal;\n        font-weight: 700;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n      @font-face {\n        font-family: Phudu;\n        font-style: normal;\n        font-weight: 600;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n      @font-face {\n        font-family: Phudu;\n        font-style: normal;\n        font-weight: 700;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n    </style>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script>\n      tailwind.config = {\n        content: [\"./*.html\"],\n        // prefix: \"dw-\",\n        theme: {\n          extend: {\n            colors: {\n              body: {\n                bg: \"#ffffff\", // --body-bg-color\n                text: \"#333333\", // --body-text-color\n              },\n              theme: {\n                main: \"#d5232b\", // --theme-main-color\n                mainColorShade: \"#C41421\",\n                hoverBox: \"rgba(82, 164, 206, 0.43)\", // --theme-box-hover-color\n                cardBorder: \"#e0e0e0\",\n                cardBg: \"#fafafa\",\n                divider: \"#eee\",\n                lowOpacityTextColor: \"#111827\",\n              },\n            },\n            fontFamily: {\n              heading: [\"Phudu\", \"sans-serif\"], // --theme-heading-font\n              text: [\"Inter\", \"sans-serif\"], // --theme-text-font\n            },\n            screens: {\n              xs: \"200px\",\n              smx: \"376px\",\n              smm: \"567px\",\n              mdx: \"768px\",\n              mdl: \"993px\",\n              lgx: \"1171px\",\n              lgs: \"1221px\",\n              lgm: \"1281px\",\n              lgl: \"1361px\",\n              xlx: \"1400px\",\n              xl1: \"1441px\",\n              xl2: \"1600px\",\n              xxl: \"1900px\",\n            },\n          },\n        },\n        plugins: [],\n      };\n    </script>\n  </head>\n  <body class=\"bg-body-bg text-body-text font-text\">\n    <div\n      class=\"my-16 mx-auto w-full px-6 xl1:max-w-[1440px] lgl:max-w-[1300px] lgl:px-6 lgs:max-w-[1200px] lgx:max-w-[1100px] mdl:max-w-[1080px] mdl:px-6 mdx:max-w-[1000px] smm:max-w-[940px] smm:px-6 smx:max-w-[700px] smx:px-6\"\n    >\n      <div class=\"flex justify-center\">\n        <h2\n          class=\"xl1:mx-[395px] font-semibold lgm:text-[48px] lgx:text-[50px] mdx:text-[46px] text-[26px] xl1:leading-[60px] lgx:leading-[46px] uppercase text-center pb-[25px] font-heading\"\n        >\n          ${Title}\n        </h2>\n      </div>\n      <div\n        class=\"grid grid-cols-1 mdx:grid-cols-2 lgs:grid-cols-3 mdx:gap-8 gap-4 xl1:gap-[50px] mb-4\"\n      >\n        <div\n          class=\"border border-theme-cardBorder rounded-[32px] bg-theme-cardBg xl1:p-[3rem_2rem_1.5rem] p-[2.5rem_1rem_1rem] min-h-full relative\"\n        >\n          <span\n            class=\"absolute z-10 bg-body-bg text-theme-lowOpacityTextColor font-normal border shadow-sm text-[14px] px-[15px] py-[8px] ml-[10px] -translate-y-[20px] rounded-full\"\n          >\n            ${Amount}\n          </span>\n          <img\n            src=\"https://busrentalcompanyclifton.com/wp-content/uploads/50-passenger-charter-bus-clifton.jpeg\"\n            class=\"rounded-[16px] w-full h-auto\"\n            alt=\"50 passenger charter bus clifton\"\n          />\n          <img\n            src=\"https://busrentalcompanyclifton.com/wp-content/uploads/50-passenger-charter-bus-interior-clifton.jpeg\"\n            class=\"rounded-[16px] w-full h-auto mt-4\"\n            alt=\"50 passenger charter bus interior clifton\"\n          />\n          <h3\n            class=\"font-semibold lgm:text-[24px] lgx:text-[22px] text-[20px] leading-[32px] uppercase tracking-[-0.176px] text-center pt-[32px] pb-[25px] font-heading mb-2\"\n          >\n            <a\n              href=\"https://busrentalcompanyclifton.com/50-passenger-charter-bus-rental/\"\n            >\n              ${link_span}\n            </a>\n          </h3>\n          <div class=\"flex justify-center gap-4 pb-[5px]\">\n            <a\n              href=\"https://busrentalcompanyclifton.com/50-passenger-charter-bus-rental/\"\n              class=\"bg-body-text text-white border-2 border-body-text rounded-full px-[16px] py-[13px] text-[18px] leading-[24px] font-medium tracking-[-0.24px] hover:border-theme-main hover:bg-theme-main\"\n            >\n              ${view_link}\n            </a>\n            <a\n              href=\"/get-quote/\"\n              class=\"bg-theme-main text-white border-2 border-theme-main rounded-full px-[16px] py-[13px] inline-flex items-center justify-center text-[18px] leading-[24px] font-medium tracking-[-0.24px]\"\n            >\n              ${Get_quote}\n            </a>\n          </div>\n        </div>\n        \n      </div>\n    </div>\n    <!-- <div\n      class=\"mx-auto w-full xl1:max-w-[1440px] lgl:max-w-[1300px] lgs:max-w-[1200px] lgx:max-w-[1100px] mdl:max-w-[1080px] mdx:max-w-[1000px] smm:max-w-[940px] smx:max-w-[700px]\"\n    ></div> -->\n  </body>\n</html></div>\n<div><div data-component=\"section Template \"><section class=\"px-6 py-12 bg-gray-50\">\n  <div class=\"max-w-6xl mx-auto\">\n    <header class=\"text-center mb-10\">\n      <h2 class=\"text-3xl font-extrabold text-gray-800\">${Title}</h2>\n      <p class=\"mt-2 text-gray-600 max-w-2xl mx-auto\">\n        ${description}\n      </p>\n    </header>\n\n    <div class=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8\">\n      {{ITEMS}}\n    </div>\n\n    <footer class=\"mt-12 text-center\">\n      <a href={${ViewButtonLink}} class=\"text-indigo-600 hover:underline font-medium\">${ViewButton}</a>\n    </footer>\n  </div>\n</section></div></div>\n\n  <script>\n    try {\n      // buses.js\n\nconst busOptions = [\n  {\n    title: \"50 Passenger Charter Bus\",\n    price: \"$180 – $500+ per hour\",\n    image1: \"https://busrentalcompanyclifton.com/wp-content/uploads/50-passenger-charter-bus-clifton.jpeg\",\n    image2: \"https://busrentalcompanyclifton.com/wp-content/uploads/50-passenger-charter-bus-interior-clifton.jpeg\",\n    link: \"https://busrentalcompanyclifton.com/50-passenger-charter-bus-rental/\"\n  },\n  {\n    title: \"40 Passenger Party Bus\",\n    price: \"$150 – $450+ per hour\",\n    image1: \"https://busrentalcompanyclifton.com/wp-content/uploads/25-passenger-minibus-clifton.jpeg\",\n    image2: \"https://busrentalcompanyclifton.com/wp-content/uploads/15-passenger-minibus-interior-clifton.jpeg\",\n    link: \"https://busrentalcompanyclifton.com/40-passenger-party-bus-rental/\"\n  },\n  {\n    title: \"Sprinter Van Rental\",\n    price: \"$80 – $150+ per hour\",\n    image1: \"https://busrentalcompanyclifton.com/wp-content/uploads/sprinter-van-with-driver-clifton.jpeg\",\n    image2: \"https://busrentalcompanyclifton.com/wp-content/uploads/sprinter-van-with-driver-interior-clifton.jpeg\",\n    link: \"https://busrentalcompanyclifton.com/sprinter-van-rental/\"\n  }\n];\n\nwindow.addEventListener(\"DOMContentLoaded\", () => {\n  const container = document.querySelector(\".grid\");\n\n  busOptions.forEach(option => {\n    const card = `\n      <div class=\"border border-theme-cardBorder rounded-[32px] bg-theme-cardBg xl1:p-[3rem_2rem_1.5rem] p-[2.5rem_1rem_1rem] min-h-full relative\">\n        <span class=\"absolute z-10 bg-body-bg text-theme-lowOpacityTextColor font-normal border shadow-sm text-[14px] px-[15px] py-[8px] ml-[10px] -translate-y-[20px] rounded-full\">\n          ${option.price}\n        </span>\n        <img src=\"${option.image1}\" class=\"rounded-[16px] w-full h-auto\" alt=\"${option.title.toLowerCase()}\">\n        <img src=\"${option.image2}\" class=\"rounded-[16px] w-full h-auto mt-4\" alt=\"${option.title.toLowerCase()} interior\">\n        <h3 class=\"font-semibold lgm:text-[24px] lgx:text-[22px] text-[20px] leading-[32px] uppercase tracking-[-0.176px] text-center pt-[32px] pb-[25px] font-heading mb-2\">\n          <a href=\"${option.link}\">${option.title}</a>\n        </h3>\n        <div class=\"flex justify-center gap-4 pb-[5px]\">\n          <a href=\"${option.link}\" class=\"bg-body-text text-white border-2 border-body-text rounded-full px-[16px] py-[13px] text-[18px] leading-[24px] font-medium tracking-[-0.24px] hover:border-theme-main hover:bg-theme-main\">\n            View This Bus\n          </a>\n          <a href=\"/get-quote/\" class=\"bg-theme-main text-white border-2 border-theme-main rounded-full px-[16px] py-[13px] inline-flex items-center justify-center text-[18px] leading-[24px] font-medium tracking-[-0.24px]\">\n            Get a Quote\n          </a>\n        </div>\n      </div>\n    `;\n\n    container.insertAdjacentHTML(\"beforeend\", card);\n  });\n});\n    } catch(e) {\n      console.error('JavaScript error:', e);\n    }\n  </script>\n</body>\n</html>", "pageComponentList": [{"name": "Test componenet", "category_id": "meffczkptv4fdqqme0j", "html_content": "<!DOCTYPE html>\n<html lang=\"en\">\n  <head> \n    <meta charset=\"UTF-8\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n    <title>Document</title>\n    <style>\n      @font-face {\n        font-family: Inter;\n        font-style: normal;\n        font-weight: 400;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n      @font-face {\n        font-family: Inter;\n        font-style: normal;\n        font-weight: 500;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n      @font-face {\n        font-family: Inter;\n        font-style: normal;\n        font-weight: 700;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n      @font-face {\n        font-family: Phudu;\n        font-style: normal;\n        font-weight: 600;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n      @font-face {\n        font-family: Phudu;\n        font-style: normal;\n        font-weight: 700;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n    </style>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script>\n      tailwind.config = {\n        content: [\"./*.html\"],\n        // prefix: \"dw-\",\n        theme: {\n          extend: {\n            colors: {\n              body: {\n                bg: \"#ffffff\", // --body-bg-color\n                text: \"#333333\", // --body-text-color\n              },\n              theme: {\n                main: \"#d5232b\", // --theme-main-color\n                mainColorShade: \"#C41421\",\n                hoverBox: \"rgba(82, 164, 206, 0.43)\", // --theme-box-hover-color\n                cardBorder: \"#e0e0e0\",\n                cardBg: \"#fafafa\",\n                divider: \"#eee\",\n                lowOpacityTextColor: \"#111827\",\n              },\n            },\n            fontFamily: {\n              heading: [\"Phudu\", \"sans-serif\"], // --theme-heading-font\n              text: [\"Inter\", \"sans-serif\"], // --theme-text-font\n            },\n            screens: {\n              xs: \"200px\",\n              smx: \"376px\",\n              smm: \"567px\",\n              mdx: \"768px\",\n              mdl: \"993px\",\n              lgx: \"1171px\",\n              lgs: \"1221px\",\n              lgm: \"1281px\",\n              lgl: \"1361px\",\n              xlx: \"1400px\",\n              xl1: \"1441px\",\n              xl2: \"1600px\",\n              xxl: \"1900px\",\n            },\n          },\n        },\n        plugins: [],\n      };\n    </script>\n  </head>\n  <body class=\"bg-body-bg text-body-text font-text\">\n    <div\n      class=\"my-16 mx-auto w-full px-6 xl1:max-w-[1440px] lgl:max-w-[1300px] lgl:px-6 lgs:max-w-[1200px] lgx:max-w-[1100px] mdl:max-w-[1080px] mdl:px-6 mdx:max-w-[1000px] smm:max-w-[940px] smm:px-6 smx:max-w-[700px] smx:px-6\"\n    >\n      <div class=\"flex justify-center\">\n        <h2\n          class=\"xl1:mx-[395px] font-semibold lgm:text-[48px] lgx:text-[50px] mdx:text-[46px] text-[26px] xl1:leading-[60px] lgx:leading-[46px] uppercase text-center pb-[25px] font-heading\"\n        >\n          ${Title}\n        </h2>\n      </div>\n      <div\n        class=\"grid grid-cols-1 mdx:grid-cols-2 lgs:grid-cols-3 mdx:gap-8 gap-4 xl1:gap-[50px] mb-4\"\n      >\n        <div\n          class=\"border border-theme-cardBorder rounded-[32px] bg-theme-cardBg xl1:p-[3rem_2rem_1.5rem] p-[2.5rem_1rem_1rem] min-h-full relative\"\n        >\n          <span\n            class=\"absolute z-10 bg-body-bg text-theme-lowOpacityTextColor font-normal border shadow-sm text-[14px] px-[15px] py-[8px] ml-[10px] -translate-y-[20px] rounded-full\"\n          >\n            ${Amount}\n          </span>\n          <img\n            src=\"https://busrentalcompanyclifton.com/wp-content/uploads/50-passenger-charter-bus-clifton.jpeg\"\n            class=\"rounded-[16px] w-full h-auto\"\n            alt=\"50 passenger charter bus clifton\"\n          />\n          <img\n            src=\"https://busrentalcompanyclifton.com/wp-content/uploads/50-passenger-charter-bus-interior-clifton.jpeg\"\n            class=\"rounded-[16px] w-full h-auto mt-4\"\n            alt=\"50 passenger charter bus interior clifton\"\n          />\n          <h3\n            class=\"font-semibold lgm:text-[24px] lgx:text-[22px] text-[20px] leading-[32px] uppercase tracking-[-0.176px] text-center pt-[32px] pb-[25px] font-heading mb-2\"\n          >\n            <a\n              href=\"https://busrentalcompanyclifton.com/50-passenger-charter-bus-rental/\"\n            >\n              ${link_span}\n            </a>\n          </h3>\n          <div class=\"flex justify-center gap-4 pb-[5px]\">\n            <a\n              href=\"https://busrentalcompanyclifton.com/50-passenger-charter-bus-rental/\"\n              class=\"bg-body-text text-white border-2 border-body-text rounded-full px-[16px] py-[13px] text-[18px] leading-[24px] font-medium tracking-[-0.24px] hover:border-theme-main hover:bg-theme-main\"\n            >\n              ${view_link}\n            </a>\n            <a\n              href=\"/get-quote/\"\n              class=\"bg-theme-main text-white border-2 border-theme-main rounded-full px-[16px] py-[13px] inline-flex items-center justify-center text-[18px] leading-[24px] font-medium tracking-[-0.24px]\"\n            >\n              ${Get_quote}\n            </a>\n          </div>\n        </div>\n        \n      </div>\n    </div>\n    <!-- <div\n      class=\"mx-auto w-full xl1:max-w-[1440px] lgl:max-w-[1300px] lgs:max-w-[1200px] lgx:max-w-[1100px] mdl:max-w-[1080px] mdx:max-w-[1000px] smm:max-w-[940px] smx:max-w-[700px]\"\n    ></div> -->\n  </body>\n</html>", "css_content": "", "js_content": "// buses.js\n\nconst busOptions = [\n  {\n    title: \"50 Passenger Charter Bus\",\n    price: \"$180 – $500+ per hour\",\n    image1: \"https://busrentalcompanyclifton.com/wp-content/uploads/50-passenger-charter-bus-clifton.jpeg\",\n    image2: \"https://busrentalcompanyclifton.com/wp-content/uploads/50-passenger-charter-bus-interior-clifton.jpeg\",\n    link: \"https://busrentalcompanyclifton.com/50-passenger-charter-bus-rental/\"\n  },\n  {\n    title: \"40 Passenger Party Bus\",\n    price: \"$150 – $450+ per hour\",\n    image1: \"https://busrentalcompanyclifton.com/wp-content/uploads/25-passenger-minibus-clifton.jpeg\",\n    image2: \"https://busrentalcompanyclifton.com/wp-content/uploads/15-passenger-minibus-interior-clifton.jpeg\",\n    link: \"https://busrentalcompanyclifton.com/40-passenger-party-bus-rental/\"\n  },\n  {\n    title: \"Sprinter Van Rental\",\n    price: \"$80 – $150+ per hour\",\n    image1: \"https://busrentalcompanyclifton.com/wp-content/uploads/sprinter-van-with-driver-clifton.jpeg\",\n    image2: \"https://busrentalcompanyclifton.com/wp-content/uploads/sprinter-van-with-driver-interior-clifton.jpeg\",\n    link: \"https://busrentalcompanyclifton.com/sprinter-van-rental/\"\n  }\n];\n\nwindow.addEventListener(\"DOMContentLoaded\", () => {\n  const container = document.querySelector(\".grid\");\n\n  busOptions.forEach(option => {\n    const card = `\n      <div class=\"border border-theme-cardBorder rounded-[32px] bg-theme-cardBg xl1:p-[3rem_2rem_1.5rem] p-[2.5rem_1rem_1rem] min-h-full relative\">\n        <span class=\"absolute z-10 bg-body-bg text-theme-lowOpacityTextColor font-normal border shadow-sm text-[14px] px-[15px] py-[8px] ml-[10px] -translate-y-[20px] rounded-full\">\n          ${option.price}\n        </span>\n        <img src=\"${option.image1}\" class=\"rounded-[16px] w-full h-auto\" alt=\"${option.title.toLowerCase()}\">\n        <img src=\"${option.image2}\" class=\"rounded-[16px] w-full h-auto mt-4\" alt=\"${option.title.toLowerCase()} interior\">\n        <h3 class=\"font-semibold lgm:text-[24px] lgx:text-[22px] text-[20px] leading-[32px] uppercase tracking-[-0.176px] text-center pt-[32px] pb-[25px] font-heading mb-2\">\n          <a href=\"${option.link}\">${option.title}</a>\n        </h3>\n        <div class=\"flex justify-center gap-4 pb-[5px]\">\n          <a href=\"${option.link}\" class=\"bg-body-text text-white border-2 border-body-text rounded-full px-[16px] py-[13px] text-[18px] leading-[24px] font-medium tracking-[-0.24px] hover:border-theme-main hover:bg-theme-main\">\n            View This Bus\n          </a>\n          <a href=\"/get-quote/\" class=\"bg-theme-main text-white border-2 border-theme-main rounded-full px-[16px] py-[13px] inline-flex items-center justify-center text-[18px] leading-[24px] font-medium tracking-[-0.24px]\">\n            Get a Quote\n          </a>\n        </div>\n      </div>\n    `;\n\n    container.insertAdjacentHTML(\"beforeend\", card);\n  });\n});", "placeholders": ["Title", "Amount", "link_span", "view_link", "Get_quote"], "preview_image": "", "version": 1, "status": "draft", "tags": [], "thumbnail_url": "", "id": "mefpovzi039o3l96utxl", "created_at": "2025-08-17T13:18:27.054Z", "updated_at": "2025-08-20T08:38:59.479Z", "category_name": "Header", "category_color": "#de2b2b", "order": 1, "repeator": "single", "cssClass": "", "uniqueId": "mefpovzi039o3l96utxl-1755768869163-pc0cm2jrk"}, {"name": "section Template ", "category_id": "meffczkptv4fdqqme0j", "html_content": "<div data-component=\"section Template \"><section class=\"px-6 py-12 bg-gray-50\">\n  <div class=\"max-w-6xl mx-auto\">\n    <header class=\"text-center mb-10\">\n      <h2 class=\"text-3xl font-extrabold text-gray-800\">${Title}</h2>\n      <p class=\"mt-2 text-gray-600 max-w-2xl mx-auto\">\n        ${description}\n      </p>\n    </header>\n\n    <div class=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8\">\n      {{ITEMS}}\n    </div>\n\n    <footer class=\"mt-12 text-center\">\n      <a href={${ViewButtonLink}} class=\"text-indigo-600 hover:underline font-medium\">${ViewButton}</a>\n    </footer>\n  </div>\n</section></div>", "css_content": "", "js_content": "", "placeholders": ["Title", "description", "ViewButtonLink", "ViewButton"], "preview_image": "", "version": 1, "status": "draft", "tags": [], "thumbnail_url": "", "id": "mek665tinxez3nyp5hl", "created_at": "2025-08-20T16:10:51.510Z", "updated_at": "2025-08-21T03:15:28.712Z", "category_name": "Header", "category_color": "#de2b2b", "order": 0, "repeator": "repeat", "cssClass": "", "uniqueId": "mek665tinxez3nyp5hl-1755768868007-grydo7h7m", "repeatedComponentId": "mek69vn9gv7h2fbr8iw", "repeatedComponentName": ["ITEMS"], "repeatedComponent": {"name": "City Template ", "category_id": "meffczkptv4fdqqme0j", "html_content": "<div data-component=\"City Template \"><article class=\"rounded-xl border p-6 shadow-sm bg-white hover:shadow-md transition\">\n    <img src=\"${img-image}\" alt=\"\" class=\"w-full h-40 object-cover rounded-lg mb-4\" />\n    <h3 class=\"text-xl font-semibold text-gray-800\">${name}</h3>\n    <p class=\"text-sm text-gray-500 mb-2\">${state}</p>\n    <ul class=\"text-sm text-gray-600 space-y-1\">\n      <li><span class=\"font-medium\">Population:</span> ${population}</li>\n      <li><span class=\"font-medium\">Famous For:</span> ${famousFor}</li>\n    </ul>\n    <button class=\"mt-4 w-full bg-indigo-600 text-white py-2 rounded-lg hover:bg-indigo-700\">\n      Explore ${name}\n    </button>\n  </article></div>", "css_content": "", "js_content": "", "placeholders": ["img-image", "name", "state", "population", "famous<PERSON><PERSON>"], "preview_image": "", "version": 1, "status": "draft", "tags": [], "thumbnail_url": "", "id": "mek69vn9gv7h2fbr8iw", "created_at": "2025-08-20T16:13:44.949Z", "updated_at": "2025-08-21T09:34:05.060Z", "category_name": "Header", "category_color": "#de2b2b"}}], "components": [{"name": "Test componenet", "category_id": "meffczkptv4fdqqme0j", "html_content": "<!DOCTYPE html>\n<html lang=\"en\">\n  <head> \n    <meta charset=\"UTF-8\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n    <title>Document</title>\n    <style>\n      @font-face {\n        font-family: Inter;\n        font-style: normal;\n        font-weight: 400;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n      @font-face {\n        font-family: Inter;\n        font-style: normal;\n        font-weight: 500;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n      @font-face {\n        font-family: Inter;\n        font-style: normal;\n        font-weight: 700;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n      @font-face {\n        font-family: Phudu;\n        font-style: normal;\n        font-weight: 600;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n      @font-face {\n        font-family: Phudu;\n        font-style: normal;\n        font-weight: 700;\n        font-display: swap;\n        src: url(https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2)\n          format(\"woff2\");\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n      }\n    </style>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script>\n      tailwind.config = {\n        content: [\"./*.html\"],\n        // prefix: \"dw-\",\n        theme: {\n          extend: {\n            colors: {\n              body: {\n                bg: \"#ffffff\", // --body-bg-color\n                text: \"#333333\", // --body-text-color\n              },\n              theme: {\n                main: \"#d5232b\", // --theme-main-color\n                mainColorShade: \"#C41421\",\n                hoverBox: \"rgba(82, 164, 206, 0.43)\", // --theme-box-hover-color\n                cardBorder: \"#e0e0e0\",\n                cardBg: \"#fafafa\",\n                divider: \"#eee\",\n                lowOpacityTextColor: \"#111827\",\n              },\n            },\n            fontFamily: {\n              heading: [\"Phudu\", \"sans-serif\"], // --theme-heading-font\n              text: [\"Inter\", \"sans-serif\"], // --theme-text-font\n            },\n            screens: {\n              xs: \"200px\",\n              smx: \"376px\",\n              smm: \"567px\",\n              mdx: \"768px\",\n              mdl: \"993px\",\n              lgx: \"1171px\",\n              lgs: \"1221px\",\n              lgm: \"1281px\",\n              lgl: \"1361px\",\n              xlx: \"1400px\",\n              xl1: \"1441px\",\n              xl2: \"1600px\",\n              xxl: \"1900px\",\n            },\n          },\n        },\n        plugins: [],\n      };\n    </script>\n  </head>\n  <body class=\"bg-body-bg text-body-text font-text\">\n    <div\n      class=\"my-16 mx-auto w-full px-6 xl1:max-w-[1440px] lgl:max-w-[1300px] lgl:px-6 lgs:max-w-[1200px] lgx:max-w-[1100px] mdl:max-w-[1080px] mdl:px-6 mdx:max-w-[1000px] smm:max-w-[940px] smm:px-6 smx:max-w-[700px] smx:px-6\"\n    >\n      <div class=\"flex justify-center\">\n        <h2\n          class=\"xl1:mx-[395px] font-semibold lgm:text-[48px] lgx:text-[50px] mdx:text-[46px] text-[26px] xl1:leading-[60px] lgx:leading-[46px] uppercase text-center pb-[25px] font-heading\"\n        >\n          ${Title}\n        </h2>\n      </div>\n      <div\n        class=\"grid grid-cols-1 mdx:grid-cols-2 lgs:grid-cols-3 mdx:gap-8 gap-4 xl1:gap-[50px] mb-4\"\n      >\n        <div\n          class=\"border border-theme-cardBorder rounded-[32px] bg-theme-cardBg xl1:p-[3rem_2rem_1.5rem] p-[2.5rem_1rem_1rem] min-h-full relative\"\n        >\n          <span\n            class=\"absolute z-10 bg-body-bg text-theme-lowOpacityTextColor font-normal border shadow-sm text-[14px] px-[15px] py-[8px] ml-[10px] -translate-y-[20px] rounded-full\"\n          >\n            ${Amount}\n          </span>\n          <img\n            src=\"https://busrentalcompanyclifton.com/wp-content/uploads/50-passenger-charter-bus-clifton.jpeg\"\n            class=\"rounded-[16px] w-full h-auto\"\n            alt=\"50 passenger charter bus clifton\"\n          />\n          <img\n            src=\"https://busrentalcompanyclifton.com/wp-content/uploads/50-passenger-charter-bus-interior-clifton.jpeg\"\n            class=\"rounded-[16px] w-full h-auto mt-4\"\n            alt=\"50 passenger charter bus interior clifton\"\n          />\n          <h3\n            class=\"font-semibold lgm:text-[24px] lgx:text-[22px] text-[20px] leading-[32px] uppercase tracking-[-0.176px] text-center pt-[32px] pb-[25px] font-heading mb-2\"\n          >\n            <a\n              href=\"https://busrentalcompanyclifton.com/50-passenger-charter-bus-rental/\"\n            >\n              ${link_span}\n            </a>\n          </h3>\n          <div class=\"flex justify-center gap-4 pb-[5px]\">\n            <a\n              href=\"https://busrentalcompanyclifton.com/50-passenger-charter-bus-rental/\"\n              class=\"bg-body-text text-white border-2 border-body-text rounded-full px-[16px] py-[13px] text-[18px] leading-[24px] font-medium tracking-[-0.24px] hover:border-theme-main hover:bg-theme-main\"\n            >\n              ${view_link}\n            </a>\n            <a\n              href=\"/get-quote/\"\n              class=\"bg-theme-main text-white border-2 border-theme-main rounded-full px-[16px] py-[13px] inline-flex items-center justify-center text-[18px] leading-[24px] font-medium tracking-[-0.24px]\"\n            >\n              ${Get_quote}\n            </a>\n          </div>\n        </div>\n        \n      </div>\n    </div>\n    <!-- <div\n      class=\"mx-auto w-full xl1:max-w-[1440px] lgl:max-w-[1300px] lgs:max-w-[1200px] lgx:max-w-[1100px] mdl:max-w-[1080px] mdx:max-w-[1000px] smm:max-w-[940px] smx:max-w-[700px]\"\n    ></div> -->\n  </body>\n</html>", "css_content": "", "js_content": "// buses.js\n\nconst busOptions = [\n  {\n    title: \"50 Passenger Charter Bus\",\n    price: \"$180 – $500+ per hour\",\n    image1: \"https://busrentalcompanyclifton.com/wp-content/uploads/50-passenger-charter-bus-clifton.jpeg\",\n    image2: \"https://busrentalcompanyclifton.com/wp-content/uploads/50-passenger-charter-bus-interior-clifton.jpeg\",\n    link: \"https://busrentalcompanyclifton.com/50-passenger-charter-bus-rental/\"\n  },\n  {\n    title: \"40 Passenger Party Bus\",\n    price: \"$150 – $450+ per hour\",\n    image1: \"https://busrentalcompanyclifton.com/wp-content/uploads/25-passenger-minibus-clifton.jpeg\",\n    image2: \"https://busrentalcompanyclifton.com/wp-content/uploads/15-passenger-minibus-interior-clifton.jpeg\",\n    link: \"https://busrentalcompanyclifton.com/40-passenger-party-bus-rental/\"\n  },\n  {\n    title: \"Sprinter Van Rental\",\n    price: \"$80 – $150+ per hour\",\n    image1: \"https://busrentalcompanyclifton.com/wp-content/uploads/sprinter-van-with-driver-clifton.jpeg\",\n    image2: \"https://busrentalcompanyclifton.com/wp-content/uploads/sprinter-van-with-driver-interior-clifton.jpeg\",\n    link: \"https://busrentalcompanyclifton.com/sprinter-van-rental/\"\n  }\n];\n\nwindow.addEventListener(\"DOMContentLoaded\", () => {\n  const container = document.querySelector(\".grid\");\n\n  busOptions.forEach(option => {\n    const card = `\n      <div class=\"border border-theme-cardBorder rounded-[32px] bg-theme-cardBg xl1:p-[3rem_2rem_1.5rem] p-[2.5rem_1rem_1rem] min-h-full relative\">\n        <span class=\"absolute z-10 bg-body-bg text-theme-lowOpacityTextColor font-normal border shadow-sm text-[14px] px-[15px] py-[8px] ml-[10px] -translate-y-[20px] rounded-full\">\n          ${option.price}\n        </span>\n        <img src=\"${option.image1}\" class=\"rounded-[16px] w-full h-auto\" alt=\"${option.title.toLowerCase()}\">\n        <img src=\"${option.image2}\" class=\"rounded-[16px] w-full h-auto mt-4\" alt=\"${option.title.toLowerCase()} interior\">\n        <h3 class=\"font-semibold lgm:text-[24px] lgx:text-[22px] text-[20px] leading-[32px] uppercase tracking-[-0.176px] text-center pt-[32px] pb-[25px] font-heading mb-2\">\n          <a href=\"${option.link}\">${option.title}</a>\n        </h3>\n        <div class=\"flex justify-center gap-4 pb-[5px]\">\n          <a href=\"${option.link}\" class=\"bg-body-text text-white border-2 border-body-text rounded-full px-[16px] py-[13px] text-[18px] leading-[24px] font-medium tracking-[-0.24px] hover:border-theme-main hover:bg-theme-main\">\n            View This Bus\n          </a>\n          <a href=\"/get-quote/\" class=\"bg-theme-main text-white border-2 border-theme-main rounded-full px-[16px] py-[13px] inline-flex items-center justify-center text-[18px] leading-[24px] font-medium tracking-[-0.24px]\">\n            Get a Quote\n          </a>\n        </div>\n      </div>\n    `;\n\n    container.insertAdjacentHTML(\"beforeend\", card);\n  });\n});", "placeholders": ["Title", "Amount", "link_span", "view_link", "Get_quote"], "preview_image": "", "version": 1, "status": "draft", "tags": [], "thumbnail_url": "", "id": "mefpovzi039o3l96utxl", "created_at": "2025-08-17T13:18:27.054Z", "updated_at": "2025-08-20T08:38:59.479Z", "category_name": "Header", "category_color": "#de2b2b", "order": 1, "repeator": "single", "cssClass": "", "uniqueId": "mefpovzi039o3l96utxl-1755768869163-pc0cm2jrk"}, {"name": "section Template", "category_id": "meffczkptv4fdqqme0j", "html_content": "<div data-component=\"section Template\"><section class=\"px-6 py-12 bg-gray-50\">\n  <div class=\"max-w-6xl mx-auto\">\n    <header class=\"text-center mb-10\">\n      <h2 class=\"text-3xl font-extrabold text-gray-800\">${Title}</h2>\n      <p class=\"mt-2 text-gray-600 max-w-2xl mx-auto\">\n        ${description}\n      </p>\n    </header>\n\n    <div class=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8\">\n      {{ITEMS}}\n    </div>\n\n    <footer class=\"mt-12 text-center\">\n      <a href={${ViewButtonLink}} class=\"text-indigo-600 hover:underline font-medium\">${ViewButton}</a>\n    </footer>\n  </div>\n</section></div>", "css_content": "", "js_content": "", "placeholders": ["Title", "description", "ViewButtonLink", "ViewButton", "ITEMS"], "preview_image": "", "version": 1, "status": "draft", "tags": [], "thumbnail_url": "", "id": "mek665tinxez3nyp5hl", "created_at": "2025-08-20T16:10:51.510Z", "updated_at": "2025-08-21T03:15:28.712Z", "category_name": "Header", "category_color": "#de2b2b", "order": 0, "repeator": "repeat", "cssClass": "", "uniqueId": "mek665tinxez3nyp5hl-1755768868007-grydo7h7m", "repeatedComponentId": "mek69vn9gv7h2fbr8iw", "repeatedComponentName": ["ITEMS"], "repeatedPlaceholder": ["ITEMS"], "repeatedComponent": {"name": "City Template ", "category_id": "meffczkptv4fdqqme0j", "html_content": "<div data-component=\"City Template \"><article class=\"rounded-xl border p-6 shadow-sm bg-white hover:shadow-md transition\">\n    <img src=\"${_img-${img-images}}\" alt=\"\" class=\"w-full h-40 object-cover rounded-lg mb-4\" />\n    <h3 class=\"text-xl font-semibold text-gray-800\">${name}</h3>\n    <p class=\"text-sm text-gray-500 mb-2\">${state}</p>\n    <ul class=\"text-sm text-gray-600 space-y-1\">\n      <li><span class=\"font-medium\">Population:</span> ${population}</li>\n      <li><span class=\"font-medium\">Famous For:</span> ${famousFor}</li>\n    </ul>\n    <button class=\"mt-4 w-full bg-indigo-600 text-white py-2 rounded-lg hover:bg-indigo-700\">\n      Explore ${name}\n    </button>\n  </article></div>", "css_content": "", "js_content": "", "placeholders": ["img-images", "name", "state", "population", "famous<PERSON><PERSON>"], "preview_image": "", "version": 1, "status": "draft", "tags": [], "thumbnail_url": "", "id": "mek69vn9gv7h2fbr8iw", "created_at": "2025-08-20T16:13:44.949Z", "updated_at": "2025-08-21T09:34:05.060Z", "category_name": "Header", "category_color": "#de2b2b"}}], "version": "v1", "pagePlaceHolder": [{"categoryName": "Test componenet", "CategoryId": "mefpovzi039o3l96utxl", "placeholders": ["Title", "Amount", "link_span", "view_link", "Get_quote"]}, {"categoryName": "section Template ", "CategoryId": "mek665tinxez3nyp5hl", "placeholders": ["Title", "description", "ViewButtonLink", "ViewButton"], "repeatedComponentName": ["ITEMS"], "repeatedComponentId": "mek69vn9gv7h2fbr8iw", "repeatedComponent": {"name": "City Template ", "category_id": "meffczkptv4fdqqme0j", "html_content": "<div data-component=\"City Template \"><article class=\"rounded-xl border p-6 shadow-sm bg-white hover:shadow-md transition\">\n    <img src=\"${_img-${img-images}}\" alt=\"\" class=\"w-full h-40 object-cover rounded-lg mb-4\" />\n    <h3 class=\"text-xl font-semibold text-gray-800\">${names}</h3>\n    <p class=\"text-sm text-gray-500 mb-2\">${state}</p>\n    <ul class=\"text-sm text-gray-600 space-y-1\">\n      <li><span class=\"font-medium\">Population:</span> ${population}</li>\n      <li><span class=\"font-medium\">Famous For:</span> ${famousFor}</li>\n    </ul>\n    <button class=\"mt-4 w-full bg-indigo-600 text-white py-2 rounded-lg hover:bg-indigo-700\">\n      Explore ${name}\n    </button>\n  </article></div>", "css_content": "", "js_content": "", "placeholders": ["img-images", "names", "state", "population", "famous<PERSON><PERSON>", "name"], "preview_image": "", "version": 1, "status": "draft", "tags": [], "thumbnail_url": "", "id": "mek69vn9gv7h2fbr8iw", "created_at": "2025-08-20T16:13:44.949Z", "updated_at": "2025-08-21T11:38:43.483Z", "category_name": "Header", "category_color": "#de2b2b"}}], "id": "mek6d46ni42euwjtdjk", "created_at": "2025-08-20T16:16:15.983Z", "updated_at": "2025-08-21T12:37:03.252Z", "url": "/bhaibhai", "type": "static", "showNavbar": true, "navPosition": 1, "order": 1, "navIndex": "2"}], "content": [], "version": 1, "status": "draft", "id": "memu53w7j3u1pftta4m", "created_at": "2025-08-22T12:57:25.495Z", "updated_at": "2025-08-23T09:08:37.602Z", "templateContentJSON": {"dynamic-city-6pm": {"dynamic hero 6 pm": {"hero_section_title": "Charter bus Rental ${slug}, ${${slug}_state}"}, "dynamic why section 6pm": {"why_section_heading": "The Most Trusted ${slug} Charter Bus & Party Bus Company", "why_section_description": "${${slug}_why_section_description}", "img-why-section-image": "${${slug}_img-why-section-image}"}}}, "contentJSON": {"dynamic-city-6pm": {"dynamic hero 6 pm": {"hero_section_description": "Book a Clifton Bus Rental in Minutes!", "contact_number": "2014640115", "contact_number_button_label": "Call Us: 201-464-0115", "quote_url": "/get-quote/", "get_quote_button_text": "Get 30-Second Online Quote", "img-hero-section-image": "_img-hero-section-image", "img-advantage-image": "_img-advantage-image", "hero_section_card1_content": "Get online pricing & availability in 30-seconds", "hero_section_card2_content": "Customer Support available everyday 24/7/365", "hero_section_card3_content": "Search All Different Types of Buses to Find the perfect fit for Your Trip", "Surat_state": "<PERSON><PERSON><PERSON><PERSON>", "Ahmedabad_state": "<PERSON><PERSON><PERSON><PERSON>", "Mumbai_state": "Maharashtra"}, "dynamic why section 6pm": {"img-background-pattern-bg-image": "background-pattern"}}, "Final Testing Purpose": {"Test componenet": {"Title": "Sample Title", "Amount": "$500", "link_span": "View Details", "view_link": "/bus-details", "Get_quote": "Get Quote Now"}, "section Template": {"Title": "Sample Title", "description": "Sample Description", "ViewButtonLink": "/bus-details", "ViewButton": "View All Buses", "ITEMS": [{"img-images": "clifton-charter-bus", "names": "Sample Title", "state": "$500", "population": "View Details", "famousFor": "/bus-details", "name": "Get Quote Now"}, {"img-images": "clifton-charter-bus1", "names": "Sample Title1", "state": "$5001", "population": "View Details1", "famousFor": "/bus-details1", "name": "Get Quote Now1"}]}}}, "FileList": {"Mumbai_img-why-section-image": "/asset/passaic-56-passenger_img-why-section-image.jpeg", "_img-advantage-image": "/asset/advantage.gif", "_img-hero-section-image": "/asset/clifton-charter-bus.jpeg", "_img-background-pattern": "/asset/background-pattern.png", "_img-images": "/asset/clifton-charter-bus.jpeg", "_img-clifton-charter-bus": "/asset/clifton-charter-bus.jpeg", "_img-clifton-charter-bus1": "/asset/clifton-bus-rental.png"}, "dynamicContent": {"dynamic-city-6pm": {"siteMapLabel": "dynamic-city-6pm Page", "navigationType": 1, "columnCount": 1, "sections": {"city": [{"id": 1, "name": "New York", "slug": "new-york"}, {"id": 2, "name": "Los Angeles", "slug": "los-angeles"}, {"id": 3, "name": "Chicago", "slug": "chicago"}]}}}}