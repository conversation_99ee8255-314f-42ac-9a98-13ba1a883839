import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Typo<PERSON> } from "antd";
import {
  WifiOutlined,
  WifiOutlined as WifiOffOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import { useInternet } from "../../contexts/InternetContext";
import useInternetConnection from "../../hooks/useInternetConnection";

const { Text } = Typography;

const InternetConnectionStatus = ({ showDetails = false }) => {
  const { isOnline, connectionQuality, isRetrying, getConnectionStats } =
    useInternet();

  const { manualRetry, getQualityInfo, canRetry } = useInternetConnection();

  const qualityInfo = getQualityInfo();
  const stats = getConnectionStats();

  const getStatusColor = () => {
    if (!isOnline) return "red";
    if (connectionQuality === "excellent") return "green";
    if (connectionQuality === "good") return "blue";
    if (connectionQuality === "fair") return "orange";
    return "red";
  };

  const getStatusIcon = () => {
    if (!isOnline) return <WifiOffOutlined />;
    return <WifiOutlined />;
  };

  const handleRetry = async () => {
    await manualRetry();
  };

  if (showDetails) {
    return (
      <div className="tw-p-4 tw-bg-white tw-rounded-lg tw-shadow-sm tw-border">
        <div className="tw-flex tw-items-center tw-justify-between tw-mb-3">
          <div className="tw-flex tw-items-center tw-gap-2">
            <Badge
              status={getStatusColor()}
              text={
                <span className="tw-flex tw-items-center tw-gap-1">
                  {getStatusIcon()}
                  <Text strong>{isOnline ? "Online" : "Offline"}</Text>
                </span>
              }
            />
            {isOnline && <Text type="secondary">({qualityInfo.label})</Text>}
          </div>

          {!isOnline && canRetry && (
            <Button
              size="small"
              icon={<ReloadOutlined />}
              onClick={handleRetry}
              loading={isRetrying}
            >
              Retry
            </Button>
          )}
        </div>

        {isOnline && (
          <div className="tw-space-y-2">
            <div className="tw-flex tw-justify-between">
              <Text type="secondary">Quality:</Text>
              <Text>{qualityInfo.label}</Text>
            </div>
            <div className="tw-flex tw-justify-between">
              <Text type="secondary">Uptime:</Text>
              <Text>{stats.uptime}%</Text>
            </div>
            <div className="tw-flex tw-justify-between">
              <Text type="secondary">Total Connections:</Text>
              <Text>{stats.totalConnections}</Text>
            </div>
          </div>
        )}

        {!isOnline && (
          <div className="tw-text-center tw-py-2">
            <Text type="secondary">
              {isRetrying ? "Checking connection..." : "No internet connection"}
            </Text>
          </div>
        )}
      </div>
    );
  }

  return (
    <Tooltip
      title={
        <div>
          <div>Status: {isOnline ? "Online" : "Offline"}</div>
          {isOnline && (
            <>
              <div>Quality: {qualityInfo.label}</div>
              <div>Uptime: {stats.uptime}%</div>
            </>
          )}
        </div>
      }
    >
      <Badge status={getStatusColor()} dot className="tw-cursor-pointer">
        <Button
          type="text"
          size="small"
          icon={getStatusIcon()}
          loading={isRetrying}
          onClick={!isOnline && canRetry ? handleRetry : undefined}
        />
      </Badge>
    </Tooltip>
  );
};

export default InternetConnectionStatus;
