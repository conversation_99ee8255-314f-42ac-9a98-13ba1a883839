import React from "react";
import { <PERSON>, Tooltip, Popconfirm } from "antd";
import { EyeOutlined, EditOutlined, DeleteOutlined } from "@ant-design/icons";

const UserActions = ({ user, onEdit, onView, onDelete }) => {
  return (
    <Space>
      <Tooltip title="View User">
        <EyeOutlined
          style={{ color: "#1890ff", cursor: "pointer", fontSize: "16px" }}
          onClick={() => onView(user)}
        />
      </Tooltip>

      <Tooltip title="Edit User">
        <EditOutlined
          style={{ color: "#52c41a", cursor: "pointer", fontSize: "16px" }}
          onClick={() => onEdit(user)}
        />
      </Tooltip>

      {user.role !== "admin" && (
        <Popconfirm
          title="Are you sure you want to delete this user?"
          description="This action cannot be undone."
          onConfirm={() => onDelete(user.id)}
          okText="Yes"
          cancelText="No"
          okButtonProps={{ danger: true }}
        >
          <Tooltip title="Delete User">
            <DeleteOutlined
              style={{ color: "#ff4d4f", cursor: "pointer", fontSize: "16px" }}
            />
          </Tooltip>
        </Popconfirm>
      )}
    </Space>
  );
};

export default UserActions;
