/**
 * Storage Adapter
 * Provides the same interface as the API but uses JSON storage
 * Allows easy switching between API and JSON storage
 */

import crudOperations from './crudOperations.js';

// Environment configuration for switching between API and JSON storage
const USE_JSON_STORAGE = import.meta.env.VITE_STORAGE_MODE === 'json'; // Use JSON storage when VITE_STORAGE_MODE is 'json'

/**
 * Storage Adapter Class
 * Mimics the API response structure and behavior
 */
class StorageAdapter {
  constructor() {
    this.isLoading = false;
  }

  // Simulate API delay for realistic behavior
  async simulateDelay(ms = 100) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Simulate API response structure
  createResponse(data, success = true, message = '') {
    return {
      success,
      data,
      message,
      timestamp: new Date().toISOString()
    };
  }

  // Generic request handler that mimics API behavior
  async handleRequest(operation, successMessage = '', errorMessage = '') {
    try {
      this.isLoading = true;
      await this.simulateDelay();

      const result = operation();
      this.isLoading = false;

      if (result === null || result === false) {
        throw new Error(errorMessage || 'Operation failed');
      }

      return this.createResponse(result, true, successMessage);
    } catch (error) {
      this.isLoading = false;
      console.error('Storage operation error:', error);
      throw error;
    }
  }

  // Categories operations
  async getCategories() {
    return this.handleRequest(
      () => crudOperations.categories.getAll(),
      'Categories fetched successfully'
    );
  }

  async getCategoryById(id) {
    return this.handleRequest(
      () => crudOperations.categories.getById(id),
      'Category fetched successfully'
    );
  }

  async createCategory(categoryData) {
    return this.handleRequest(
      () => crudOperations.categories.create(categoryData),
      'Category created successfully',
      'Failed to create category'
    );
  }

  async updateCategory(id, updateData) {
    return this.handleRequest(
      () => crudOperations.categories.update(id, updateData),
      'Category updated successfully',
      'Failed to update category'
    );
  }

  async deleteCategory(id) {
    return this.handleRequest(
      () => crudOperations.categories.delete(id),
      'Category deleted successfully',
      'Failed to delete category'
    );
  }

  // Components operations
  async getComponents() {
    return this.handleRequest(
      () => crudOperations.components.getAll(),
      'Components fetched successfully'
    );
  }

  async getComponentById(id) {
    return this.handleRequest(
      () => crudOperations.components.getById(id),
      'Component fetched successfully'
    );
  }

  async createComponent(componentData) {
    return this.handleRequest(
      () => crudOperations.components.create(componentData),
      'Component created successfully',
      'Failed to create component'
    );
  }

  async updateComponent(id, updateData) {
    return this.handleRequest(
      () => crudOperations.components.update(id, updateData),
      'Component updated successfully',
      'Failed to update component'
    );
  }

  async deleteComponent(id) {
    return this.handleRequest(
      () => crudOperations.components.delete(id),
      'Component deleted successfully',
      'Failed to delete component'
    );
  }

  // Pages operations
  async getPages() {
    return this.handleRequest(
      () => crudOperations.pages.getAll(),
      'Pages fetched successfully'
    );
  }

  async getPageById(id) {
    return this.handleRequest(
      () => crudOperations.pages.getById(id),
      'Page fetched successfully'
    );
  }

  async createPage(pageData) {
    return this.handleRequest(
      () => crudOperations.pages.create(pageData),
      'Page created successfully',
      'Failed to create page'
    );
  }

  async updatePage(id, updateData) {
    return this.handleRequest(
      () => crudOperations.pages.update(id, updateData),
      'Page updated successfully',
      'Failed to update page'
    );
  }

  async deletePage(id) {
    return this.handleRequest(
      () => crudOperations.pages.delete(id),
      'Page deleted successfully',
      'Failed to delete page'
    );
  }

  // Templates operations
  async getTemplates() {
    return this.handleRequest(
      () => crudOperations.templates.getAll(),
      'Templates fetched successfully'
    );
  }

  async getTemplateById(id) {
    return this.handleRequest(
      () => crudOperations.templates.getById(id),
      'Template fetched successfully'
    );
  }

  async createTemplate(templateData) {
    return this.handleRequest(
      () => crudOperations.templates.create(templateData),
      'Template created successfully',
      'Failed to create template'
    );
  }

  async updateTemplate(id, updateData) {
    return this.handleRequest(
      () => crudOperations.templates.update(id, updateData),
      'Template updated successfully',
      'Failed to update template'
    );
  }

  async deleteTemplate(id) {
    return this.handleRequest(
      () => crudOperations.templates.delete(id),
      'Template deleted successfully',
      'Failed to delete template'
    );
  }

  // Generic method to handle any CRUD operation
  async executeOperation(type, operation, id = null, data = null) {
    const operations = {
      categories: {
        get: () => this.getCategories(),
        getById: (id) => this.getCategoryById(id),
        create: (data) => this.createCategory(data),
        update: (id, data) => this.updateCategory(id, data),
        delete: (id) => this.deleteCategory(id)
      },
      components: {
        get: () => this.getComponents(),
        getById: (id) => this.getComponentById(id),
        create: (data) => this.createComponent(data),
        update: (id, data) => this.updateComponent(id, data),
        delete: (id) => this.deleteComponent(id)
      },
      pages: {
        get: () => this.getPages(),
        getById: (id) => this.getPageById(id),
        create: (data) => this.createPage(data),
        update: (id, data) => this.updatePage(id, data),
        delete: (id) => this.deletePage(id)
      },
      templates: {
        get: () => this.getTemplates(),
        getById: (id) => this.getTemplateById(id),
        create: (data) => this.createTemplate(data),
        update: (id, data) => this.updateTemplate(id, data),
        delete: (id) => this.deleteTemplate(id)
      }
    };

    if (!operations[type] || !operations[type][operation]) {
      throw new Error(`Invalid operation: ${type}.${operation}`);
    }

    if (operation === 'getById' || operation === 'delete') {
      return operations[type][operation](id);
    } else if (operation === 'update') {
      return operations[type][operation](id, data);
    } else if (operation === 'create') {
      return operations[type][operation](data);
    } else {
      return operations[type][operation]();
    }
  }
}

// Create singleton instance
const storageAdapter = new StorageAdapter();

export default storageAdapter;
export { USE_JSON_STORAGE };
