import { Input } from "antd";
import { Loader2, Search } from "lucide-react";
import React, { useEffect, useState } from "react";

const SearchBar = ({
  type = "default",
  handleSearch,
  placeholder = "Search...",
  disabled = false,
}) => {
  const [searchTerm, setSearchTerm] = useState(null);
  const [isSearching, setIsSearching] = useState(false);
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");

  // Handle search with immediate callback (debouncing handled by parent)
  useEffect(() => {
    if (searchTerm != null && !disabled) {
      setIsSearching(true);
      handleSearch(searchTerm);
      // Reset searching state after a short delay for visual feedback
      const timer = setTimeout(() => {
        setIsSearching(false);
      }, 100);

      return () => {
        clearTimeout(timer);
      };
    } else {
      setIsSearching(false);
    }
  }, [searchTerm, disabled, handleSearch]);
  return (
    <>
      <Input
        placeholder={
          disabled ? "Select a page or component to enable search" : placeholder
        }
        size="middle"
        disabled={disabled}
        prefix={
          isSearching ? (
            <Loader2 className="tw-w-4 tw-h-4 tw-text-blue-500 tw-animate-spin" />
          ) : (
            <Search
              className={`tw-w-4 tw-h-4 ${
                disabled ? "tw-text-gray-300" : "tw-text-gray-400"
              }`}
            />
          )
        }
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        className={`tw-rounded-lg ${
          type === "page" ? "search-input-enhanced" : "tw-mb-4"
        } ${disabled ? "tw-bg-gray-50 tw-cursor-not-allowed" : ""}`}
        allowClear
      />
    </>
  );
};

export default SearchBar;
