import React, { createContext, useContext, useState, useEffect } from "react";
import { eraseCookie } from "../util/API/Cookies";
import { setAuthDetails, deleteAuthDetails } from "../util/API/authStorage";
import { CONSTANTS } from "../util/constant/CONSTANTS";

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const token = localStorage.getItem("token");
    const userData = localStorage.getItem("user");

    if (token && userData) {
      setUser(JSON.parse(userData));
    }
    setLoading(false);
  }, []);

  const login = async (credentials) => {
    try {
      // Use environment variable for base URL
      const baseURL = import.meta.env.VITE_API_URL;
      const loginEndpoint = CONSTANTS.API.auth.login.endpoint;

      console.log(`${baseURL}${loginEndpoint}`);

      const response = await fetch(`${baseURL}${loginEndpoint}`, {
        method: CONSTANTS.API.auth.login.type,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(credentials),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || data.error || "Login failed");
      }

      // Handle response structure - check if data has token directly or nested
      const token = data.token || data.data?.token;
      const userData = data.user || data.data?.user || data.data;

      if (!token) {
        throw new Error("No authentication token received");
      }

      // Use setAuthDetails for consistent token management
      setAuthDetails(token);
      localStorage.setItem("user", JSON.stringify(userData));

      // Fetch user profile using /users/me endpoint
      try {
        const getMeEndpoint = CONSTANTS.API.auth.getMe.endpoint;
        const userResponse = await fetch(`${baseURL}${getMeEndpoint}`, {
          method: CONSTANTS.API.auth.getMe.type,
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        });

        if (userResponse.ok) {
          const userProfileData = await userResponse.json();
          // Handle the expected response structure: { status: "success", data: {...} }
          const userProfile = userProfileData.data || userProfileData;

          // Update user state with complete profile
          localStorage.setItem("user", JSON.stringify(userProfile));
          setUser(userProfile);
        } else {
          // If /users/me fails, use the user data from login response
          setUser(userData);
        }
      } catch (userError) {
        console.warn(
          "Failed to fetch user profile, using login data:",
          userError
        );
        // Fallback to user data from login response
        setUser(userData);
      }

      return { success: true };
    } catch (error) {
      console.error("Login error:", error);
      return { success: false, error: error.message };
    }
  };

  const logout = () => {
    console.log("Logging out...");
    // Use deleteAuthDetails for consistent token management
    deleteAuthDetails();
    localStorage.removeItem("user");
    setUser(null);
  };

  const getAuthHeaders = () => {
    const token = localStorage.getItem("token");
    return token ? { Authorization: `Bearer ${token}` } : {};
  };

  const value = {
    user,
    login,
    logout,
    getAuthHeaders,
    loading,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
