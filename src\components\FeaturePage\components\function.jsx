// Helper: deep clone + replace ${slug} everywhere in string values
export const hydrateOldContentData = (source, slugObj) => {
  const replaceInString = (s) => s?.replace(/\$\{slug\}/g, slugObj.slug);

  const walk = (node, parentNode) => {
    if (node == null) return node;

    if (typeof node === "string") {
      // check if the string contains ${slug} if yes then replace it with slugObj.slug else replace it with slugObj.value
      // after replacing add another key with that value replce

      if (node.includes("${slug}") && node?.toString()) {
        // only key value which is in between ${} is key and value is outside of ${}
        const key = replaceInString(node)?.match(/\$\{([^}]+)\}/)?.[1];
        parentNode[parentNode.key] = replaceInString(key);
        return replaceInString(node);
      }
      // else {
      //   return node.replace(/\$\{slug\}/g, slugObj.name);
      // }
      //   return replaceInString(node);
    }

    if (Array.isArray(node)) {
      return node.map(walk);
    }

    if (typeof node === "object") {
      const out = {};
      for (const [k, v] of Object.entries(node)) {
        out[k] = walk(v, node);
      }
      return out;
    }

    return node; // numbers, booleans, etc.
  };

  return walk(source);
};

export const processStringValue = (s, slug, label) => {
  if (typeof s !== "string") return { text: s, injections: [] };

  // 1) Handle nested tokens: ${ ... } allowing one {...} inside
  const TOKEN_RE = /\$\{(?:[^{}]|\{[^{}]*\})*\}/g;
  let injections = [];
  let text = s.replace(TOKEN_RE, (whole) => {
    const inside = whole.slice(2, -1); // content without ${}
    if (inside.includes("${slug}")) {
      // Replace only inner ${slug} -> slug
      const replacedInside = inside.replace(/\$\{slug\}/g, slug);
      const newToken = "${" + replacedInside + "}";

      // Record injection key (the content inside ${} after replacement)
      // e.g., ${${slug}_state} -> inject "chicago_state"
      injections.push(replacedInside);
      return newToken;
    }
    return whole; // untouched token
  });

  // 2) Replace any leftover plain ${slug} -> slug
  text = text.replace(/\$\{slug\}/g, label);
  return { text, injections };
};

export const transformForSlug = (node, slug, label) => {
  if (node == null) return node;
  if (typeof node === "string") {
    // No place to inject here; just return processed text
    return processStringValue(node, slug, label).text;
  }

  if (Array.isArray(node)) {
    return node.map((item) => transformForSlug(item, slug, label));
  }

  if (typeof node === "object") {
    const out = {};
    for (const [k, v] of Object.entries(node)) {
      if (typeof v === "string") {
        const { text, injections } = processStringValue(v, slug, label);
        out[k] = text;

        // Add discovered injection keys at this same object level
        for (const injKey of injections) {
          if (!(injKey in out)) {
            out[injKey] = ""; // default empty string
          }
        }
      } else {
        out[k] = transformForSlug(v, slug, label);
      }
    }
    return out;
  }

  // numbers, booleans, etc.
  return node;
};

export const buildAllSlugJSON = (oldContentData, slugsAll) => {
  const bySlug = {};
  for (const { slug, name, label } of slugsAll) {
    bySlug[slug] = transformForSlug(oldContentData, slug, label);
  }
  return bySlug;
};
// =========================
// Matches ${var_name} where var_name can include a-z, 0-9, _, and dashes
const VAR_TOKEN_RE = /\$\{([a-z0-9_\-]+)\}/gi;

/**
 * Replace ${var} in a string using a vars map. Unknown vars are kept as-is.
 */
export const resolveString = (str, vars) => {
  if (typeof str !== "string") return str;
  return str.replace(VAR_TOKEN_RE, (_, key) =>
    Object.prototype.hasOwnProperty.call(vars, key)
      ? String(vars[key])
      : `\${${key}}`
  );
};
export const buildVarsOnlyForSlug = (newConObj, slug) => {
  const out = {};
  const slugObj = newConObj?.[slug] || {};
  for (const [sectionName, sectionObj] of Object.entries(slugObj)) {
    if (
      !sectionObj ||
      typeof sectionObj !== "object" ||
      Array.isArray(sectionObj)
    )
      continue;

    const vars = {};
    // a) keys already injected
    for (const [k, v] of Object.entries(sectionObj)) {
      if (k.startsWith(`${slug}_`)) vars[k] = v;
    }
    // b) discover tokens `${slug_*}` inside strings
    for (const v of Object.values(sectionObj)) {
      if (typeof v !== "string") continue;
      let m;
      while ((m = VAR_TOKEN_RE.exec(v))) {
        const token = m[1];
        if (token.startsWith(`${slug}_`) && !(token in vars)) {
          vars[token] = ""; // ensure the var exists
        }
      }
    }

    if (Object.keys(vars).length) out[sectionName] = vars;
  }
  return out;
};
export const resolveContentForSlug = (newConObj, slug, varsBySection) => {
  const src = newConObj?.[slug] || {};
  const clone = JSON.parse(JSON.stringify(src)); // deep copy for a pure “view”

  for (const [sectionName, sectionObj] of Object.entries(clone)) {
    const scope = varsBySection?.[sectionName] || {};
    for (const [k, v] of Object.entries(sectionObj)) {
      if (typeof v === "string" && !k.startsWith(`${slug}_`)) {
        sectionObj[k] = resolveString(v, scope);
      }
    }
  }
  return clone;
};

export const buildVarsForAllSlugs = (newConObj) => {
  const out = {};

  for (const [slug, slugObj] of Object.entries(newConObj || {})) {
    if (!slugObj || typeof slugObj !== "object") continue;

    for (const [sectionName, sectionObj] of Object.entries(slugObj)) {
      if (
        !sectionObj ||
        typeof sectionObj !== "object" ||
        Array.isArray(sectionObj)
      )
        continue;

      const vars = {};

      // (a) keys already injected: section keys that begin with `${slug}_`
      for (const [k, v] of Object.entries(sectionObj)) {
        if (k.startsWith(`${slug}_`)) vars[k] = v;
      }

      // (b) discover tokens ${slug_*} inside string values
      for (const v of Object.values(sectionObj)) {
        if (typeof v !== "string") continue;
        VAR_TOKEN_RE.lastIndex = 0; // reset before each new string
        let m;
        while ((m = VAR_TOKEN_RE.exec(v))) {
          const token = m[1]; // e.g. "mySlug_title"
          if (token.startsWith(`${slug}_`) && !(token in vars)) {
            vars[token] = ""; // ensure the var exists
          }
        }
      }

      if (Object.keys(vars).length) {
        if (!out[slug]) out[slug] = {};
        out[slug][sectionName] = vars;
      }
    }
  }
  return out;
};

export const dynamicContentForm = {
  "buses-d": {
    sitemapLabel: "buses listing",
    navigationType: "Multi Column",
    breadcrumbName: "Our ${default_city} Fleet",
    // staticBreadcrumbName: "Our ${default_city} Fleet",
    // staticBreadcrumbURL: "buses",
    sections: [
      {
        sectionLabel: "Charter Buses",
        // breadcrumbURL: "/buses",
        sectionItems: [
          {
            label: "50 Passenger Charter Bus",
            slug: "50-passenger-charter-bus-rental",
            extra: {
              sitemapLabel: "50 Passenger Charter Bus",
              breadcrumbName: "50 Passenger Charter Bus Rental",
              "bus-image": "${default_city}-50-passenger-charter-bus",
              "bus-interior-image-1":
                "${default_city}-50-passenger-charter-bus-rental",
              "bus-interior-image-2":
                "${default_city}-50-passenger-charter-bus-interior",
              "bus-interior-image-3":
                "${default_city}-50-passenger-charter-bus-inside",
            },
          },
          {
            label: "54 Passenger Charter Bus",
            slug: "54-passenger-charter-bus-rental",
            extra: {
              sitemapLabel: "54 Passenger Charter Bus",
              breadcrumbName: "54 Passenger Charter Bus Rental",
              "bus-image": "${default_city}-54-passenger-charter-bus",
              "bus-interior-image-1":
                "${default_city}-54-passenger-charter-bus-rental",
              "bus-interior-image-2":
                "${default_city}-54-passenger-charter-bus-interior",
              "bus-interior-image-3":
                "${default_city}-54-passenger-charter-bus-inside",
            },
          },
          {
            label: "55 Passenger Charter Bus",
            slug: "55-passenger-charter-bus-rental",
            extra: {
              sitemapLabel: "55 Passenger Charter Bus",
              breadcrumbName: "55 Passenger Charter Bus Rental",
              "bus-image": "${default_city}-55-passenger-charter-bus",
              "bus-interior-image-1":
                "${default_city}-55-passenger-charter-bus-rental",
              "bus-interior-image-2":
                "${default_city}-55-passenger-charter-bus-interior",
              "bus-interior-image-3":
                "${default_city}-55-passenger-charter-bus-inside",
            },
          },
          {
            label: "56 Passenger Charter Bus",
            slug: "56-passenger-charter-bus-rental",
            extra: {
              sitemapLabel: "56 Passenger Charter Bus",
              breadcrumbName: "56 Passenger Charter Bus Rental",
              "bus-image": "${default_city}-56-passenger-charter-bus",
              "bus-interior-image-1":
                "${default_city}-56-passenger-charter-bus-rental",
              "bus-interior-image-2":
                "${default_city}-56-passenger-charter-bus-interior",
              "bus-interior-image-3":
                "${default_city}-56-passenger-charter-bus-inside",
            },
          },
        ],
      },
      {
        sectionLabel: "Specialty Vehicles",
        // breadcrumbName: "Buses",
        // breadcrumbURL: "/buses",
        sectionItems: [
          {
            label: "Sprinter Van Rental",
            slug: "sprinter-van-rental-with-driver",
            extra: {
              sitemapLabel: "Sprinter Van Rental",
              breadcrumbName: "Sprinter Van Rental With Driver",
              "bus-image": "${default_city}-sprinter-van-with-driver",
              "bus-interior-image-1":
                "${default_city}-sprinter-van-with-driver-rental",
              "bus-interior-image-2":
                "${default_city}-sprinter-van-with-driver-interior",
              "bus-interior-image-3":
                "${default_city}-sprinter-van-with-driver-inside",
            },
          },
          {
            label: "Party Bus",
            slug: "party-bus-rental",
            // isCustom: true,
            extra: {
              sitemapLabel: "Party Bus Rental",
              breadcrumbName: "10 to 50 Passenger Party Bus Rental",
              "bus-image": "${default_city}-party-bus-rental",
              "bus-interior-image-1": "${default_city}-party-bus-rental",
              "bus-interior-image-2":
                "${default_city}-party-bus-rental-interior",
              "bus-interior-image-3": "${default_city}-party-bus-rental-inside",
            },
          },
          {
            label: "Sprinter Limo",
            slug: "sprinter-limo-rental",
            extra: {
              sitemapLabel: "Sprinter Limo Rental",
              breadcrumbName: "Sprinter Limo Rental",
              "bus-image": "${default_city}-sprinter-limo-rental",
              "bus-interior-image-1": "${default_city}-sprinter-limo-rental",
              "bus-interior-image-2":
                "${default_city}-sprinter-limo-rental-interior",
              "bus-interior-image-3":
                "${default_city}-sprinter-limo-rental-inside",
            },
          },
          {
            label: "School Bus",
            slug: "school-bus-rental",
            extra: {
              sitemapLabel: "School Bus Rental",
              breadcrumbName: "School Bus Rental",
              "bus-image": "${default_city}-school-bus-rental",
              "bus-interior-image-1": "${default_city}-school-bus-rental",
              "bus-interior-image-2":
                "${default_city}-school-bus-rental-interior",
              "bus-interior-image-3":
                "${default_city}-school-bus-rental-inside",
            },
          },
        ],
      },

      {
        sectionLabel: "Minibuses",
        // breadcrumbName: "Buses",
        // breadcrumbURL: "/buses",
        sectionItems: [
          {
            label: "15 Passenger Minibus",
            slug: "15-passenger-minibus-rental",
            extra: {
              sitemapLabel: "15 Passenger Minibus",
              breadcrumbName: "15 Passenger Minibus Rental",
              "bus-image": "${default_city}-15-passenger-minibus",
              "bus-interior-image-1":
                "${default_city}-15-passenger-minibus-rental",
              "bus-interior-image-2":
                "${default_city}-15-passenger-minibus-interior",
              "bus-interior-image-3":
                "${default_city}-15-passenger-minibus-inside",
            },
          },
          {
            label: "18 Passenger Minibus",
            slug: "18-passenger-minibus-rental",
            extra: {
              sitemapLabel: "18 Passenger Minibus",
              breadcrumbName: "18 Passenger Minibus Rental",
              "bus-image": "${default_city}-18-passenger-minibus",
              "bus-interior-image-1":
                "${default_city}-18-passenger-minibus-rental",
              "bus-interior-image-2":
                "${default_city}-18-passenger-minibus-interior",
              "bus-interior-image-3":
                "${default_city}-18-passenger-minibus-inside",
            },
          },
          {
            label: "20 Passenger Minibus",
            slug: "20-passenger-minibus-rental",
            extra: {
              sitemapLabel: "20 Passenger Minibus",
              breadcrumbName: "20 Passenger Minibus Rental",
              "bus-image": "${default_city}-20-passenger-minibus",
              "bus-interior-image-1":
                "${default_city}-20-passenger-minibus-rental",
              "bus-interior-image-2":
                "${default_city}-20-passenger-minibus-interior",
              "bus-interior-image-3":
                "${default_city}-20-passenger-minibus-inside",
            },
          },
          {
            label: "25 Passenger Minibus",
            slug: "25-passenger-minibus-rental",
            extra: {
              sitemapLabel: "25 Passenger Minibus",
              breadcrumbName: "25 Passenger Minibus Rental",
              "bus-image": "${default_city}-25-passenger-minibus",
              "bus-interior-image-1":
                "${default_city}-25-passenger-minibus-rental",
              "bus-interior-image-2":
                "${default_city}-25-passenger-minibus-interior",
              "bus-interior-image-3":
                "${default_city}-25-passenger-minibus-inside",
            },
          },
          {
            label: "28 Passenger Minibus",
            slug: "28-passenger-minibus-rental",
            extra: {
              sitemapLabel: "28 Passenger Minibus",
              breadcrumbName: "28 Passenger Minibus Rental",
              "bus-image": "${default_city}-28-passenger-minibus",
              "bus-interior-image-1":
                "${default_city}-28-passenger-minibus-rental",
              "bus-interior-image-2":
                "${default_city}-28-passenger-minibus-interior",
              "bus-interior-image-3":
                "${default_city}-28-passenger-minibus-inside",
            },
          },
          {
            label: "30 Passenger Minibus",
            slug: "30-passenger-minibus-rental",
            extra: {
              sitemapLabel: "30 Passenger Minibus",
              breadcrumbName: "30 Passenger Minibus Rental",
              "bus-image": "${default_city}-30-passenger-minibus",
              "bus-interior-image-1":
                "${default_city}-30-passenger-minibus-rental",
              "bus-interior-image-2":
                "${default_city}-30-passenger-minibus-interior",
              "bus-interior-image-3":
                "${default_city}-30-passenger-minibus-inside",
            },
          },
          {
            label: "35 Passenger Minibus",
            slug: "35-passenger-minibus-rental",
            extra: {
              sitemapLabel: "35 Passenger Minibus",
              breadcrumbName: "35 Passenger Minibus Rental",
              "bus-image": "${default_city}-35-passenger-minibus",
              "bus-interior-image-1":
                "${default_city}-35-passenger-minibus-rental",
              "bus-interior-image-2":
                "${default_city}-35-passenger-minibus-interior",
              "bus-interior-image-3":
                "${default_city}-35-passenger-minibus-inside",
            },
          },
        ],
      },
    ],
    columnCount: "2",
  },
  "services-d": {
    sitemapLabel: "services we offer",
    navigationType: "Multi Column",
    breadcrumbName: "${default_city} Group Transportation Services",
    // staticBreadcrumbName: "${default_city} Group Transportation Services",
    // staticBreadcrumbURL: "group-transportation-services",
    sections: [
      {
        sectionLabel: "Services",
        // breadcrumbURL: "/group-transportation-services",
        sectionItems: [
          {
            label: "Airport Shuttles",
            slug: "airport-shuttles",
            extra: {
              sitemapLabel: "Airport Bus Rentals",
              breadcrumbName: "Airport Bus Rental",
              content_image1: "${default_city}-airport-shuttle-bus",
              content_image2: "${default_city}-airport-bus-rental",
              content_title:
                "Book Your ${default_city} Airport Shuttles Today!",
              event_card: [
                {
                  event_name: "Sports teams",
                },
                {
                  event_name: "business groups",
                },
                {
                  event_name: "marching bands",
                },
                {
                  event_name: "military groups",
                },
                {
                  event_name: "private tours",
                },
                {
                  event_name: "mission trips",
                },
              ],
            },
          },
          {
            label: "Construction Site Shuttles",
            slug: "construction-site-shuttle-bus-rental",
            extra: {
              sitemapLabel: "Construction Site Shuttle Services",
              breadcrumbName: "Construction Site Shuttle Bus Rental",
              content_image1:
                "${default_city}-construction-site-shuttle-services",
              content_image2:
                "${default_city}-construction-site-shuttle-bus-service",
              content_title:
                "Book Your Construction Site Shuttle Service Today!",
              event_card: [
                {
                  event_name: "Daily, Monthly, Yearly Travel",
                },
                {
                  event_name: "Remote Job Sites",
                },
                {
                  event_name: "Urban Construction Projects",
                },
                {
                  event_name: "Multiple Project Locations",
                },
                {
                  event_name: "Highway Construction Projects",
                },
                {
                  event_name: "Site Visits",
                },
              ],
            },
          },
          {
            label: "Corporate Events",
            slug: "corporate-bus-rental",
            extra: {
              sitemapLabel: "Corporate Bus Rentals",
              breadcrumbName: "Corporate Bus Rental",
              content_image1: "${default_city}-corporate-bus-rental",
              content_image2: "${default_city}-employee-shuttle",
              content_title:
                "Book Your ${default_city} Corporate Transportation Today!",
              event_card: [
                {
                  event_name: "Commuter Shuttles",
                },
                {
                  event_name: "Team-Building Events",
                },
                {
                  event_name: "Happy Hours",
                },
                {
                  event_name: "Holiday Parties",
                },
                {
                  event_name: "Conventions",
                },
                {
                  event_name: "Retreats",
                },
              ],
            },
          },
          {
            label: "Emergency Services",
            slug: "emergency-response-bus-rentals",
            extra: {
              sitemapLabel: "Emergency Transportation Services",
              breadcrumbName: "Emergency Bus Service",
              content_image1:
                "${default_city}-emergency-transportation-service",
              content_image2:
                "${default_city}-emergency-transportation-services",
              content_title: "Book Emergency Transportation With Us!",
              event_card: [
                {
                  event_name: "Evacuations",
                },
                {
                  event_name: "Disaster Relief",
                },
                {
                  event_name: "Senior Living Facilities",
                },
                {
                  event_name: "Rapid Response",
                },
                {
                  event_name: "Medical Personnel Transportation",
                },
                {
                  event_name: "Emergency Airport Transfers",
                },
              ],
            },
          },
          {
            label: "Government & Military Groups",
            slug: "government-bus-rentals",
            extra: {
              sitemapLabel: "Government & Military Bus Rentals",
              breadcrumbName: "Government & Military Travel",
              content_image1:
                "${default_city}-government-and-military-bus-rentals",
              content_image2: "${default_city}-government-bus-rental",
              content_title:
                "Bus Rentals to Government & Military Spaces in the Tri-State Area",
              event_card: [
                {
                  event_name: "Airport Travel",
                },
                {
                  event_name: "Troop Movements",
                },
                {
                  event_name: "Off-Base Events",
                },
                {
                  event_name: "Group Deployment/Redeployment",
                },
                {
                  event_name: "Emergency Response & Disaster Relief",
                },
                {
                  event_name: "Training Exercises",
                },
              ],
            },
          },
          {
            label: "Hospital & Healthcare Shuttles",
            slug: "hospital-shuttles",
            extra: {
              sitemapLabel: "Hospital & Healthcare Shuttles",
              breadcrumbName: "Hospital Shuttle Bus Services",
              content_image1:
                "${default_city}-hospital-and-healthcare-shuttles",
              content_image2: "${default_city}-hospital-shuttles",
              content_title:
                "Meet the Network Behind Your Healthcare Transportation",
              event_card: [
                {
                  event_name: "Non-Emergency Medical Transport",
                },
                {
                  event_name: "Employee Parking Shuttles",
                },
                {
                  event_name: "Patient/Visitor Parking Shuttles",
                },
                {
                  event_name: "Group Therapy Appointments",
                },
                {
                  event_name: "Inter-Facility Shuttles",
                },
                {
                  event_name: "Airport Travel",
                },
              ],
            },
          },
          {
            label: "Private Events",
            slug: "event-transportation-services",
            extra: {
              sitemapLabel: "Private Bus Rentals",
              breadcrumbName: "Private Bus Rental",
              content_image1: "${default_city}-private-event-bus-rental",
              content_image2: "${default_city}-private-bus-rental",
              content_title:
                "Book Your ${default_city} Event Transportation Today!",
              event_card: [
                {
                  event_name: "Birthdays",
                },
                {
                  event_name: "Family Reunions",
                },
                {
                  event_name: "Weddings",
                },
                {
                  event_name: "School trips",
                },
                {
                  event_name: "Corporate Events",
                },
                {
                  event_name: "Sporting events",
                },
              ],
            },
          },
          {
            label: "Proms & Homecomings",
            slug: "prom-bus-rental",
            extra: {
              sitemapLabel: "Prom & Homecoming",
              breadcrumbName: "Prom & Homecoming Bus Rental",
              content_image1:
                "${default_city}-prom-and-homecoming-party-bus-rentals",
              content_image2: "${default_city}-prom-and-homecoming",
              content_title: "Book Your Prom Bus Rental Today!",
              event_card: [
                {
                  event_name: "Prom Limos & Party Buses",
                },
                {
                  event_name: "VIP Packages",
                },
                {
                  event_name: "Red Carpet Entrance",
                },
                {
                  event_name: "After-Party Drop-Offs",
                },
                {
                  event_name: "Vehicles for Prom Pictures",
                },
                {
                  event_name: "Pre-Prom Pickups",
                },
              ],
            },
          },
          {
            label: "Religious Groups",
            slug: "church-bus-rental",
            extra: {
              sitemapLabel: "Religious Charter Bus & Minibus Rentals",
              breadcrumbName: "Religious Group Bus Rental",
              content_image1:
                "${default_city}-religious-charter-bus-minibus-rentals",
              content_image2:
                "${default_city}-religious-group-charter-bus-minibus-rentals",
              content_title:
                "Long-Distance Travel, Short-Term or Long-Term: Bus Rental Company ${default_city} Can Do It",
              event_card: [
                {
                  event_name: "Retreats",
                },
                {
                  event_name: "Weekly Service Shuttles",
                },
                {
                  event_name: "Conferences/Conventions",
                },
                {
                  event_name: "Mission Trips",
                },
                {
                  event_name: "Youth Group Events",
                },
                {
                  event_name: "Volunteering",
                },
              ],
            },
          },
          {
            label: "School Trips",
            slug: "school-event-bus-rental",
            extra: {
              sitemapLabel: "Field Trip Bus Rentals",
              breadcrumbName: "School Event Bus Rental",
              content_image1: "${default_city}-school-trip-bus-rental",
              content_image2: "${default_city}-school-event-bus-rental",
              content_title:
                "Book Your ${default_city} Field Trip & School Bus Rentals Today!",
              event_card: [
                {
                  event_name: "field trips",
                },
                {
                  event_name: "athletic events",
                },
                {
                  event_name: "Greek events",
                },
                {
                  event_name: "Marches, Protests and Non-profit Events",
                },
                {
                  event_name: "summer camps",
                },
                {
                  event_name: "college tours",
                },
              ],
            },
          },
          {
            label: "Sports Teams",
            slug: "sports-team-transportation",
            extra: {
              sitemapLabel: "Sports Team Bus Rentals",
              breadcrumbName: "Sports Team Bus Rental",
              content_image1: "${default_city}-sports-charter-bus-rental",
              content_image2: "${default_city}-sports-bus-rental",
              content_title:
                "Book Your ${default_city} Sporting Event Transportation Today!",
              event_card: [
                {
                  event_name: "Team, Club & University Travel",
                },
                {
                  event_name: "Away games & Tournaments",
                },
                {
                  event_name: "Game day Transportation & Shuttles",
                },
                {
                  event_name: "Fan, Cheer and Band Moves",
                },
                {
                  event_name: "Airport Transfers",
                },
                {
                  event_name: "Off-site practices & team-building trips",
                },
              ],
            },
          },
          {
            label: "Summer Camps",
            slug: "camp-bus-rentals",
            extra: {
              sitemapLabel: "Summer Camp Transportation & Bus Rentals",
              breadcrumbName: "Summer Camp Bus Rental",
              content_image1:
                "${default_city}-summer-camp-transportation-and-bus-rentals",
              content_image2: "${default_city}-camp-bus-rentals",
              content_title:
                "Bus Rentals Built For Long-Distance Adventures Outside of NJ",
              event_card: [
                {
                  event_name: "Shuttles To/From Camp",
                },
                {
                  event_name: "Day Trips",
                },
                {
                  event_name: "Inter-Camp Competitions/Events",
                },
                {
                  event_name: "Staff Transportation",
                },
                {
                  event_name: "Airport/Train Station Transfers",
                },
                {
                  event_name: "Camp Open House/Visitor Days",
                },
              ],
            },
          },
          {
            label: "Travel Agents",
            slug: "bus-rentals-for-travel-agents",
            extra: {
              sitemapLabel: "Bus Rentals for Travel Agents",
              breadcrumbName: "Travel Agent Bus Tours",
              content_image1: "${default_city}-bus-rentals-for-travel-agents",
              content_image2: "${default_city}-bus-rental-for-travel-agents",
              content_title: "Cruise Ship Shuttles for Port Liberty and Beyond",
              event_card: [
                {
                  event_name: "Group vacations",
                },
                {
                  event_name: "Airport Transfers",
                },
                {
                  event_name: "Theme Park Trips",
                },
                {
                  event_name: "Company Retreats & Conventions",
                },
                {
                  event_name: "Cruise Port Transfers",
                },
                {
                  event_name: "School & Educational Trips",
                },
              ],
            },
          },
          {
            label: "Vacations & Family Trips",
            slug: "family-vacation-bus-rentals",
            extra: {
              sitemapLabel: "Vacation Bus Rentals for Family Trips",
              breadcrumbName: "Family Vacation Bus Rental",
              content_image1:
                "${default_city}-vacation-bus-rentals-for-family-trips",
              content_image2:
                "${default_city}-vacation-bus-rental-for-family-trip",
              content_title:
                "Book Your ${default_city} Vacation Bus Rental Today!",
              event_card: [
                {
                  event_name: "Family Reunions",
                },
                {
                  event_name: "Vacations",
                },
                {
                  event_name: "Sightseeing Tours",
                },
                {
                  event_name: "Birthdays",
                },
                {
                  event_name: "Weddings",
                },
                {
                  event_name: "Wine Tours",
                },
              ],
            },
          },
          {
            label: "Wedding Shuttles",
            slug: "wedding-bus-rental",
            extra: {
              sitemapLabel: "Wedding Shuttle Rentals",
              breadcrumbName: "Wedding Bus Rental",
              content_image1: "${default_city}-wedding-charter-bus-rental",
              content_image2: "${default_city}-wedding-bus-rentals",
              content_title:
                "Book Your ${default_city} Wedding Bus Rental Today!",
              event_card: [
                {
                  event_name: "guest transportation & shuttles",
                },
                {
                  event_name: "bachelor & bachelorette transportation",
                },
                {
                  event_name: "Ceremony to Reception shuttles",
                },
                {
                  event_name: "Pre-Wedding Events",
                },
                {
                  event_name: "Airport transfers",
                },
                {
                  event_name: "destination weddings",
                },
              ],
            },
          },
          {
            label: "Wine Tours & Pub Crawls",
            slug: "wine-tour-pub-crawl-bus-rentals",
            extra: {
              sitemapLabel: "Wine Tour & Pub Crawl Bus Rentals",
              breadcrumbName: "Winery Tour Charter Bus Rental",
              content_image1:
                "${default_city}-wine-tour-and-pub-crawl-bus-rentals",
              content_image2: "${default_city}-wine-tour-pub-crawl-bus-rentals",
              content_title: "We’re Ready To Serve Your Group",
              event_card: [
                {
                  event_name: "Corporate Team-Building Retreat",
                },
                {
                  event_name: "Distillery Tours",
                },
                {
                  event_name: "Bachelor/ette Trips",
                },
                {
                  event_name: "Birthdays",
                },
                {
                  event_name: "Brewery & Distillery Tours",
                },
                {
                  event_name: "Winery trips",
                },
              ],
            },
          },
        ],
      },
    ],
    columnCount: "3",
  },
  // clifton ============
  // "service-area-d": {
  //   sitemapLabel: "bus locations by city",
  //   navigationType: "Single Column",
  //   sections: [
  //     {
  //       sectionLabel: "Services",
  //       breadcrumbName: "Cities We Serve",
  //       breadcrumbURL: "/services-area",
  //       sectionItems: [
  //         {
  //           label: "Passaic",
  //           slug: "passaic-bus-rental",
  //           extra: {
  //             breadcrumbName: "Passaic Bus Rental",
  //             sitemapLabel: "Passaic Bus Rental",
  //             state: "New Jersey",
  //           },
  //         },
  //         {
  //           label: "Paterson",
  //           slug: "paterson-bus-rental",
  //           extra: {
  //             breadcrumbName: "Paterson Bus Rental",
  //             sitemapLabel: "Paterson Bus Rental",
  //             state: "New Jersey",
  //           },
  //         },
  //         {
  //           label: "Bloomfield",
  //           slug: "bloomfield-bus-rental",
  //           extra: {
  //             breadcrumbName: "Bloomfield Bus Rental",
  //             sitemapLabel: "Bloomfield Bus Rental",
  //             state: "New Jersey",
  //           },
  //         },
  //         {
  //           label: "Hackensack",
  //           slug: "hackensack-bus-rental",
  //           extra: {
  //             breadcrumbName: "Hackensack Bus Rental",
  //             sitemapLabel: "Hackensack Bus Rental",
  //             state: "New Jersey",
  //           },
  //         },
  //         {
  //           label: "East Orange",
  //           slug: "east-orange-bus-rental",
  //           extra: {
  //             breadcrumbName: "East Orange Bus Rental",
  //             sitemapLabel: "East Orange Bus Rental",
  //             state: "New Jersey",
  //           },
  //         },
  //         {
  //           label: "Wayne",
  //           slug: "wayne-bus-rental",
  //           extra: {
  //             breadcrumbName: "Wayne Bus Rental",
  //             sitemapLabel: "Wayne Bus Rental",
  //             state: "New Jersey",
  //           },
  //         },
  //         {
  //           label: "Kearny",
  //           slug: "kearny-bus-rental",
  //           extra: {
  //             breadcrumbName: "Kearny Bus Rental",
  //             sitemapLabel: "Kearny Bus Rental",
  //             state: "New Jersey",
  //           },
  //         },
  //         {
  //           label: "Union City",
  //           slug: "union-city-bus-rental",
  //           extra: {
  //             breadcrumbName: "Union City Bus Rental",
  //             sitemapLabel: "Union City Bus Rental",
  //             state: "New Jersey",
  //           },
  //         },
  //         {
  //           label: "North Bergen",
  //           slug: "north-bergen-bus-rental",
  //           extra: {
  //             breadcrumbName: "North Bergen Bus Rental",
  //             sitemapLabel: "North Bergen Bus Rental",
  //             state: "New Jersey",
  //           },
  //         },
  //         {
  //           label: "Teaneck",
  //           slug: "teaneck-bus-rental",
  //           extra: {
  //             breadcrumbName: "Teaneck Bus Rental",
  //             sitemapLabel: "Teaneck Bus Rental",
  //             state: "New Jersey",
  //           },
  //         },
  //       ],
  //     },
  //   ],
  //   columnCount: "1",
  // },
  // grand island ================
  "service-area-d": {
    sitemapLabel: "bus locations by city",
    navigationType: "Single Column",
    breadcrumbName: "Cities We Serve",
    // staticBreadcrumbName: "Cities We Serve",
    // staticBreadcrumbURL: "service-area",
    sections: [
      {
        sectionLabel: "Services",
        // breadcrumbURL: "/services-area",
        sectionItems: [
          {
            label: "Lincoln",
            slug: "lincoln-charter-bus-rental",
            extra: {
              breadcrumbName: "Lincoln Bus Rental",
              sitemapLabel: "Lincoln Bus Rental",
              state: "Nebraska",
            },
          },
          {
            label: "Omaha",
            slug: "omaha-charter-bus-rental",
            extra: {
              breadcrumbName: "Omaha Bus Rental",
              sitemapLabel: "Omaha Bus Rental",
              state: "Nebraska",
            },
          },
          {
            label: "Bellevue",
            slug: "bellevue-charter-bus-rental",
            extra: {
              breadcrumbName: "Bellevue Bus Rental",
              sitemapLabel: "Bellevue Bus Rental",
              state: "Nebraska",
            },
          },
          {
            label: "Sioux Falls",
            slug: "sioux-falls-charter-bus-rental",
            extra: {
              breadcrumbName: "Sioux Falls Bus Rental",
              sitemapLabel: "Sioux Falls Bus Rental",
              state: "South Dakota",
            },
          },
          {
            label: "Wichita",
            slug: "wichita-charter-bus-rental",
            extra: {
              breadcrumbName: "Wichita Bus Rental",
              sitemapLabel: "Wichita Bus Rental",
              state: "Kansas",
            },
          },
          {
            label: "Kansas City",
            slug: "kansas-city-charter-bus-rental",
            extra: {
              breadcrumbName: "Kansas City Bus Rental",
              sitemapLabel: "Kansas City Bus Rental",
              state: "Missouri",
            },
          },
          {
            label: "Denver",
            slug: "denver-charter-bus-rental",
            extra: {
              breadcrumbName: "Denver Bus Rental",
              sitemapLabel: "Denver Bus Rental",
              state: "Colorado",
            },
          },
          {
            label: "Minneapolis",
            slug: "minneapolis-charter-bus-rental",
            extra: {
              breadcrumbName: "Minneapolis Bus Rental",
              sitemapLabel: "Minneapolis Bus Rental",
              state: "Minnesota",
            },
          },
          {
            label: "St. Paul",
            slug: "st-paul-charter-bus-rental",
            extra: {
              breadcrumbName: "St. Paul Bus Rental",
              sitemapLabel: "St. Paul Bus Rental",
              state: "Minnesota",
            },
          },
          {
            label: "Des Moines",
            slug: "des-moines-charter-bus-rental",
            extra: {
              breadcrumbName: "Des Moines Bus Rental",
              sitemapLabel: "Des Moines Bus Rental",
              state: "Iowa",
            },
          },
        ],
      },
    ],
    columnCount: "1",
  },
};

export const resolveTemplate = (input, content, maxDepth = 20) => {
  if (typeof input !== "string") return input;

  // Resolve ${...} inside a *name* (e.g., "${slug}_state" -> "surat_state")
  function resolveInName(name) {
    let out = name,
      guard = 0;
    while (guard++ < maxDepth) {
      const next = out.replace(/\$\{([^}]+)\}/g, (_, inner) => {
        const key = inner.trim();
        return Object.prototype.hasOwnProperty.call(content, key)
          ? String(content[key])
          : "${" + key + "}"; // keep unresolved placeholders
      });
      if (next === out) break;
      out = next;
    }
    return out;
  }

  let result = input;
  let changed = true;
  let rounds = 0;

  while (changed && rounds++ < maxDepth) {
    changed = false;

    result = result.replace(/\$\{([^}]+)\}/g, (full, expr) => {
      // 1) First, resolve any ${...} inside the placeholder *name*
      const resolvedName = resolveInName(expr.trim());

      // 2) If that final name exists in content, replace the whole placeholder
      if (Object.prototype.hasOwnProperty.call(content, resolvedName)) {
        changed = true;
        return String(content[resolvedName]);
      }

      // 3) Otherwise, leave it as-is for a later round
      return "${" + expr + "}";
    });
  }

  return result;
};

// function extractVariables(str) {
//   const regex = /\$\{([^}]+)\}/g;
//   let match;
//   const vars = new Set();

//   while ((match = regex.exec(str)) !== null) {
//     let expr = match[1].trim();

//     // If it's a nested template like ${${slug}_state}
//     // recursively extract inner variables
//     if (expr.startsWith("${") && expr.endsWith("}")) {
//       vars.add(expr);
//       vars.forEach((v) => extractVariables(v).forEach((x) => vars.add(x)));
//     } else {
//       vars.add(expr);
//     }
//   }

//   return Array.from(vars);
// }

// extractVariables("${slug} ${${slug}_state}");

export const deepMerge = (target, source) => {
  for (const key in source) {
    const lowerKey = key;
    // const lowerKey = key.toLowerCase();
    if (
      typeof source[key] === "object" &&
      source[key] !== null &&
      !Array.isArray(source[key])
    ) {
      if (!target[lowerKey]) target[lowerKey] = {};
      deepMerge(target[lowerKey], source[key]);
    } else {
      target[lowerKey] = source[key];
    }
  }
  return target;
};

const DEFAULTS = {
  navigationType: "Single Column",
  columnCount: 0,
};

const isPlainObject = (v) => v && typeof v === "object" && !Array.isArray(v);

const isEmptyScalar = (v) =>
  v === undefined || v === null || v === "" || v === 0;

const clone = (v) => {
  if (Array.isArray(v)) return v.map(clone);
  if (isPlainObject(v))
    return Object.fromEntries(
      Object.entries(v).map(([k, val]) => [k, clone(val)])
    );
  return v;
};

export const mergePreferExisting = (target, source, defaults = DEFAULTS) => {
  // Handle arrays: keep target if it has items, else take source
  if (Array.isArray(target) || Array.isArray(source)) {
    if (Array.isArray(target) && target.length > 0) return clone(target);
    return Array.isArray(source) ? clone(source) : clone(target);
  }

  // Handle plain objects
  if (isPlainObject(target) && isPlainObject(source)) {
    const out = { ...target };
    for (const key of Object.keys(source)) {
      const tVal = out[key];
      const sVal = source[key];

      if (isPlainObject(tVal) && isPlainObject(sVal)) {
        out[key] = mergePreferExisting(tVal, sVal, defaults);
        continue;
      }

      if (Array.isArray(tVal) || Array.isArray(sVal)) {
        out[key] = mergePreferExisting(tVal, sVal, defaults);
        continue;
      }

      // Scalars
      const def = Object.prototype.hasOwnProperty.call(defaults, key)
        ? defaults[key]
        : undefined;
      const targetIsEmpty =
        isEmptyScalar(tVal) || (def !== undefined && tVal === def);

      if (targetIsEmpty && sVal !== undefined && sVal !== null && sVal !== "") {
        out[key] = clone(sVal);
      } // else keep existing tVal
    }

    // Add keys that exist only in source
    for (const key of Object.keys(source)) {
      if (!(key in out)) out[key] = clone(source[key]);
    }
    return out;
  }

  // Scalars: keep target if non-empty, else take source
  return isEmptyScalar(target) ? clone(source) : clone(target);
};

//fallback template.json file inside
//  "fallBackContentJSON": {
//     "dynamic-city-6pm": {
//       "dynamic hero 6 pm": {
//         "hero_section_description": "Book a Clifton Bus Rental in Minutes!",
//         "contact_number": "2014640115",
//         "contact_number_button_label": "Call Us: 201-464-0115",
//         "quote_url": "/get-quote/",
//         "get_quote_button_text": "Get 30-Second Online Quote",
//         "img-hero-section-image": "charter-bus",
//         "img-advantage-image": "advantage",
//         "hero_section_card1_content": "Get online pricing & availability in 30-seconds",
//         "hero_section_card2_content": "Customer Support available everyday 24/7/365",
//         "hero_section_card3_content": "Search All Different Types of Buses to Find the perfect fit for Your Trip"
//       },
//       "dynamic why section 6pm": {
//         "img-background-pattern-bg-image": "background-pattern"
//       }
//     },
//     "Final Testing Purpose": {
//       "Test componenet": {
//         "Title": "Sample Title",
//         "Amount": "$500",
//         "link_span": "View Details",
//         "view_link": "/bus-details",
//         "Get_quote": "Get Quote Now"
//       },
//       "section Template": {
//         "Title": "Sample Title",
//         "description": "Sample Description",
//         "ViewButtonLink": "/bus-details",
//         "ViewButton": "View All Buses",
//         "ITEMS": [
//           {
//             "img-images": "charter-bus",
//             "names": "Sample Title",
//             "state": "$500",
//             "population": "View Details",
//             "famousFor": "/bus-details",
//             "name": "Get Quote Now"
//           },
//           {
//             "img-images": "charter-bus1",
//             "names": "Sample Title1",
//             "state": "$5001",
//             "population": "View Details1",
//             "famousFor": "/bus-details1",
//             "name": "Get Quote Now1"
//           }
//         ]
//       }
//     }
//   },
