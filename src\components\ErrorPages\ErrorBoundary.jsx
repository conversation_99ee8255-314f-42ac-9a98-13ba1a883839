import React from "react";
import { Result, Button } from "antd";
import { ReloadOutlined, HomeOutlined } from "@ant-design/icons";

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error to console or error reporting service
    console.error("ErrorBoundary caught an error:", error, errorInfo);
    this.setState({
      error: error,
      errorInfo: errorInfo,
    });
  }

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = "/";
  };

  render() {
    if (this.state.hasError) {
      return (
        <div className="tw-min-h-screen tw-flex tw-items-center tw-justify-center tw-bg-gray-50">
          <div className="tw-max-w-md tw-w-full tw-mx-4">
            <Result
              status="error"
              title="Something went wrong"
              subTitle="An unexpected error occurred. Please try refreshing the page or contact support if the problem persists."
              extra={[
                <Button
                  type="primary"
                  icon={<ReloadOutlined />}
                  onClick={this.handleReload}
                  className="tw-mr-2"
                  key="reload"
                >
                  Reload Page
                </Button>,
                <Button
                  icon={<HomeOutlined />}
                  onClick={this.handleGoHome}
                  key="home"
                >
                  Go Home
                </Button>,
              ]}
            />

            {process.env.NODE_ENV === "development" && this.state.error && (
              <div className="tw-mt-6 tw-p-4 tw-bg-red-50 tw-border tw-border-red-200 tw-rounded">
                <h4 className="tw-text-red-800 tw-font-semibold tw-mb-2">
                  Error Details (Development Only):
                </h4>
                <pre className="tw-text-xs tw-text-red-700 tw-overflow-auto">
                  {this.state.error.toString()}
                  {this.state.errorInfo.componentStack}
                </pre>
              </div>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
