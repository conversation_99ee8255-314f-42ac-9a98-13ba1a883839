import React, { useState, useEffect } from "react";
import {
  <PERSON>ton,
  Typo<PERSON>,
  <PERSON>,
  Row,
  Col,
  Card,
  message,
  Spin,
  Empty,
} from "antd";
import { PlusOutlined, UserOutlined, TeamOutlined } from "@ant-design/icons";
import useHttp from "../../hooks/use-http";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import UserTable from "./UserTable";
import UserModal from "./UserModal";
import UserSearch from "./UserSearch";
import SearchBar from "../common/SearchBar";
import useDebounce from "../../hooks/useDebounce";
import { apiGenerator } from "../../util/functions";

const { Title, Text } = Typography;

const UserManager = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState("");
  const [selectedUser, setSelectedUser] = useState(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modalMode, setModalMode] = useState("add"); // 'add', 'edit', 'view'
  const [deleteLoading, setDeleteLoading] = useState({});
  const debouncedSearchText = useDebounce(searchText, 300);

  const api = useHttp();

  useEffect(() => {
    fetchUsers();
  }, [debouncedSearchText]);

  const fetchUsers = () => {
    setLoading(true);
    api.sendRequest(
      apiGenerator(
        CONSTANTS.API.users.get,
        {},
        `?search=${debouncedSearchText}`
      ),
      (res) => {
        setUsers(res?.data?.rows || res || []);
        setLoading(false);
      },
      null,
      null,
      (error) => {
        message.error("Failed to fetch users");
        setLoading(false);
      }
    );
  };

  const handleAddUser = () => {
    setSelectedUser(null);
    setModalMode("add");
    setIsModalVisible(true);
  };

  const handleEditUser = (user) => {
    setSelectedUser(user);
    setModalMode("edit");
    setIsModalVisible(true);
  };

  const handleViewUser = (user) => {
    setSelectedUser(user);
    setModalMode("view");
    setIsModalVisible(true);
  };

  const handleDeleteUser = (userId) => {
    setDeleteLoading((prev) => ({ ...prev, [userId]: true }));
    api.sendRequest(
      apiGenerator(CONSTANTS.API.users.delete, { id: userId }),
      (res) => {
        message.success("User deleted successfully");
        fetchUsers();
        setDeleteLoading((prev) => ({ ...prev, [userId]: false }));
      },
      null,
      null,
      (error) => {
        message.error(error || "Failed to delete user");
        setDeleteLoading((prev) => ({ ...prev, [userId]: false }));
      }
    );
  };

  const handleModalClose = () => {
    setIsModalVisible(false);
    setSelectedUser(null);
  };

  const handleUserSaved = () => {
    fetchUsers();
    handleModalClose();
  };

  // const filteredUsers = users?.filter(
  //   (user) =>
  //     user.username?.toLowerCase().includes(searchText.toLowerCase()) ||
  //     user.email?.toLowerCase().includes(searchText.toLowerCase())
  // );

  return (
    <div className="tw-p-6">
      <Card className="tw-bg-transparent tw-border-none">
        <Row
          justify="space-between"
          align="middle"
          className="tw-mb-6"
          gutter={[16, 16]}
        >
          <Col xs={24} sm={12} md={12} lg={12}>
            <Space align="center">
              <Title level={2} className="!tw-mb-0">
                Users ({users.length})
              </Title>
            </Space>
          </Col>
          <Col xs={24} sm={12} md={12} lg={12} style={{ textAlign: "right" }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddUser}
              className="tw-px-6 tw-h-10 tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
            >
              Add User
            </Button>
          </Col>
        </Row>

        <Row className="tw-mb-4">
          <Col span={24}>
            <SearchBar
              type="page"
              placeholder="Search users..."
              handleSearch={(e) => setSearchText(e)}
            />
            {/* <UserSearch
              searchText={searchText}
              onSearchChange={setSearchText}
            /> */}
          </Col>
        </Row>

        <UserTable
          users={users}
          loading={loading || api.isLoading}
          deleteLoading={deleteLoading}
          onEdit={handleEditUser}
          onView={handleViewUser}
          onDelete={handleDeleteUser}
        />

        {/* {filteredUsers.length === 0 && !loading && (
          <Empty
            image={<UserOutlined style={{ fontSize: 64, color: "#d9d9d9" }} />}
            description={
              <Space direction="vertical" size="small">
                <Text type="secondary">No users found</Text>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAddUser}
                  className="tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-border-none"
                >
                  Create First User
                </Button>
              </Space>
            }
          />
        )} */}
      </Card>

      <UserModal
        visible={isModalVisible}
        mode={modalMode}
        user={selectedUser}
        onClose={handleModalClose}
        onSave={handleUserSaved}
      />
    </div>
  );
};

export default UserManager;
