# Technical Context

## 1. Technology Stack

- **Frontend:** React 18, Vite.js
- **Styling:** Tailwind CSS (prefix: tw-), Ant Design (antd)
- **Routing:** React Router DOM
- **State Management:** React Context API (`AuthContext`, `SidebarContext`)
- **Caching**: Runtime caching (`src/utils/runtimeCache.js`) for in-memory caching of components and categories.
- **UI Components:** Lucide React for icons
- **Code Editor:** `react-simple-code-editor` with `prismjs` for syntax highlighting
- **Drag and Drop:** `react-dnd` with `react-dnd-html5-backend`
- **Backend (inferred from dependencies):** Express.js, SQLite3, bcryptjs for hashing, jsonwebtoken for auth. The backend seems to be running within the same project structure under the `server` directory.
- **File Handling:** `jszip`, `archiver`, `file-saver`, `mammoth`, `multer`, `xml2js`
- **HTTP Client:** Axios
- **Headless Browser:** Puppeteer

## 2. Development Environment

- **Prerequisites:** Node.js, npm/yarn
- **Installation:** `npm install`
- **Running the app:** `npm run dev`
- **Linting:** ESLint with TypeScript support

## 3. Technical Constraints

- The project uses Vite.js, so the environment variable handling is done via `import.meta.env`.
- The backend and frontend are likely intended to be run concurrently, with the frontend making API calls to the backend.

## 4. Dependencies

- **Jira:** Task tracking
- **Slack:** Team Communication
- **Figma:** UI Design
- **Postman:** API Testing
- **GitHub:** Version Control & PRs
