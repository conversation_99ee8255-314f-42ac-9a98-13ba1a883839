import React, { useCallback, useMemo, useRef, useState } from "react";
import SearchBar from "../../common/SearchBar";
import { Button, Collapse, Input, message } from "antd";
import JsonContentCollapse from "../../common/JsonContentCollapse";
import JoditTextEditor from "../../common/JoditTextEditor";
import mammoth from "mammoth";
import {
  addAtPath,
  deleteAtPath,
  parseContent,
  updateAtPath,
} from "../../../util/functions";
import { deepMerge } from "../../FeaturePage/components/function";
import { prePareContent } from "../../../utils/Content";
// import { buildItemList } from "../../../util/functions";

const { Panel } = Collapse;
const { TextArea } = Input;

const ContentCollapseBar = ({
  contentJSON,
  setContentJSON,
  saving,
  setReset,
  templateObj = {},
  dynamicContentForm = {},
  isImportDoc = false,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [expandedAll, setExpandedAll] = useState(false);
  const [importing, setImporting] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const fileInputRef = useRef(null);
  const fileInputExportRef = useRef(null);

  const handleAddItem = useCallback((path, key) => {
    try {
      // setContentJSON((prev) => addAtPath(prev, path));
      addItemHandler(path, key);
      message.success("Item added");
    } catch (e) {
      console.error("Error in handleAddItem:", e);
      message.error("Failed to add item");
    }
  }, []);

  const handleSearch = useCallback((value) => {
    // console.log(value);
    setSearchTerm(value);
  }, []);

  const handleDelete = useCallback((path, fieldKey = null) => {
    try {
      setContentJSON((prev) => deleteAtPath(prev, path, fieldKey));
      message.success("Item deleted successfully");
    } catch (e) {
      console.error("Error in handleDelete:", e);
      message.error("Failed to delete item");
    }
  }, []);
  const contentChangeHandler = ({ variableName, value, e, path }) => {
    // console.log(
    //   { variableName, value: e.target.value, path },
    //   "contentChangeHandler"
    // );
    if (path?.length < 2) return console.error("path is less than 2");
    setContentJSON((prev) => {
      // console.log(prev, "prev");
      const newValue = JSON.parse(JSON.stringify(prev));
      let current = newValue;
      path?.map((key) => {
        current = current[key] || {};
      });
      current[variableName] = value;
      return newValue;
    });
    setReset((prev) => !prev);
  };

  const addItemHandler = (path, key) => {
    setContentJSON((prev) => {
      // console.log(prev, "prev");
      const newValue = JSON.parse(JSON.stringify(prev));
      let currentpage = templateObj?.pages?.find(
        (page) => page.name?.trim() == path[0]?.trim()
      );
      let currentCom = currentpage?.components?.find(
        (com) => com.name?.trim() == path[1]?.trim()
      );
      let current = newValue;
      path?.map((key) => {
        current = current[key] || {};
      });
      const intialPlaceholder = {};
      currentCom?.repeatComponents?.placeholders?.map((placeholder) => {
        intialPlaceholder[placeholder] = "";
      });
      current[key].push(intialPlaceholder);
      return newValue;
    });
    setReset((prev) => !prev);
  };

  const deleteItemHandler = (path, index) => {
    setContentJSON((prev) => {
      // console.log(prev, "prev");
      const newValue = JSON.parse(JSON.stringify(prev));
      let current = newValue;
      path?.map((key) => {
        current = current[key] || {};
      });
      current.splice(index, 1);
      // console.log({ newValue }, "newValue");
      return newValue;
    });
    setReset((prev) => !prev);
  };

  const buildItemList = (data, expandedAll, path = []) => {
    if (Array.isArray(data)) {
      return data.map((item, index) => ({
        key: [...path, index].join("_"),
        label: `Position ${index + 1}`,
        isDelete: true,
        onDelete: () => deleteItemHandler([...path], index),
        children: (
          <>
            {/* recurse deeper only if object/array */}
            {typeof item === "object" && item !== null ? (
              <JsonContentCollapse
                expanded={expandedAll}
                itemList={buildItemList(item, expandedAll, [...path, index])}
                // onDeleteItem={(it) => handleDelete([...path, index], it.key)}
              />
            ) : (
              <JoditTextEditor
                value={item}
                key={[...path, index].join("_")}
                height="100"
                placeholder="Enter your content here..."
                onBlur={(newContent) => {
                  // contentChangeHandler({ variableName: key, e: { target: { value: newContent } }, path: [...path, index] });
                  // console.log(path, index, newContent, "newContent");
                  setContentJSON((prev) =>
                    updateAtPath(prev, [...path, index], () => newContent)
                  );
                }}
              />
            )}
          </>
        ),
      }));
    } else if (typeof data === "object" && data !== null) {
      return Object.keys(data).map((key) => ({
        key: [...path, key].join("_"),
        label: key,

        extra: Array.isArray(data[key]),
        // onDelete: () => handleDelete(path, key),
        children:
          typeof data[key] === "object" && data[key] !== null ? (
            <>
              <JsonContentCollapse
                expanded={expandedAll}
                itemList={buildItemList(data[key], expandedAll, [...path, key])}
                // onDeleteItem={(it) => handleDelete([...path, key], it.key)}
              />
              {Array.isArray(data[key]) && (
                <div className="tw-mt-2 tw-w-full tw-flex tw-justify-center tw-items-center">
                  <Button
                    type="dashed"
                    className="tw-w-full"
                    onClick={() => handleAddItem([...path], key)}
                  >
                    + Add Item
                  </Button>
                </div>
              )}
            </>
          ) : (
            <JoditTextEditor
              value={data[key]}
              height="100"
              placeholder="Enter your content here..."
              key={[...path, key].join("_")}
              onBlur={(newContent) => {
                // Update contentChangeHandler to work with Jodit's onChange
                contentChangeHandler({
                  variableName: key,
                  value: newContent,

                  // e,
                  path,
                });
                // if (path?.length < 2)
                //   return console.error("path is less than 2");
                // setContentJSON((prev) => {
                //   const newValue = JSON.parse(JSON.stringify(prev));
                //   let current = newValue;
                //   path?.map((pathKey) => {
                //     current = current[pathKey] || {};
                //   });
                //   current[key] = newContent;
                //   return newValue;
                // });
                // setReset((prev) => !prev);
              }}
            />
          ),
      }));
    } else {
      // ✅ Primitive leaf node → directly render input, no nested collapse
      return [
        {
          key: path.join("_"),
          label: path[path.length - 1],
          children: (
            <JoditTextEditor
              value={data}
              height="100"
              key={path.join("_")}
              placeholder="Enter your content here..."
              onBlur={(newContent) =>
                setContentJSON((prev) =>
                  updateAtPath(prev, path, () => newContent)
                )
              }
            />
          ),
        },
      ];
    }
  };

  const toggleExpandAll = () => {
    setLoading(true);
    setTimeout(() => {
      setExpandedAll((prev) => !prev);
      setLoading(false);
    }, 0);
  };

  const handleImportJSON = () => {
    if (fileInputRef.current) fileInputRef.current.click();
  };

  const onFileChange = async (e) => {
    const file = e.target.files?.[0];
    if (!file) return;
    setImporting(true);
    try {
      const text = await file.text();
      const parsed = JSON.parse(text);
      if (!parsed || typeof parsed !== "object" || Array.isArray(parsed)) {
        throw new Error("Invalid JSON structure. Expected an object at root.");
      }
      // console.log(parsed, "parsed");
      setContentJSON((pr) => {
        // console.log(deepMerge(pr, parsed), "deepMerge", parsed, pr);
        return deepMerge(pr, parsed);
      });
      message.success("JSON imported successfully");
    } catch (err) {
      console.error("Import JSON error:", err);
      message.error("Failed to import JSON. Please check the file.");
    } finally {
      setImporting(false);
      setReset((prev) => !prev);
      // Reset input value to re-trigger change event if same file is selected again
      if (fileInputRef.current) fileInputRef.current.value = "";
    }
  };

  const filteredContent = useMemo(() => {
    if (!contentJSON) return {};
    if (!searchTerm) return contentJSON;

    const lowercasedTerm = searchTerm.toLowerCase();
    return Object.keys(contentJSON)
      .filter((key) => key.toLowerCase().includes(lowercasedTerm))
      .reduce((obj, key) => {
        obj[key] = contentJSON[key];
        return obj;
      }, {});
  }, [contentJSON, searchTerm]);

  const itemList = useMemo(() => {
    const dataToDisplay = searchTerm ? filteredContent : contentJSON;
    if (!dataToDisplay) return [];
    return Object.keys(dataToDisplay).map((key) => ({
      key,
      label: key,
      // extra: Array.isArray(dataToDisplay[key]),
      children: (
        <>
          <JsonContentCollapse
            expanded={expandedAll}
            itemList={buildItemList(dataToDisplay[key], expandedAll, [key])}
            // onDeleteItem={(it) => handleDelete([key], it.key)}
          />
        </>
      ),
    }));
  }, [
    contentJSON,
    searchTerm,
    filteredContent,
    expandedAll,
    handleDelete,
    handleAddItem,
  ]);

  const handleImportDOC = () => {
    if (fileInputExportRef.current) fileInputExportRef.current.click();
  };

  const handleFileSelect = async (e) => {
    e.preventDefault();

    const selectedFile = e.target.files?.[0];
    if (!selectedFile) return;

    // setFile(selectedFile);
    // setError(null)
    setIsProcessing(true);

    try {
      const arrayBuffer = await selectedFile.arrayBuffer();
      const result = await mammoth.convertToHtml({ arrayBuffer });
      // console.log(result);
      if (result?.messages && result?.messages?.length > 0) {
        console.warn("Conversion warnings:", result?.messages);
      }

      // setHtmlContent(result.value);

      // Parse the HTML content into structured JSON
      const structuredData = parseContent(result.value);
      // const structuredData = parser.parse();
      // console.log(structuredData, "structuredData");
      const content = prePareContent(structuredData, dynamicContentForm);
      // console.log(content, "content");
      setContentJSON((pr) => {
        // console.log(pr, content, deepMerge(pr, content), "deepMerge");
        return deepMerge(pr, content);
      });
      // setParsedData(structuredData);
    } catch (err) {
      console.error("Error processing file:", err);
      // setError(`Error processing file: ${err.message}`)
    } finally {
      setIsProcessing(false);
      setReset((prev) => !prev);
    }
  };

  return (
    <div className="tw-h-full tw-flex tw-flex-col tw-overflow-hidden">
      {/* Header Section - Fixed */}
      <div className="tw-flex-shrink-0 tw-space-y-3 tw-px-4 ">
        <div className="">
          <SearchBar type="page" handleSearch={(e) => handleSearch(e)} />
        </div>
        <div className="tw-flex tw-space-x-4 tw-w-full tw-justify-between tw-items-center">
          <Button
            type="primary"
            size="large"
            onClick={handleImportJSON}
            disabled={saving || importing}
            className="tw-px-6 tw-w-full tw-h-10 tw-flex tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
          >
            Import JSON
          </Button>
          {isImportDoc && (
            <Button
              type="primary"
              size="large"
              onClick={handleImportDOC}
              disabled={saving || isProcessing}
              className="tw-px-6 tw-w-full tw-h-10 tw-flex tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
            >
              Import Doc
            </Button>
          )}

          <Button
            size="large"
            onClick={toggleExpandAll}
            loading={loading}
            disabled={loading}
            className="tw-flex tw-w-full tw-items-center tw-text-black tw-border-blue-200 hover:tw-bg-blue-50"
          >
            {expandedAll ? "Collapse All" : "Expand All"}
          </Button>
        </div>
        {/* Hidden file input for JSON import */}
        <input
          type="file"
          accept="application/json,.json"
          ref={fileInputRef}
          onChange={onFileChange}
          style={{ display: "none" }}
        />
        <input
          ref={fileInputExportRef}
          type="file"
          accept=".docx"
          onChange={handleFileSelect}
          className="file-input"
          style={{ display: "none" }}
        />
      </div>

      {/* Scrollable Content Section */}
      <div className="tw-flex-1 tw-overflow-y-auto tw-p-4">
        {itemList?.length ? (
          <JsonContentCollapse itemList={itemList} expanded={expandedAll} />
        ) : (
          <div className="tw-text-center tw-py-8">
            <p className="tw-text-gray-500 tw-mb-2">No content fields found</p>
            <p className="tw-text-sm tw-text-gray-400">
              Add content fields to see them here
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ContentCollapseBar;
