import { Radio } from "antd";
import React from "react";

const TabList = ({ tabContents, setPreviewMode, previewMode }) => {
  return (
    <>
      <div className=" tw-px-4 tw-py-3">
        <Radio.Group
          value={previewMode}
          onChange={(e) => setPreviewMode(e.target.value)}
          buttonStyle="solid"
          size="large"
          style={{ width: "100%" }}
          className="component-tab-list tw-w-full tw-flex tw-p-[2px] tw-border tw-border-[#2563EB] tw-rounded-[10px]"
        >
          {Object.values(tabContents)?.map((tab) => (
            <Radio.Button
              key={tab.key}
              value={tab.key}
              className="tw-text-center !tw-rounded-[10px] before:!tw-w-0 tw-border-0 border-b-"
              style={{ width: "100%" }}
              // style={{ width: "33.33%" }}
            >
              <div className="tw-flex tw-items-center tw-justify-center">
                {tab?.icon ?? ""}
                {tab?.label ?? ""}
              </div>
            </Radio.Button>
          ))}
        </Radio.Group>
      </div>

      {tabContents[previewMode]?.content ? (
        tabContents[previewMode]?.content
      ) : (
        <></>
      )}
    </>
  );
};

export default TabList;
