import { <PERSON><PERSON>, But<PERSON>, message, Modal, Space, Typography } from "antd";
import React, { useMemo } from "react";
import { stringifyPreservingUndefined } from "../../../util/functions";

const WarningModal = ({ empties, open, setOpen }) => {
  const handleDownloadMissing = () => {
    const blob = new Blob([emptyJson], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "missing-fields.json";
    a.click();
    URL.revokeObjectURL(url);
  };

  const emptyJson = useMemo(() => {
    if (!empties) return "";
    return stringifyPreservingUndefined(empties);
  }, [empties]);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(emptyJson);
      message.success("Missing fields JSON copied");
    } catch {
      message.error("Copy failed");
    }
  };
  return (
    <Modal
      title="Some fields are empty"
      open={open}
      onCancel={() => setOpen(false)}
      footer={
        <Space style={{ width: "100%", justifyContent: "space-between" }}>
          <Space>
            <Button onClick={handleCopy}>Copy JSON</Button>
            <Button onClick={handleDownloadMissing}>Download JSON</Button>
          </Space>
          <Space>
            <Button onClick={() => setOpen(false)}>Cancel</Button>
            {/* <Button type="primary" danger loading={pendingExport} onClick={handleContinue}>
                Continue & Export
              </Button> */}
          </Space>
        </Space>
      }
    >
      <Alert
        type="warning"
        showIcon
        message="We found keys with empty values (“”, null, or undefined). Fill them to avoid incomplete content."
        style={{ marginBottom: 12 }}
      />
      <Typography.Paragraph type="secondary" style={{ marginBottom: 8 }}>
        Missing/empty fields preview:
      </Typography.Paragraph>
      <pre
        style={{
          background: "#0b0b0b0a",
          border: "1px solid #eee",
          borderRadius: 8,
          padding: 12,
          maxHeight: 320,
          overflow: "auto",
          margin: 0,
          fontSize: 12,
        }}
      >
        {emptyJson || "// No missing fields"}
      </pre>
      {empties && (
        <Typography.Paragraph style={{ marginTop: 8 }} type="secondary">
          Note: <code>__undefined__</code> represents JavaScript{" "}
          <code>undefined</code>.
        </Typography.Paragraph>
      )}
    </Modal>
  );
};

export default WarningModal;
