
import fs from "fs";


const INPUT_FILE = "./grandisland-schema.json";
const OUTPUT_FILE = "output.json";

// Toggle: use your requested logo pattern or keep original logo
const USE_LOGO_PATTERN = true;

// Helper: safe JSON parse for individual <script> contents
function safeParse(jsonText) {
    try {
        return JSON.parse(jsonText);
    } catch {
        return null;
    }
}

// Replace the site's origin with ${website_url} (preserve path)
function replaceBaseUrl(href) {
    if (typeof href !== "string") return href;
    try {
        const u = new URL(href);
        return "${website_url}" + (u.pathname === "/" ? "" : u.pathname);
    } catch {
        // if relative or invalid, best-effort
        return href.replace(/^https?:\/\/[^/]+/i, "${website_url}");
    }
}

// Build LocalBusiness template (your Home example generalized)
function buildLocalBusinessTemplate(original) {
    const out = {
        "@context": "https://schema.org",
        "@type": "LocalBusiness",
        "name": "${company_name}",
        "url": "${website_url}",
        "logo": USE_LOGO_PATTERN
            ? "${website_url}assets/${default_city_small}-logo.png"
            : (original?.logo ? replaceBaseUrl(original.logo) : "${website_url}assets/${default_city_small}-logo.png"),
        "telephone": "${mobile_num}",
        "description":
            "${company_name} is a highly rated minibus, motor coach & charter bus rental in ${default_city}, ${default_state} open 24/7/365. Experience reservation agents are \n\tavailable at ${mobile_num}",
        "priceRange": "${priceRange}"
    };
    return out;
}

// Build Product/AggregateRating template
function buildProductTemplate() {
    return {
        "@context": "https://schema.org",
        "@type": "Product",
        "name": "${company_name}",
        "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "${ratingValue}",
            "bestRating": "${bestRating}",
            "reviewCount": "${reviewCount}"
        }
    };
}

// Build WebSite template
function buildWebsiteTemplate() {
    return {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "${company_name}",
        "url": "${website_url}"
    };
}

// Templatize BreadcrumbList but keep structure (swap domain → ${website_url})
function buildBreadcrumbTemplate(original) {
    const itemListElement = Array.isArray(original?.itemListElement)
        ? original.itemListElement.map((li) => {
            const copy = { ...li };
            if (copy.item) copy.item = replaceBaseUrl(copy.item);
            return copy;
        })
        : original?.itemListElement;

    return {
        "@context": "https://schema.org/",
        "@type": "BreadcrumbList",
        "itemListElement": itemListElement || []
    };
}

// Parse the concatenated raw HTML string into array of <script> contents
function splitScripts(raw) {
    if (typeof raw !== "string") return [];
    const scripts = [];
    const startTag = '<script type="application/ld+json">';
    const endTag = "</script>";
    let idx = 0;
    while (true) {
        const s = raw.indexOf(startTag, idx);
        if (s === -1) break;
        const e = raw.indexOf(endTag, s + startTag.length);
        if (e === -1) break;
        const inner = raw.slice(s + startTag.length, e).trim();
        scripts.push(inner);
        idx = e + endTag.length;
    }
    return scripts;
}

// Convert one page's value (concatenated scripts) → templated concatenated scripts
function convertPageValueToTemplate(rawScripts, pageKey) {
    const blocks = splitScripts(rawScripts);
    const templatedBlocks = [];

    for (const block of blocks) {
        const obj = safeParse(block);

        // If not JSON or unknown, preserve as-is (wrapped again)
        if (!obj || typeof obj !== "object") {
            templatedBlocks.push(`<script type="application/ld+json">\n${block}\n</script>`);
            continue;
        }

        const type = obj["@type"];

        // Some pages (Home) need the specific 3 scripts in order.
        // We'll use type-based mapping for all pages, but preserve any Breadcrumbs.
        let outObj = null;

        if (type === "LocalBusiness") {
            outObj = buildLocalBusinessTemplate(obj);
        } else if (type === "Product") {
            outObj = buildProductTemplate();
        } else if (type === "WebSite") {
            outObj = buildWebsiteTemplate();
        } else if (type === "BreadcrumbList") {
            outObj = buildBreadcrumbTemplate(obj);
        } else {
            // Unknown types: pass-through but normalize base URLs
            // (we'll attempt to replace any url-like fields at top-level)
            outObj = { ...obj };
            for (const k of ["url", "logo", "image"]) {
                if (outObj[k]) outObj[k] = replaceBaseUrl(outObj[k]);
            }
        }

        templatedBlocks.push(
            `<script type="application/ld+json">\n${JSON.stringify(outObj, null, 2)}\n</script>`
        );
    }

    // Special case: If page is exactly "Home" but doesn’t contain WebSite script, append one (to mirror your sample)
    if (pageKey === "Home" && !blocks.some(b => /"@type"\s*:\s*"WebSite"/.test(b))) {
        templatedBlocks.push(
            `<script type="application/ld+json">${JSON.stringify(buildWebsiteTemplate())}</script>`
        );
    }

    return templatedBlocks.join("\n");
}

// -------- Main --------
const input = JSON.parse(fs.readFileSync(INPUT_FILE, "utf8"));
const output = {};

for (const [pageName, rawScripts] of Object.entries(input)) {
    output[pageName] = convertPageValueToTemplate(rawScripts, pageName);
}

fs.writeFileSync(OUTPUT_FILE, JSON.stringify(output, null, 2), "utf8");
console.log(`Converted ${Object.keys(output).length} pages -> ${OUTPUT_FILE}`);
