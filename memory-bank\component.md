#

### DeviceButtons

- path: src/components/common/DeviceButtons.jsx
- A component that renders a set of buttons to switch between different device preview modes (e.g., desktop, tablet, mobile).

#

### EditorSnippet

- path: src/components/common/EditorSnippet.jsx
- A wrapper around MonacoCodeEditor that provides advanced code editing with syntax validation, auto-completion, and IntelliSense for HTML, CSS, and JavaScript.

#

### MonacoCodeEditor

- path: src/components/common/MonacoCodeEditor.jsx
- Advanced code editor component using Monaco Editor with built-in validation, auto-completion, IntelliSense, and custom placeholder syntax support for ${variableName}.

#

### JoditTextEditor

- path: src/components/common/JoditTextEditor.jsx
- Rich text editor component using Jodit React with comprehensive formatting options, toolbar customization, and WYSIWYG editing capabilities.

#

### JsonContentCollapse

- path: src/components/common/JsonContentCollapse.jsx
- A reusable and customizable collapse component for displaying and editing JSON data. It supports external control for expanding/collapsing all items.

#

### SearchBar

- path: src/components/common/SearchBar.jsx
- A reusable search bar with a debouncing effect to delay the search action.

#

### TabList

- path: src/components/common/TabList.jsx
- A component that renders a set of tabs using Ant Design's `Radio.Group` and displays the content of the selected tab.

#

### ContentCollapseBar

- path: src/components/Templates/Component/ContentCollapseBar.jsx
- A component that provides a collapsible interface for editing JSON content, with features like search, expand/collapse all, and importing from JSON or DOCX files.

#

### MediaBar

- path: src/components/Templates/Component/MediaBar.jsx
- A media manager component that allows users to upload, view, search, and delete media files.

#

### PageLibrary

- path: src/components/Pages/Component/PageLibrary.jsx
- A component that displays a library of components that can be dragged and dropped onto a page.

#

### PagePreview

- path: src/components/Pages/Component/PagePreview.jsx
- A component that renders a preview of a page and provides a drop zone for adding new components.

#

### PageSetting

- path: src/components/Pages/Component/PageSetting.jsx
- A modal component for editing page settings like name, slug, and meta information.

#

### FormFields

- path: src/components/common/FormFields.jsx
- A reusable form field component that can render an antd Input or TextArea with a label, tooltip, and validation rules.

#

### ScrollPagination

- path: src/components/common/ScrollPagination.jsx
- A reusable infinite scroll component that loads paginated data and renders it as the user scrolls.

#
