/**
 * Runtime Cache Utility for Page Builder Components
 * Provides efficient in-memory caching with automatic cleanup
 * Replaces IndexedDB with runtime-only caching that resets on page reload
 */

class RuntimeCache {
    constructor() {
        this.cache = new Map();
        this.maxCacheSize = 100; // Maximum number of cached items
        this.defaultTTL = 30 * 60 * 1000; // 30 minutes default TTL
    }

    /**
     * Generate cache key for component
     */
    generateCacheKey(componentId, version = 1) {
        return `component_${componentId}_v${version}`;
    }

    /**
     * Generate cache key for category components
     */
    generateCategoryCacheKey(categoryId) {
        return `category_${categoryId}_components`;
    }

    /**
     * Generate cache key for categories list
     */
    generateCategoriesListKey() {
        return 'categories_list';
    }

    /**
     * Generate cache key for template pages
     */
    generateTemplatePagesKey(templateId = 'all') {
        return `template_pages_${templateId}`;
    }

    /**
     * Generate cache key for available template pages (filtered)
     */
    generateAvailableTemplatePagesKey(templateId, usedPageIds = []) {
        const usedPagesHash = usedPageIds.sort().join(',');
        return `available_template_pages_${templateId}_${usedPagesHash}`;
    }

    /**
     * Cache a component with version management
     */
    cacheComponent(component, ttl = this.defaultTTL) {
        const cacheKey = this.generateCacheKey(component.id, component.version || 1);
        const cacheData = {
            data: component,
            cachedAt: Date.now(),
            expiresAt: Date.now() + ttl,
            lastAccessed: Date.now(),
            type: 'component'
        };

        this.cache.set(cacheKey, cacheData);
        console.log(`Component ${component.id} v${component.version || 1} cached in runtime`);

        // Clean up old versions of the same component
        this.cleanupOldVersions(component.id, component.version || 1);

        // Cleanup cache if it exceeds max size
        this.cleanupCache();

        return cacheData;
    }

    /**
     * Retrieve component from cache
     */
    getCachedComponent(componentId, version = 1) {
        const cacheKey = this.generateCacheKey(componentId, version);
        const cacheData = this.cache.get(cacheKey);

        if (!cacheData) {
            console.log(`Component ${componentId} v${version} not found in runtime cache`);
            return null;
        }

        // Check if cache has expired
        if (Date.now() > cacheData.expiresAt) {
            console.log(`Component ${componentId} v${version} cache expired, removing`);
            this.cache.delete(cacheKey);
            return null;
        }

        // Update last accessed time
        cacheData.lastAccessed = Date.now();
        console.log(`Component ${componentId} v${version} retrieved from runtime cache`);
        return cacheData.data;
    }

    /**
     * Cache multiple components for a category
     */
    cacheComponentsByCategory(categoryId, components, ttl = this.defaultTTL) {
        const cacheKey = this.generateCategoryCacheKey(categoryId);
        const cacheData = {
            data: components,
            cachedAt: Date.now(),
            expiresAt: Date.now() + ttl,
            lastAccessed: Date.now(),
            type: 'category_components'
        };

        this.cache.set(cacheKey, cacheData);
        console.log(`${components.length} components cached for category ${categoryId}`);

        // Also cache individual components
        components.forEach(component => {
            this.cacheComponent(component, ttl);
        });

        this.cleanupCache();
        return cacheData;
    }

    /**
     * Get cached components by category
     */
    getCachedComponentsByCategory(categoryId) {
        const cacheKey = this.generateCategoryCacheKey(categoryId);
        const cacheData = this.cache.get(cacheKey);

        if (!cacheData) {
            console.log(`Components for category ${categoryId} not found in runtime cache`);
            return null;
        }

        // Check if cache has expired
        if (Date.now() > cacheData.expiresAt) {
            console.log(`Components for category ${categoryId} cache expired, removing`);
            this.cache.delete(cacheKey);
            return null;
        }

        // Update last accessed time
        cacheData.lastAccessed = Date.now();
        console.log(`Retrieved ${cacheData.data.length} cached components for category ${categoryId}`);
        return cacheData.data;
    }

    /**
     * Cache categories list
     */
    cacheCategories(categories, ttl = this.defaultTTL) {
        const cacheKey = this.generateCategoriesListKey();
        const cacheData = {
            data: categories,
            cachedAt: Date.now(),
            expiresAt: Date.now() + ttl,
            lastAccessed: Date.now(),
            type: 'categories'
        };

        this.cache.set(cacheKey, cacheData);
        console.log(`${categories.length} categories cached in runtime`);
        this.cleanupCache();
        return cacheData;
    }

    /**
     * Get cached categories
     */
    getCachedCategories() {
        const cacheKey = this.generateCategoriesListKey();
        const cacheData = this.cache.get(cacheKey);

        if (!cacheData) {
            console.log('Categories not found in runtime cache');
            return null;
        }

        // Check if cache has expired
        if (Date.now() > cacheData.expiresAt) {
            console.log('Categories cache expired, removing');
            this.cache.delete(cacheKey);
            return null;
        }

        // Update last accessed time
        cacheData.lastAccessed = Date.now();
        console.log(`Retrieved ${cacheData.data.length} cached categories`);
        return cacheData.data;
    }

    /**
     * Check if component exists in cache with specific version
     */
    isComponentCached(componentId, version = 1) {
        const component = this.getCachedComponent(componentId, version);
        return component !== null;
    }

    /**
     * Check if category components are cached
     */
    isCategoryCached(categoryId) {
        const components = this.getCachedComponentsByCategory(categoryId);
        return components !== null;
    }

    /**
     * Cache template pages
     */
    cacheTemplatePages(pages, templateId = 'all', ttl = this.defaultTTL) {
        const cacheKey = this.generateTemplatePagesKey(templateId);
        const cacheData = {
            data: pages,
            cachedAt: Date.now(),
            expiresAt: Date.now() + ttl,
            lastAccessed: Date.now(),
            type: 'template_pages'
        };

        this.cache.set(cacheKey, cacheData);
        console.log(`${pages.length} template pages cached for template ${templateId}`);
        this.cleanupCache();
        return cacheData;
    }

    /**
     * Get cached template pages
     */
    getCachedTemplatePages(templateId = 'all') {
        const cacheKey = this.generateTemplatePagesKey(templateId);
        const cacheData = this.cache.get(cacheKey);

        if (!cacheData) {
            console.log(`Template pages for template ${templateId} not found in runtime cache`);
            return null;
        }

        // Check if cache has expired
        if (Date.now() > cacheData.expiresAt) {
            console.log(`Template pages for template ${templateId} cache expired, removing`);
            this.cache.delete(cacheKey);
            return null;
        }

        // Update last accessed time
        cacheData.lastAccessed = Date.now();
        console.log(`Retrieved ${cacheData.data.length} cached template pages for template ${templateId}`);
        return cacheData.data;
    }

    /**
     * Cache available template pages (filtered by used pages)
     */
    cacheAvailableTemplatePages(pages, templateId, usedPageIds = [], ttl = this.defaultTTL) {
        const cacheKey = this.generateAvailableTemplatePagesKey(templateId, usedPageIds);
        const cacheData = {
            data: pages,
            cachedAt: Date.now(),
            expiresAt: Date.now() + ttl,
            lastAccessed: Date.now(),
            type: 'available_template_pages',
            metadata: { templateId, usedPageIds: [...usedPageIds] }
        };

        this.cache.set(cacheKey, cacheData);
        console.log(`${pages.length} available template pages cached for template ${templateId}`);
        this.cleanupCache();
        return cacheData;
    }

    /**
     * Get cached available template pages
     */
    getCachedAvailableTemplatePages(templateId, usedPageIds = []) {
        const cacheKey = this.generateAvailableTemplatePagesKey(templateId, usedPageIds);
        const cacheData = this.cache.get(cacheKey);

        if (!cacheData) {
            console.log(`Available template pages for template ${templateId} not found in runtime cache`);
            return null;
        }

        // Check if cache has expired
        if (Date.now() > cacheData.expiresAt) {
            console.log(`Available template pages for template ${templateId} cache expired, removing`);
            this.cache.delete(cacheKey);
            return null;
        }

        // Update last accessed time
        cacheData.lastAccessed = Date.now();
        console.log(`Retrieved ${cacheData.data.length} cached available template pages for template ${templateId}`);
        return cacheData.data;
    }

    /**
     * Update template page availability in cache
     */
    updateTemplatePageAvailability(pageId, isAvailable, templateId = 'all') {
        // Invalidate all available template pages cache entries for this template
        const keysToDelete = [];

        for (const [key, value] of this.cache.entries()) {
            if (value.type === 'available_template_pages' &&
                value.metadata &&
                value.metadata.templateId === templateId) {
                keysToDelete.push(key);
            }
        }

        keysToDelete.forEach(key => {
            this.cache.delete(key);
            console.log(`Invalidated available template pages cache: ${key}`);
        });
        console.log(templateId, pageId, keysToDelete, "keysToDelete", this.cache.entries(), keysToDelete.length);
        console.log(`Updated template page ${pageId} availability to ${isAvailable} for template ${templateId}`);
        return keysToDelete.length;
    }

    /**
     * Check if template pages are cached
     */
    isTemplatePagesCache(templateId = 'all') {
        const pages = this.getCachedTemplatePages(templateId);
        return pages !== null;
    }

    /**
     * Check if available template pages are cached
     */
    isAvailableTemplatePagesCache(templateId, usedPageIds = []) {
        const pages = this.getCachedAvailableTemplatePages(templateId, usedPageIds);
        return pages !== null;
    }

    /**
     * Clean up old versions of a component
     */
    cleanupOldVersions(componentId, currentVersion) {
        const keysToDelete = [];

        for (const [key, value] of this.cache.entries()) {
            if (value.type === 'component' && key.startsWith(`component_${componentId}_v`)) {
                const versionMatch = key.match(/component_\d+_v(\d+)/);
                if (versionMatch) {
                    const version = parseInt(versionMatch[1]);
                    if (version < currentVersion) {
                        keysToDelete.push(key);
                    }
                }
            }
        }

        keysToDelete.forEach(key => {
            this.cache.delete(key);
            console.log(`Cleaned up old version: ${key}`);
        });
    }

    /**
     * Clean up cache if it exceeds max size
     */
    cleanupCache() {
        if (this.cache.size <= this.maxCacheSize) {
            return;
        }

        // Convert to array and sort by last accessed time (oldest first)
        const entries = Array.from(this.cache.entries()).sort((a, b) => {
            return a[1].lastAccessed - b[1].lastAccessed;
        });

        // Remove oldest entries until we're under the limit
        const entriesToRemove = entries.slice(0, this.cache.size - this.maxCacheSize);
        entriesToRemove.forEach(([key]) => {
            this.cache.delete(key);
            console.log(`Cache cleanup: removed ${key}`);
        });

        console.log(`Cache cleanup completed. Size: ${this.cache.size}/${this.maxCacheSize}`);
    }

    /**
     * Clear expired entries
     */
    clearExpired() {
        const now = Date.now();
        const keysToDelete = [];

        for (const [key, value] of this.cache.entries()) {
            if (now > value.expiresAt) {
                keysToDelete.push(key);
            }
        }

        keysToDelete.forEach(key => {
            this.cache.delete(key);
            console.log(`Removed expired cache entry: ${key}`);
        });

        if (keysToDelete.length > 0) {
            console.log(`Cleared ${keysToDelete.length} expired cache entries`);
        }
    }

    /**
     * Clear all cache data
     */
    clearCache() {
        const size = this.cache.size;
        this.cache.clear();
        console.log(`Runtime cache cleared. Removed ${size} entries`);
    }

    /**
     * Get cache statistics
     */
    getCacheStats() {
        const stats = {
            totalEntries: this.cache.size,
            components: 0,
            categories: 0,
            categoryComponents: 0,
            expired: 0
        };

        const now = Date.now();
        for (const [key, value] of this.cache.entries()) {
            if (now > value.expiresAt) {
                stats.expired++;
            }

            switch (value.type) {
                case 'component':
                    stats.components++;
                    break;
                case 'categories':
                    stats.categories++;
                    break;
                case 'category_components':
                    stats.categoryComponents++;
                    break;
            }
        }

        return stats;
    }

    /**
     * Invalidate cache for a specific component
     */
    invalidateComponent(componentId) {
        const keysToDelete = [];

        for (const [key, value] of this.cache.entries()) {
            if (value.type === 'component' && key.includes(`component_${componentId}_`)) {
                keysToDelete.push(key);
            }
        }

        keysToDelete.forEach(key => {
            this.cache.delete(key);
            console.log(`Invalidated component cache: ${key}`);
        });

        return keysToDelete.length;
    }

    /**
     * Invalidate cache for a specific category
     */
    invalidateCategory(categoryId) {
        const cacheKey = this.generateCategoryCacheKey(categoryId);
        const deleted = this.cache.delete(cacheKey);

        if (deleted) {
            console.log(`Invalidated category cache: ${cacheKey}`);
        }

        return deleted;
    }

    /**
     * Invalidate template pages cache
     */
    invalidateTemplatePages(templateId = 'all') {
        const cacheKey = this.generateTemplatePagesKey(templateId);
        const deleted = this.cache.delete(cacheKey);

        if (deleted) {
            console.log(`Invalidated template pages cache: ${cacheKey}`);
        }

        // Also invalidate all available template pages cache for this template
        this.updateTemplatePageAvailability(null, null, templateId);

        return deleted;
    }

    /**
     * Invalidate all template-related cache entries
     */
    invalidateAllTemplateCache() {
        const keysToDelete = [];

        for (const [key, value] of this.cache.entries()) {
            if (value.type === 'template_pages' || value.type === 'available_template_pages') {
                keysToDelete.push(key);
            }
        }

        keysToDelete.forEach(key => {
            this.cache.delete(key);
            console.log(`Invalidated template cache: ${key}`);
        });

        console.log(`Invalidated ${keysToDelete.length} template cache entries`);
        return keysToDelete.length;
    }

    /**
     * Start automatic cleanup interval
     */
    startCleanupInterval(intervalMs = 5 * 60 * 1000) { // 5 minutes default
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }

        this.cleanupInterval = setInterval(() => {
            this.clearExpired();
            this.cleanupCache();
        }, intervalMs);

        console.log(`Runtime cache cleanup interval started (${intervalMs}ms)`);
    }

    /**
     * Stop automatic cleanup interval
     */
    stopCleanupInterval() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
            console.log('Runtime cache cleanup interval stopped');
        }
    }
}

// Create singleton instance
const runtimeCache = new RuntimeCache();

// Start automatic cleanup
runtimeCache.startCleanupInterval();

export default runtimeCache;
