// vite.config.ts
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { monaco } from "@bithero/monaco-editor-vite-plugin";
// import monacoEditorPlugin from "vite-plugin-monaco-editor"; // <- lowerCamel, default import

export default defineConfig({
  plugins: [
    react(),
    monaco({
      features: "all",
      languages: ["html", "css", "javascript", "json"],
      globalAPI: true,
    }),
  ],
  optimizeDeps: {
    // keep monaco out of optimize step (usually fine either way with this plugin)
    exclude: ["monaco-editor", "lucide-react"],
  },
  server: { port: 3000 },
  esbuild: { target: "es2020" },
});
