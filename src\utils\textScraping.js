// import fs from "node:fs/promises";
// import path from "node:path";
// import puppeteer from "puppeteer";

// const delay = (ms) => new Promise((r) => setTimeout(r, ms));

// async function readUrlsFromArg(arg) {
//     try {
//         const txt = await fs.readFile(arg, "utf8");
//         return txt.split(/\r?\n/).map((s) => s.trim()).filter(Boolean);
//     } catch {
//         return [arg];
//     }
// }

// function buildInPageExtractor(selector) {
//     return `
//   (() => {
//     function getSlug() {
//       const p = location.pathname.replace(/\\/+\$/,""); // trim trailing slash
//       const last = p.split("/").filter(Boolean).pop() || "home";
//       return last.replace(/\\.[a-z0-9]+\$/i, ""); // strip extension
//     }
//     function getEventMap(selector = ".service-hero-box-banner") {
//       const nodes = Array.from(document.querySelectorAll(selector));
//       const events = nodes.map((el) => {
//         const labelEl = el.querySelector("p, span, h1, h2, h3, h4, h5, h6");
//         let text;
//         if (labelEl) {
//           text = labelEl.textContent.trim();
//         } else {
//           const clone = el.cloneNode(true);
//           clone.querySelectorAll("svg,script,style").forEach((n) => n.remove());
//           text = clone.textContent.replace(/\\s+/g, " ").trim();
//         }
//         return text ? { event_name: text } : null;
//       }).filter(Boolean);
//       const slug = getSlug();
//       return { [slug]: events };
//     }
//     return getEventMap(${JSON.stringify(selector)});
//   })();`;
// }

// function mergeResults(target, addition) {
//     for (const [slug, list] of Object.entries(addition || {})) {
//         if (!Array.isArray(list)) continue;
//         if (!target[slug]) target[slug] = [];
//         target[slug].push(...list);
//     }
//     return target;
// }

// async function scrapeAll(urls, selector) {
//     const browser = await puppeteer.launch({
//         headless: true, // "new" is removed; use true/false
//         args: ["--no-sandbox", "--disable-setuid-sandbox"],
//     });
//     const page = await browser.newPage();

//     await page.setExtraHTTPHeaders({
//         Accept: "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
//         "Accept-Language": "en-US,en;q=0.7",
//     });
//     await page.setUserAgent(
//         "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 " +
//         "(KHTML, like Gecko) Chrome/********* Safari/537.36"
//     );

//     const final = {};
//     for (const url of urls) {
//         try {
//             console.log("→ Visiting:", url);
//             await page.goto(url, { waitUntil: "domcontentloaded", timeout: 45_000 });

//             // Wait for the selector if possible; otherwise give a small grace delay
//             await page
//                 .waitForSelector(selector, { timeout: 15000 })
//                 .catch(() => delay(800));

//             const result = await page.evaluate(buildInPageExtractor(selector));
//             mergeResults(final, result);
//             console.log("  ✓ Captured");
//         } catch (err) {
//             console.warn("  ✗ Failed:", url, "-", err?.message || err);
//         }
//     }

//     await browser.close();

//     const pretty = JSON.stringify(final, null, 2);
//     console.log(pretty);
//     return final;
// }

// const listUrl = [
//     {
//         label: "Airport Shuttles",
//         sitemapLabel: "Airport Bus Rentals",
//         slug: "airport-shuttles",
//         extra: {
//             content_image1: "${company_name}-airport-shuttle-bus",
//             content_image2: "${company_name}-airport-bus-rental",
//             content_title:
//                 "Book Your ${company_name} Airport Shuttles Today!",

//         },
//     },
//     {
//         label: "Construction Site Shuttles",
//         sitemapLabel: "Construction Site Shuttle Services",
//         slug: "construction-site-shuttle-bus-rental",
//         extra: {
//             content_image1:
//                 "${company_name}-construction-site-shuttle-services",
//             content_image2:
//                 "${company_name}-construction-site-shuttle-bus-service",
//             content_title:
//                 "Book Your Construction Site Shuttle Service Today!",
//         },
//     },
//     {
//         label: "Corporate Events",
//         sitemapLabel: "Corporate Bus Rentals",
//         slug: "corporate-bus-rental",
//         extra: {
//             content_image1: "${company_name}-corporate-bus-rental",
//             content_image2: "${company_name}-employee-shuttle",
//             content_title:
//                 "Book Your ${company_name} Corporate Transportation Today!",
//         },
//     },
//     {
//         label: "Emergency Services",
//         sitemapLabel: "Emergency Transportation Services",
//         slug: "emergency-response-bus-rentals",
//         extra: {
//             content_image1:
//                 "${company_name}-emergency-transportation-service",
//             content_image2:
//                 "${company_name}-emergency-transportation-services",
//             content_title: "Book Emergency Transportation With Us!",
//         },
//     },
//     {
//         label: "Government & Military Groups",
//         sitemapLabel: "Government & Military Bus Rentals",
//         slug: "government-bus-rentals",
//         extra: {
//             content_image1:
//                 "${company_name}-government-and-military-bus-rentals",
//             content_image2: "${company_name}-government-bus-rental",
//             content_title:
//                 "Bus Rentals to Government & Military Spaces in the Tri-State Area",
//         },
//     },
//     {
//         label: "Hospital & Healthcare Shuttles",
//         sitemapLabel: "Hospital & Healthcare Shuttles",
//         slug: "hospital-shuttles",
//         extra: {
//             content_image1:
//                 "${company_name}-hospital-and-healthcare-shuttles",
//             content_image2: "${company_name}-hospital-shuttles",
//             content_title:
//                 "Meet the Network Behind Your Healthcare Transportation",
//         },
//     },
//     {
//         label: "Private Events",
//         sitemapLabel: "Private Bus Rentals",
//         slug: "event-transportation-services",
//         extra: {
//             content_image1: "${company_name}-private-event-bus-rental",
//             content_image2: "${company_name}-private-bus-rental",
//             content_title:
//                 "Book Your ${company_name} Event Transportation Today!",
//         },
//     },
//     {
//         label: "Proms & Homecomings",
//         sitemapLabel: "Prom & Homecoming",
//         slug: "prom-bus-rental",
//         extra: {
//             content_image1:
//                 "${company_name}-prom-and-homecoming-party-bus-rentals",
//             content_image2: "${company_name}-prom-and-homecoming",
//             content_title: "Book Your Prom Bus Rental Today!",
//         },
//     },
//     {
//         label: "Religious Groups",
//         sitemapLabel: "Religious Charter Bus & Minibus Rentals",
//         slug: "church-bus-rental",
//         extra: {
//             content_image1:
//                 "${company_name}-religious-charter-bus-minibus-rentals",
//             content_image2:
//                 "${company_name}-religious-group-charter-bus-minibus-rentals",
//             content_title:
//                 "Long-Distance Travel, Short-Term or Long-Term: Bus Rental Company ${company_name} Can Do It",
//         },
//     },
//     {
//         label: "School Trips",
//         sitemapLabel: "Field Trip Bus Rentals",
//         slug: "school-event-bus-rental",
//         extra: {
//             content_image1: "${company_name}-school-trip-bus-rental",
//             content_image2: "${company_name}-school-event-bus-rental",
//             content_title:
//                 "Book Your ${company_name} Field Trip & School Bus Rentals Today!",
//         },
//     },
//     {
//         label: "Sports Teams",
//         sitemapLabel: "Sports Team Bus Rentals",
//         slug: "sports-team-transportation",
//         extra: {
//             content_image1: "${company_name}-sports-charter-bus-rental",
//             content_image2: "${company_name}-sports-bus-rental",
//             content_title:
//                 "Book Your ${company_name} Sporting Event Transportation Today!",
//         },
//     },
//     {
//         label: "Summer Camps",
//         sitemapLabel: "Summer Camp Transportation & Bus Rentals",
//         slug: "camp-bus-rentals",
//         extra: {
//             content_image1:
//                 "${company_name}-summer-camp-transportation-and-bus-rentals",
//             content_image2: "${company_name}-camp-bus-rentals",
//             content_title:
//                 "Bus Rentals Built For Long-Distance Adventures Outside of NJ",
//         },
//     },
//     {
//         label: "Travel Agents",
//         sitemapLabel: "Bus Rentals for Travel Agents",
//         slug: "bus-rentals-for-travel-agents",
//         extra: {
//             content_image1: "${company_name}-bus-rentals-for-travel-agents",
//             content_image2: "${company_name}-bus-rental-for-travel-agents",
//             content_title: "Cruise Ship Shuttles for Port Liberty and Beyond",
//         },
//     },
//     {
//         label: "Vacations & Family Trips",
//         sitemapLabel: "Vacation Bus Rentals for Family Trips",
//         slug: "family-vacation-bus-rentals",
//         extra: {
//             content_image1:
//                 "${company_name}-vacation-bus-rentals-for-family-trips",
//             content_image2:
//                 "${company_name}-vacation-bus-rental-for-family-trip",
//             content_title:
//                 "Book Your ${company_name} Vacation Bus Rental Today!",
//         },
//     },
//     {
//         label: "Wedding Shuttles",
//         sitemapLabel: "Wedding Shuttle Rentals",
//         slug: "wedding-bus-rental",
//         extra: {
//             content_image1: "${company_name}-wedding-charter-bus-rental",
//             content_image2: "${company_name}-wedding-bus-rentals",
//             content_title:
//                 "Book Your ${company_name} Wedding Bus Rental Today!",
//         },
//     },
//     {
//         label: "Wine Tours & Pub Crawls",
//         sitemapLabel: "Wine Tour & Pub Crawl Bus Rentals",
//         slug: "wine-tour-pub-crawl-bus-rentals",
//         extra: {
//             content_image1:
//                 "${company_name}-wine-tour-and-pub-crawl-bus-rentals",
//             content_image2: "${company_name}-wine-tour-pub-crawl-bus-rentals",
//             content_title: "We’re Ready To Serve Your Group",
//         },
//     },
// ]
// const urls = listUrl?.map(el => `https://busrentalcompanyclifton.com/clifton-${el?.slug}/`);
// await scrapeAll(urls, ".service-hero-box-banner");

import fs from "node:fs/promises";
import path from "node:path";
import puppeteer from "puppeteer";

const delay = (ms) => new Promise((r) => setTimeout(r, ms));

async function readUrlsFromArg(arg) {
    try {
        const txt = await fs.readFile(arg, "utf8");
        return txt.split(/\r?\n/).map((s) => s.trim()).filter(Boolean);
    } catch {
        return [arg];
    }
}

function buildInPageExtractorForMetaAndLD() {
    // This function runs inside the browser context
    return `
(() => {
  function getSlug() {
    const p = location.pathname.replace(/\\\\+\\$/,""); // trim trailing slash
    const last = p.split("/").filter(Boolean).pop() || "home";
    return last.replace(/\\.[a-z0-9]+$/i, ""); // strip extension
  }

  // Try to parse JSON-LD robustly, including multiple objects in one <script>
  function tryParseJsonLd(text) {
    const results = [];
    const errors = [];

    if (!text || !text.trim()) return { items: results, errors };

    // Most JSON-LD will be a single JSON object/array.
    // If parsing fails, attempt to find multiple JSON objects with a loose matcher.
    function safeParse(block) {
      try {
        const parsed = JSON.parse(block);
        results.push(parsed);
      } catch (e) {
        errors.push(String(e?.message || e));
        // keep raw when parsing fails
        results.push({ _raw: block });
      }
    }

    // First attempt: as-is
    try {
      const once = JSON.parse(text);
      results.push(once);
      return { items: results, errors };
    } catch {
      // Second attempt: extract likely JSON blocks (objects or arrays)
      // Matches balanced braces/brackets at top level (rough heuristic)
      const blocks = [];
      let depth = 0;
      let start = -1;
      let inString = false;
      let esc = false;

      const pushBlock = (endIdx) => {
        const raw = text.slice(start, endIdx + 1).trim();
        if (raw) blocks.push(raw);
      };

      for (let i = 0; i < text.length; i++) {
        const ch = text[i];

        if (inString) {
          if (!esc && ch === "\\\\") {
            esc = true;
          } else if (!esc && ch === '"') {
            inString = false;
          } else {
            esc = false;
          }
          continue;
        }

        if (ch === '"') {
          inString = true;
          continue;
        }

        if (ch === "{" || ch === "[") {
          if (depth === 0) start = i;
          depth++;
        } else if (ch === "}" || ch === "]") {
          depth--;
          if (depth === 0 && start !== -1) {
            pushBlock(i);
            start = -1;
          }
        }
      }

      if (blocks.length) {
        for (const b of blocks) safeParse(b);
      } else {
        // fallback: keep raw text if nothing matched
        results.push({ _raw: text });
      }
      return { items: results, errors };
    }
  }

  const slug = getSlug();
  const metaTitle =
    document.querySelector("title")?.textContent?.trim() ||
    document.querySelector('meta[property="og:title"]')?.getAttribute("content")?.trim() ||
    "";

  const ldNodes = Array.from(
    document.querySelectorAll('script[type="application/ld+json"]')
  );

  const ld_list = ldNodes.map((node, idx) => {
    const raw = node.textContent || "";
    const { items, errors } = tryParseJsonLd(raw);
    return {
      index: idx,
      raw_length: raw.length,
      parsed: items,     // array of parsed objects or {_raw: "..."} items
      parse_errors: errors, // array of error messages (if any)
    };
  });

  return { [slug]: { meta_title: metaTitle, ld_json_list: ld_list } };
})();`;
}

function mergeResults(target, addition) {
    for (const [slug, data] of Object.entries(addition || {})) {
        if (!target[slug]) target[slug] = { meta_title: "", ld_json_list: [] };
        // Prefer first seen non-empty title
        if (!target[slug].meta_title && data?.meta_title) {
            target[slug].meta_title = data.meta_title;
        }
        // concat LD lists
        if (Array.isArray(data?.ld_json_list)) {
            target[slug].ld_json_list.push(...data.ld_json_list);
        }
    }
    return target;
}

async function scrapeMetaAndLd(urls) {
    const browser = await puppeteer.launch({
        headless: true,
        args: ["--no-sandbox", "--disable-setuid-sandbox"],
    });
    const page = await browser.newPage();

    await page.setExtraHTTPHeaders({
        Accept: "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.7",
    });
    await page.setUserAgent(
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 " +
        "(KHTML, like Gecko) Chrome/********* Safari/537.36"
    );

    const final = {};
    for (const url of urls) {
        try {
            console.log("→ Visiting:", url);
            await page.goto(url, { waitUntil: "domcontentloaded", timeout: 45_000 });

            // Give pages a tiny grace to render client-side <script> tags if any
            await delay(600);

            const result = await page.evaluate(buildInPageExtractorForMetaAndLD());
            mergeResults(final, result);
            console.log("  ✓ Captured");
        } catch (err) {
            console.warn("  ✗ Failed:", url, "-", err?.message || err);
        }
    }

    await browser.close();

    const pretty = JSON.stringify(final, null, 2);
    console.log(pretty);
    return final;
}

/* =======================
   EXAMPLE USAGE: same list → URLs
   ======================= */

const listUrl = [
    {
        sectionLabel: "Charter Buses",
        sectionItems: [
            {
                label: "50 Passenger Charter Bus",
                sitemapLabel: "50 Passenger Charter Bus",
                slug: "50-passenger-charter-bus-rental",
            },
            {
                label: "54 Passenger Charter Bus",
                sitemapLabel: "54 Passenger Charter Bus",
                slug: "54-passenger-charter-bus-rental",
            },
            {
                label: "55 Passenger Charter Bus",
                sitemapLabel: "55 Passenger Charter Bus",
                slug: "55-passenger-charter-bus-rental",
            },
            {
                label: "56 Passenger Charter Bus",
                sitemapLabel: "56 Passenger Charter Bus",
                slug: "56-passenger-charter-bus-rental",
            },
        ],
    },
    {
        sectionLabel: "Specialty Vehicles",
        sectionItems: [
            {
                label: "Sprinter Van Rental",
                sitemapLabel: "Sprinter Van Rental",
                slug: "sprinter-van-rental-with-driver",
            },
            {
                label: "Party Bus",
                sitemapLabel: "Party Bus Rental",
                slug: "party-bus-rental",
            },
            {
                label: "Sprinter Limo",
                sitemapLabel: "Sprinter Limo Rental",
                slug: "sprinter-limo-rental",
            },
            {
                label: "School Bus",
                sitemapLabel: "School Bus Rental",
                slug: "school-bus-rental",
            },
        ],
    },
    {
        sectionLabel: "Minibuses",
        sectionItems: [
            {
                label: "15 Passenger Minibus",
                sitemapLabel: "15 Passenger Minibus",
                slug: "15-passenger-minibus-rental",
            },
            {
                label: "18 Passenger Minibus",
                sitemapLabel: "18 Passenger Minibus",
                slug: "18-passenger-minibus-rental",
            },
            {
                label: "20 Passenger Minibus",
                sitemapLabel: "20 Passenger Minibus",
                slug: "20-passenger-minibus-rental",
            },
            {
                label: "25 Passenger Minibus",
                sitemapLabel: "25 Passenger Minibus",
                slug: "25-passenger-minibus-rental",
            },
            {
                label: "28 Passenger Minibus",
                sitemapLabel: "28 Passenger Minibus",
                slug: "28-passenger-minibus-rental",
            },
            {
                label: "30 Passenger Minibus",
                sitemapLabel: "30 Passenger Minibus",
                slug: "30-passenger-minibus-rental",
            },
            {
                label: "35 Passenger Minibus",
                sitemapLabel: "35 Passenger Minibus",
                slug: "35-passenger-minibus-rental",
            },
        ],
    },
]

const urls = ["https://busrentalcompanyclifton.com/contact-us/", "https://busrentalcompanyclifton.com/privacy-policy/"]

// listUrl?.forEach(el => {
//     el?.sectionItems?.forEach(item => {
//         urls.push(`https://busrentalcompanyclifton.com/${item.slug}/`)
//     })
// });

// Run it (top-level await allowed if your runtime supports it)
await scrapeMetaAndLd(urls);




