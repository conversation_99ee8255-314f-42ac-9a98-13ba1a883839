import { But<PERSON> } from "antd";
import J<PERSON>Z<PERSON> from "jszip";
import { busesD<PERSON><PERSON>, serviceAreaD<PERSON><PERSON> } from "../util/content";

function generateSitemapXML(urls) {
  const now = new Date().toISOString();
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urls
  ?.map(
    (url) => `<url>
    <loc>${url?.loc}</loc>
  </url>`
  )
  .join("\n")}
</urlset>`;
  return sitemap;
}
// <lastmod>${url.lastmod || now}</lastmod>
// <changefreq>${url.changefreq || "weekly"}</changefreq>
// <priority>${url.priority || "0.8"}</priority>

// Font URLs to download and self-host
const FONT_URLS = {
  "Inter-400.woff2":
    "https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2",
  "Inter-500.woff2":
    "https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2",
  "Inter-700.woff2":
    "https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2",
  "Phudu-600.woff2":
    "https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2",
  "Phudu-700.woff2":
    "https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2",
};

// Complete working CSS with fonts and Tailwind
const WORKING_CSS = `
/* Font Face Declarations */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('../fonts/Inter-400.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url('../fonts/Inter-500.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url('../fonts/Inter-700.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Phudu';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url('../fonts/Phudu-600.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Phudu';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url('../fonts/Phudu-700.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
`;

// function optimizeHTMLContent(htmlContent) {
//   // Remove Tailwind CDN script and replace with optimized CSS
//   let optimizedHTML = htmlContent;
//   // .replace(/<script src="https:\/\/cdn\.tailwindcss\.com"><\/script>/g, "")
//   // .replace(/<script src="\/\/unpkg\.com\/alpinejs" defer><\/script>/g, "")
//   // .replace(
//   //   /<script>\s*tailwind\.config\s*=\s*\{[\s\S]*?\};\s*<\/script>/g,
//   //   ""
//   // );

//   // Replace font-face declarations with optimized local fonts
//   optimizedHTML = optimizedHTML.replace(/@font-face\s*\{[^}]*\}/g, "");

//   // Add optimized CSS and fonts to head
//   const headEndIndex = optimizedHTML.indexOf("</head>");
//   if (headEndIndex !== -1) {
//     const optimizedHead = `
//     <link rel="preload" href="./css/styles.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
//     <noscript><link rel="stylesheet" href="./css/styles.css"></noscript>
//     <link rel="preload" href="../fonts/Inter-400.woff2" as="font" type="font/woff2" crossorigin>
//     <link rel="preload" href="../fonts/Phudu-600.woff2" as="font" type="font/woff2" crossorigin>
//     `;

//     optimizedHTML =
//       optimizedHTML.slice(0, headEndIndex) +
//       optimizedHead +
//       optimizedHTML.slice(headEndIndex);
//   }

//   // Fix image paths to use relative paths
//   // optimizedHTML = optimizedHTML.replace(
//   //   /src="\/asset\/([^"]+)"/g,
//   //   'src="./assets/$1"'
//   // );

//   // Add lazy loading to images
//   optimizedHTML = optimizedHTML.replace(
//     /<img([^>]*?)src="([^"]*?)"([^>]*?)>/g,
//     '<img$1src="$2"$3loading="lazy"$4>'
//   );

//   return optimizedHTML;
// }

// Function to download fonts
async function downloadFonts(zip) {
  const fontsFolder = zip.folder("fonts");

  for (const [filename, url] of Object.entries(FONT_URLS)) {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      fontsFolder.file(filename, blob);
      console.log(`📦 Downloaded font: ${filename}`);
    } catch (error) {
      console.error(`❌ Failed to download font ${filename}:`, error);
    }
  }
}

/**
 * Generate ZIP file with pages and assets
 */
export const downloadWebsiteZip = async ({
  pageList,
  zipName = "website-export.zip",
  fileList,
  dynamicContent,
  baseUrl,
}) => {
  try {
    // console.log("Starting website ZIP generation...");

    const zip = new JSZip();

    // const cssFolder = zip.folder("css");
    // cssFolder.file("styles.css", WORKING_CSS);

    // // Download and add fonts
    // await downloadFonts(zip);

    // 1) Add HTML pages at their respective paths
    // console.log(`Adding ${pages.length} HTML pages...`);
    for (const { html, path } of pageList) {
      if (!path || !html) continue;

      const cleanPath = path.replace(/^\/+/, ""); // remove leading slash
      const finalPath = cleanPath || "index.html";

      // Ensure HTML content is properly formatted
      const htmlContent = typeof html === "string" ? html : String(html);
      // const optimizedHTML = optimizeHTMLContent(htmlContent);
      zip.file(finalPath, htmlContent);

      // console.log(`✓ Added page: ${finalPath}`);
    }
    // const publicManifest = [
    //   "favicon.ico",
    //   "favicon.png",
    //   "clifton-charter-bus.jpeg",
    // ];
    // 2) Create public/assets folder inside ZIP

    // console.log(fileList, "fileList");
    // if (assetsFolder && fileList) {
    //   for (const [key, value] of Object.entries(fileList)) {
    //     // key = "Mumbai_img-why-section-image"
    //     // value = "/asset/passaic-56-passenger_img-why-section-image.jpeg"

    //     // Take only the extension from value (e.g., ".jpeg")
    //     const ext = value.includes(".")
    //       ? value.substring(value.lastIndexOf("."))
    //       : "";

    //     // Build final filename: key + extension
    //     const finalName = `${key}${ext}`;

    //     // Fetch or load file data
    //     const response = await fetch(value);
    //     const blob = await response.blob();

    //     // Save under assets/
    //     assetsFolder.file(finalName, blob);
    //     console.log(fileList, "fileList");

    //     console.log(`📦 Added asset: assets/${finalName}`);
    //   }
    // }
    // =============================
    // const publicFolder = zip.folder("public");
    // if (publicFolder) {
    //   for (const filePath of publicManifest) {
    //     const response = await fetch(filePath);
    //     const blob = await response.blob();
    //     publicFolder.file(filePath.replace(/^\//, ""), blob);
    //   }
    // }
    // =============================
    const assetsFolder = zip.folder("assets");
    if (assetsFolder && fileList) {
      for (const [key, value] of Object.entries(fileList)) {
        // key = "Mumbai_img-why-section-image"
        // value = "/asset/passaic-56-passenger_img-why-section-image.jpeg"

        // Take only the extension from value (e.g., ".jpeg")
        // const ext = value.includes(".")
        //   ? value.substring(value.lastIndexOf("."))
        //   : "";
        // // filename with extension:
        // const fileWithExt = value?.split("/").pop();
        // // "50-passenger-charter-bus-rental_img-bus_interior_image_1.jpeg"

        // // filename without extension:
        // const fileNoExt = fileWithExt?.replace(/\.[^/.]+$/, "");
        // // "50-passenger-charter-bus-rental_img-bus_interior_image_1"
        // // Remove `_img-` from the key
        // const cleanedKey = key?.replace(/_img-/, "");

        // Build final filename: cleanedKey + extension
        const finalName = `${value?.name}`;

        // Fetch or load file data
        const response = await fetch(value?.url);
        const blob = await response.blob();

        // Save under assets/
        assetsFolder.file(finalName, blob);

        // console.log(`📦 Added asset: assets/${finalName}`);
      }
    }

    //site map ===================================
    // Generate single comprehensive sitemap XML file
    const currentDate = new Date().toISOString().split("T")[0];
    const allUrls = [];

    // Add static pages
    pageList.forEach((page) => {
      allUrls.push({
        loc: `${baseUrl}${page.path || `${page.name || "page"}.html`}`,
        lastmod: currentDate,
        changefreq: "weekly",
        priority: page.path === "index.html" ? "1.0" : "0.8",
      });
    });

    // Add dynamic content pages if available
    if (dynamicContent) {
      // Add bus type pages
      const busSection = dynamicContent?.[busesDKey]?.sections?.[0];
      if (busSection?.sectionItems) {
        busSection.sectionItems.forEach((item) => {
          allUrls.push({
            loc: `${baseUrl}${item.slug}.html`,
            lastmod: currentDate,
            changefreq: "monthly",
            priority: "0.7",
          });
        });
      }

      // Add city pages
      const citySection = dynamicContent?.[serviceAreaDKey]?.sections?.[0];
      if (citySection?.sectionItems) {
        citySection.sectionItems.forEach((item) => {
          allUrls.push({
            loc: `${baseUrl}${item.slug}.html`,
            lastmod: currentDate,
            changefreq: "monthly",
            priority: "0.7",
          });
        });
      }

      // Add service pages
      Object.keys(dynamicContent).forEach((key) => {
        if (key.includes("service") && dynamicContent[key]?.sections) {
          dynamicContent[key].sections.forEach((section) => {
            if (section.sectionItems) {
              section.sectionItems.forEach((item) => {
                allUrls.push({
                  loc: `${baseUrl}${item.slug}.html`,
                  lastmod: currentDate,
                  changefreq: "monthly",
                  priority: "0.7",
                });
              });
            }
          });
        }
      });
    }

    // Generate single comprehensive sitemap
    if (allUrls.length > 0) {
      const comprehensiveSitemap = generateSitemapXML(allUrls);
      zip.file("sitemap.xml", comprehensiveSitemap);
      console.log(
        `🗺️ Added comprehensive sitemap: sitemap.xml (${allUrls.length} URLs)`
      );
    }

    // 4) Generate and download ZIP
    console.log("Generating ZIP file...");
    const zipBlob = await zip.generateAsync({
      type: "blob",
      compression: "DEFLATE",
      compressionOptions: { level: 6 },
    });

    // 5) Trigger download
    const url = URL.createObjectURL(zipBlob);
    const link = document.createElement("a");
    link.href = url;
    link.download = zipName;

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    console.log(`✅ Website ZIP downloaded: ${zipName}`);
    return { success: true, message: "Website exported successfully!" };
  } catch (error) {
    console.error("❌ Export failed:", error);
    throw new Error(`Export failed: ${error.message}`);
  }
};

/**
 * Simple download button component
 */
export const DownloadWebsiteButton = ({
  pages,
  className = "",
  children = "Download Website ZIP",
}) => {
  const handleDownload = async () => {
    try {
      await downloadWebsiteZip(pages);
    } catch (error) {
      alert(`Export failed: ${error.message}`);
    }
  };

  return (
    <Button
      onClick={handleDownload}
      disabled={!pages || pages.length === 0}
      className={`px-4 py-2 rounded-lg bg-blue-600 text-white hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed ${className}`}
    >
      {children}
    </Button>
  );
};

/**
 * Convert pageList to the required format
 */
export const convertPageListToExportFormat = (pageList, fileList) => {
  return pageList.map((page) => ({
    html:
      page?.full_page_content ||
      `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${page?.name || "Page"}</title>
</head>
<body>
  <h1>${page?.name || "Page"}</h1>
  <p>Content will be added here.</p>
</body>
</html>`,
    path: getPagePath(page),
  }));
};

/**
 * Get proper file path for a page
 */
export const getPagePath = (page) => {
  if (page?.type === "static") {
    const pageName = page?.url?.replace(/^\//, "") || page?.name;
    return pageName === "" || pageName === "home" || pageName === "Home"
      ? "index.html"
      : `${pageName}.html`;
  } else if (page?.type === "dynamic") {
    const urlParts = page.url?.split("/").filter(Boolean) || [];
    // if (urlParts.length >= 2) {
    const category = urlParts[0];
    const slug = urlParts[urlParts.length - 1];
    // switch (category) {
    //   case "bues":
    //     return `${slug}-rental.html`;
    //   case "service-area":
    //     return `${slug}-rental.html`;
    //   case "services":
    //     return `${slug}-rental.html`;
    //   default:
    //     return `${slug}.html`;
    // }
    return `${slug}.html`;
    // return `${category}/${slug}.html`;
    // }
    // return `${page.name}.html`;
  }
  return `${page?.name || "page"}.html`;
};
