import React, { useState, useEffect, useCallback } from "react";
import {
  Modal,
  Form,
  Input,
  Select,
  Button,
  Row,
  Col,
  Typography,
  Space,
  Tooltip,
  message,
} from "antd";
import useDebounce from "../../../hooks/useDebounce";
import { X } from "lucide-react";
import useHttp from "../../../hooks/use-http";
import { CONSTANTS } from "../../../util/constant/CONSTANTS";
import { apiGenerator } from "../../../util/functions";
import { autoDetectRepeatPlaceHolder } from "../../../util/functions";

const { Title, Text } = Typography;

const SelectComponentModal = ({
  visible,
  onClose,
  component,
  onSave,
  pageData,
  setPageData,
}) => {
  const [form] = Form.useForm();
  const api = useHttp();
  const [selectedComponents, setSelectedComponents] = useState({});
  const [detectedPlaceholders, setDetectedPlaceholders] = useState([]);
  const [componentOptions, setComponentOptions] = useState({});
  const [loadingStates, setLoadingStates] = useState({});
  const [searchValue, setSearchValue] = useState("");
  const [activeIndex, setActiveIndex] = useState(null);
  const debouncedSearch = useDebounce(searchValue, 500);

  // Effect for debounced search
  useEffect(() => {
    if (
      activeIndex !== null &&
      debouncedSearch !== undefined &&
      debouncedSearch != ""
    ) {
      console.log("Searching for:", debouncedSearch);
      loadComponents(activeIndex, debouncedSearch, 1);
    }
  }, [debouncedSearch, activeIndex]);
  // Detect placeholders from component HTML
  useEffect(() => {
    if (
      component?.repeatComponents &&
      component?.repeatComponents?.length > 0
    ) {
      const initialValues = {};
      const placeholders = [];
      const initialComponentOptions = {};

      component?.repeatComponents.forEach((rc, index) => {
        initialValues[`key_${index}`] = rc?.key;
        // Fix: Use the correct field mapping for transformed data
        // For component selection, we need the componentId (which is the actual component ID)
        initialValues[`component_${index}`] = rc?.componentId;
        // For version selection, we need the versionId (which is the component version ID)
        initialValues[`version_${index}`] = rc?.versionId;

        // Set selected component with proper structure for the modal
        setSelectedComponents((prev) => ({
          ...prev,
          [index]: rc,
        }));

        // IMPORTANT: Add the existing component to componentOptions so the Select shows the label
        // This ensures that when editing, the dropdown shows the component name instead of ID
        initialComponentOptions[index] = [
          {
            value: rc?.componentId,
            label:
              rc?.name ||
              rc?.componentName ||
              rc?.label ||
              `Component ${rc?.componentId}`,
            id: rc?.componentId,
            name: rc?.name || rc?.componentName || rc?.label,
            componentversions: rc?.componentversions || [],
            ...rc,
          },
        ];

        placeholders.push(rc?.key);
      });

      console.log("Initial Values:", initialValues, placeholders);
      console.log("Initial Component Options:", initialComponentOptions);

      setDetectedPlaceholders(placeholders);
      setComponentOptions(initialComponentOptions);
      form.setFieldsValue(initialValues);
    } else {
      if (component?.html) {
        const placeholders = autoDetectRepeatPlaceHolder(component?.html);
        setDetectedPlaceholders(placeholders);
        // Initialize form with detected placeholders
        const initialValues = {};
        placeholders?.forEach((placeholder, index) => {
          initialValues[`key_${index}`] = placeholder;
          initialValues[`component_${index}`] = null;
          initialValues[`version_${index}`] = null;
        });
        form.setFieldsValue(initialValues);
      }
    }
  }, [component, form]);

  // Load components from API for specific placeholder
  const loadComponents = async (placeholderIndex, search = "", page = 1) => {
    setLoadingStates((prev) => ({ ...prev, [placeholderIndex]: true }));

    try {
      api.sendRequest(
        apiGenerator(
          CONSTANTS.API.components.getAllForPage,
          {},
          `?page=${page}&limit=10&search=${search}`
        ),
        (res) => {
          const components = res?.data?.rows || [];
          const newOptions = components
            ?.filter((comp) => comp?.id != component?.componentId)
            ?.map((comp) => ({
              value: comp?.id,
              label: comp?.name,
              ...comp,
            }));
          setComponentOptions((prev) => {
            const existingOptions = prev[placeholderIndex] || [];

            // If we have existing options (from edit mode), merge them with new options
            // but avoid duplicates based on component ID
            if (existingOptions.length > 0) {
              const existingIds = new Set(
                existingOptions.map((opt) => opt.value)
              );
              const uniqueNewOptions = newOptions.filter(
                (opt) => !existingIds.has(opt.value)
              );
              console.log(uniqueNewOptions, existingOptions);
              return {
                ...prev,
                [placeholderIndex]: [...existingOptions, ...uniqueNewOptions],
              };
            } else {
              // No existing options, just use the new ones
              return {
                ...prev,
                [placeholderIndex]: newOptions,
              };
            }
          });
        },
        null,
        null,
        (error) => {
          console.error("Error loading components:", error);
          message.error("Failed to load components");
        }
      );
    } catch (error) {
      console.error("Error loading components:", error);
      message.error("Failed to load components");
    } finally {
      setLoadingStates((prev) => ({ ...prev, [placeholderIndex]: false }));
    }
  };

  // Initialize component options for each placeholder
  useEffect(() => {
    if (visible && detectedPlaceholders.length > 0) {
      detectedPlaceholders.forEach((_, index) => {
        loadComponents(index, "", 1);
      });
    }
  }, [visible, detectedPlaceholders]);

  // Handle component selection change
  const handleComponentChange = (placeholderIndex, componentId) => {
    const componentOptionsForPlaceholder =
      componentOptions[placeholderIndex] || [];
    const selectedComponent = componentOptionsForPlaceholder.find(
      (comp) => comp.id === componentId
    );
    if (selectedComponent) {
      setSelectedComponents((prev) => ({
        ...prev,
        [placeholderIndex]: selectedComponent,
      }));

      // Reset version when component changes
      form.setFieldValue(`version_${placeholderIndex}`, null);
    }
  };

  // Handle search in component dropdown
  const handleComponentSearch = useCallback((placeholderIndex, value) => {
    setSearchValue(value);
    setActiveIndex(placeholderIndex);
  }, []);

  // Handle form submission with component data fetching
  const handleSubmit = async () => {
    try {
      if (detectedPlaceholders.length == 0) {
        onClose();
        return;
      }
      const values = await form.validateFields();
      const repeatComponents = [];

      // Process each placeholder and fetch component data
      for (let index = 0; index < detectedPlaceholders?.length; index++) {
        const key = values[`key_${index}`];
        const versionId = values[`version_${index}`];
        const componentId = values[`component_${index}`];

        if (key && versionId && componentId) {
          // Fetch full component data using CONSTANTS.API.componentVersions.getById
          try {
            const componentData = await new Promise((resolve, reject) => {
              api.sendRequest(
                apiGenerator(CONSTANTS.API.componentVersions.getById, {
                  id: versionId,
                }),
                (res) => {
                  const fullComponentData = res?.data?.[0] || res;
                  resolve(fullComponentData);
                },
                null,
                null,
                (error) => {
                  console.error("Error fetching component version:", error);
                  reject(error);
                }
              );
            });

            repeatComponents?.push({
              key: `${key}`,
              versionId,
              componentId,
              componentName: selectedComponents[index]?.name || "",
              ...selectedComponents[index],
              ...componentData, // Store the full component data
            });
          } catch (error) {
            message.error(
              `Failed to fetch data for component: ${selectedComponents[index]?.name}`
            );
            return;
          }
        }
      }

      // Update pageData with repeat components
      const updatedPageData = {
        repeatComponents,
      };

      onSave(updatedPageData);
      onClose();
      message.success("Component configuration saved successfully");
    } catch (error) {
      console.error("Form validation failed:", error);
      message.error("Please fill in all required fields");
    }
  };

  return (
    <Modal
      title={
        <Space className="tw-items-center">
          <Title level={4} className="tw-mb-0 tw-text-gray-800">
            Select Component
          </Title>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={800}
      footer={
        <div className="tw-flex tw-justify-end tw-space-x-3 tw-pt-4 tw-border-t tw-border-gray-200">
          <Button
            type="default"
            size="large"
            onClick={onClose}
            className="tw-px-6 tw-h-10 tw-rounded-lg tw-font-medium tw-border-gray-300 hover:tw-border-gray-400"
          >
            Cancel
          </Button>
          <Button
            type="primary"
            size="large"
            onClick={handleSubmit}
            className="tw-px-6 tw-h-10 tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
          >
            Save
          </Button>
        </div>
      }
      centered
      className=""
      styles={{
        body: { padding: "6px" },
        header: { borderBottom: "1px solid #f0f0f0", paddingBottom: "8px" },
      }}
      closeIcon={
        <Button
          type="text"
          icon={<X className="tw-w-4 tw-h-4" />}
          className="tw-text-gray-400 hover:tw-text-gray-600 tw-border-0"
        />
      }
    >
      <Form form={form} layout="vertical" className="tw-space-y-6" size="large">
        {/* Placeholder Cards */}
        {detectedPlaceholders?.map((placeholder, index) => (
          <div
            key={index}
            className="tw-bg-gray-[#F6F7F9] tw-rounded-lg tw-p-4 tw-border tw-border-gray-200"
          >
            {/* <Title level={5} className="tw-mb-4 tw-text-gray-800">
              Placeholder {index + 1}
            </Title> */}

            <Row gutter={[16, 16]}>
              <Col xs={24} md={8}>
                <Form.Item
                  label={<Text strong>{"Key"}</Text>}
                  name={`key_${index}`}
                  rules={[{ required: true, message: "Please enter key" }]}
                  className="tw-mb-0"
                >
                  <Input
                    placeholder={`{{${placeholder}}}`}
                    className="tw-rounded-lg"
                    disabled={true}
                  />
                </Form.Item>
              </Col>

              <Col xs={24} md={8}>
                <Form.Item
                  label={<Text strong>{"Component"}</Text>}
                  name={`component_${index}`}
                  className="tw-mb-0"
                  rules={[
                    { required: true, message: "Please select component" },
                  ]}
                >
                  <Select
                    placeholder="Select component"
                    className="tw-rounded-lg"
                    loading={loadingStates[index] || api.isLoading}
                    showSearch
                    filterOption={false}
                    onSearch={(value) => handleComponentSearch(index, value)}
                    onChange={(value) => handleComponentChange(index, value)}
                    options={(componentOptions[index] || [])?.filter((opt) =>
                      opt.label
                        ?.toLowerCase()
                        .includes(searchValue?.toLowerCase())
                    )}
                    notFoundContent={
                      loadingStates[index]
                        ? "Loading..."
                        : "No components found"
                    }
                  />
                </Form.Item>
              </Col>

              <Col xs={24} md={8}>
                <Form.Item
                  label={<Text strong>{"Version"}</Text>}
                  name={`version_${index}`}
                  className="tw-mb-0"
                  rules={[{ required: true, message: "Please select version" }]}
                >
                  <Select
                    placeholder="Select version"
                    className="tw-rounded-lg"
                    disabled={!selectedComponents[index]}
                    options={
                      selectedComponents[index]?.componentversions?.map(
                        (version) => ({
                          value: version.id,
                          label: `v${version?.version}`,
                        })
                      ) || []
                    }
                  />
                </Form.Item>
              </Col>
            </Row>
          </div>
        ))}

        {detectedPlaceholders.length === 0 && (
          <div className="tw-text-center tw-py-8">
            <Text className="tw-text-gray-500">
              No repeat placeholders detected in this component.
            </Text>
          </div>
        )}
      </Form>
    </Modal>
  );
};

export default SelectComponentModal;
