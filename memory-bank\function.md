#

### getFooter

- path: src/utils/footer.js
- Generates the HTML for the website's footer, including dynamic links to service areas.

#

### getHeader

- path: src/utils/header.js
- Generates the HTML for the website's header, including dynamic navigation links for services and buses.

#

### generateTailwindCSS

- path: src/utils/generatePreviewCSS.js
- Generates a string of CSS that includes a reset and a subset of Tailwind CSS classes for styling component previews.

#

### prePareContent

- path: src/utils/Content.js
- The main function that takes scraped data and a dynamic content form, and transforms it into a structured JSON object for the website. It uses several helper functions to process different types of pages.

#

### random

- path: src/utils/Content.js
- Returns a number that is +/- 10% of the input number.

#

### randomPrice

- path: src/utils/Content.js
- Returns a price with a random variation.

#

### getRandomTitle

- path: src/utils/Content.js
- Returns a random title for a given page key.

#

### generateGlobalPreviewHTML

- path: src/components/Components/content.jsx
- A comprehensive function that generates the complete HTML for a page preview, including the head, body, styles, and scripts. It handles component composition, placeholder replacement, and the injection of custom CSS and JavaScript.

#

### deviceConfigs

- path: src/components/Components/content.jsx
- A function that returns an object containing configurations for different device previews (laptop, tablet, mobile), including dimensions, icons, and labels.

#

### getSampleData

- path: src/components/Components/content.jsx
- A function that returns sample data based on a placeholder string.

#

### replacePlaceholders

- path: src/components/Components/content.jsx
- A helper function to replace placeholders in an HTML string with values from a content object.

#

### getComponentContent

- path: src/components/Components/content.jsx
- A helper function to get component-specific content from a hierarchical content JSON object.

#

### findNestedContent

- path: src/components/Components/content.jsx
- A helper function to find nested content within an object by a key path, useful for repeated components.

#

### processRepeatedComponent

- path: src/components/Components/content.jsx
- A helper function to process and generate HTML for repeated components.

#

### isNotNullOrEmpty

- path: src/util/functions.js
- Checks if a value is not null, undefined, or an empty string (including strings with only whitespace).

#

### apiGenerator

- path: src/util/functions.js
- Generates an API endpoint string by replacing placeholders with values from an exchange pair object.

#

### generateRoutes

- path: src/util/functions.js
- Recursively generates routes based on a role, filtering out routes that the role does not have access to.

#

### autoDetectRepeatPlaceHolder

- path: src/util/functions.js
- Detects and returns unique placeholder strings in the format `{{placeholder}}` from a given string.

#

### autoDetectPlaceholders

- path: src/util/functions.js
- Detects and returns unique placeholder strings in the format `${placeholder}` or `${_img-${placeholder}}` from a given string.

#

### updateAtPath

- path: src/util/functions.js
- Updates a value in a nested object or array at a given path.

#

### addAtPath

- path: src/util/functions.js
- Adds a new item to an array within a nested object at a given path.

#

### deleteAtPath

- path: src/util/functions.js
- Deletes an item from an array or a key from an object within a nested structure at a given path.

#

### findAndReplaceVariable

- path: src/util/functions.js
- Recursively finds and replaces variables in a string with values from a content object, handling nested variables and media files.

#

### replaceTemplateContent

- path: src/util/functions.js
- Replaces placeholders in a page's components with content from various sources to generate the final HTML.

#

### parseContent

- path: src/util/functions.js
- Parses HTML content from a document and transforms it into a structured JSON object based on h1, h2, and p tags.

#

### deepBlank

- path: src/util/functions.js
- Recursively traverses an object or array and replaces all primitive values with their "empty" equivalents.

#

### filterEmptyFields

- path: src/util/functions.js
- Recursively filters out empty fields from an object or array.

#

### getCompanySlug

- path: src/util/functions.js
- Converts a string into a URL-friendly slug.

#
