import React from "react";
import useInternetConnection from "../../hooks/useInternetConnection";
import { ROUTES } from "../../util/Route";

const InternetConnectionMonitor = () => {
  // Use the internet connection hook with automatic redirection
  useInternetConnection({
    redirectOnOffline: true,
    redirectUrl: `/${ROUTES.internetLoss}`,
    autoRetry: true,
    maxRetries: 1,
    retryDelay: 4000,
    showNotifications: true,
  });

  // This component doesn't render anything, it just monitors connection
  return null;
};

export default InternetConnectionMonitor;
