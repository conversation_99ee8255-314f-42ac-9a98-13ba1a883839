import React, { useState, useEffect, useRef, useCallback } from "react";
import { Edit2, Trash2, Plus, FolderTree, Pencil } from "lucide-react";
// import useHttp from "../../hooks/use-http"; // Commented for future API use

import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { apiGenerator, removeSpacesDeep } from "../../util/functions";
import {
  Button,
  Spin,
  Card,
  Form,
  Typography,
  Row,
  Col,
  Empty,
  Popconfirm,
  message,
  Tooltip,
  Divider,
} from "antd";
import { PlusOutlined } from "@ant-design/icons";
import Categorymodal from "./CategoryComponent/Categorymodal";
import { predefinedColors } from "../../util/content";
import useHttp from "../../hooks/use-http";
import { Pagination, Radio } from "antd";
import ScrollPagination from "../common/ScrollPagination";
import SearchBar from "../common/SearchBar";
import useDebounce from "../../hooks/useDebounce";
import CustomTooltip from "../common/CustomTooltip";
import { useAuth } from "../../contexts/AuthContext";

const { Title, Text, Paragraph } = Typography;

const CategoryManager = () => {
  const { user } = useAuth();
  console.log(user);
  const [categories, setCategories] = useState([]);
  const [showForm, setShowForm] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [saving, setSaving] = useState(false);
  const api = useHttp(); // Commented for future API use
  // const api = useStorage(); // Using JSON storage
  const [form] = Form.useForm();
  const [searchTerm, setSearchTerm] = useState("");
  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    colour: "#3B82F6",
  });
  // Patch single item in infinite scroll list after edit/create without refetch
  const [patchedItem, setPatchedItem] = useState(null);
  // Pagination state and view mode
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(9);
  // const [viewMode, setViewMode] = useState("scroll"); // 'pagination' | 'scroll'

  // Keep last fetched params to avoid duplicate fetches (e.g., StrictMode)
  // const lastFetchRef = useRef({ page: null, size: null, mode: null });

  // Fetch categories for numbered pagination (stable)
  const fetchCategories = useCallback((page, size) => {
    api.sendRequest(
      CONSTANTS.API.categories.get,
      (res) => {
        setCategories(res?.data?.rows || []);
        setTotalCount(res?.data?.count || 0);
      },
      { page, limit: size }
    );
  }, []);

  // useEffect(() => {
  //   fetchCategories(currentPage, pageSize);
  // }, [fetchCategories, currentPage, pageSize]);

  // Loader for infinite scroll component (returns a Promise) - memoized to avoid identity changes
  const loadPage = useCallback(
    ({ page, pageSize: size }) =>
      new Promise((resolve, reject) => {
        const payload = {
          page,
          limit: size,
          search: debouncedSearchTerm || "",
        };
        api.sendRequest(
          CONSTANTS.API.categories.get,
          (res) => {
            const rows = res?.data?.rows || [];
            const count = res?.data?.count || 0;
            setTotalCount((prev) => (prev !== count ? count : prev));
            resolve({ items: rows, totalCount: count });
          },
          payload,
          null,
          (err) => reject(err)
        );
      }),
    [debouncedSearchTerm]
  );

  const renderCategoryCard = (category) => (
    <Card
      className="tw-h-full tw-shadow-sm hover:tw-shadow-lg tw-transition-all tw-duration-300 tw-border-1 tw-border-gray-200 tw-rounded-[20px]"
      styles={{
        body: {
          padding: "24px",
        },
      }}
    >
      <div className="tw-mb-4">
        <div className="tw-flex tw-items-center tw-justify-between">
          <div className="tw-flex tw-items-center tw-mb-3 tw-gap-x-2 tw-min-w-0 tw-flex-1">
            <div
              className="tw-w-4 tw-h-4 tw-rounded-full  tw-flex-shrink-0"
              style={{
                backgroundColor: category?.colour || "#3B82F6",
              }}
            />
            <Title
              ellipsis={{ rows: 1, expandable: false, tooltip: true }}
              level={4}
              className="!tw-mb-0 tw-text-gray-900 tw-truncate tw-min-w-0"
            >
              {category?.name}
            </Title>
          </div>
          <div>
            <CustomTooltip title="Edit Category" key="edit">
              <Button
                type="text"
                icon={<Pencil className="tw-w-4 tw-h-4" />}
                onClick={() => handleEdit(category)}
                className="tw-text-gray-500 hover:tw-text-blue-600"
              />
            </CustomTooltip>

            <CustomTooltip title="Delete Category" key="delete">
              <Popconfirm
                title="Delete Category"
                description="Are you sure you want to delete this category?"
                onConfirm={() => handleDelete(category?.id)}
                okText="Yes"
                cancelText="No"
                okButtonProps={{ danger: true }}
              >
                <Button
                  type="text"
                  danger
                  icon={<Trash2 className="tw-w-4 tw-h-4" />}
                  className="tw-text-gray-500 hover:tw-text-red-600"
                />
              </Popconfirm>
            </CustomTooltip>
          </div>
        </div>
        {/* when 2 line more then tooltip hove show full content */}
        {category?.description && (
          <Paragraph
            ellipsis={{ rows: 2, expandable: false, tooltip: true }}
            className="tw-text-gray-600 tw-text-sm tw-mb-4"
          >
            {category?.description}
          </Paragraph>
        )}
      </div>

      <div className="tw-flex tw-items-center tw-justify-between tw-border-gray-100">
        <div className="tw-flex tw-items-center tw-gap-2">
          <Text type="secondary" className="tw-text-xs">
            ID: {category?.id}
          </Text>
        </div>
        <Text type="secondary" className="tw-text-xs">
          {category?.createdAt || category?.created_at
            ? new Date(
                category?.createdAt || category?.created_at
              )?.toLocaleDateString()
            : "N/A"}
        </Text>
      </div>
    </Card>
  );

  // useEffect(() => {
  //   if (viewMode !== "pagination") return;
  //   console.log(viewMode, currentPage, pageSize);
  //   const last = lastFetchRef.current;
  //   if (
  //     last.page === currentPage &&
  //     last.size === pageSize &&
  //     last.mode === viewMode
  //   )
  //     return;
  //   lastFetchRef.current = {
  //     page: currentPage,
  //     size: pageSize,
  //     mode: viewMode,
  //   };
  //   fetchCategories(currentPage, pageSize);
  // }, [currentPage, pageSize, viewMode, fetchCategories]);

  const handleSubmit = async (values) => {
    try {
      setSaving(true);

      const submitData = removeSpacesDeep({
        ...values,
        colour: formData.colour, // Include color from state
      });

      const apiConfig = editingCategory
        ? apiGenerator(CONSTANTS.API.categories.update, {
            id: editingCategory.id,
          })
        : CONSTANTS.API.categories.create;

      api.sendRequest(
        apiConfig,
        (res) => {
          message.success(
            `Category ${editingCategory ? "updated" : "created"} successfully!`
          );

          // Build updated item from response or fallback to submitted data
          const serverItem = res?.data;
          const updatedItem =
            serverItem && serverItem.id !== undefined
              ? serverItem
              : {
                  ...(editingCategory || {}),
                  ...submitData,
                  id: editingCategory?.id,
                };

          // Patch UI list without refetch (infinite scroll mode)
          setPatchedItem({
            ...updatedItem,
            type: editingCategory ? "update" : "create",
          });

          resetForm();
          setSaving(false);
        },
        submitData,
        null, // Remove duplicate message
        (error) => {
          // console.error("Error saving category:", error);
          message.error(error || "Failed to save category. Please try again.");
          setSaving(false);
        }
      );
    } catch (error) {
      console.error("Form validation failed:", error);
      setSaving(false);
    }
  };

  const handleEdit = (category) => {
    setEditingCategory(category);
    const initialData = {
      name: category.name,
      description: category.description || "",
      colour: category.colour || "#3B82F6",
    };
    setFormData({
      ...initialData,
    });
    form.setFieldsValue(initialData);
    setShowForm(true);
  };

  const handleDelete = async (id) => {
    api.sendRequest(
      apiGenerator(CONSTANTS.API.categories.delete, { id }),
      (res) => {
        setPatchedItem({
          id,
          type: "delete",
        });
        console.log("Category deleted successfully:", res);
        message.success("Category deleted successfully!");
        // Refresh categories list (pagination mode)
        // if (viewMode === "pagination") {
        //   fetchCategories(currentPage, pageSize);
        // }
      },
      null,
      null, // Remove duplicate message
      (error) => {
        console.error("Error deleting category:", error);
        message.error("Failed to delete category. Please try again.");
      }
    );
  };

  const resetForm = () => {
    const defaultData = {
      name: "",
      description: "",
      colour: "#3B82F6",
    };
    form.resetFields();
    form.setFieldsValue(defaultData);
    setFormData(defaultData);
    setEditingCategory(null);
    setShowForm(false);
    setSaving(false);
  };

  // if (api.isLoading) {
  //   return (
  //     <div className="tw-flex tw-justify-center tw-items-center tw-h-screen tw-w-full">
  //       <Spin size="large" />
  //     </div>
  //   );
  // }

  return (
    <div className="tw-p-6 tw-bg-gray-50 tw-min-h-screen">
      <div className="tw-max-w-7xl tw-mx-auto">
        {/* Header Section */}
        <div className="tw-mb-8">
          <div className="tw-flex tw-justify-between tw-items-center">
            <div>
              <Title level={3} className="!tw-mb-0">
                Categories ({totalCount})
              </Title>
            </div>

            <div className="tw-flex tw-items-center tw-gap-3">
              <Button
                type="primary"
                size="large"
                icon={<Plus />}
                onClick={() => setShowForm(true)}
                className="tw-px-6 tw-h-10 tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
              >
                Add Category
              </Button>
            </div>
          </div>
          <Divider className="tw-my-6" />
        </div>
        {/* Search and Filter Section */}
        <div className="tw-mb-4">
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={24} md={24}>
              <SearchBar
                placeholder="Search categories"
                value={searchTerm}
                handleSearch={(e) => setSearchTerm(e)}
                size="large"
                type="page"
                // className="search-input-enhanced"
                allowClear
              />
            </Col>
          </Row>
        </div>

        {/* Category Form Modal */}
        <Categorymodal
          showForm={showForm}
          setShowForm={setShowForm}
          formData={formData}
          setFormData={setFormData}
          editingCategory={editingCategory}
          setEditingCategory={setEditingCategory}
          predefinedColors={predefinedColors}
          saving={saving}
          handleSubmit={handleSubmit}
          form={form}
          resetForm={resetForm}
        />

        <ScrollPagination
          key={`cat-${debouncedSearchTerm}-${pageSize}`}
          loadPage={loadPage}
          updatedItem={patchedItem}
          idKey="id"
          renderItem={(category) => (
            <div key={category?.id}>{renderCategoryCard(category)}</div>
          )}
          pageSize={pageSize}
          useWindow
          className="tw-grid tw-grid-cols-1 sm:tw-grid-cols-2 xl:tw-grid-cols-3 tw-gap-6"
          emptyScreen={
            <Card className="tw-text-center tw-py-16 tw-bg-transparent">
              <Empty
                image={
                  <FolderTree className="tw-w-16 tw-h-16 tw-text-gray-400" />
                }
                // image={Empty.PRESENTED_IMAGE_SIMPLE}

                description={
                  <div>
                    <Text
                      type="primary"
                      className="tw-text-2xl tw-font-semibold tw-block tw-mb-2 tw-text-font-color"
                    >
                      {debouncedSearchTerm
                        ? "No categories found"
                        : "No categories yet"}
                    </Text>
                    <Text type="secondary">
                      {debouncedSearchTerm
                        ? "Try adjusting your search criteria"
                        : "Create your first category to organize components"}
                    </Text>
                  </div>
                }
              >
                {/* <Button
                  type="primary"
                  size="large"
                  icon={<PlusOutlined />}
                  onClick={() => setShowForm(true)}
                  className="tw-mt-4 tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-border-0"
                >
                  Create First Category
                </Button> */}
              </Empty>
            </Card>
          }
        />
      </div>
    </div>
  );
};

export default CategoryManager;
