import React from "react";
import { Input } from "antd";
import { SearchOutlined } from "@ant-design/icons";

const UserSearch = ({ searchText, onSearchChange }) => {
  return (
    <Input
      placeholder="Search user"
      prefix={<SearchOutlined />}
      value={searchText}
      onChange={(e) => onSearchChange(e.target.value)}
      allowClear
      style={{ maxWidth: 300, width: "100%" }}
      className="tw-w-full sm:tw-w-auto"
    />
  );
};

export default UserSearch;
