// build-svg-switch.js
// Usage: node build-svg-switch.js ./src ./src/svg.js
// If outFile is omitted, it writes to ./src/svg.js by default.
import fs from 'fs';
import path from 'path';

function escapeForTemplateLiteral(svg) {
    // Keep the content intact but make it safe inside a template literal.
    return svg
        .replace(/\\/g, '\\\\')      // escape backslashes first
        .replace(/`/g, '\\`')        // backticks
        .replace(/\$\{/g, '\\${');   // ${ ... } so it doesn't interpolate
}

function readAllSvgs(dir) {
    const results = [];
    (function walk(current) {
        for (const entry of fs.readdirSync(current, { withFileTypes: true })) {
            const full = path.join(current, entry.name);
            if (entry.isDirectory()) {
                walk(full);
            } else if (entry.isFile() && entry.name.toLowerCase().endsWith('.svg')) {
                const raw = fs.readFileSync(full, 'utf8').trim();
                // strip XML declaration if present (optional safeguard)
                const cleaned = raw.replace(/^\s*<\?xml[\s\S]*?\?>\s*/i, '').trim();
                results.push({
                    name: path.basename(entry.name, '.svg'), // filename without extension
                    svg: escapeForTemplateLiteral(cleaned),
                    relPath: path.relative(dir, full).split(path.sep).join('/'),
                });
            }
        }
    })(dir);
    return results.sort((a, b) => a.name.localeCompare(b.name));
}

function generateSwitchFile(entries) {
    const cases = entries.map(
        ({ name, svg }) => `    case '${name}':\n      return \`${svg}\`;`
    ).join('\n\n');

    return `// AUTO-GENERATED FILE — do not edit.
// Generated by build-svg-switch.js
// Exposes: getSvg(name: string): string | null
// icon names correspond to SVG filenames (without ".svg")

/**
 * Return raw SVG markup (as a string) for a given icon name.
 * Example: getSvg('arrow-left')
 */
export function getSvg(name) {
  switch (name) {
${cases}

    default:
      return null; // unknown icon
  }
}
`;
}

function main() {
    const srcDir = process.argv[2] ? path.resolve(process.argv[2]) : path.resolve('src');
    const outFile = process.argv[3]
        ? path.resolve(process.argv[3])
        : path.join(srcDir, 'svg.js');

    if (!fs.existsSync(srcDir) || !fs.statSync(srcDir).isDirectory()) {
        console.error(`Source folder not found: ${srcDir}`);
        process.exit(1);
    }

    const svgs = readAllSvgs(srcDir);
    if (svgs.length === 0) {
        console.warn(`No .svg files found in ${srcDir}`);
    }

    const fileContent = generateSwitchFile(svgs);
    fs.writeFileSync(outFile, fileContent, 'utf8');

    console.log(`✅ Generated ${outFile} with ${svgs.length} icons.`);
    console.log(`ℹ️  Icon names (use exactly as filename without .svg):`);
    console.log('   ' + svgs.map(s => s.name).join(', '));
}


main();


