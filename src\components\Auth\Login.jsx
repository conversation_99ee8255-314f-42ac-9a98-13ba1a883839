import React, { useState } from "react";
import { Navigate } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import sideBarImg from "../../../public/img/login-side.jpg";
import logoImg from "../../../public/img/dream-logo.png";
import { Mail, LockKeyhole } from "lucide-react";
import { Form, Input, Button, Image, notification } from "antd";

const Login = () => {
  const [form] = Form.useForm();
  // const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const { user, login } = useAuth();

  if (user) {
    return <Navigate to="/" />;
  }

  const handleSubmit = async (values) => {
    setLoading(true);
    // setError("");

    const result = await login(values);

    if (!result.success) {
      notification.error({
        message: "Login Failed",
        description: result.error || "An error occurred during login.",
        // duration: 5,
      });
      // setError(result.error);
    }

    setLoading(false);
  };

  const handleFormChange = () => {
    // Clear error when user starts typing
    // if (error) {
    //   setError("");
    // }
  };

  return (
    <div className="tw-h-screen tw-flex tw-flex-col lg:tw-flex-row tw-overflow-hidden">
      {/* Left Side - Illustration */}
      <div className="tw-hidden lg:tw-flex lg:tw-w-[45%] tw-bg-gradient-to-br tw-from-purple-400 tw-via-purple-500 tw-to-purple-600 tw-relative tw-overflow-hidden tw-m-3 tw-rounded-lg">
        <Image
          src={sideBarImg}
          alt="login-sidebar"
          wrapperClassName="!tw-w-full"
          className="!tw-w-full !tw-h-full tw-object-cover tw-rounded-lg"
          preview={false}
        />
      </div>

      {/* Right Side - Login Form */}
      <div className="tw-flex-1 lg:tw-w-[55%] tw-flex tw-items-center tw-justify-center tw-p-4 sm:tw-p-8 tw-bg-gray-50 tw-overflow-y-auto">
        <div className="tw-w-full tw-max-w-md tw-my-auto">
          <div className="tw-mb-4 sm:tw-mb-8 lg:tw-mb-12">
            <Image
              src={logoImg}
              alt="Logo"
              className="tw-w-48 sm:tw-w-60 lg:tw-w-72 tw-h-auto tw-mx-auto "
              preview={false}
            />
          </div>
          {/* Logo and Title */}
          <div className="tw-text-center tw-mb-6 sm:tw-mb-8">
            <h2 className="tw-text-2xl sm:tw-text-3xl lg:tw-text-4xl tw-font-bold tw-text-gray-900 tw-mb-2">
              Welcome Back
            </h2>
            <p className="tw-text-gray-600 tw-text-base sm:tw-text-lg">
              Login into your account to manage sites.
            </p>
          </div>

          {/* {error && (
            <div className="tw-bg-red-50 tw-border tw-border-red-200 tw-rounded-lg tw-p-4 tw-mb-6">
              <p className="tw-text-red-800 tw-text-sm">{error}</p>
            </div>
          )} */}

          <Form
            form={form}
            onFinish={handleSubmit}
            onValuesChange={handleFormChange}
            layout="vertical"
            className="tw-space-y-6"
          >
            <Form.Item
              name="email"
              label={
                <span className="tw-text-sm tw-font-medium tw-text-font-color">
                  Email
                </span>
              }
              rules={[
                { required: true, message: "Please enter your email" },
                { type: "email", message: "Please enter a valid email" },
              ]}
              className="tw-mb-6"
            >
              <Input
                placeholder="Enter email"
                prefix={<Mail className="tw-w-5 tw-h-5 tw-text-gray-400" />}
                className="tw-rounded-lg tw-py-3"
                size="large"
              />
            </Form.Item>

            <Form.Item
              name="password"
              label={
                <span className="tw-text-sm tw-font-medium tw-text-font-color">
                  Password
                </span>
              }
              rules={[
                { required: true, message: "Please enter your password" },
                { min: 6, message: "Password must be at least 6 characters" },
              ]}
              className="tw-mb-6"
            >
              <Input.Password
                placeholder="Enter password"
                prefix={
                  <LockKeyhole className="tw-w-5 tw-h-5 tw-text-gray-400" />
                }
                className="tw-rounded-lg tw-py-3"
                size="large"
              />
            </Form.Item>

            <Form.Item shouldUpdate className="tw-mb-0">
              {() => (
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  disabled={
                    !form.isFieldsTouched(true) ||
                    !!form
                      .getFieldsError()
                      .filter(({ errors }) => errors.length).length
                  }
                  className="tw-w-full tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-text-white tw-px-4 tw-py-3 tw-rounded-lg tw-font-medium tw-hover:tw-from-blue-700 tw-hover:tw-to-purple-700 tw-flex tw-items-center tw-justify-center tw-whitespace-nowrap tw-transition-all tw-duration-200 tw-disabled:tw-opacity-50 tw-disabled:tw-cursor-not-allowed tw-border-none tw-h-12"
                  size="large"
                >
                  {loading ? "Signing In..." : "Login"}
                </Button>
              )}
            </Form.Item>
          </Form>

          {/* </div> */}

          {/* <div className="tw-mt-8 tw-pt-6 tw-border-t tw-border-gray-200">
            <div className="tw-text-center tw-text-sm tw-text-gray-600">
              <p className="tw-mb-2">Demo Credentials:</p>
              <p>
                <strong>Admin:</strong> email: admin, password: admin123
              </p>
            </div>
          </div> */}
        </div>
      </div>
    </div>
  );
};

export default Login;
